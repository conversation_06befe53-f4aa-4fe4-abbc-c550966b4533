import sv.com.tarjetaone.buildsrc.AppConfig
import sv.com.tarjetaone.buildsrc.extension.common.commonAndroidDefaultConfig
import sv.com.tarjetaone.buildsrc.extension.common.commonProguardLibraryConfig
import sv.com.tarjetaone.buildsrc.extension.common.configureBuildFlavors
import sv.com.tarjetaone.buildsrc.extension.module.addAnalyticsPropsToBuildConfig

plugins {
    id("com.android.library")
    id("org.jetbrains.kotlin.android")
    alias(libs.plugins.ksp)
}

android {
    // Common default android configurations.
    commonAndroidDefaultConfig(project.name)

    // Common default proguard and product flavors logic.
    commonProguardLibraryConfig()
    configureBuildFlavors()
    addAnalyticsPropsToBuildConfig()

    kotlinOptions {
        jvmTarget = AppConfig.jvmTarget
    }
}

dependencies {
    implementation(project(":common"))

    // Hilt
    implementation(libs.hilt.android.library)
    ksp(libs.hilt.compiler)

    // Marketing SDKs
    implementation(libs.dynatrace.agent)
    implementation(libs.amplitude.analytics)
    implementation(libs.facebook.core)
    api(libs.appsflyer.sdk)
}
