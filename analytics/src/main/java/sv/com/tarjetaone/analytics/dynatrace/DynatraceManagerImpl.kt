package sv.com.tarjetaone.analytics.dynatrace

import android.app.Application
import android.util.Log
import com.dynatrace.android.agent.Dynatrace
import com.dynatrace.android.agent.conf.DynatraceConfigurationBuilder
import javax.inject.Inject
import sv.com.tarjetaone.analytics.BuildConfig
import sv.com.tarjetaone.analytics.dynatrace.DynatraceManager.ResponseError
import sv.com.tarjetaone.common.utils.extensions.TAG

/**
 * Manager class to centralize all Dynatrace functions, like error reporting, user identification, etc
 * All Dynatrace operations are exclusively for production only, if the Dynatrace star up doesn't succeed
 * Then any function to track events won't be invoked.
 */
class DynatraceManagerImpl @Inject constructor() : DynatraceManager {

    private val canReportErrors = !BuildConfig.DEBUG

    override fun startup(application: Application, applicationId: String, beaconUrl: String) {
        // startup Dynatrace for prod environment only
        if (canReportErrors) {
            Dynatrace.startup(
                application,
                DynatraceConfigurationBuilder(applicationId, beaconUrl)
                    .withUserOptIn(false)
                    .withActivityMonitoring(false)
                    .withStartupLoadBalancing(true)
                    .buildConfiguration()
            )
        }
    }

    /**
     * Sends an In-App event to Dynatrace.
     * @param event the event to be sent.
     */
    override fun sendInAppEvent(event: DynatraceEvent) {
        if (canReportErrors) {
            Dynatrace.enterAction(event.name).also { it.leaveAction() }
        } else {
            logMessage("Event sent: ${event.name}")
        }
    }

    /**
     * Report Any API error to Dynatrace.
     * @param responseCode the API error status code.
     * @param requestUrl the whole endpoint that failed.
     * @param requestError the error from the requestStatus obj.
     * @param responseError the error from the responseStatus obj.
     * @param stackTrace other msg or stackTrace when [requestError] and [responseError] are null.
     */
    override fun reportDynatraceError(
        responseCode: Int,
        requestUrl: String,
        requestError: ResponseError?,
        responseError: ResponseError?,
        stackTrace: String?,
        requestId: String?
    ) {
        val errorName = "Endpoint: $requestUrl | Req-Id: $requestId"
        val errorDesc = buildErrorThrowable(
            responseCode = responseCode,
            requestUrl = requestUrl,
            requestError = requestError,
            responseError = responseError,
            stackTrace = stackTrace
        )

        if (canReportErrors) {
            Dynatrace.reportError(errorName, errorDesc)
        } else {
            logMessage("Error reported - $errorName \n${errorDesc.message}")
        }
    }

    override fun identifyDynatraceUser(userId: String?) {
        if (canReportErrors) {
            Dynatrace.identifyUser(userId)
        } else {
            logMessage("User identified: $userId")
        }
    }

    override fun endDynatraceVisit() {
        if (canReportErrors) {
            Dynatrace.endVisit()
        } else {
            logMessage("Visit ended")
        }
    }

    private fun logMessage(msg: String) {
        Log.d(TAG, msg)
    }

    private fun buildErrorThrowable(
        responseCode: Int,
        requestUrl: String,
        requestError: ResponseError?,
        responseError: ResponseError?,
        stackTrace: String?
    ): Throwable {
        val requestErrorMsg = requestError?.let {
            """
                Request code: ${it.errorCode}
                Request message: ${it.errorMessage}
                
                
            """.trimIndent()
        }
        val responseErrorMsg = responseError?.let {
            """
                Response code: ${it.errorCode}
                Response message: ${it.errorMessage}
                
                
            """.trimIndent()
        }
        val endpointData = """
            Endpoint: $requestUrl
            HTTP code: $responseCode
            
        """.trimIndent()

        val description = buildString {
            requestErrorMsg?.let { append(it) }
            responseErrorMsg?.let { append(it) }
            append(endpointData)
            stackTrace?.let { append("\n$it \n\n") }
        }
        return Throwable(description)
    }
}
