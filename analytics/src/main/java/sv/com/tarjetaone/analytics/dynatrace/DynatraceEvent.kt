package sv.com.tarjetaone.analytics.dynatrace

/**
 * Represents all events that can be tracked using Dynatrace analytics.
 *
 * This sealed class groups different categories of user actions (e.g., Login, Home, etc.),
 * each represented as a sealed subclass. Use these events to ensure type-safe and consistent tracking.
 */
sealed class DynatraceEvent(val name: String) {

    /**
     * Events intended to be globally reused.
     */
    sealed class Global(name: String) : DynatraceEvent(name) {
        data object Continue : Global("continue")
        data object OpenBiometry : Global("open_biometry")
        data object ConfirmPortraitScan : Global("confirm_portrait_scan")
        data object RetakePortraitScan : Global("retake_portrait_scan")
        data object BiometryFraud : Global("biometry_fraud")
        data object BiometryFail : Global("biometry_fail")
        data object BiometryError : Global("biometry_error")
        data object BiometryBlock : Global("biometry_block")
        data object BiometryQuality : Global("biometry_quality")
        data object BiometryPos : Global("biometry_pos")
        data object BiometryFace : Global("biometry_face")
        data object BiometrySmall : Global("biometry_small")
        data object BiometryMany : Global("biometry_many")
        data object BiometryEyes : Global("biometry_eyes")
    }

    /**
     * Events related to the Onboarding process.
     */
    sealed class ShortCapture(name: String) : DynatraceEvent(name) {
        // Welcome actions
        data object ViewWelcome : ShortCapture("view_welcome")
        data object StartLogin : ShortCapture("start_login")
        data object StartOnboarding : ShortCapture("start_onboarding")
        data object StartCheckTc : ShortCapture("start_check_tc")
        data object StartViewTc : ShortCapture("start_view_tc")
        data object StartViewPolicy : ShortCapture("start_view_policy")

        // Term and Conditions actions
        data object ViewTermAndConditions : ShortCapture("view_term_and_conditions")
        data object TcReadText : ShortCapture("tc_read_text")

        // Begin onboarding actions
        data object ViewFirstInstruction : ShortCapture("view_first_instruction")
        data object BeginOnboarding : ShortCapture("begin_onboarding")
        data object ContinueOnboarding : ShortCapture("continue_onboarding")
        data object ContinueOnboardingAccept : ShortCapture("continue_onboarding_accept")

        // Tutorial document actions
        data object ViewDocumentTutorial : ShortCapture("view_document_tutorial")
        data object CloseDocumentTutorial1 : ShortCapture("close_document_tutorial1")
        data object DocumentTutorial3 : ShortCapture("document_tutorial3")
        data object CloseDocumentTutorial : ShortCapture("close_document_tutorial")

        // Tutorial selfie actions
        data object ViewSelfieTutorial : ShortCapture("view_selfie_tutorial")
        data object PortraitTutorial1 : ShortCapture("portrait_tutorial1")
        data object ClosePortraitTutorial1 : ShortCapture("close_portrait_tutorial1")

        // Document Preview actions
        data object ViewDocumentValidation : ShortCapture("view_document_validation")
        data object ConfirmDocumentScan : ShortCapture("confirm_document_scan")
        data object RetakeDocumentScan : ShortCapture("retake_document_scan")
        data object InvalidDocumentScan : ShortCapture("invalid_document_scan")
        data object TimeOutDocumentScan : ShortCapture("timeout_document_scan")

        // Selfie instruction actions
        data object ViewSelfieInstruction : ShortCapture("view_selfie_instruction")

        // Selfie validation actions
        data object ViewSelfieValidation : ShortCapture("view_selfie_validation")

        // Identity validation actions
        data object ViewBiometryValidation : ShortCapture("view_biometry_validation")

        // Personal data actions
        data object ViewPersonalData : ShortCapture("view_personal_data")
        data object ConfirmPersonalData : ShortCapture("confirm_personal_data")
        data object WrongPersonalData : ShortCapture("wrong_personal_data")
        data object ReportPersonalData : ShortCapture("report_personal_data")

        // Address validation actions
        data object ViewPersonalAddress : ShortCapture("view_personal_address")
        data object ConfirmAddress : ShortCapture("confirm_address")
        data object UpdateAddress : ShortCapture("update_address")
        data object RequestAddress : ShortCapture("request_address")

        // Map actions
        data object ViewAddressMap : ShortCapture("view_adress_map")
        data object SaveMap : ShortCapture("save_map")
        data object SaveUpdatedAddress : ShortCapture("save_updated_address")
        data object OpenMap : ShortCapture("open_map")

        // New address validation actions
        data object ViewAddressConfirmation : ShortCapture("view_address_confirmation")
        data object ConfirmMap : ShortCapture("confirm_map")
        data object UpdateMap : ShortCapture("update_map")
        data object ModifyUpdatedAddress : ShortCapture("modify_updated_address")

        // Phone actions
        data object ViewPhone : ShortCapture("view_phone")
        data object EnterPhone : ShortCapture("enter_phone")
        data object OtpWhatsapp : ShortCapture("otp_whatsapp")
        data object OtpSms : ShortCapture("otp_sms")

        // Email actions
        data object ViewEmail : ShortCapture("view_email")
        data object EnterEmail : ShortCapture("enter_email")

        // OTP validation actions
        data object ViewOtpVerification : ShortCapture("view_otp_verification")
        data object ConfirmOtp : ShortCapture("confirm_otp")
        data object RequestOtp : ShortCapture("request_otp")
        data object MaxRequestOtp : ShortCapture("max_request_otp")
        data object MaxWrongOtp : ShortCapture("max_wrong_otp")
        data object WrongOtp : ShortCapture("wrong_otp")

        // BUREAU actions
        data object ViewBureauAuthorization : ShortCapture("view_bureau_autorization")
        data object CheckBureau : ShortCapture("check_bureau")
        data object ReadBureau : ShortCapture("read_bureau")
        data object ConfirmBureau : ShortCapture("confirm_bureau")
        data object AuthorizeBureau : ShortCapture("authorize_bureau")
        data object RejectBureau : ShortCapture("reject_bureau")

        // First checkpoint actions
        data object ViewSaveShortCaptureInfo : ShortCapture("view_save_short_capture_info")
        data object CcApp1Success : ShortCapture("ccapp1_success")
        data object CcApp1Fail : ShortCapture("ccapp1_fail")
    }

    /**
     * Events related to OB Long capture flow
     */
    sealed class LongCapture(name: String) : DynatraceEvent(name) {
        // Demographic Data
        data object ViewMoreAboutYou : LongCapture("view_more_about_you")

        // Pep Question
        data object ViewPEP : LongCapture("view_pep")
        data object NoPEP : LongCapture("no_pep")
        data object YesPEP : LongCapture("yes_pep")

        // PEP validation
        data object ViewPEPValidation : LongCapture("view_pep_validation")

        // Incomes From
        data object ViewMonthlySalary : LongCapture("view_monthly_salary")
        data object MonthlySalaryContinue : LongCapture("monthly_salary_continue")

        // Work Information
        data object ViewWorkInformation : LongCapture("view_work_information")
        data object WorkInformationContinue : LongCapture("work_information_continue")

        // Work Address
        data object ViewWorkAddress : LongCapture("view_work_address")
        data object WorkAddressContinue : LongCapture("work_address_continue")

        // My Incomes
        data object ViewSalaryInfo : LongCapture("view_salary_info")
        data object AddAnother : LongCapture("add_another")
        data object InformationDelete : LongCapture("information_delete")
        data object InformationModify : LongCapture("information_modify")

        // Afp Validation
        data object ViewAfpIncomeValidation : LongCapture("view_afp_income_validation")
        data object Authorize : LongCapture("authorize")
        data object Omit : LongCapture("omit")

        // Afp Query
        data object ViewAfpConsult : LongCapture("view_afp_consult")
        data object Consult : LongCapture("consult")

        // Afp Save
        data object ViewSaveAfpInfo : LongCapture("view_save_afp_info")

        // References
        data object ViewReferences : LongCapture("view_references")
        data object ReferenceAddPersonal : LongCapture("reference_add_personal")
        data object ReferenceModifyPersonal : LongCapture("reference_modify_personal")
        data object ReferenceAddFamily : LongCapture("reference_add_family")
        data object ReferenceModifyFamily : LongCapture("reference_modify_family")

        // View Offer
        data object ViewOffer : LongCapture("view_offer")
        data object Accepted : LongCapture("accepted")
        data object ViewDateRejection : LongCapture("view_date_rejection")
        data object NotApprovedDate : LongCapture("not_approved_date")
        data object ViewUndefinedRejection : LongCapture("view_undefined_rejection")
        data object NotApprovedPermanent : LongCapture("not_approved_permanent")
        data object ViewIncomeRejection : LongCapture("view_income_rejection")
        data object NotApprovedIncome : LongCapture("not_approved_income")
        data object ViewPendingOffer : LongCapture("view_pending_offer")
        data object Waiting : LongCapture("waiting")
    }

    /**
     * Events related to trasiego flow.
     */
    sealed class Trasiego(name: String) : DynatraceEvent(name) {
        // Benefits
        data object ViewBenefits : Trasiego("view_benefits")

        // Cashback
        data object ViewCashback : Trasiego("view_cashback")
        data object CreateCashback : Trasiego("create_cashback")
        data object ViewAccountCreation : Trasiego("view_account_creation")
        data object SeeSavingsAccountContract : Trasiego("see_savings_account_contract")
        data object OtherBenefit : Trasiego("other_benefit")
        data object TypeBenefitCashback : Trasiego("type_benefit_cashback")
        data object ViewBeneficiaries : Trasiego("view_beneficiaries")
        data object BeneficiaryModify : Trasiego("beneficiary_modify")
        data object BeneficiaryDelete : Trasiego("beneficiary_delete")
        data object BeneficiaryAdd : Trasiego("beneficiary_add")
        data object BeneficiariesContinue : Trasiego("beneficiaries_continue")

        // Fatca
        data object ViewFatcaLaw : Trasiego("view_fatca_law")
        data object TypeOfferCashback : Trasiego("type_offer_cashback")

        // Save CCApplication 8
        data object ViewChoosingBenefitValidation : Trasiego("view_choosing_benefit_validation")

        // Contracts
        data object ViewReviewContracts : Trasiego("view_review_contracts")
        data object AcceptContracts : Trasiego("accept_contracts")
        data object SignContracts : Trasiego("sign_contracts")
        data object ViewSignContracts : Trasiego("view_sign_contracts")
        data object SelectSign : Trasiego("select_sign")
        data object Sign : Trasiego("sign")

        // Signature
        data object ViewSaveSignatureContract : Trasiego("view_save_signature_contract")

        // Delivery card
        data object ViewWhereDelivery : Trasiego("view_where_delivery")
        data object SendOtherAddress : Trasiego("send_other_address")
        data object WhereDeliverContinue : Trasiego("where_deliver_continue")
        data object ViewChooseDelivery : Trasiego("view_choose_delivery")
        data object ChooseDeliveryContinue : Trasiego("choose_delivery_continue")
        data object ViewDeliveryDetail : Trasiego("view_delivery_detail")
        data object SelectOtherDate : Trasiego("select_other_date") // TODO: Add this action once button is implemented in UI
        data object DeliveryDetailContinue : Trasiego("delivery_detail_continue")

        // Card personalization
        data object ViewCardPersonalization : Trasiego("view_card_personalization")
        data object SavePersonalization : Trasiego("save_personalization")
        data object ViewSaveCCPersonalization : Trasiego("view_save_cc_personalization")
        data object PersonalizeAgain : Trasiego("personalize_again")
        data object SendCard : Trasiego("send_card")

        // Save CCApplication 10
        data object ViewFinishOBValidation : Trasiego("view_finish_OB_validation")
    }

    /**
     * Events related to the login flow.
     */
    sealed class Login(name: String) : DynatraceEvent(name) {
        data object ViewLogin : Login("view_login")
        data object AttemptLogin : Login("log_in")
        data object FirstLogin : Login("first_login")
        data object FrequentLogin : Login("frequent_login")
        data object ForgotPassword : Login("forgot_pwd")
        data object ForgotUser : Login("forgot_user")
        data object NotMe : Login("not_me")
        data object AlertResetPwdContinue : Login("alert_reset_pwd_continue")
        data object ViewAlertCredentialsError : Login("view_alert_credentials_error")
        data object AlertCredentialsErrorAcceptance : Login("alert_credentials_error_acceptance")
        data object ViewBiometricLogin : Login("view_biometric_login")
        data object BiometricLogin : Login("biometric_login")
        data object BiometricLoginNotMe : Login("biometric_login_not_me")
        data object BiometricLoginPassword : Login("biometric_login_pwd")
    }

    /**
     * Events related to access recovery actions.
     */
    sealed class AccessRecovery(name: String) : DynatraceEvent(name) {
        data object ViewUpdatePasswordInfo : AccessRecovery("view_update_pwd_info")
        data object PasswordInfoContinue : AccessRecovery("pwd_info_continue")
        data object ViewUpdatePasswordRequest : AccessRecovery("view_update_pwd_request")
        data object ChangePassword : AccessRecovery("change_pwd")
        data object ViewPasswordConfirmation : AccessRecovery("view_pwd_confirmation")
        data object PasswordConfirmationContinue : AccessRecovery("pwd_confirmation_continue")
        data object ViewUserRecovery : AccessRecovery("view_user_recovery")
        data object UserRecovery : AccessRecovery("user_recovery")
        data object ViewSuccessUserRecovery : AccessRecovery("view_success_user_recovery")
        data object ReadySuccessUserRecovery : AccessRecovery("ready_success_user_recovery")
    }

    /**
     * Events triggered from the home screen.
     */
    sealed class Home(name: String) : DynatraceEvent(name) {
        data object ViewHome : Home("view_home")
        data object CardDetails : Home("card_details")
        data object CardMovements : Home("card_movements")
        data object AccountStatus : Home("account_status")
        data object GoCashback : Home("go_cashback")
        data object GoPoints : Home("go_points")
    }

    /**
     * Events related to activate biometric login
     */
    sealed class BiometricSheet(name: String) : DynatraceEvent(name) {
        data object Activate : BiometricSheet("activate")
        data object NoActivate : BiometricSheet("no_activate")
        data object TryAgain : BiometricSheet("try_again")
        data object ModalBiometryContinue : BiometricSheet("modal_biometry_continue")
    }

    /**
     * Events related to My movements screen
     */
    sealed class MyMovements(name: String) : DynatraceEvent(name) {
        data object ViewTransactions : MyMovements("view_transactions")
        data object TransactionDetail : MyMovements("transaction_detail")
        data object TransactionPointPayment : MyMovements("transaction_point_payment")
        data object TransactionReport : MyMovements("transaction_report")
    }

    /**
     * Events related to Account statement screen
     */
    sealed class MyAccountStatus(name: String) : DynatraceEvent(name) {
        data object ViewAccountStatus : MyAccountStatus("view_account_status")
        data object Download : MyAccountStatus("download")
    }

    /**
     * Events related to my mega points screen
     */
    sealed class MyMegaPoints(name: String) : DynatraceEvent(name) {
        data object ViewPoints : MyMegaPoints("view_points")
    }

    /**
     * Events related to my menu screen
     */
    sealed class MyMenu(name: String) : DynatraceEvent(name) {
        data object ViewMenu : MyMenu("view_menu")
        data object GoServiceRequests : MyMenu("go_service_requests")
        data object GoProtectionPlan : MyMenu("go_protection_plan")
        data object GoComplains : MyMenu("go_complains")
        data object GoDocuments : MyMenu("go_documentos")
        data object GoPersonalData : MyMenu("go_personal_data")
        data object GoConfigurationNotification : MyMenu("go_configuration_notification")
        data object CloseSession : MyMenu("close_session")
        data object GoDeleteAccount : MyMenu("go_delete_account")
    }

    /**
     * Events related to card menu screen
     */
    sealed class CardMenu(name: String) : DynatraceEvent(name) {
        data object ViewCardMenu : CardMenu("view_card_menu")
        data object GoCardOptions : CardMenu("go_card_options")
        data object GoPayCard : CardMenu("go_pay_card")
        data object GoLifemiles : CardMenu("go_lifemiles_transaction")
        data object GoChangePin : CardMenu("go_change_pin")
        data object GoCancelCard : CardMenu("go_cancel_card")
        data object GoBlockCard : CardMenu("go_card_block")
        data object GoAdditionalCard : CardMenu("go_request_additional_card")
    }

    /**
     * Events related to product to pay screen
     */
    sealed class ProductToPay(name: String) : DynatraceEvent(name) {
        data object ViewProductToPay : ProductToPay("view_product_to_pay")
        data object Continue : ProductToPay("continuar")
    }

    /**
     * Events related to credit card payment screen
     */
    sealed class CreditCardPayment(name: String) : DynatraceEvent(name) {
        data object ViewPayCard : CreditCardPayment("view_pay_card")
        data object PayFullAmount : CreditCardPayment("pay_full_amount")
        data object PayMinimumAmount : CreditCardPayment("pay_minimum_amount")
        data object PayAnotherAmount : CreditCardPayment("pay_another_amount")
        data object PayCardContinue : CreditCardPayment("pay_card_continuar")
    }

    /**
     * Events related to life miles points translate screen
     */
    sealed class LifeMiles(name: String) : DynatraceEvent(name) {
        data object ViewLifeMilesTransaction : LifeMiles("view_lifemiles_transaction")
        data object LifeMilesContinue : LifeMiles("lifemiles_transaction_continuar")
    }

    /**
     * Events related to pin change screen
     */
    sealed class ChangePin(name: String) : DynatraceEvent(name) {
        data object ViewChangePin : ChangePin("view_change_pin")
        data object Continue : ChangePin("continue_change_pin")
        data object SavePin : ChangePin("save_change_pin")
    }

    /**
     * Events related to card options screen
     */
    sealed class CardOptions(name: String) : DynatraceEvent(name) {
        data object ViewCardOptions : CardOptions("view_card_options")
        data object CardActivation : CardOptions("card_activation")
        data object CashWithdrawal : CardOptions("cash_withdrawal")
        data object OnlinePurchase : CardOptions("online_purchase")
        data object InternationalPurchase : CardOptions("international_purchase")
        data object LimitPurchase : CardOptions("limit_purchase")
    }
}
