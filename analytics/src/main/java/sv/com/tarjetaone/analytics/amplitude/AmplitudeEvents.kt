package sv.com.tarjetaone.analytics.amplitude

object AmplitudeEvents {
    const val ON_START_ONBOARDING_EVENT = "iniciar onboarding"
    const val ON_DUI_SCAN_EVENT = "escanear dui"
    const val ON_TAKE_PHOTOGRAPHY_EVENT = "tomar fotografia"
    const val ON_SEND_OTP_EVENT = "enviar otp"
    const val ON_RESEND_OTP_EVENT = "reenviar otp"
    const val ON_CONTINUE_REQUEST_EVENT = "continuar solicitud"
    const val ON_COMPLETE_REVENUE_TYPE_EVENT = "completar tipo ingreso"
    const val ON_ADD_REVENUE_TYPE_EVENT = "agregar otro ingreso"
    const val ON_AUTHORIZE_AFP_EVENT = "autorizar afp"
    const val ON_SALE_VIEW_EVENT = "ver pantalla oferta"
    const val ON_SALE_ACCEPT_EVENT = "aceptar oferta"
    const val ON_SELECT_ADDRESS_EVENT = "seleccionar direccion"
    const val ON_SELECT_SCHEDULE_EVENT = "seleccionar horario"
    const val ON_SELECT_BENEFIT_EVENT = "seleccionar beneficio"
    const val ON_ACCEPT_SAVING_ACCOUNT_EVENT = "aceptar cuenta ahorro"
    const val ON_SELECT_COLOR_EVENT = "seleccionar color"
    const val ON_FINISH_ONBOARDING_EVENT = "finalizar onboarding"
    const val ON_COMPLETE_SURVEY_EVENT = "completar encuesta"
    const val ON_SELECT_CONTACT_EVENT = "seleccionar forma contacto"
    const val ON_SHOW_BIOMETRIC_ERROR = "mostrar error biometria"

    //region category: IN-APP
    const val ON_LOGIN_EVENT = "iniciar sesion"
    const val ON_LOGIN_FIRST_TIME_EVENT = "iniciar sesion primera vez"
    const val ON_CHANGE_PASSWORD_EVENT = "cambiar contraseña login"
    const val ON_TAKE_PICTURE_ACTIVATION_EVENT = "tomar fotografia activacion"
    const val ON_CHECK_ACTIVATION_DIGITS_EVENT = "verificar digitos activacion"
    const val ON_ACTIVATE_CARD_EVENT = "activar tarjeta"
    const val ON_USER_RECOVERY_EVENT = "recuperar usuario"
    const val ON_ACTIVATE_BIOMETRIC_LOGIN_EVENT = "activar login biometrico"
    const val ON_OPEN_PROTECTION_PROGRAM_MENU_EVENT = "abrir menu programa proteccion"
    const val ON_START_CONTRACTING_PROTECTION_PROGRAM_EVENT = "iniciar contratacion programa proteccion"
    const val ON_SIGN_PROTECTION_PROGRAM_EVENT = "firmar programa proteccion"
    const val ON_TAKE_PICTURE_GET_PROTECTION_PROGRAM_EVENT = "tomar fotografia contratación programa proteccion"
    const val ON_GET_PROTECTION_PROGRAM = "contratar programa proteccion"
    const val ON_TAKE_PICTURE_ACTIVE_PRODUCT = "tomar fotografia producto activo"
    //endregion category: IN-APP

    enum class BiometryError(val code: String) {
        MORPHOLOGY(code = "Morfología"),
        FRAUD(code = "Fraude"),
        FAILED_ATTEMPT(code = "Intento fallido"),
        FACEPHI_ERROR(code = "Error Facephi"),
        BLOCKING(code = "Bloqueo"),
        DOES_NOT_MATCH(code = "No hace match"),
        NOT_VALID_DOCUMENT(code = "Documento inválido"),
        RETRY_MORPHOLOGY(code = "Reintento morfología")
    }
}

object AmplitudeEventProperties {
    const val SEND_OTP = "otp enviado"
    const val RESEND_OTP = "otp reenviado"
    const val CELL_OTP_METHOD = "Celular"
    const val MAIL_OTP_METHOD = "Correo"
    const val ADDED_INCOME = "ingreso agregado"
    const val CARD_SALE_TYPE = "oferta tipo"
    const val CARD_SALE_TYPE_ACCEPTED = "Aprobada"
    const val CARD_SALE_TYPE_REJECTED = "Rechazada"
    const val CARD_SALE_TYPE_PENDING = "En evaluación"
    const val CARD_SELECTED_COLOR = "color seleccionado"
    const val CARD_SALE_AMOUNT = "oferta monto"
    const val CARD_SALE_RATE = "oferta tasa"
    const val CARD_SEND_ADDRESS = "envio direccion"
    const val CARD_SEND_SCHEDULE = "envio horario"
    const val CARD_BENEFIT_SELECTED = "beneficio seleccionado"
    const val SURVEY_GRADE = "calificacion obtenida"
    const val SURVEY_NEGATIVE_RESULT = "negativa"
    const val SURVEY_NEUTRAL_RESULT = "neutra"
    const val SURVEY_POSITIVE_RESULT = "positiva"
    const val SURVEY_CONTACT_CHANNEL = "forma contacto"
    const val SURVEY_PHONE_CONTACT = "Teléfono"
    const val SURVEY_MAIL_CONTACT = "Correo"
    const val SURVEY_NONE_CONTACT = "Ninguna"
    const val OTHER = "otro"
    const val RESIDENCE = "residencia"
    const val HOME = "Casa"
    const val JOB = "trabajo"
    const val OFFICE = "Oficina"
    const val ERROR_TYPE = "tipo de error"
}
