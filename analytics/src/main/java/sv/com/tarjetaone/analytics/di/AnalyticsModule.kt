package sv.com.tarjetaone.analytics.di

import android.content.Context
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import sv.com.tarjetaone.analytics.amplitude.AmplitudeManager
import sv.com.tarjetaone.analytics.amplitude.AmplitudeManagerImpl
import javax.inject.Singleton
import sv.com.tarjetaone.analytics.appsflyer.AppsFlyerManager
import sv.com.tarjetaone.analytics.appsflyer.AppsFlyerManagerImpl
import sv.com.tarjetaone.analytics.dynatrace.DynatraceManager
import sv.com.tarjetaone.analytics.dynatrace.DynatraceManagerImpl

@Module
@InstallIn(SingletonComponent::class)
object AnalyticsModule {
    @Provides
    @Singleton
    fun provideDynatraceManager(): DynatraceManager = DynatraceManagerImpl()

    @Provides
    @Singleton
    fun provideAppsFlyerManager(@ApplicationContext context: Context): AppsFlyerManager =
        AppsFlyerManagerImpl(context)

    @Provides
    @Singleton
    fun provideAmplitudeManager(): AmplitudeManager = AmplitudeManagerImpl()
}
