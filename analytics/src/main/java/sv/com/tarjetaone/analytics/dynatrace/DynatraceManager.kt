package sv.com.tarjetaone.analytics.dynatrace

import android.app.Application

interface DynatraceManager {

    fun startup(
        application: Application,
        applicationId: String,
        beaconUrl: String
    )

    fun reportDynatraceError(
        responseCode: Int,
        requestUrl: String,
        requestError: ResponseError?,
        responseError: ResponseError?,
        stackTrace: String?,
        requestId: String?
    )

    /**
     * Sends an In-App event to Dynatrace.
     */
    fun sendInAppEvent(event: DynatraceEvent)

    fun identifyDynatraceUser(userId: String?)

    fun endDynatraceVisit()

    data class ResponseError(
        val errorCode: Int?,
        val errorMessage: String?,
    )
}
