package sv.com.tarjetaone.analytics.amplitude

import com.amplitude.android.Amplitude
import com.amplitude.android.Configuration

class AmplitudeManagerImpl : AmplitudeManager {
    private var amplitude: Amplitude? = null

    override fun initialize(config: Configuration) {
        amplitude?.flush()
        amplitude = Amplitude(config)
    }

    override fun track(type: String, properties: Map<String, Any?>?) {
        amplitude?.track(type, properties)
    }

    override fun setUserId(userId: String?) {
        resetSession()
        amplitude?.setUserId(userId)
    }

    override fun resetSession() {
        amplitude?.apply {
            flush()
            reset()
        }
    }
}
