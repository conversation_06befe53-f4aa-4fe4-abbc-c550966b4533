package sv.com.tarjetaone.analytics.appsflyer

import android.content.Context
import android.util.Log
import com.appsflyer.AppsFlyerLib
import com.appsflyer.deeplink.DeepLinkResult
import javax.inject.Inject
import sv.com.tarjetaone.common.utils.extensions.TAG

class AppsFlyerManagerImpl @Inject constructor(val context: Context) : AppsFlyerManager {

    /**
     * Initialize the AppsFlyerSDK to register marketing events.
     *
     * Call this function before calling start, ideally in the application class.
     * @param key the AppsFlyer key.
     */
    override fun init(key: String) {
        AppsFlyerLib.getInstance().apply {
            init(key, null, context)
            subscribeForDeepLink { onDeepLinkResult(it) }
            start(context)
        }
    }

    /**
     * Handle deepLink result from A<PERSON><PERSON>ly<PERSON>, check if status is found to get the deepLink data.
     *
     * @param deepLinkResult the result of the deepLink.
     */
    private fun onDeepLinkResult(deepLinkResult: DeepLinkResult) {
        if (deepLinkResult.status == DeepLinkResult.Status.FOUND) {
            val deepLinkData = deepLinkResult.deepLink
            try {
                Log.d(TAG, "deeplink data: ${deepLinkData?.deepLinkValue}")
            } catch (e: Exception) {
                Log.d(TAG, "Failed to get deepLink data: ${e.message.orEmpty()}")
            }
        }
    }

    /**
     * Set the customer user id to AppsFlyer.
     *
     * @param customerId the customerId returned from BE after login or onboarding registration.
     */
    override fun setCustomerUserId(customerId: String?) {
        AppsFlyerLib.getInstance().setCustomerUserId(customerId)
    }

    /**
     * Logs any app event to AppsFlyer.
     *
     * @param eventName the name of the event.
     * @param eventData the data of the event represented as a HashMap of key values.
     */
    override fun logEvent(eventName: String, eventData: Map<String, Any>?) {
        AppsFlyerLib.getInstance().logEvent(context, eventName, eventData)
    }
}
