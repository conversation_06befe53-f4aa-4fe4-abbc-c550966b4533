{"formatVersion": 1, "database": {"version": 1, "identityHash": "fe18a0f3b8ccf0c9e0e508fed19ef39f", "entities": [{"tableName": "companies", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `isActive` INTEGER, `name` TEXT, `parentId` TEXT, `behaviourInfo` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isActive", "columnName": "isActive", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "parentId", "columnName": "parentId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "behaviourInfo", "columnName": "behaviourInfo", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "message", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`messageIndex` INTEGER NOT NULL, `body` TEXT NOT NULL, `date` TEXT NOT NULL, `isMedia` INTEGER NOT NULL, `mediaSId` TEXT, `isAction` INTEGER NOT NULL, `actionCode` INTEGER, `isFromSupport` INTEGER NOT NULL, `wasSave` INTEGER NOT NULL, PRIMARY KEY(`messageIndex`))", "fields": [{"fieldPath": "messageIndex", "columnName": "messageIndex", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "body", "columnName": "body", "affinity": "TEXT", "notNull": true}, {"fieldPath": "date", "columnName": "date", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isMedia", "columnName": "isMedia", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "mediaSId", "columnName": "mediaSId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isAction", "columnName": "isAction", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "actionCode", "columnName": "actionCode", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isFromSupport", "columnName": "isFromSupport", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "wasSave", "columnName": "wasSave", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["messageIndex"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'fe18a0f3b8ccf0c9e0e508fed19ef39f')"]}}