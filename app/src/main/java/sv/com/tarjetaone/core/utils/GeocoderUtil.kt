package sv.com.tarjetaone.core.utils

import android.location.Address
import android.location.Geocoder
import android.location.Geocoder.GeocodeListener
import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import com.google.android.gms.maps.model.LatLng
import com.google.android.libraries.places.api.model.Place
import com.google.android.libraries.places.api.net.FetchPlaceRequest
import com.google.android.libraries.places.api.net.FindAutocompletePredictionsRequest
import com.google.android.libraries.places.api.net.PlacesClient
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import javax.inject.Inject
import sv.com.tarjetaone.common.utils.AppConstants.COUNTRY_SV
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.SV_BOX_LOWER_LEFT_CORNER_LAT
import sv.com.tarjetaone.common.utils.AppConstants.SV_BOX_LOWER_LEFT_CORNER_LNG
import sv.com.tarjetaone.common.utils.AppConstants.SV_BOX_UPPER_RIGHT_CORNER_LAT
import sv.com.tarjetaone.common.utils.AppConstants.SV_BOX_UPPER_RIGHT_CORNER_LNG
import sv.com.tarjetaone.common.utils.isAndroidVersionTiramisuOrAbove
import sv.com.tarjetaone.domain.core.DispatchersProvider
import sv.com.tarjetaone.domain.entities.response.SuggestionsItemUi
import kotlin.coroutines.resume

/**
 * Created by Kenny Tapia
 *
 * This class is used to get information about the user's location
 * like address, coordinates, etc. It uses the Geocoder class and an implementation
 * of the Google Places API
 *
 * **/
class GeocoderUtilImpl @Inject constructor(
    private val geocoder: Geocoder,
    private val placesClient: PlacesClient,
    private val dispatchersProvider: DispatchersProvider
) : GeocoderUtil {

    /**
     * Allow to get the address information based on latitude and longitude for Android Tiramisu and above
     * @param coordinates latitude and longitude
     * @return instance of [Address]. Its return is null if there's no a match result
     * **/
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private suspend fun getAddressByLocationTiramisu(coordinates: Pair<Double, Double>): Address? =
        withContext(dispatchersProvider.io) {
            suspendCancellableCoroutine { cont ->
                geocoder.getFromLocation(
                    coordinates.first,
                    coordinates.second,
                    MAX_RESULTS_BY_ADDRESS_LOCATION,
                    object : GeocodeListener {
                        override fun onGeocode(addresses: MutableList<Address>) {
                            cont.resume(addresses.firstOrNull())
                        }

                        override fun onError(errorMessage: String?) {
                            super.onError(errorMessage)
                            // Resume with null due to there's no a defined way to handle the error in UI
                            cont.resume(null)
                            Log.e("GeocoderUtil", "Timeout or IO error: $errorMessage")
                        }
                    }
                )
            }
        }

    /**
     * Allow to get Address information based on latitude and longitude for Android versions below Tiramisu
     * @param coordinates latitude and longitude
     * @return instance of [Address]. Its return is null if there's no a match result
     * */
    @Suppress("DEPRECATION")
    private suspend fun getAddressByLocationLegacy(coordinates: Pair<Double, Double>): Address? =
        withContext(dispatchersProvider.io) {
            suspendCancellableCoroutine { cont ->
                runCatching {
                    val result = geocoder.getFromLocation(
                        coordinates.first,
                        coordinates.second,
                        MAX_RESULTS_BY_ADDRESS_LOCATION
                    )?.firstOrNull()
                    cont.resume(result)
                }.getOrElse {
                    Log.e("GeocoderUtil", "Timeout or IO error: ${it.message}")
                    cont.resume(null)
                }
            }
        }

    override suspend fun getAddressByLocation(coordinates: Pair<Double, Double>): Address? {
        return if (isAndroidVersionTiramisuOrAbove()) {
            getAddressByLocationTiramisu(coordinates)
        } else {
            getAddressByLocationLegacy(coordinates)
        }
    }


    /**
     * Allow to get the address information based on location name
     * @param name location name to search
     * @param maxResults max limit of results to return
     * @return list [Address]**/
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private suspend fun getAddressesByLocationName(name: String, maxResults: Int): List<Address>? {
        if (name.isBlank() or (maxResults < ONE_VALUE)) return null
        return withContext(dispatchersProvider.io) {
            suspendCancellableCoroutine { cont ->
                geocoder.getFromLocationName(
                    name,
                    maxResults,
                    SV_BOX_LOWER_LEFT_CORNER_LAT,
                    SV_BOX_LOWER_LEFT_CORNER_LNG,
                    SV_BOX_UPPER_RIGHT_CORNER_LAT,
                    SV_BOX_UPPER_RIGHT_CORNER_LNG,
                    object : GeocodeListener {
                        override fun onGeocode(addresses: MutableList<Address>) {
                            cont.resume(addresses)
                        }

                        override fun onError(errorMessage: String?) {
                            super.onError(errorMessage)
                            // Resume with null due to there's no a defined way to handle the error in UI
                            cont.resume(null)
                            Log.e("GeocoderUtil", "Timeout or IO error: $errorMessage")
                        }
                    }
                )
            }
        }
    }

    @Suppress("DEPRECATION")
    private suspend fun getAddressesByLocationNameLegacy(name: String, maxResults: Int): List<Address>? {
        if (name.isBlank() or (maxResults < ONE_VALUE)) return null
        return withContext(dispatchersProvider.io) {
            suspendCancellableCoroutine { cont ->
                runCatching {
                    val result = geocoder.getFromLocationName(
                        name,
                        maxResults,
                        SV_BOX_LOWER_LEFT_CORNER_LAT,
                        SV_BOX_LOWER_LEFT_CORNER_LNG,
                        SV_BOX_UPPER_RIGHT_CORNER_LAT,
                        SV_BOX_UPPER_RIGHT_CORNER_LNG
                    )
                    cont.resume(result)
                }.getOrElse {
                    Log.e("GeocoderUtil", "Timeout or IO error: ${it.message}")
                    cont.resume(null)
                }
            }
        }
    }

    override suspend fun getAddressByLocationName(name: String): Address? {
        return if (isAndroidVersionTiramisuOrAbove()) {
            getAddressesByLocationName(name, MAX_RESULTS_BY_ADDRESS_LOCATION)?.firstOrNull()
        } else {
            getAddressesByLocationNameLegacy(name, MAX_RESULTS_BY_ADDRESS_LOCATION)?.firstOrNull()
        }
    }

    /**
     * Allow to get info about places more precisely using Google places API
     * @param query location name to search
     * @param maxResults max limit of results to return
     * @see [https://developers.google.com/maps/documentation/places/android-sdk/autocomplete#get_place_predictions]
     **/
    override suspend fun getPlacesByQuery(query: String, maxResults: Int): List<SuggestionsItemUi>? =
        withContext(dispatchersProvider.io) {
            suspendCancellableCoroutine { cont ->
                val request = FindAutocompletePredictionsRequest.builder()
                    .setCountry(COUNTRY_SV)
                    .setQuery(query)
                    .build()

                placesClient.findAutocompletePredictions(request)
                    .addOnSuccessListener {
                        val predictions = it.autocompletePredictions.take(maxResults).map { prediction ->
                            SuggestionsItemUi(
                                id = prediction.placeId,
                                address = prediction.getPrimaryText(null).toString()
                            )
                        }
                        cont.resume(predictions)
                    }.addOnFailureListener {
                        cont.resume(null)
                        Log.e("PlacesByQuery", "${it.message}")
                    }
            }
        }

    /**
     * Allow to get latitude and longitude using Google places API based on place id
     * @param placeId id of the place assigned by Google
     * @see [https://developers.google.com/maps/documentation/places/android-sdk/autocomplete#get_place_predictions]
     **/
    override suspend fun getCoordinatesById(placeId: String): LatLng? =
        withContext(dispatchersProvider.io) {
            suspendCancellableCoroutine { cont ->
                val request = FetchPlaceRequest.builder(placeId, listOf(Place.Field.LAT_LNG)).build()
                placesClient.fetchPlace(request)
                    .addOnSuccessListener { response ->
                        response.place.latLng?.let { latLng -> cont.resume(latLng) }
                    }.addOnFailureListener {
                        cont.resume(null)
                        Log.e("PlacesByQuery", "${it.message}")
                    }
            }
        }

    companion object {
        const val MAX_RESULTS_BY_ADDRESS_LOCATION = 1
    }

}

interface GeocoderUtil {
    suspend fun getAddressByLocation(coordinates: Pair<Double, Double>): Address?
    suspend fun getAddressByLocationName(name: String): Address?
    suspend fun getPlacesByQuery(query: String, maxResults: Int): List<SuggestionsItemUi>?
    suspend fun getCoordinatesById(placeId: String): LatLng?
}