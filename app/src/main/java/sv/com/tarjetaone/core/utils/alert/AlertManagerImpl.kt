package sv.com.tarjetaone.core.utils.alert

import sv.com.tarjetaone.domain.entities.alerts.AlertTypeUI
import sv.com.tarjetaone.domain.entities.alerts.AlertUI
import java.util.LinkedList
import java.util.Queue
import javax.inject.Inject

class AlertManagerImpl @Inject constructor() : AlertManager {
    private val alertStore: Map<AlertTypeUI, Queue<AlertUI>> by lazy {
        mapOf(
            AlertTypeUI.ALERT to LinkedList(),
            AlertTypeUI.BANNER to LinkedList(),
            AlertTypeUI.MODAL to LinkedList(),
        )
    }

    private val shownAlerts: MutableSet<String> = mutableSetOf()

    override fun storeAlerts(alerts: List<AlertUI>) {
        if (alerts.isEmpty()) return
        alertStore.values.forEach { it.clear() }
        alerts.forEach { alert ->
            if (shownAlerts.contains(alert.id)) return@forEach
            alertStore[alert.type]?.add(alert)
        }
    }

    override fun getNextAlert(type: AlertTypeUI): AlertUI? = alertStore[type]?.poll()?.also {
        it.id?.let { id -> shownAlerts.add(id) }
    }

    override fun getAlerts(type: AlertTypeUI): List<AlertUI> =
        alertStore[type]?.toList() ?: emptyList()

    override fun hideAlert(alertId: String?, type: AlertTypeUI) {
        alertId?.let {
            shownAlerts.add(alertId)
            runCatching {
                alertStore[type]?.removeIf { it.id == alertId }
            }
        }
    }

    override fun removeAllAlerts() =
        alertStore.forEach { it.value.clear() }.also { shownAlerts.clear() }
}
