package sv.com.tarjetaone.core.utils.files

import android.net.Uri

interface FileUtils {
    /**
     * Stores a PDF file in the app's cache directory.
     * @param useFileScheme Defines if the Uri should use the file scheme (file://) or the content scheme (content://).
     * @return the Uri of the stored file, or null if the file could not be stored.
     */
    fun storeTempPdfFile(
        fileName: String,
        fileContent: ByteArray?,
        useFileScheme: Boolean
    ): Uri?

    /**
     * Returns the Uri of a stored file if it exists.
     * @param useFileScheme Defines if the Uri should use the file scheme (file://) or the content scheme (content://).
     * @return the Uri of the stored file, or null if the file does not exist.
     */
    fun getUriIfExists(fileName: String, useFileScheme: Boolean): Uri?
    fun deleteAllTempPdfFiles()

    suspend fun saveTempPdfFileAsync(
        fileName: String,
        fileContent: ByteArray?,
        useFileScheme: Boolean
    ): Uri?

    suspend fun deleteAllTempPdfFilesAsync()
}
