package sv.com.tarjetaone.core.utils.facephi

import android.graphics.Bitmap
import com.facephi.fphiselphidwidgetcore.WidgetExceptionType
import com.facephi.fphiselphidwidgetcore.WidgetSelphIDResult
import com.facephi.fphiwidgetcore.WidgetConfiguration
import com.facephi.fphiwidgetcore.WidgetExceptionType as SelfieWidgetExceptionType
import com.facephi.fphiwidgetcore.WidgetResult
import com.google.gson.Gson
import sv.com.tarjetaone.common.utils.helpers.CryptoHelper
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.extensions.removeLineBreak
import sv.com.tarjetaone.core.utils.facephi.CaptureType.DocumentType
import sv.com.tarjetaone.data.api.models.OCRCode
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import javax.inject.Inject

/**
 * Class that handles and processes the result of the facephi widget by mapping, storing and
 * returning the necessary data
 */
class FacephiResultHandler @Inject constructor(
    private val cryptoHelper: CryptoHelper,
    private val gson: Gson,
    private val imageUtils: ImageUtils,
    private val sharedPrefsRepo: SecureSharedPreferencesRepository
) {
    private fun handleDuiCapture(widgetResult: WidgetSelphIDResult): Document? {
        val frontBitmap = widgetResult.documentFrontImage
        val backBitmap = widgetResult.documentBackImage
        val rawFrontImage = widgetResult.rawDocumentFrontImage
        val rawBackImage = widgetResult.rawDocumentBackImage
        val tokenFaceImage = widgetResult.tokenFaceImage
        val ocrResults = widgetResult.ocrResults

        val frontFileName = imageUtils.storeTempImage(frontBitmap)
        val backFileName = imageUtils.storeTempImage(backBitmap)
        val frontRawFileName = imageUtils.storeTempImage(rawFrontImage)
        val backRawFileName = imageUtils.storeTempImage(rawBackImage)

        // Ensure that all images were stored successfully
        if (frontFileName == null || backFileName == null || frontRawFileName == null || backRawFileName == null) {
            return null
        }

        sharedPrefsRepo.apply {
            tokenFaceImage?.let { putBiometryToken1(it) }
            putDocFrontFileName(frontFileName)
            putDocBackFileName(backFileName)
            putRawDocFrontFileName(frontRawFileName)
            putRawDocBackFileName(backRawFileName)

            val ocrCode = gson.fromJson(gson.toJson(ocrResults), OCRCode::class.java)
            sharedPrefsRepo.putOcr(ocrCode)
        }

        return Document(frontBitmap, backBitmap)
    }

    private fun handleNitCapture(): Document? {
        TODO() // Not implemented as there is currently no need for NIT capture
    }

    /**
     * Processes the document capture result and stores the necessary data for later use.
     * @return Object containing the front and back images of the document, `null` if the
     * image(s) failed to be stored in temp storage
     */
    fun onDocumentCaptured(
        widgetResult: WidgetSelphIDResult,
        type: DocumentType = DocumentType.Dui
    ): Document? {
        return if (type is DocumentType.Dui) {
            handleDuiCapture(widgetResult)
        } else {
            handleNitCapture()
        }
    }

    fun onSelfieCaptured(widgetResult: WidgetResult): Bitmap {
        val templateRaw = getTemplateRawBase64(widgetResult)
        val imageBuffer = getImageBuffer(widgetResult)

        sharedPrefsRepo.apply {
            // Always store encoded strings of the image
            putBiometryToken2(templateRaw.removeLineBreak())
            imageBuffer?.let { putSelfieString(it.removeLineBreak()) }

            // attempt to store the image in temp storage
            imageUtils.storeTempImage(widgetResult.bestImage.image)?.let {
                putSelfieFileName(it)
            }
        }
        return widgetResult.bestImage.image
    }

    /**
     * Handle when the document capture fails
     * @param result The result returned by the launcher
     * @param onFailureAction Callback executed only when the failure cause is not manual
     * cancellation
     */
    fun onDocumentCaptureFailed(
        result: WidgetSelphIDResult,
        onFailureAction: (WidgetExceptionType) -> Unit
    ) {
        val exceptionType = result.exception.exceptionType ?: return
        if (exceptionType != WidgetExceptionType.StoppedManually) {
            onFailureAction(exceptionType)
        }
    }

    /**
     * Handle when the selfie capture fails
     * @param result The result returned by the launcher
     * @param onFailureAction Callback executed only when the failure cause is not manual
     * cancellation
     */
    fun onSelfieCaptureFailed(
        result: WidgetResult,
        onFailureAction: (SelfieWidgetExceptionType) -> Unit
    ) {
        val exceptionType = result.exception.exceptionType ?: return
        if (exceptionType != SelfieWidgetExceptionType.StoppedManually) {
            onFailureAction(exceptionType)
        }
    }

    private fun getTemplateRawBase64(widgetResult: WidgetResult): String {
        return widgetResult.templateRaw?.let { cryptoHelper.encryptBASE64(it) }.orEmpty()
    }

    private fun getImageBuffer(widgetResult: WidgetResult): String? {
        val imageToken = widgetResult.bestImage?.image?.let {
            WidgetConfiguration.generateTemplateRawFromBitmap(it)
        }
        return imageToken?.let { CryptoHelper.encryptBASE64(it) }
    }

    class Document internal constructor(
        val front: Bitmap,
        val back: Bitmap
    )
}
