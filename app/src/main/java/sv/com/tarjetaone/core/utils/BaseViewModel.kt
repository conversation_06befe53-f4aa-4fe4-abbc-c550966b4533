package sv.com.tarjetaone.core.utils

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.ErrorResponse
import sv.com.tarjetaone.domain.utils.ApiResultHandle
import sv.com.tarjetaone.domain.utils.onApiError
import sv.com.tarjetaone.domain.utils.onAuthenticationError
import sv.com.tarjetaone.domain.utils.onNetworkError
import sv.com.tarjetaone.domain.utils.onServerError
import sv.com.tarjetaone.domain.utils.onSuccess
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.OneDialogParams
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.utils.launch
import javax.inject.Inject

abstract class BaseViewModel : ViewModel() {

    private val _uiEvents: Channel<UiEvent> = Channel()
    val uiEvents: Flow<UiEvent> = _uiEvents.receiveAsFlow()
    private val _sideEffects: Channel<SideEffect> = Channel()
    val sideEffects: Flow<SideEffect> = _sideEffects.receiveAsFlow()

    @Inject
    lateinit var baseSharedPrefs: SecureSharedPreferencesRepository

    protected fun dispatch(
        dispatcher: CoroutineDispatcher = Dispatchers.IO,
        action: suspend () -> Unit
    ) {
        launch(dispatcher) { action() }
    }

    /**
     * Sends an event to the [uiEvents] flow to be collected by the view.
     *
     * Example of events collection from the view:
     *
     * ```
     * viewModel.uiEvents.collectEventsWithLifecycle { event ->
     *    when (event) {
     *       // Do something based on event type
     *    }
     * }
     * ```
     */
    fun sendEvent(event: UiEvent) {
        dispatch {
            if (event is SideEffect) {
                _sideEffects.send(event)
            } else {
                _uiEvents.send(event)
            }
        }
    }

    protected fun showOneDialog(params: OneDialogParams) {
        sendEvent(SideEffect.ShowOneDialog(params))
    }

    /**
     * This is a common event to show a simple error dialog message on the UI. Moved here to avoid repeating this function
     * on every viewModel.
     * @param message the error message to be displayed on the Dialog UI.
     */
    protected open fun showSimpleError(
        message: String,
        buttonText: Int = R.string.accept_label,
        isDismissible: Boolean = true,
        onButtonClick: () -> Unit = {}
    ) {
        sendEvent(SideEffect.Loading(false))
        sendEvent(UiEvent.Loading(false))
        val msg = if (message.isBlank()) {
            UiText.StringResource(R.string.something_went_wrong_simple)
        } else UiText.DynamicString(message)
        sendEvent(
            UiEvent.ShowCommonDialog(
                CommonDialogWithIconParams(
                    message = msg,
                    buttonText = UiText.StringResource(buttonText),
                    isDismissible = isDismissible,
                    onButtonClick = onButtonClick
                )
            )
        )
    }

    /**
     * This is a common event to show a simple error dialog message on the UI. Moved here to avoid repeating this function
     * on every viewModel.
     */
    fun showUpsErrorMessage(
        isDismissible: Boolean = true,
        message: UiText = UiText.StringResource(R.string.general_technical_issues_message),
        onButtonClick: () -> Unit = {}
    ) {
        sendEvent(SideEffect.Loading(false))
        sendEvent(UiEvent.Loading(false))
        sendEvent(
            UiEvent.ShowCommonDialog(
                CommonDialogWithIconParams(
                    isDismissible = isDismissible,
                    icon = R.drawable.ic_error_yellow,
                    title = UiText.StringResource(R.string.ups_label),
                    message = message,
                    buttonText = UiText.StringResource(R.string.try_again_label),
                    buttonColor = R.color.yellow_alert_button,
                    onButtonClick = onButtonClick
                )
            )
        )
    }

    /**
     * This generic function is a part of the API handling logic and should be implemented in all API calls
     * to ensure consistent error handling throughout the application.
     * If any of the callback actions below are not overridden in the child viewModel implementation, a default
     * action will be executed.
     *
     * Custom actions or error handling specific to a view should be implemented by overriding the following callbacks.
     *
     * @param {function} onApiErrorAction A callback to be executed whenever an API error occurs.
     * @param {function} onServerErrorAction A callback action for handling internal server errors (HTTP 500).
     * @param {function} onAuthenticationErrorAction A callback action for handling authentication errors (HTTP 401).
     * @param {function} onNetworkErrorAction A callback action for handling network errors (e.g., no internet connection).
     * @param {function} onSuccessAction A callback action that MUST be implemented in child views to handle successful API responses.
     */
    inline fun <U : Any?, T : ApiResultHandle<U>> T.executeUseCase(
        crossinline onApiErrorAction: (code: String?, error: ErrorResponse?, result: String?, messages: String?) -> Unit = { _, _, _, _ -> onDefaultApiError() },
        crossinline onServerErrorAction: () -> Unit = { onDefaultServerError() },
        crossinline onAuthenticationErrorAction: () -> Unit = { onAuthorizationExpired() },
        crossinline onNetworkErrorAction: () -> Unit = { onDefaultNetworkError() },
        crossinline onSuccessAction: suspend (U) -> Unit,
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            <EMAIL> {
                onSuccessAction(it)
            }.onAuthenticationError {
                onAuthenticationErrorAction()
            }.onServerError {
                onServerErrorAction()
            }.onNetworkError {
                onNetworkErrorAction()
            }.onApiError { code, error, result, messages ->
                onApiErrorAction(code, error, result, messages)
            }
        }
    }

    protected inline fun <T> ApiResultHandle<T>.handleErrors(
        crossinline onApiErrorAction: (code: String?, error: ErrorResponse?, result: String?, messages: String?) -> Unit = { _, _, _, _ -> onDefaultApiError() },
        crossinline onServerErrorAction: () -> Unit = { onDefaultServerError() },
        crossinline onAuthenticationErrorAction: () -> Unit = { onAuthorizationExpired() },
        crossinline onNetworkErrorAction: () -> Unit = { onDefaultNetworkError() },
    ): Boolean {
        this
            .onApiError(onApiErrorAction)
            .onAuthenticationError(onAuthenticationErrorAction)
            .onServerError { onServerErrorAction() }
            .onNetworkError { onNetworkErrorAction() }
        return this !is ApiResultHandle.Success
    }

    /**
     * Default API error, it will show the "ups" dialog first, then navigate back as a common behaviour on all screens.
     */
    fun onDefaultApiError() {
        showUpsErrorMessage(false)
    }

    /**
     * Whenever there is an API error when saving a step, show the "ups" message and navigate to the previous screen
     */
    fun onSaveStepDefaultError() {
        showUpsErrorMessage(false) {
            sendEvent(UiEvent.NavigateBack)
        }
    }

    /**
     * Default API Server Error, it will show the "ups" dialog first, then navigate back to the welcome screen
     * as a common behaviour on all screens.
     */
    fun onDefaultServerError() {
        showUpsErrorMessage(false) {
            sendEvent(UiEvent.NavigateBackTo(R.id.welcomeFragment, true))
        }
    }

    /**
     * Whenever there is a network error, show the "ups" dialog and navigate to the previous screen.
     */
    fun onDefaultNetworkError() {
        showUpsErrorMessage(
            isDismissible = false,
            message = UiText.StringResource(R.string.something_went_wrong)
        ) {
            sendEvent(UiEvent.NavigateBack)
        }
    }

    /**
     * Whenever a 401 occurs, send the user to the Welcome screen.
     */
    fun onAuthorizationExpired(backToFragment: Int = R.id.welcomeFragment) {
        baseSharedPrefs.putSessionExpiredStatus(true)
        sendEvent(SideEffect.Loading(false))
        sendEvent(UiEvent.Loading(false))
        sendEvent(UiEvent.NavigateBackTo(backToFragment, true))
    }
}
