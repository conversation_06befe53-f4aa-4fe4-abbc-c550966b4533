package sv.com.tarjetaone.core.utils

import sv.com.tarjetaone.analytics.amplitude.AmplitudeEvents
import sv.com.tarjetaone.analytics.dynatrace.DynatraceEvent

object FacephiConstants {
    const val FACIAL_AUTH_METHOD_2 = "ME_AUTH_FACIAL_2"
    const val FACIAL_AUTH_METHOD_5 = "ME_AUTH_FACIAL_5"
    const val FACEPHI_SUCCESS_CODE = 0
}

enum class IdentityValidationResult {
    SUCCESS,
    BLOCK,
    TRY_FRD,
    TRY_FAILD,
    TRY_FACPHI,
    TRY_QUALITY,
    TRY_POS,
    TRY_FACE,
    TRY_SMALL,
    TRY_MANY,
    TRY_EYES,
    INVALID_DOC,
    PENDING_DOC
}

fun getBiometryAmplitudeError(resultCode: String): AmplitudeEvents.BiometryError {
    val result = valueOf<IdentityValidationResult>(resultCode)
    return when (result) {
        IdentityValidationResult.BLOCK -> AmplitudeEvents.BiometryError.BLOCKING
        IdentityValidationResult.TRY_FAILD -> AmplitudeEvents.BiometryError.FAILED_ATTEMPT
        IdentityValidationResult.INVALID_DOC -> AmplitudeEvents.BiometryError.NOT_VALID_DOCUMENT
        IdentityValidationResult.TRY_FACPHI -> AmplitudeEvents.BiometryError.FACEPHI_ERROR
        IdentityValidationResult.TRY_FRD -> AmplitudeEvents.BiometryError.FRAUD
        else -> AmplitudeEvents.BiometryError.FACEPHI_ERROR
    }
}

fun getBiometryDynatraceError(resultCode: String): DynatraceEvent {
    val result = valueOf<IdentityValidationResult>(resultCode)
    return when (result) {
        IdentityValidationResult.BLOCK -> DynatraceEvent.Global.BiometryBlock
        IdentityValidationResult.TRY_FAILD -> DynatraceEvent.Global.BiometryFail
        IdentityValidationResult.INVALID_DOC -> DynatraceEvent.ShortCapture.TimeOutDocumentScan
        IdentityValidationResult.TRY_FACPHI -> DynatraceEvent.Global.BiometryError
        IdentityValidationResult.TRY_FRD -> DynatraceEvent.Global.BiometryFraud
        IdentityValidationResult.TRY_QUALITY -> DynatraceEvent.Global.BiometryQuality
        IdentityValidationResult.TRY_POS -> DynatraceEvent.Global.BiometryPos
        IdentityValidationResult.TRY_FACE -> DynatraceEvent.Global.BiometryFace
        IdentityValidationResult.TRY_SMALL -> DynatraceEvent.Global.BiometrySmall
        IdentityValidationResult.TRY_MANY -> DynatraceEvent.Global.BiometryMany
        IdentityValidationResult.TRY_EYES -> DynatraceEvent.Global.BiometryEyes
        else -> DynatraceEvent.Global.BiometryFail
    }
}

enum class BiometryVerificationResult {
    APPROVED_VERIFIED,
    FAILED,
    PENDING,
    DENIED
}
