package sv.com.tarjetaone.core.di

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import javax.inject.Qualifier
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object CoroutinesDispatcherModule {

    @DefaultDispatcher
    @Provides
    @Singleton
    fun providesDefaultDispatcher(): CoroutineDispatcher = Dispatchers.Default

    @IoDispatcher
    @Provides
    @Singleton
    fun providesIoDispatcher(): CoroutineDispatcher = Dispatchers.IO

    @MainDispatcher
    @Provides
    @Singleton
    fun providesMainDispatcher(): CoroutineDispatcher = Dispatchers.Main
}

@Qualifier
@Retention(AnnotationRetention.BINARY)
@Deprecated("Use IoDispatcher from sv.com.tarjetaone.domain.core.DispatchersProvider instead")
annotation class IoDispatcher

@Qualifier
@Retention(AnnotationRetention.BINARY)
@Deprecated("Use MainDispatcher from sv.com.tarjetaone.domain.core.DispatchersProvider instead")
annotation class MainDispatcher

@Qualifier
@Retention(AnnotationRetention.BINARY)
@Deprecated("Use DefaultDispatcher from sv.com.tarjetaone.domain.core.DispatchersProvider instead")
annotation class DefaultDispatcher
