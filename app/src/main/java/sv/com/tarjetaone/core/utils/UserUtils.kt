package sv.com.tarjetaone.core.utils

import sv.com.tarjetaone.data.api.models.User
import sv.com.tarjetaone.domain.entities.request.UserLocationUI
import sv.com.tarjetaone.domain.entities.response.UserProfileResponseUI

object UserUtils {

    private var user: User? = null
    private val userLocationList: ArrayList<UserLocationUI> = ArrayList()
    @Deprecated("Use userProfile from SecureSharedPreferencesRepository instead, " +
            "for now it will be kept for onboarding screens, however consider to refactor " +
            "to use SecureSharedPreferencesRepository instead")
    var userProfile: UserProfileResponseUI = UserProfileResponseUI()
    var tokenAuth: String? = ""
    private var creditCardId: Int? = null
    private var isFATCA: Boolean = false
    private var managementLink: Int = 0
    private var isAcitveSession: Boolean = false
    private var isLogin: Boolean = false

    fun getUser(): User? = user

    fun setUser(user: User?) {
        this.user = user
    }

    fun getisLogin(): Boolean = isLogin

    fun setisLogin(_isLogin: Boolean) {
        this.isLogin = _isLogin
    }

    fun getAcitveSession(): Boolean = isAcitveSession

    fun setIsAcitveSession(isAcitveSession: Boolean) {
        this.isAcitveSession = isAcitveSession
    }

    fun setManagementLink(managementLink: Int) {
        this.managementLink = managementLink
    }

    fun getCreditCardId(): Int? = creditCardId

    fun setCreditCardId(creditCardId: Int?) {
        this.creditCardId = creditCardId
    }

    fun getUserLocations(): List<UserLocationUI> {
        return userLocationList
    }

    fun addUserLocation(userLocation: UserLocationUI) {
        userLocationList.add(userLocation)
    }

    fun getUserLocationByType(type: String): UserLocationUI? {
        return userLocationList.find { it.addressType?.equals(type, true) == true }
    }

    fun clearUserLocationByType(type: String) {
        userLocationList.removeIf { it.addressType?.equals(type, true) == true }
    }

    fun clearUserLocation() {
        userLocationList.clear()
    }

    fun resetUser() {
        user = null
        userLocationList.clear()
        userProfile = UserProfileResponseUI()
    }

    fun getIsFATCA(): Boolean = isFATCA

    fun setIsFATCA(isFATCA: Boolean) {
        this.isFATCA = isFATCA
    }
}
