package sv.com.tarjetaone.core.utils.extensions

import android.graphics.BlendMode
import android.graphics.BlendModeColorFilter
import android.os.Build
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import androidx.annotation.LayoutRes
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import sv.com.tarjetaone.R

fun View.makeGone() {
    this.visibility = View.GONE
}

fun View.makeVisible() {
    this.visibility = View.VISIBLE
}

fun View.isVisible(value: Boolean) {
    this.visibility = if (value) View.VISIBLE else View.GONE
}

inline fun <T : Any, R> T?.withNotNull(block: (T) -> R): R? {
    return this?.let(block)
}

fun ProgressBar.setColorProgress() {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        val color = context.getColor(R.color.color_accent)
        indeterminateDrawable.colorFilter = BlendModeColorFilter(
            color,
            BlendMode.SRC_IN
        )
    }
}

/**
 * Extension function to inflate layouts with DataBinding using a ViewDataBinding generic param.
 */
fun <T : ViewDataBinding> dataBindingInflate(
    inflater: LayoutInflater,
    container: ViewGroup?,
    @LayoutRes layoutId: Int
) =
    DataBindingUtil.inflate(
        inflater,
        layoutId,
        container,
        false
    ) as T

fun getNotificationIcons(): List<Int> {
    return listOf(
        R.drawable.ic_calendar_check,
        R.drawable.ic_service,
        R.drawable.ic_notification,
        R.drawable.ic_noun_secure,
        R.drawable.ic_exclamation_octagon,
        R.drawable.ic_card_menu_icon,
        R.drawable.ic_limit_card_menu_icon,
        R.drawable.ic_lock_card_menu_icon,
        R.drawable.ic_request_card_menu_icon,
        R.drawable.ic_request_pin_menu_icon,
        R.drawable.ic_report_trip_menu_icon,
        R.drawable.ic_traslate_points_menu_icon,
        R.drawable.ic_folder_menu,
        R.drawable.ic_locked,
        R.drawable.ic_option_menu_card_icon,
        R.drawable.ic_notification,
        R.drawable.ic_question_octagon,
        R.drawable.ic_exclamation_octagon,
        R.drawable.ic_services_payment,
        R.drawable.ic_atlantida,
        R.drawable.ic_card_menu_cancel,
        R.drawable.ic_control_gastos,
        R.drawable.ic_user_menu_logout
    )
}
