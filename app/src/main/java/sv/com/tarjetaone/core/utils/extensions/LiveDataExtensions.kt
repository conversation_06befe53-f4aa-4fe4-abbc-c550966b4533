package sv.com.tarjetaone.core.utils.extensions

import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData

fun MediatorLiveData<Boolean>.validateEmptyStrings(
    listString: List<MutableLiveData<String>>,
    optionalFields: List<MutableLiveData<String>> = emptyList()
) {
    this.apply {
        (listString + optionalFields).forEach { input ->
            <EMAIL>(input) {
                <EMAIL> = validateDataEmpty(listString, optionalFields)
            }
        }
    }
}

private fun validateDataEmpty(
    listString: List<MutableLiveData<String>>,
    optionalFields: List<MutableLiveData<String>> = emptyList()
): Boolean {
    listString.forEach {
        if (it.value.isNullOrEmpty()) return false
    }
    optionalFields.forEach {
        if (it.value == null || it.value?.isEmpty() == true)
            return@forEach
    }
    return true
}
