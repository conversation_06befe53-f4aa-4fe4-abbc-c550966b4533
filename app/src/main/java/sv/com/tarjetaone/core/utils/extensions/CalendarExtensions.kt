package sv.com.tarjetaone.core.utils.extensions

import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

object CalendarFormat {
    const val YEAR_MONTH_DAY = "yyyyMMdd"
    const val DAY_MONTH_YEAR = "dd/MM/yyyy"
    const val FORMAT2 = "yyyy-MM-dd'T'HH:mm:ss"
}

fun Date.formatDate(format: String): String {
    return SimpleDateFormat(format, Locale("es", "SV")).format(this)
}

/**
 * Extension of [Calendar] that helps to get the current date
 * */
fun Calendar.getCurrentDate(format: String): String = this.time.formatDate(format)

/**
 * Extension of [Calendar] that helps to get a date of the past
 * @param monthsAgo the number of months ago
 * @param format the format of the date
 * @return a [String] with the date n months ago
 */
fun Calendar.getPastDate(monthsAgo: Int, format: String): String {
    this.add(Calendar.MONTH, -monthsAgo)
    return this.time.formatDate(format)
}

fun Date.addDays(days: Int): Date {
    val calendar = Calendar.getInstance()
    calendar.time = this
    calendar.add(Calendar.DATE, days)
    return calendar.time
}

fun Calendar.getFormattedDateFromTo(dateFormat: String): String {
    val esLocale = Locale("es", "SV")
    val sdf2 = SimpleDateFormat(dateFormat, esLocale)
    return sdf2.format(this.time)
}

fun Calendar.getLast6Months(): Pair<String, String> {
    this.add(Calendar.MONTH, -1)
    val endDay = this.getActualMaximum(Calendar.DAY_OF_MONTH)
    val endMonth = this.get(Calendar.MONTH)
    val endYear = this.get(Calendar.YEAR)
    this.set(endYear, endMonth, endDay)

    val endDate = this.getFormattedDateFromTo(CalendarFormat.FORMAT2)

    val startDay = 1
    this.add(Calendar.MONTH, -6)
    val startMonth = this.get(Calendar.MONTH)
    val startYear = this.get(Calendar.YEAR)
    this.set(startYear, startMonth, startDay)

    val startDate = this.getFormattedDateFromTo(CalendarFormat.FORMAT2)

    return Pair(startDate, endDate)
}

/**
 * Extension of [Calendar] that helps to get a date of last year
 * @return a [Pair] of String first is the start date and the second is the end date
 */
fun Calendar.getLastYear(): Pair<String, String> {
    this.add(Calendar.YEAR, -1)

    val startDay = 1
    val startMonth = 0
    val startYear = this.get(Calendar.YEAR)
    this.set(startYear, startMonth, startDay)

    val startDate = this.getFormattedDateFromTo(CalendarFormat.FORMAT2)

    val endDay = 31
    val endMonth = 11
    val endYear = this.get(Calendar.YEAR)
    this.set(endYear, endMonth, endDay)

    val endDate = this.getFormattedDateFromTo(CalendarFormat.FORMAT2)

    return Pair(startDate, endDate)
}

/**
 * Extension of [Calendar] that helps to get a date of last 2 years
 * @return a [Pair] of String first is the start date and the second is the end date
 */

fun Calendar.getLast2Years(): Pair<String, String> {
    this.add(Calendar.YEAR, -2)

    val startDay = 1
    val startMonth = 0
    val startYear = this.get(Calendar.YEAR)
    this.set(startYear, startMonth, startDay)

    val startDate = this.getFormattedDateFromTo(CalendarFormat.FORMAT2)

    val endDay = 31
    val endMonth = 11
    val endYear = this.get(Calendar.YEAR) + 1
    this.set(endYear, endMonth, endDay)

    val endDate = this.getFormattedDateFromTo(CalendarFormat.FORMAT2)

    return Pair(startDate, endDate)
}
