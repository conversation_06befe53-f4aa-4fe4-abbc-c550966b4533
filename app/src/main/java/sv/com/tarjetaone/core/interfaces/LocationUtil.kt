package sv.com.tarjetaone.core.interfaces

import android.location.LocationListener

interface LocationUtil : LocationListener {
    fun getLastLocation(): Pair<Double, Double>?
    fun getLocationWhenChanged(onChanged: LocationUtilOnLocationChanged)
    fun startTracking(): Boolean
    fun checkIfGpsIsActiveAndHasLocationPermission(): Boolean
    fun isLocationProviderEnabled(): Boolean

    fun interface LocationUtilOnLocationChanged {
        fun onChange(latLng: Pair<Double, Double>)
    }
}
