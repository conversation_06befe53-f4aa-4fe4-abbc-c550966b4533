package sv.com.tarjetaone.core.utils

import android.app.Dialog
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.GravityInt
import androidx.core.view.isVisible
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.extensions.isVisible
import sv.com.tarjetaone.databinding.CommonDialogWithIconBinding
import sv.com.tarjetaone.presentation.helpers.UiText

@Deprecated(
    "Use SideEffect.ShowOneDialog with OneDialogParams instead.",
    ReplaceWith("SideEffect.ShowOneDialog(params)")
)
class CommonDialogWithIcon(
    private val params: CommonDialogWithIconParams = CommonDialogWithIconParams()
) : BaseDialogFragment() {
    private lateinit var binding: CommonDialogWithIconBinding

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        isCancelable = params.isDismissible
        return super.onCreateDialog(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = CommonDialogWithIconBinding.inflate(inflater, container, false).apply {
            lifecycleOwner = viewLifecycleOwner
        }
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.apply {
            if (params.isDismissible) rootLayoutDialog.setOnClickListener {
                params.onDismiss()
                dismiss()
            }
            ivClose.isVisible(params.showCloseButton)
            ivClose.setOnClickListener {
                params.onCloseButtonClick()
                dismiss()
            }
            ivIcon.isVisible(params.showIcon)
            ivIcon.setImageResource(params.icon)
            tvTitle.isVisible(params.showTitle)
            tvTitle.text = params.title.asString(requireContext())
            tvMessage.isVisible(params.showMessage)
            tvMessage.apply {
                text = params.message.asCharSequence(requireContext())
                gravity = params.messageGravity
                params.messageTextSize?.let { textSize = it }
                params.messageColor?.let { setTextColor(resources.getColor(it)) }
            }
            tvBtnText.apply {
                text = params.buttonText.asString(requireContext())
                params.primaryActionTextSize?.let { textSize = it }
            }
            btnAction.backgroundTintList =
                resources.getColorStateList(params.buttonColor, activity?.theme)
            btnAction.setOnClickListener {
                params.onButtonClick()
                dismiss()
            }
            tvSecondaryAction.isVisible = params.secondaryActionVisible
            tvSecondaryAction.text = params.secondaryActionText.asCharSequence(requireContext())
            tvSecondaryAction.setOnClickListener {
                params.onSecondaryActionClick()
                dismiss()
            }
        }
    }
}

/**
 * Class to hold the necessary params to build and show a common dialog.
 */
@Deprecated(
    "Use SideEffect.ShowOneDialog with OneDialogParams instead.",
    ReplaceWith("SideEffect.ShowOneDialog(params)")
)
data class CommonDialogWithIconParams(
    @DrawableRes val icon: Int = R.drawable.ic_match_error,
    val title: UiText = UiText.StringResource(R.string.error_titile),
    val message: UiText = UiText.StringResource(R.string.something_went_wrong_simple),
    @GravityInt val messageGravity: Int = Gravity.CENTER_HORIZONTAL,
    val messageTextSize: Float? = null,
    @ColorRes val messageColor: Int? = null,
    val buttonText: UiText = UiText.StringResource(R.string.continue_button_label),
    val primaryActionTextSize: Float? = null,
    @ColorRes val buttonColor: Int = R.color.error_red,
    val isDismissible: Boolean = true,
    val showIcon: Boolean = true,
    val showTitle: Boolean = true,
    val showMessage: Boolean = true,
    val onButtonClick: () -> Unit = {},
    val secondaryActionVisible: Boolean = false,
    val secondaryActionText: UiText = UiText.StringResource(R.string.cancel),
    val onSecondaryActionClick: () -> Unit = {},
    val showCloseButton: Boolean = false,
    val onCloseButtonClick: () -> Unit = {},
    val onDismiss: () -> Unit = {}
)
