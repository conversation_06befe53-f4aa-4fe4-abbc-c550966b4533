package sv.com.tarjetaone.core.utils

import sv.com.tarjetaone.core.interfaces.DuiValidation
import java.util.regex.Pattern

/**
 * Class made by Atlantida
 */

class DuiValidationImpl : DuiValidation {
    override fun checkIfDuiIsValid(dui: String): Bo<PERSON>an {
        val regex = "[0-9]{8}(-)?[0-9]"

        if (!Pattern.matches(regex, dui)) return false
        if (dui.replace("-", "").toLong() == 0L) return false

        // Variable para llevar el control de la suma del algoritmo
        var calculo = 0

        // Ciclo que nos ayuda a ir aumentando la posicion que se utiliza posteriormente
        // en el algoritmo
        for ((posicion, x) in (9 downTo 2).withIndex()) {
            calculo = (calculo + Character.getNumericValue(dui[posicion]) * x)
        }

        // sacamos el modular 10 de calculo
        calculo %= 10
        if (calculo == 0) {
            calculo = 10
        }
        // Si el resultado nos da mayor a uno se le resta a 10 esta respuesta
        calculo = 10 - calculo

        val resultado = if (dui[8].toString() == "-") {
            calculo == Character.getNumericValue(dui[9])
        } else {
            calculo == Character.getNumericValue(dui[8])
        }

        // enviamos el resultado
        return resultado
    }
}