package sv.com.tarjetaone.core.utils.extensions

import java.text.NumberFormat
import java.util.Locale
import sv.com.tarjetaone.common.utils.AppConstants.FIVE_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE

private val numberFormatter = NumberFormat.getNumberInstance(Locale.US).apply {
    maximumFractionDigits = ZERO_VALUE
}

fun Int.formatAsString(): String = numberFormatter.format(this)

fun Int.configFormatNumberCurrency(): String {
    val locale = Locale("en", "US")
    val tw = NumberFormat.getCurrencyInstance(locale)
    tw.maximumFractionDigits = ZERO_VALUE
    val numberFormat = tw.format(this)
    return numberFormat
}

fun Int.configCurrencyWithFractions(): String {
    val locale = Locale("en", "US")
    val tw = NumberFormat.getCurrencyInstance(locale)
    tw.maximumFractionDigits = TWO_VALUE
    val numberFormat = tw.format(this)
    return numberFormat
}

fun Double.configFormatNumberCurrency(): String {
    val locale = Locale("en", "US")
    val tw = NumberFormat.getCurrencyInstance(locale)
    tw.maximumFractionDigits = ZERO_VALUE
    val numberFormat = tw.format(this)
    return numberFormat
}

fun Double.configCurrencyWithFractions(): String {
    val locale = Locale("en", "US")
    val tw = NumberFormat.getCurrencyInstance(locale)
    tw.maximumFractionDigits = TWO_VALUE
    val numberFormat = tw.format(this)
    return numberFormat
}

fun Float.configCurrencyWithFractions(): String {
    val locale = Locale("en", "US")
    val tw = NumberFormat.getCurrencyInstance(locale)
    tw.maximumFractionDigits = TWO_VALUE
    val numberFormat = tw.format(this)
    return numberFormat
}

fun Int.formattedAmount(multiplyBy: Int = FIVE_VALUE): String {
    return (this * multiplyBy).configFormatNumberCurrency()
}
