package sv.com.tarjetaone.core.utils.extensions

import java.text.DateFormat
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

fun String.substringAfter(index: Int) = try { substring(index, length) } catch (_: Exception) { this }

fun String.takeIfNotEmpty(): String? = takeIf { it.isNotEmpty() }

fun String.takeIfNotBlank(): String? = takeIf { it.isNotBlank() }

fun String.setGender(): String {
    return if (equals("M", true)) "Masculino" else "Femenino"
}

fun String?.getImageName(): String {
    if (!this.isNullOrEmpty()) {
        return try {
            val lastIndexDiagonal = lastIndexOf("/")
            substring(lastIndexDiagonal + 1, length)
        } catch (e: Exception) {
            this
        }
    }
    return ""
}

fun String.getFormattedDateFromTo(from: String, to: String): String {
    return try {
        val originalFormat: DateFormat = SimpleDateFormat(from, Locale.getDefault())
        val targetFormat: DateFormat = SimpleDateFormat(to, Locale("es", "SV"))
        val date: Date? = originalFormat.parse(this)
        return if (date != null) {
            targetFormat.format(date)
        } else {
            this
        }
    } catch (e: Exception) {
        this
    }
}

fun String.toDate(dateFormat: String): Date? {
    return try {
        val format: DateFormat = SimpleDateFormat(dateFormat, Locale("es", "SV"))
        return format.parse(this)
    } catch (e: Exception) {
        null
    }
}

fun String.removeLineBreak(): String {
    return replace("\n", "")
}

fun String.duiFormat(): String {
    return substring(0, 8) + "-" + substring(8, 9)
}

fun String.nitFormat(): String {
    return try {
        substring(0, 4) + "-" + substring(4, 10) + "-" + substring(10, 13) + "-" + substring(13, 14)
    } catch (ex: Exception) {
        this
    }
}
