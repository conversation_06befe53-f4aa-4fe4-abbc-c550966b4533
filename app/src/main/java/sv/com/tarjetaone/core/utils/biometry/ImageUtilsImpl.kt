package sv.com.tarjetaone.core.utils.biometry

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Environment
import androidx.core.content.FileProvider
import kotlinx.coroutines.withContext
import sv.com.tarjetaone.domain.core.DispatchersProvider
import java.io.File
import java.io.FileFilter
import java.io.FileOutputStream

class ImageUtilsImpl(
    private val context: Context,
    private val dispatchersProvider: DispatchersProvider
) : ImageUtils {
    private val storageDirPath = context.filesDir

    private val ioDispatcher by lazy { dispatchersProvider.io }

    override fun storeTempImage(image: Bitmap?): String? {
        return image?.let {
            val tempFile = File.createTempFile(IMG_PREFIX, null, storageDirPath)
            if (it.compress(Bitmap.CompressFormat.JPEG, 100, tempFile.outputStream())) {
                tempFile.name
            } else {
                null
            }
        }
    }

    override fun getTempImage(fileName: String): Bitmap? {
        return BitmapFactory.decodeFile("${storageDirPath.absolutePath}/$fileName")
    }

    override fun deleteAllTempImages() {
        val files = storageDirPath.listFiles(FileFilter { it.name.startsWith(IMG_PREFIX) })
        files?.forEach { it.delete() }
    }

    /**
     * Save an image in the app's External directory.
     */
    override fun saveImage(
        fileName: String,
        bitmap: Bitmap,
        onImageSaved: (Uri) -> Unit
    ) {
        val file = File(
            context.getExternalFilesDir(Environment.DIRECTORY_PICTURES),
            "$fileName$PNG_EXTENSION"
        )

        FileOutputStream(file).use { out ->
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
        }

        val uri = FileProvider.getUriForFile(context, "${context.packageName}.provider", file)
        onImageSaved(uri)
    }

    override suspend fun storeTempImageAsync(image: Bitmap?): String? = withContext(ioDispatcher) {
        storeTempImage(image)
    }

    override suspend fun getTempImageAsync(fileName: String): Bitmap? = withContext(ioDispatcher) {
        getTempImage(fileName)
    }

    override suspend fun saveImageAsync(
        fileName: String,
        bitmap: Bitmap,
        onImageSaved: (Uri) -> Unit
    ) = withContext(ioDispatcher) { saveImage(fileName, bitmap, onImageSaved) }

    override suspend fun deleteAllTempImagesAsync() = withContext(ioDispatcher) {
        deleteAllTempImages()
    }

    companion object {
        private const val IMG_PREFIX = "img"
        private const val PNG_EXTENSION = ".png"
    }
}
