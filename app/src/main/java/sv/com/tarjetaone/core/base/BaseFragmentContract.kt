package sv.com.tarjetaone.core.base

import androidx.databinding.ViewDataBinding
import sv.com.tarjetaone.core.utils.BaseViewModel

interface BaseFragmentContract<T : ViewDataBinding> {

    /**
     * ViewModel abstract val.
     * Must be overridden in the child classes.
     * e.g.
     * override val viewModel: DemoViewModel by viewModel()
     *
     * Set to null if no ViewModel is needed.
     * e.g.
     * override val viewModel: ViewModel? = null
     */
    val viewModel: BaseViewModel?

    var binding: T
}