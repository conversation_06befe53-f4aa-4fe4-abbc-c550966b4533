package sv.com.tarjetaone.core.utils

import android.annotation.SuppressLint
import androidx.core.util.Pair
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.google.android.material.datepicker.CalendarConstraints
import com.google.android.material.datepicker.CalendarConstraints.DateValidator
import com.google.android.material.datepicker.MaterialDatePicker
import sv.com.tarjetaone.R

/****
 * Class to create a custom date range picker
 * @param params: CustomDateRangePickerParams
 * **/
class CustomDateRangePicker(
    private val params: CustomDateRangePickerParams
) {

    fun createDateRangePicker(): MaterialDatePicker<Pair<Long, Long>> {
        return MaterialDatePicker.Builder
            .dateRangePicker()
            .setTheme(R.style.CustomMaterialCalendarStyle_FillRange)
            .setCalendarConstraints(setUpCalendarConstraints())
            .setPositiveButtonText(params.positiveButtonText)
            .setNegativeButtonText(params.negativeButtonText)
            .build()
            .apply {
                lifecycle.addObserver(object : DefaultLifecycleObserver {
                    override fun onStart(owner: LifecycleOwner) {
                        view?.apply {
                            setPadding(PADDING_VALUE, PADDING_VALUE, PADDING_VALUE, PADDING_VALUE)
                        }
                        dialog?.window?.apply {
                            setBackgroundDrawableResource(R.drawable.bg_rounded_custom_calendar)
                        }
                    }

                    override fun onDestroy(owner: LifecycleOwner) {
                        lifecycle.removeObserver(this)
                    }
                })
                addOnPositiveButtonClickListener {
                    params.onPositiveButtonAction(it)
                }
                addOnNegativeButtonClickListener {
                    params.onNegativeButtonAction()
                }
                addOnDismissListener {
                    params.onDismissAction()
                }
            }
    }

    @SuppressLint("RestrictedApi")
    private fun setUpCalendarConstraints(): CalendarConstraints {
        val calendarConstraintsBuilder = CalendarConstraints.Builder()
        params.calendarConstraints?.apply {
            start?.let { calendarConstraintsBuilder.setStart(it) }
            end?.let { calendarConstraintsBuilder.setEnd(it) }
            validator?.let { calendarConstraintsBuilder.setValidator(it) }
            openAt?.let { calendarConstraintsBuilder.setOpenAt(it) }
            firstDayOfWeek?.let { calendarConstraintsBuilder.setFirstDayOfWeek(it) }
        }
        return calendarConstraintsBuilder.build()
    }

    companion object {
        const val PADDING_VALUE = 16
    }
}

/**
 * Class to define the parameters for the CustomDateRangePicker
 * @param calendarConstraints: CalendarConstraints.Builder
 * @param positiveButtonText: String
 * @param negativeButtonText: String
 * @param onPositiveButtonAction: (Pair<Long, Long>) -> Unit Used to define the action to be executed when the positive button is clicked
 * @param onNegativeButtonAction: () -> Unit Used to define the action to be executed when the negative button is clicked
 * @param onDismissAction: () -> Unit Used to define the action to be executed when the dialog is dismissed
 * **/
data class CustomDateRangePickerParams(
    val calendarConstraints: CalendarConstraintsParams? = null,
    val positiveButtonText: String,
    val negativeButtonText: String,
    val onPositiveButtonAction: (Pair<Long, Long>) -> Unit = {},
    val onNegativeButtonAction: () -> Unit = {},
    val onDismissAction: () -> Unit = {}
)

/**
 * Class to define the parameters for the CalendarConstraints
 * @param start: Long? Used to define the start date for the calendar
 * @param end: Long? Used to define the end date for the calendar
 * @param validator: DateValidator? Used to define the validators for the calendar
 * @param openAt: Long? Used to define the date to be open at
 * @param firstDayOfWeek: Int? Used to define the first day of the week
 * */
data class CalendarConstraintsParams(
    val start: Long? = null,
    val end: Long? = null,
    val validator: DateValidator? = null,
    val openAt: Long? = null,
    val firstDayOfWeek: Int? = null
)