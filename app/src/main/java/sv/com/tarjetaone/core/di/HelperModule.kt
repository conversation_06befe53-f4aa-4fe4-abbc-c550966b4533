package sv.com.tarjetaone.core.di

import android.app.Application
import android.location.LocationManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import sv.com.tarjetaone.common.utils.helpers.CryptoHelper
import sv.com.tarjetaone.core.interfaces.DuiValidation
import sv.com.tarjetaone.core.interfaces.LocationUtil
import sv.com.tarjetaone.core.interfaces.PatternsValidator
import sv.com.tarjetaone.core.utils.DuiValidationImpl
import sv.com.tarjetaone.core.utils.LocationUtilImpl
import sv.com.tarjetaone.core.utils.PatternsValidatorImpl
import java.util.regex.Pattern
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object HelperModule {

    @Singleton
    @Provides
    fun provideCryptoHelper(): CryptoHelper = CryptoHelper

    @Provides
    @Singleton
    fun providesPatternValidator(@EmailPattern pattern: Pattern): PatternsValidator =
        PatternsValidatorImpl(pattern)

    @Provides
    @Singleton
    fun provideDuiValidation(): DuiValidation = DuiValidationImpl()


    @Provides
    @Singleton
    fun provideLocationUtil(
        application: Application,
        locationManager: LocationManager
    ): LocationUtil = LocationUtilImpl(application, locationManager)
}
