package sv.com.tarjetaone.core.utils

import android.Manifest
import android.content.Context
import android.content.ContextWrapper
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Canvas
import android.os.Build
import androidx.activity.ComponentActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import com.google.android.gms.maps.model.BitmapDescriptor
import com.google.android.gms.maps.model.BitmapDescriptorFactory
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE

fun Context.vectorToBitmap(icLocation: Int): BitmapDescriptor {
    val vectorDrawable = ResourcesCompat.getDrawable(resources, icLocation, null)
    vectorDrawable?.setTint(getColor(R.color.color_primary))
    val bitmap = Bitmap.createBitmap(
        vectorDrawable?.intrinsicWidth ?: ZERO_VALUE,
        vectorDrawable?.intrinsicHeight ?: ZERO_VALUE, Bitmap.Config.ARGB_8888
    )
    val canvas = Canvas(bitmap)
    vectorDrawable?.setBounds(0, 0, canvas.width, canvas.height)
    vectorDrawable?.draw(canvas)
    return BitmapDescriptorFactory.fromBitmap(bitmap)
}

fun Context.getActivity(): ComponentActivity = when (this) {
    is ComponentActivity -> this
    is ContextWrapper -> baseContext.getActivity()
    else -> throw IllegalStateException("Not an activity context")
}

fun Context.shouldShowRationale(permission: String): Boolean {
    return ActivityCompat.shouldShowRequestPermissionRationale(this.getActivity(), permission)
}

/**
 * Extension function to check if a permission is granted.
 */
fun Context.hasPermission(permission: String): Boolean {
    return ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
}

/**
 * Extension function to check if a permission should be requested based on the Android version.
 * If the Android version is lower than Android 12, the permission should be requested.
 */
fun Context.shouldRequestStoragePermission(): Boolean {
    return (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q &&
            !this.hasPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE))
}
