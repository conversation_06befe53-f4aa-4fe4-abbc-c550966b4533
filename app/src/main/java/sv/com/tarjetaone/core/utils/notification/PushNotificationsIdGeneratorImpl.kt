package sv.com.tarjetaone.core.utils.notification

import com.google.firebase.messaging.FirebaseMessaging
import javax.inject.Inject
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * <AUTHOR> Solis email: <EMAIL>
 * Use case that helps to get the token for push notifications
 */
class PushNotificationsIdGeneratorImpl @Inject constructor(
    private val firebaseMessaging: FirebaseMessaging
) : PushNotificationsIdGenerator {

    override suspend fun getDeviceId(): String? {
        // Converts the callback into a coroutine
        return suspendCoroutine { continuation ->
            firebaseMessaging.token.addOnSuccessListener {
                continuation.resume(it)
            }
        }
    }
}
