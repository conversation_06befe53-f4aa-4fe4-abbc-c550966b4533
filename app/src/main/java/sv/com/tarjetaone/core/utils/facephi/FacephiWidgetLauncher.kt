package sv.com.tarjetaone.core.utils.facephi

import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import com.facephi.fphiselphidwidgetcore.WidgetSelphIDConfiguration
import com.facephi.fphiselphidwidgetcore.WidgetSelphIDDocumentType
import com.facephi.fphiselphidwidgetcore.WidgetSelphIDResult
import com.facephi.fphiselphidwidgetcore.WidgetSelphIDScanMode
import com.facephi.fphiwidgetcore.WidgetConfiguration
import com.facephi.fphiwidgetcore.WidgetLivenessMode
import com.facephi.fphiwidgetcore.WidgetResult
import com.facephi.selphid.Widget
import com.facephi.selphi.Widget as SelfieWidget
import sv.com.tarjetaone.BuildConfig

class FacephiWidgetLauncher<T>(
    private val activityResultLauncher: ActivityResultLauncher<Intent>,
    private val type: CaptureType<T>
) {
    private val config = when (type) {
        is CaptureType.Document -> if (type.docType is CaptureType.DocumentType.Dui) {
            DUI_CAPTURE_CONFIG
        } else {
            NIT_CAPTURE_CONFIG
        }
        is CaptureType.Selfie -> SELFIE_CAPTURE_CONFIG
    }

    /**
     * Launches the facephi widget with the appropriate configuration for capture [type]
     */
    fun initWidget(context: Context) {
        Intent(
            context,
            if (type == CaptureType.Document()) Widget::class.java else SelfieWidget::class.java
        ).apply {
            putExtra(WIDGET_CONFIG_EXTRA, config)
        }.let(activityResultLauncher::launch)
    }

    companion object {
        // Fix locale
        private const val LOCALE_ES = "es"
        // SelphID properties (document capture)
        private const val SV_DOC_DATA = "SV|<ALL>"
        private const val SELPHID_WIDGET_RESOURCES = "fphi-selphid-widget-resources-selphid-1.0.zip"

        // Selphi properties (selfie capture)
        private const val SELPHI_WIDGET_RESOURCES = "fphi-selphi-widget-resources-selphi-live-1.2.zip"

        /**
         * Padding for the cropped image configuration
         */
        private const val CROPPED_IMAGE_PADDING_PROP = "return_cropped_images_with_padding"
        private const val CROPPED_IMAGE_PADDING_VAL = "20"


        private val BASE_DOCUMENT_CAPTURE_CONFIG: WidgetSelphIDConfiguration
            // Create with getter to avoid singleton behavior
            get() = WidgetSelphIDConfiguration().apply {
                license = BuildConfig.FACEPHI_LICENCE
                tutorialFlag = false
                resourcesPath = SELPHID_WIDGET_RESOURCES
                enableImages(true)
                wizardMode = true
                setParam(CROPPED_IMAGE_PADDING_PROP,CROPPED_IMAGE_PADDING_VAL)
                isGenerateRawImages = true
                scanMode = WidgetSelphIDScanMode.SMSearch
                specificData = SV_DOC_DATA
                locale = LOCALE_ES
            }

        private val DUI_CAPTURE_CONFIG = BASE_DOCUMENT_CAPTURE_CONFIG.apply {
            documentType = WidgetSelphIDDocumentType.DTIDCard
        }

        private val NIT_CAPTURE_CONFIG = BASE_DOCUMENT_CAPTURE_CONFIG.apply {
            documentType = WidgetSelphIDDocumentType.DTForeignCard
        }

        private val SELFIE_CAPTURE_CONFIG: WidgetConfiguration
            get() = WidgetConfiguration().apply {
                livenessMode = WidgetLivenessMode.LIVENESS_PASSIVE
                tutorialFlag = false
                resourcesPath = SELPHI_WIDGET_RESOURCES
                fullscreen = true
            }

        private const val WIDGET_CONFIG_EXTRA = "configuration"
        const val WIDGET_RESULT_EXTRA = "result"

        @Suppress("DEPRECATION")
        inline fun <reified T> parseWidgetResult(result: ActivityResult): T? {
            val data = result.data
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                data?.getParcelableExtra(WIDGET_RESULT_EXTRA, T::class.java)
            } else {
                data?.getParcelableExtra(WIDGET_RESULT_EXTRA)
            }
        }
    }

}

/**
 * Helper interface to hold the result type of the facephi widget capture
 */
sealed interface CaptureType<T> {
    data class Document(val docType: DocumentType = DocumentType.Dui) : CaptureType<WidgetSelphIDResult>
    data object Selfie : CaptureType<WidgetResult>

    sealed interface DocumentType {
        data object Dui : DocumentType
        data object Nit : DocumentType
    }
}

/**
 * Remembers a [FacephiWidgetLauncher] for the given [type]. The [onCaptured] lambda is called when
 * the widget returns a non-null result.
 *
 * Usage example:
 * ```
 * val launcher = rememberFacephiWidgetLauncher(type = CaptureType.Document) {
 *     // handle capture result
 * }
 *
 * val context = LocalContext.current
 * // Call init method inside of a non-compose context such as a LaunchedEffect block or onClick lambda
 * LaunchedEffect(Unit) {
 *     launcher.initWidget(context)
 * }
 * ```
 */
@Composable
inline fun <reified T> rememberFacephiWidgetLauncher(
    type: CaptureType<T>,
    crossinline onFailure: (T) -> Unit = {},
    crossinline onCaptured: (T) -> Unit
): FacephiWidgetLauncher<T> {
    val launcher = rememberLauncherForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        FacephiWidgetLauncher.parseWidgetResult<T>(result)?.let {
            when (result.resultCode) {
                Widget.RESULT_OK -> onCaptured(it)
                Widget.RESULT_CANCELED -> onFailure(it)
                else -> Unit
            }
        }
    }
    return remember { FacephiWidgetLauncher(launcher, type) }
}
