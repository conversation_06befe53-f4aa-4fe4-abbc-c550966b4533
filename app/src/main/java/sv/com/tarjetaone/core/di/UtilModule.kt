package sv.com.tarjetaone.core.di

import android.app.Application
import android.content.Context
import android.location.Geocoder
import android.location.LocationManager
import com.google.android.libraries.places.api.Places
import com.google.android.libraries.places.api.net.PlacesClient
import com.google.gson.Gson
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import java.util.regex.Pattern
import javax.inject.Qualifier
import javax.inject.Singleton
import sv.com.tarjetaone.common.utils.extensions.SV_LOCALE
import sv.com.tarjetaone.core.remoteconfig.FirebaseRemoteConfigManager
import sv.com.tarjetaone.core.remoteconfig.RemoteConfigManager
import sv.com.tarjetaone.core.utils.GeocoderUtil
import sv.com.tarjetaone.core.utils.GeocoderUtilImpl
import sv.com.tarjetaone.core.utils.UserUtils
import sv.com.tarjetaone.core.utils.alert.AlertManager
import sv.com.tarjetaone.core.utils.alert.AlertManagerImpl
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.biometry.ImageUtilsImpl
import sv.com.tarjetaone.core.utils.bitmap.BitmapUtilImpl
import sv.com.tarjetaone.core.utils.bitmap.BitmapUtils
import sv.com.tarjetaone.core.utils.files.FileUtils
import sv.com.tarjetaone.core.utils.files.FileUtilsImpl
import sv.com.tarjetaone.domain.core.DispatchersProvider

@Module
@InstallIn(SingletonComponent::class)
object UtilModule {

    @Provides
    @Singleton
    fun provideLocationManager(application: Application): LocationManager {
        return application.getSystemService(Context.LOCATION_SERVICE) as LocationManager
    }

    @EmailPattern
    @Provides
    @Singleton
    fun proveEmailPattern() : Pattern {
        return android.util.Patterns.EMAIL_ADDRESS
    }

    @Singleton
    @Provides
    fun provideUserUtils(): UserUtils {
        return UserUtils
    }

    @Singleton
    @Provides
    fun provideGson(): Gson {
        return Gson()
    }

    @Provides
    @Singleton
    fun provideImageUtils(
        @ApplicationContext context: Context,
        dispatchersProvider: DispatchersProvider
    ): ImageUtils = ImageUtilsImpl(context, dispatchersProvider)

    @Provides
    @Singleton
    fun provideFileUtils(
        @ApplicationContext context: Context,
        dispatchersProvider: DispatchersProvider
    ): FileUtils =
        FileUtilsImpl(context, dispatchersProvider)

    @Provides
    @Singleton
    fun provideGeoCoder(@ApplicationContext context: Context): Geocoder =
        Geocoder(context, SV_LOCALE)

    @Provides
    @Singleton
    fun providePlaceClient(@ApplicationContext context: Context): PlacesClient =
        Places.createClient(context)

    @Provides
    @Singleton
    fun provideGeocoderUtil(
        geocoder: Geocoder,
        placesClient: PlacesClient,
        dispatchersProvider: DispatchersProvider
    ): GeocoderUtil =
        GeocoderUtilImpl(geocoder, placesClient, dispatchersProvider)

    @Provides
    @Singleton
    fun provideAlertManager(): AlertManager = AlertManagerImpl()

    @Provides
    @Singleton
    fun provideFirebaseRemoteConfigManager(): RemoteConfigManager = FirebaseRemoteConfigManager()

    @Provides
    @Singleton
    fun provideBitmapUtils(
        @ApplicationContext context: Context,
        dispatchersProvider: DispatchersProvider
    ): BitmapUtils = BitmapUtilImpl(context, dispatchersProvider)
}

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class EmailPattern
