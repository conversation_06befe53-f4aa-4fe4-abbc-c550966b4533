package sv.com.tarjetaone.core.utils

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.core.app.ActivityOptionsCompat
import androidx.core.graphics.ColorUtils
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import sv.com.tarjetaone.BuildConfig
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_HUNDRED_VALUE
import sv.com.tarjetaone.common.utils.extensions.enableOrDisableScreenshots
import sv.com.tarjetaone.core.remoteconfig.RemoteConfigManager
import sv.com.tarjetaone.core.remoteconfig.RemoteProperty
import sv.com.tarjetaone.core.utils.extensions.makeVisible
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.view.help.SupportActivity
import javax.inject.Inject

/**
 * Common events and actions for activities
 */
abstract class BaseActivity : AppCompatActivity() {

    private lateinit var progressView: View
    private var commonDialog: CommonDialogWithIcon? = null

    @Inject
    lateinit var remoteConfigManager: RemoteConfigManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Only listen to remote config changes in debug mode
        if (::remoteConfigManager.isInitialized && BuildConfig.DEBUG) {
            remoteConfigManager.getLatestProperty(RemoteProperty.ScreenshotEnabled)
                .collectLatestWithLifecycleActivity { enableOrDisableScreenshots(it) }
        }
    }

    /**
     * Use this function to collect a flow in a lifecycle way to avoid
     * listening flows when the app is in the background.
     * @param dispatch it will return the corresponding result as a callback whenever .
     * See also
     * [Safer Way to Collect Flow] (https://medium.com/androiddevelopers/a-safer-way-to-collect-flows-from-android-uis-23080b1f8bda)
     */
    fun <T> Flow<T>.collectLatestWithLifecycleActivity(dispatch: (T) -> Unit) {
        lifecycleScope.launch {
            flowWithLifecycle(lifecycle).collectLatest(dispatch)
        }
    }

    /**
     * Use this function to collect a flow of one time events in a lifecycle aware manner to avoid
     * listening to flows when the app is in the background.
     * See also: [safer way to collect one time events](https://youtu.be/njchj9d_Lf8?t=820)
     * @param dispatch it will return the corresponding result as a callback.
     */
    protected fun Flow<UiEvent>.collectEventsWithLifecycle(dispatch: (UiEvent) -> Unit) {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                // To avoid the rare occasion when an event gets lost
                withContext(Dispatchers.Main.immediate) {
                    collect(dispatch)
                }
            }
        }
    }

    /**
     * Set Compose content screen as a whole replacement of traditional XML view screen.
     * @param viewCompositionStrategy the desired composition strategy to dispose the compose view.
     * Normally, it will be disposed when the view's lifecycleOwner is destroyed.
     * @see more at: https://developer.android.com/jetpack/compose/migrate/interoperability-apis/compose-in-views#composition-strategy
     */
    open fun ComposeView.setContentScreen(
        viewCompositionStrategy: ViewCompositionStrategy = ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed,
        content: @Composable () -> Unit
    ) {
        setViewCompositionStrategy(viewCompositionStrategy)
        setContent {
            OneAppTheme { content() }
        }
    }

    /**
     * Config the progress bar color
     */
    protected fun configProgress(progressView: View) {
        this.progressView = progressView
    }

    /**
     *  show the progress bar full screen
     */
    open fun showProgress(isBackgroundTransparent: Boolean = false) {
        if (::progressView.isInitialized) {
            val color = if (isBackgroundTransparent) {
                ColorUtils.setAlphaComponent(getColor(R.color.white), ONE_HUNDRED_VALUE)
            } else {
                getColor(R.color.white)
            }

            progressView.apply {
                makeVisible()
                setBackgroundColor(color)
            }
        }
    }

    /**
     * Hide the progress bar full screen
     */
    open fun hideProgress() {
        if (::progressView.isInitialized) {
            progressView.visibility = View.GONE
        }
    }

    open fun goSupport() {
        Intent(this, SupportActivity::class.java).apply {
            val options = ActivityOptionsCompat.makeCustomAnimation(
                this@BaseActivity,
                R.anim.slide_in_right,
                android.R.anim.fade_out
            )
            startActivity(this, options.toBundle())
        }
    }

    protected fun showCommonDialogWithIcon(params: CommonDialogWithIconParams) {
        commonDialog?.dismiss()
        commonDialog = CommonDialogWithIcon(params).also { it.show(supportFragmentManager, null) }
    }
}
