package sv.com.tarjetaone.core.utils

import android.content.Intent
import android.net.Uri
import androidx.annotation.IdRes
import androidx.navigation.NavDirections
import sv.com.tarjetaone.core.utils.files.ContentType
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.OneDialogParams
import sv.com.tarjetaone.presentation.helpers.UiText

/**
 * Sealed interface to represent the different events that can be sent to the UI
 */
sealed interface UiEvent {
    data class Navigate(val direction: NavDirections) : UiEvent
    data object NavigateBack : UiEvent
    data class NavigateBackTo(
        @IdRes val destinationId: Int,
        val inclusive: Boolean = false
    ) : UiEvent

    @Deprecated(
        message = "Use SideEffect.ShowOneDialog with OneDialogParams instead.",
        replaceWith = ReplaceWith("SideEffect.ShowOneDialog(params)")
    )
    data class ShowCommonDialog(val params: CommonDialogWithIconParams) : UiEvent

    @Deprecated(
        message = "Use SideEffect.Loading instead.",
        replaceWith = ReplaceWith("SideEffect.Loading(isLoading)")
    )
    data class Loading(val isLoading: Boolean, val isTransparent: Boolean = false) : UiEvent

    data object TwilioClick : UiEvent

    @Deprecated(
        message = "Use SideEffect.ShareContent instead.",
        replaceWith = ReplaceWith("SideEffect.ShareContent(params)")
    )
    data class ShareContent(val contentUri: Uri, val contentType: ContentType) : UiEvent

    @Deprecated(
        message = "Use SideEffect.StartIntent instead.",
        replaceWith = ReplaceWith("SideEffect.StartIntent(intent)")
    )
    data class StartIntent(val intent: Intent) : UiEvent
    data object Logout : UiEvent
}

/**
 * Sealed interface to represent side effects that can be triggered from the VM and be consumed and
 * handled in a composable screen
 *
 * Make sure the screen is collecting these side effects by wrapping the content using the
 * `SideEffectHandler` composable
 *
 * ```
 * val uiState by viewModel.uiState.collectAsStateWithLifecycle()
 * SideEffectHandler(viewModel.sideEffects) {
 *    SomeScreen(
 *       uiState = uiState,
 *       onEvent = viewModel::onEvent
 *    )
 * }
 * ```
 */
sealed interface SideEffect : UiEvent {
    data class ShowOneDialog(val params: OneDialogParams) : SideEffect
    data class Loading(val isLoading: Boolean) : SideEffect
    data class ShowToast(val message: UiText) : SideEffect
    data class ShareContent(val contentUri: Uri, val contentType: ContentType) : SideEffect
    data class StartIntent(val intent: Intent) : SideEffect
}
