package sv.com.tarjetaone.core.utils

import android.Manifest.permission.ACCESS_FINE_LOCATION
import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageManager
import android.location.Location
import android.location.LocationManager
import androidx.core.content.ContextCompat
import sv.com.tarjetaone.core.interfaces.LocationUtil
import javax.inject.Inject

class LocationUtilImpl @Inject constructor(
    private val context: Context,
    private val locationManager: LocationManager
) : LocationUtil {

    private var onLocationChangeListener: LocationUtil.LocationUtilOnLocationChanged? = null

    private fun hasLocationPermission(): Boolean =
        ContextCompat.checkSelfPermission(context, ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED

    override fun isLocationProviderEnabled(): Boolean =
        locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER) ||
            locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)

    override fun getLastLocation(): Pair<Double, Double>? {
        if (hasLocationPermission() && isLocationProviderEnabled()) {
            // get last know location of the gps
            val lastKnowLocation = getLastKnownLocation()
            // check if the last location is not null and add the gps marker
            if (lastKnowLocation != null) {
                return Pair(
                    lastKnowLocation.latitude,
                    lastKnowLocation.longitude
                )
            }
        }
        return null
    }

    override fun getLocationWhenChanged(onChanged: LocationUtil.LocationUtilOnLocationChanged) {
        onLocationChangeListener = onChanged
    }

    @SuppressLint("MissingPermission")
    override fun startTracking(): Boolean {
        if (hasLocationPermission() && isLocationProviderEnabled()) {
            // if gps hardware is enable startTracking with GPS
            if (locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
                locationManager.requestLocationUpdates(
                    LocationManager.GPS_PROVIDER,
                    1000,
                    0f,
                    this
                )
                return true
            }

            // If gps is not enable start tracking with Network
            if (locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)) {
                locationManager.requestLocationUpdates(
                    LocationManager.NETWORK_PROVIDER,
                    1000,
                    0f,
                    this
                )
                return true
            }
        } else if (!isLocationProviderEnabled()) {
            return false
        }

        return true
    }

    override fun checkIfGpsIsActiveAndHasLocationPermission(): Boolean {
        return hasLocationPermission() && isLocationProviderEnabled()
    }

    @SuppressLint("MissingPermission")
    private fun getLastKnownLocation(): Location? {
        var bestLocation: Location? = null
        val providers = locationManager.getProviders(true)
        for (provider in providers) {
            val location = locationManager.getLastKnownLocation(provider) ?: continue
            if (bestLocation == null || location.accuracy < bestLocation.accuracy) {
                bestLocation = location
            }
        }
        return bestLocation
    }

    override fun onLocationChanged(location: Location) {
        val newLocation = Pair(location.latitude, location.longitude)
        onLocationChangeListener?.onChange(newLocation)
        locationManager.removeUpdates(this)
    }
}
