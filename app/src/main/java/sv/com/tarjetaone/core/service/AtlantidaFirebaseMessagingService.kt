package sv.com.tarjetaone.core.service

import android.R
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.google.gson.Gson
import dagger.hilt.android.AndroidEntryPoint
import sv.com.tarjetaone.core.utils.UserUtils
import sv.com.tarjetaone.data.api.models.Link
import sv.com.tarjetaone.presentation.view.lobby.LobbyActivity
import javax.inject.Inject
import sv.com.tarjetaone.common.utils.AppConstants.ARG_MANAGEMENT

@AndroidEntryPoint
class AtlantidaFirebaseMessagingService : FirebaseMessagingService() {
    @Inject
    lateinit var userUtils: UserUtils

    private var notificationId = 0

    override fun onNewToken(token: String) {
        super.onNewToken(token)
    }

    override fun onMessageReceived(p0: RemoteMessage) {
        super.onMessageReceived(p0)

        print("onMessage data: ${p0.data["data"]}")
        val management = try {
            Gson().fromJson(p0.data["link"], Array<Link>::class.java)
        } catch (e: Exception) {
            arrayOf()
        }

        val intent: Intent?

        if (userUtils.getAcitveSession()) {
            intent = Intent(this, LobbyActivity::class.java).apply {
                management?.forEach {
                    if (it.managment != null) {
                        userUtils.setManagementLink(it.managment ?: 0)
                        putExtra(ARG_MANAGEMENT, it.managment)
                    }
                }
                `package` = "sv.com.tarjetaone"
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            }
        } else {
            intent =
                this.baseContext?.packageManager?.getLaunchIntentForPackage(this.baseContext.packageName)
                    .apply {
                        management?.forEach {
                            if (it.managment != null) {
                                this?.putExtra(ARG_MANAGEMENT, it.managment)
                            }
                        }
                        this?.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                    }
        }

        val notifyPendingIntent: PendingIntent =
            PendingIntent.getActivity(
                this,
                0,
                intent,
                PendingIntent.FLAG_CANCEL_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

        val title = p0.notification?.title
        val message = p0.notification?.body

        val importance = NotificationManager.IMPORTANCE_HIGH
        val channel = NotificationChannel("1001", "Configuración", importance)
        channel.description = "Reminders"
        var mNotificationManager = this.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        mNotificationManager.createNotificationChannel(channel)

        val mBuilder = NotificationCompat.Builder(this, "1001").apply {
            setContentIntent(notifyPendingIntent)
            setContentTitle(title)
            setContentText(message)
            setSmallIcon(R.drawable.ic_dialog_info)
            setStyle(NotificationCompat.BigTextStyle().bigText(""))
            setDefaults(NotificationCompat.DEFAULT_ALL)
            setAutoCancel(true)
        }

        with(NotificationManagerCompat.from(this)) {
            notificationId++
            mNotificationManager.notify(notificationId, mBuilder.build())
        }
    }
}