package sv.com.tarjetaone.core.utils.bitmap

import android.graphics.Bitmap
import sv.com.tarjetaone.common.utils.AppConstants.BITMAP_COMPRESS_QUALITY

interface BitmapUtils {
    suspend fun toByteArray(
        bitmap: Bitmap,
        compressType: Bitmap.CompressFormat = Bitmap.CompressFormat.PNG,
        quality: Int = BITMAP_COMPRESS_QUALITY
    ): ByteArray

    suspend fun toBase64(bitmap: Bitmap): String

    suspend fun generateSignatureImage(signatureText: String): Bitmap?
}