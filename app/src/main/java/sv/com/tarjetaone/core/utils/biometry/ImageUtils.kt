package sv.com.tarjetaone.core.utils.biometry

import android.graphics.Bitmap
import android.net.Uri

interface ImageUtils {
    /**
     * Store an image in the app's temp storage
     * @param image The image to store
     * @return the image filename or `null` if failed to save [image]
     */
    fun storeTempImage(image: Bitmap?): String?

    /**
     * Retrieves an image stored in the app's temp storage
     * @param fileName the name of the file to retrieve
     * @return the image or `null` if the image couldn't be retrieved
     */
    fun getTempImage(fileName: String): Bitmap?

    fun deleteAllTempImages()

    fun saveImage(fileName: String, bitmap: Bitmap, onImageSaved: (Uri) -> Unit)

    suspend fun storeTempImageAsync(image: Bitmap?): String?

    suspend fun getTempImageAsync(fileName: String): Bitmap?

    suspend fun saveImageAsync(fileName: String, bitmap: Bitmap, onImageSaved: (Uri) -> Unit)

    suspend fun deleteAllTempImagesAsync()
}
