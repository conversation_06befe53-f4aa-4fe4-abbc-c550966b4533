package sv.com.tarjetaone.core.utils.files

import android.content.Context
import android.net.Uri
import androidx.core.content.FileProvider
import kotlinx.coroutines.withContext
import sv.com.tarjetaone.BuildConfig
import sv.com.tarjetaone.domain.core.DispatchersProvider
import java.io.File
import java.io.FileFilter

class FileUtilsImpl(
    private val context: Context,
    private val dispatchersProvider: DispatchersProvider
) : FileUtils {
    private val cacheDirPath = context.cacheDir.absolutePath
    private val ioDispatcher by lazy { dispatchersProvider.io }

    override fun storeTempPdfFile(
        fileName: String,
        fileContent: ByteArray?,
        useFileScheme: Boolean
    ): Uri? {
        return fileContent?.let {
            try {
                val file = File("$cacheDirPath/$fileName.$PDF_EXTENSION")
                file.outputStream().apply {
                    write(fileContent)
                    close()
                }
                if (useFileScheme) {
                    // returns a Uri with file scheme: file://
                    Uri.fromFile(file)
                } else {
                    // returns a Uri with content scheme: content://
                    FileProvider.getUriForFile(
                        context,
                        FILE_AUTHORITY,
                        file
                    )
                }
            } catch (e: Exception) {
                null
            }
        }
    }

    override fun getUriIfExists(fileName: String, useFileScheme: Boolean): Uri? {
        return try {
            File("$cacheDirPath/$fileName.$PDF_EXTENSION").takeIf { it.exists() }?.let {
                if (useFileScheme) {
                    Uri.fromFile(it)
                } else {
                    FileProvider.getUriForFile(
                        context,
                        FILE_AUTHORITY,
                        it
                    )
                }
            }
        } catch (e: Exception) {
            null
        }
    }

    override fun deleteAllTempPdfFiles() {
        context.cacheDir.listFiles(FileFilter { it.extension == PDF_EXTENSION })
            ?.forEach { it.delete() }
    }

    override suspend fun saveTempPdfFileAsync(
        fileName: String,
        fileContent: ByteArray?,
        useFileScheme: Boolean
    ): Uri? = withContext(ioDispatcher) {
        storeTempPdfFile(
            fileName,
            fileContent,
            useFileScheme
        )
    }

    override suspend fun deleteAllTempPdfFilesAsync() = withContext(ioDispatcher) {
        deleteAllTempPdfFiles()
    }

    companion object {
        private const val PDF_EXTENSION = "pdf"
        private const val FILE_AUTHORITY = BuildConfig.APPLICATION_ID + ".provider"
    }
}
