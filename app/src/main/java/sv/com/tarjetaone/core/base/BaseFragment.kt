package sv.com.tarjetaone.core.base

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.LayoutRes
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.NavController
import androidx.navigation.NavDirections
import androidx.navigation.findNavController
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import sv.com.tarjetaone.analytics.amplitude.AmplitudeManager
import sv.com.tarjetaone.core.utils.BaseActivity
import sv.com.tarjetaone.core.utils.CommonDialogWithIcon
import sv.com.tarjetaone.core.utils.CommonDialogWithIconParams
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.dataBindingInflate
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.util.shareContent
import sv.com.tarjetaone.presentation.view.lobby.LobbyActivity
import sv.com.tarjetaone.presentation.view.utils.AutoClearedValue
import sv.com.tarjetaone.presentation.view.utils.requireBaseActivity
import sv.com.tarjetaone.presentation.view.utils.requireLobbyActivity
import javax.inject.Inject

/**
 * BaseFragment provides common functionality for child fragments and facilitates the inflation
 * of ViewDataBinding by taking a layout ID and a ViewModel variable ID from the XML.
 */
abstract class BaseFragment<T : ViewDataBinding>(
    @LayoutRes private val layoutId: Int
) : Fragment(), BaseFragmentContract<T> {

    override var binding by AutoClearedValue<T>()

    @Inject
    lateinit var amplitudeManager: AmplitudeManager

    protected lateinit var navController: NavController

    private var commonDialog: CommonDialogWithIcon? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = dataBindingInflate(inflater, container, layoutId)
        return binding.apply {
            lifecycleOwner = viewLifecycleOwner
        }.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        navController = view.findNavController()
        collectUiEvents()
    }

    /**
     * Set Compose content screen as a whole replacement of traditional XML view screen.
     * @param viewCompositionStrategy the desired composition strategy to dispose the compose view.
     * Normally, it will be disposed when the view's lifecycleOwner is destroyed.
     * @see [composition-strategy](https://developer.android.com/jetpack/compose/migrate/interoperability-apis/compose-in-views#composition-strategy)
     */
    open fun ComposeView.setContentScreen(
        viewCompositionStrategy: ViewCompositionStrategy = ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed,
        content: @Composable () -> Unit
    ) {
        setViewCompositionStrategy(viewCompositionStrategy)
        setContent {
            OneAppTheme { content() }
        }
    }

    @Deprecated(
        message = "Track Amplitude events using the AmplitudeManager in the VM.",
        replaceWith = ReplaceWith(
            expression = "amplitudeManager.track(event, properties)",
            imports = ["sv.com.tarjetaone.analytics.amplitude.AmplitudeManager"]
        )
    )
    protected fun dispatchAmplitudeEvent(
        event: String,
        properties: Map<String, Any?>? = null
    ) {
        if (::amplitudeManager.isInitialized) {
            amplitudeManager.track(event, properties)
        }
    }

    /**
     * Navigates to next fragment using [NavDirections] with safe args
     * @param direction the direction to use with safe args.
     * see more:  https://developer.android.com/guide/navigation/navigation-getting-started
     */
    private fun navigateToNextFragment(direction: NavDirections) {
        navController.navigate(direction)
    }

    /**
     * Does pop to the destination fragment cleaning fragments stored in the backstack
     * @param fragmentId fragment to navigate
     * @param inclusive include given destination in the pop
     * see more: https://developer.android.com/guide/navigation/backstack
     */
    protected fun popToPreviousFragment(fragmentId: Int, inclusive: Boolean) {
        val backStackStatus = navController.popBackStack(fragmentId, inclusive)
        if (!backStackStatus) {
            navController.navigate(fragmentId)
        }
    }

    private fun collectUiEvents() {
        viewModel?.uiEvents?.collectEventsWithLifecycle { event ->
            when (event) {
                is UiEvent.Loading -> if (event.isLoading) {
                    showLoading(isTransparent = event.isTransparent)
                } else {
                    hideLoading()
                }

                is UiEvent.Navigate -> navigateToNextFragment(event.direction)
                UiEvent.NavigateBack -> goBack()
                is UiEvent.NavigateBackTo -> popToPreviousFragment(
                    event.destinationId,
                    event.inclusive
                )

                is UiEvent.ShowCommonDialog -> showCommonDialogWithIcon(event.params)

                is UiEvent.TwilioClick -> navigateToTwilio()

                is UiEvent.ShareContent -> {
                    shareContent(requireContext(), event.contentUri, event.contentType)
                }

                is UiEvent.StartIntent -> startActivity(event.intent)
                is UiEvent.Logout -> {
                    if (requireActivity() is LobbyActivity) {
                        requireLobbyActivity().closeSession()
                    }
                }

                else -> Unit
            }
        }
    }

    /**
     * Navigate to the previous screen in the back stack.
     * Override this method if you want to supply your own onBackPressed logic.
     */
    protected fun goBack() = navController.navigateUp()

    private fun navigateToTwilio() = try {
        requireBaseActivity().run {
            goSupport()
        }
    } catch (e: Exception) {
        Log.e(this::class.simpleName, e.stackTraceToString())
    }

    /**
     * Use this function to collect a flow in a lifecycle way to avoid
     * listening flows when the app is in the background.
     * @param dispatch it will return the corresponding result as a callback whenever .
     * See also
     * [Safer Way to Collect Flow] (https://medium.com/androiddevelopers/a-safer-way-to-collect-flows-from-android-uis-23080b1f8bda)
     */
    fun <T> Flow<T>.collectLatestWithLifecycle(dispatch: (T) -> Unit) {
        lifecycleScope.launch {
            flowWithLifecycle(viewLifecycleOwner.lifecycle).collectLatest(dispatch)
        }
    }

    /**
     * Use this function to collect a flow of one time events in a lifecycle aware manner to avoid
     * listening to flows when the app is in the background.
     * See also: [safer way to collect one time events](https://youtu.be/njchj9d_Lf8?t=820)
     * @param dispatch it will return the corresponding result as a callback.
     */
    private fun Flow<UiEvent>.collectEventsWithLifecycle(dispatch: (UiEvent) -> Unit) {
        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                // To avoid the rare occasion when an event gets lost
                withContext(Dispatchers.Main.immediate) {
                    collect(dispatch)
                }
            }
        }
    }

    /**
     * Shows a progress bar
     * @param title text optional that is going to show in the loading screen
     */
    @Deprecated(
        "Remove implementation when SideEffect.Loading(Boolean) is applied to " +
                "the required viewModels"
    )
    protected fun showLoading(isTransparent: Boolean = false) {
        if (activity is BaseActivity) {
            (activity as BaseActivity).showProgress(isTransparent)
        }
    }

    /**
     * Hides the progressbar
     */
    @Deprecated(
        "Remove implementation when SideEffect.Loading(Boolean) is applied to " +
                "the required viewModels"
    )
    protected fun hideLoading() {
        if (activity is BaseActivity) {
            (activity as BaseActivity).hideProgress()
        }
    }

    private fun showCommonDialogWithIcon(params: CommonDialogWithIconParams) {
        commonDialog?.dismiss()
        commonDialog = CommonDialogWithIcon(params).also { it.show(childFragmentManager, null) }
    }
}
