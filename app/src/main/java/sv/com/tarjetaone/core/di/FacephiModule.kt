package sv.com.tarjetaone.core.di

import com.facephi.fphiselphidwidgetcore.WidgetSelphIDConfiguration
import com.facephi.fphiselphidwidgetcore.WidgetSelphIDDocumentType
import com.facephi.fphiselphidwidgetcore.WidgetSelphIDScanMode
import com.facephi.fphiwidgetcore.WidgetConfiguration
import com.facephi.fphiwidgetcore.WidgetLivenessMode
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import sv.com.tarjetaone.BuildConfig
import javax.inject.Qualifier
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object FacephiModule {
    // SelphID properties (document capture)
    private const val SV_DOC_DATA = "SV|<ALL>"
    private const val SELPHID_WIDGET_RESOURCES = "fphi-selphid-widget-resources-selphid-1.0.zip"

    // Selphi properties (selfie capture)
    private const val SELPHI_WIDGET_RESOURCES = "fphi-selphi-widget-resources-selphi-live-1.2.zip"
    /**
     * Padding for the cropped image configuration
     */
    private const val CROPPED_IMAGE_PADDING_PROP = "return_cropped_images_with_padding"
    private const val CROPPED_IMAGE_PADDING_VAL = "20"

    @DuiWidgetConfiguration
    @Provides
    @Singleton
    fun provideWidgetConfigurationDui(): WidgetSelphIDConfiguration {
        return WidgetSelphIDConfiguration().apply {
            license = BuildConfig.FACEPHI_LICENCE
            tutorialFlag = false
            resourcesPath = SELPHID_WIDGET_RESOURCES
            enableImages(true)
            wizardMode = true
            isGenerateRawImages = true
            setParam(CROPPED_IMAGE_PADDING_PROP,CROPPED_IMAGE_PADDING_VAL)
            documentType = WidgetSelphIDDocumentType.DTIDCard
            scanMode = WidgetSelphIDScanMode.SMSearch
            specificData = SV_DOC_DATA
        }
    }

    @NitWidgetConfiguration
    @Provides
    @Singleton
    fun provideWidgetConfigurationNit(): WidgetSelphIDConfiguration {
        return WidgetSelphIDConfiguration().apply {
            license = BuildConfig.FACEPHI_LICENCE
            tutorialFlag = false
            resourcesPath = SELPHID_WIDGET_RESOURCES
            enableImages(true)
            wizardMode = true
            isGenerateRawImages = true
            documentType = WidgetSelphIDDocumentType.DTForeignCard
            scanMode = WidgetSelphIDScanMode.SMSearch
            specificData = SV_DOC_DATA
        }
    }

    @Provides
    @Singleton
    fun provideWidgetConfiguration() : WidgetConfiguration {
        return WidgetConfiguration().apply {
            livenessMode = WidgetLivenessMode.LIVENESS_PASSIVE
            tutorialFlag = false
            resourcesPath = SELPHI_WIDGET_RESOURCES
            fullscreen = true
        }
    }
}

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class NitWidgetConfiguration

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class DuiWidgetConfiguration