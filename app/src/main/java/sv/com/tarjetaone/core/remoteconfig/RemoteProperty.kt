package sv.com.tarjetaone.core.remoteconfig

import sv.com.tarjetaone.core.remoteconfig.model.CustomerSupportInfo

/**
 * Represents a remote property that can be fetched from Firebase Remote Config.
 */
sealed interface RemoteProperty<T> {
    data object ScreenshotEnabled : RemoteProperty<Boolean>
    data object LastBuildNumber : RemoteProperty<Long>
    data object ForceUpdateMessage : RemoteProperty<String>
    data object CustomerSupportInfoProperty : RemoteProperty<CustomerSupportInfo?>
}
