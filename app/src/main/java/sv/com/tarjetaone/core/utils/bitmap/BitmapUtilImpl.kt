package sv.com.tarjetaone.core.utils.bitmap

import android.content.Context
import android.graphics.Bitmap
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.withContext
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.extensions.bitmapToBase64
import sv.com.tarjetaone.core.utils.extensions.convertTextToImage
import sv.com.tarjetaone.domain.core.DispatchersProvider
import java.io.ByteArrayOutputStream
import javax.inject.Inject

class BitmapUtilImpl @Inject constructor(
    @ApplicationContext private val context: Context,
    private val dispatchersProvider: DispatchersProvider
) : BitmapUtils {
    private val defaultDispatcher by lazy { dispatchersProvider.default }

    override suspend fun toByteArray(
        bitmap: Bitmap, compressType: Bitmap.CompressFormat, quality: Int
    ): ByteArray = withContext(defaultDispatcher) {
        ByteArrayOutputStream().use { out ->
            bitmap.compress(compressType, quality, out)
            out.toByteArray()
        }
    }

    override suspend fun toBase64(bitmap: Bitmap) = withContext(defaultDispatcher) {
        bitmap.bitmapToBase64()
    }

    override suspend fun generateSignatureImage(signatureText: String): Bitmap? =
        withContext(defaultDispatcher) {
            runCatching {
                signatureText.convertTextToImage(
                    context,
                    R.font.seaweed_script
                )
            }.getOrNull()
        }
}
