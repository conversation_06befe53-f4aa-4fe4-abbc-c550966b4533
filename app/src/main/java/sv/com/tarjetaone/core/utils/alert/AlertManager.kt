package sv.com.tarjetaone.core.utils.alert

import sv.com.tarjetaone.domain.entities.alerts.AlertTypeUI
import sv.com.tarjetaone.domain.entities.alerts.AlertUI

/**
 * In-memory manager for UI alerts.
 *
 * Main behavior (based on the current implementation):
 * - Keeps independent queues per [AlertTypeUI] (ALERT, BANNER, and MODAL).
 * - Preserves insertion order per type: the "next" is always the oldest pending item.
 * - Allows hiding/removing alerts so they are not shown again.
 * - No disk persistence; storage is volatile and can be cleared with [removeAllAlerts] or when reloading batches.
 * - Duplicate ids within the same storage session are not re-inserted (ignored if already considered "shown").
 *
 * Notes:
 * - [getNextAlert] polls and returns the next element from the queue for the given [type]; applies to any type.
 * - [getAlerts] returns a snapshot (copy) of the current queue content for the given [type] without modifying it.
 * - [hideAlert] removes the alert with the given id from its queue and marks it as shown/hidden.
 */
interface AlertManager {
    /**
     * Stores a batch of alerts in memory preserving their order.
     *
     * Typically invoked after fetching alerts from the backend. Alerts are automatically
     * routed to their corresponding queue based on their [AlertTypeUI]. If the batch is
     * empty, nothing happens. If an id has already been marked as shown/hidden in this
     * session, it will not be enqueued again.
     *
     * @param alerts List of [AlertUI] to enqueue.
     */
    fun storeAlerts(alerts: List<AlertUI>)

    /**
     * Retrieves and removes the next pending element for the given [type].
     *
     * Returns the first (oldest) available element and marks it as shown so it will not
     * appear again in subsequent calls.
     *
     * @param type The alert type to query.
     * @return The next [AlertUI], or null if none are pending.
     */
    fun getNextAlert(type: AlertTypeUI): AlertUI?

    /**
     * Returns all pending alerts for the given [type], in their current order.
     *
     * Useful, for example, for BANNERs rendered in a carousel. This call does not consume
     * the queue.
     *
     * @param type The alert type to list.
     * @return A list of alerts, or an empty list if there are none.
     */
    fun getAlerts(type: AlertTypeUI): List<AlertUI>

    /**
     * Hides and removes a specific alert from its queue so it is not shown again.
     *
     * Use this when the user dismisses the alert or its action is processed.
     *
     * @param alertId The id of the alert to remove.
     * @param type The [AlertTypeUI] the alert belongs to.
     */
    fun hideAlert(alertId: String?, type: AlertTypeUI)

    /**
     * Removes all alerts of all types and clears the shown state.
     */
    fun removeAllAlerts()
}
