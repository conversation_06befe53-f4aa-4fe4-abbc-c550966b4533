package sv.com.tarjetaone.core.utils

import sv.com.tarjetaone.common.utils.AppConstants.DOT_STRING
import sv.com.tarjetaone.core.di.EmailPattern
import sv.com.tarjetaone.core.interfaces.PatternsValidator
import java.util.regex.Pattern
import javax.inject.Inject

/**
 * Class that contains REGEX validators
 */
class PatternsValidatorImpl @Inject constructor(
    @EmailPattern private val emailPattern: Pattern
) : PatternsValidator {

    override fun isEmailAddressValid(email: CharSequence): Boolean {
        if (email.startsWith(DOT_STRING) || email.endsWith(DOT_STRING)) return false
        return emailPattern.matcher(email).matches()
    }
}
