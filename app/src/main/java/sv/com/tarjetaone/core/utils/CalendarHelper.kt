package sv.com.tarjetaone.core.utils

import java.time.LocalDate
import java.util.Calendar
import sv.com.tarjetaone.common.utils.extensions.SV_LOCALE
import sv.com.tarjetaone.common.utils.extensions.capitalize

private const val MIN_START_JOB_YEAR = 1960

/**
 * Get the months of the year in a list of pairs
 * @return List<Pair<Int, String>> with the month number and the month name
 */
fun getMonthData(): List<Pair<Int, String>> {
    return Calendar
        .getInstance()
        .getDisplayNames(Calendar.MONTH, Calendar.LONG, SV_LOCALE)
        ?.map { it.value.plus(1) to it.key.capitalize() }
        ?.sortedBy { it.first }
        .orEmpty()
}

/**
 * Get the years of the year in a list of pairs
 * @return List<Int> with the years
 */
fun getYearData(descending: Boolean = true): List<Int> {
    val years = (MIN_START_JOB_YEAR..Calendar.getInstance().get(Calendar.YEAR)).toList()
    return years.let {
        if (descending) it.reversed() else it
    }
}

/**
 * Check if the start date is valid
 * @param month Int? with the month
 * @param year Int? with the year
 * @param action (Boolean) -> Unit with the action to perform
 * @return Boolean with the result
 */
fun isValidStartDate(
    month: Int?,
    year: Int?,
    action: (Boolean) -> Unit = {}
): Boolean {
    return if (month == null || year == null) {
        false
    } else {
        val today = LocalDate.now()
        val date = LocalDate.of(year, month, 1)

        return date.isBefore(today).also {
            action(it)
        }
    }
}
