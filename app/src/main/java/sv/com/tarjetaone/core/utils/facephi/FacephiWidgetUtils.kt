package sv.com.tarjetaone.core.utils.facephi

import com.facephi.fphiselphidwidgetcore.WidgetExceptionType
import com.facephi.fphiwidgetcore.WidgetExceptionType as SelfieWidgetExceptionType
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.CommonDialogWithIconParams
import sv.com.tarjetaone.presentation.helpers.UiText

fun getDialogParamsForException(
    exceptionType: WidgetExceptionType,
    onButtonClick: () -> Unit = {}
): CommonDialogWithIconParams {
    val title = when (exceptionType) {
        WidgetExceptionType.Timeout -> R.string.facephi_timeout_title
        else -> R.string.error_doc_picture_title
    }

    return CommonDialogWithIconParams(
        isDismissible = false,
        icon = R.drawable.ic_error_yellow,
        buttonColor = R.color.yellow_alert_button,
        title = UiText.StringResource(title),
        message = UiText.StringResource(R.string.error_doc_picture_desc),
        buttonText = UiText.StringResource(R.string.facephi_dialog_agree),
        onButtonClick = onButtonClick
    )
}

fun getDialogParamsForException(
    exceptionType: SelfieWidgetExceptionType,
    onButtonClick: () -> Unit = {}
): CommonDialogWithIconParams {
    val title = when (exceptionType) {
        SelfieWidgetExceptionType.Timeout -> R.string.facephi_timeout_title
        else -> R.string.not_identified
    }

    return CommonDialogWithIconParams(
        isDismissible = false,
        icon = R.drawable.ic_error_yellow,
        buttonColor = R.color.yellow_alert_button,
        title = UiText.StringResource(title),
        message = UiText.StringResource(R.string.error_doc_picture_desc),
        buttonText = UiText.StringResource(R.string.facephi_dialog_agree),
        onButtonClick = onButtonClick
    )
}
