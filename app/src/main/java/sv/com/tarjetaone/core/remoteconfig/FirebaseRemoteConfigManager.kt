package sv.com.tarjetaone.core.remoteconfig

import com.google.firebase.Firebase
import com.google.firebase.remoteconfig.ConfigUpdate
import com.google.firebase.remoteconfig.ConfigUpdateListener
import com.google.firebase.remoteconfig.FirebaseRemoteConfigException
import com.google.firebase.remoteconfig.remoteConfig
import com.google.firebase.remoteconfig.remoteConfigSettings
import javax.inject.Inject
import kotlin.coroutines.resume
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import sv.com.tarjetaone.BuildConfig
import sv.com.tarjetaone.core.remoteconfig.model.CustomerSupportInfo
import java.util.concurrent.TimeUnit

/**
 * Implementation of [RemoteConfigManager] that provides access to Remote Config properties.
 * This class is responsible for fetching and providing the latest values of remote config properties.
 */
class FirebaseRemoteConfigManager @Inject constructor() : RemoteConfigManager {

    /**
     * jsonInstance added in order to ignore unknown keys to prevent crashes when a parameter
     * is not added in the class or comes null from firebase
     * */
    private val jsonInstance = Json { ignoreUnknownKeys = true }

    private val remoteConfig = Firebase.remoteConfig

    /**
     * Initializes the remote config by setting config settings and default values.
     */
     override fun initialize() {
        remoteConfig.apply {
            setConfigSettingsAsync(
                remoteConfigSettings {
                    minimumFetchIntervalInSeconds = if (BuildConfig.DEBUG) {
                        // For development builds only: set a low fetch interval
                        MINIMUM_FETCH_INTERVAL_DEBUG
                    } else {
                        TimeUnit.HOURS.toSeconds(MINIMUM_FETCH_INTERVAL_PRD)
                    }
                    fetchTimeoutInSeconds = FETCH_TIME_OUT
                }
            )
            setDefaultsAsync(
                mapOf(
                    SCREENSHOT_ENABLE_PROPERTY to true,
                    LAST_BUILD_NUMBER_PROPERTY to BuildConfig.VERSION_CODE.toLong(),
                    FORCE_UPDATE_MESSAGE to DEFAULT_UPDATE_APP_MESSAGE,
                    CUSTOMER_SUPPORT_INFO to jsonInstance.encodeToString(
                        DEFAULT_CUSTOMER_SUPPORT_INFO
                    )
                )
            )
        }
    }

    /**
     * Fetches the remote config and activates it, returning true if successful.
     * It will suspend the coroutine until the fetch is completed.
     */
    override suspend fun fetchAndActivate(): Boolean = suspendCancellableCoroutine { cont ->
        remoteConfig
            .fetchAndActivate()
            .addOnCompleteListener { cont.resume(it.isSuccessful) }
    }

    override fun <T> getLatestProperty(property: RemoteProperty<T>) = callbackFlow {
        trySend(getProperty(property))
        val listener = remoteConfig.addOnConfigUpdateListener(
            object : ConfigUpdateListener {
                override fun onUpdate(configUpdate: ConfigUpdate) {
                    if (configUpdate.updatedKeys.contains(property.mapToKey())) {
                        remoteConfig.activate().addOnCompleteListener {
                            trySend(getProperty(property))
                        }
                    }
                }

                override fun onError(error: FirebaseRemoteConfigException) = Unit
            }
        )

        awaitClose { listener.remove() }
    }

    /**
     * Maps the property to the corresponding remote config key and returns its value.
     */
    @Suppress("UNCHECKED_CAST")
    override fun <T> getProperty(property: RemoteProperty<T>): T {
        return when (property) {
            is RemoteProperty.LastBuildNumber -> {
                remoteConfig.getLong(LAST_BUILD_NUMBER_PROPERTY) as T
            }

            is RemoteProperty.ForceUpdateMessage -> {
                remoteConfig.getString(FORCE_UPDATE_MESSAGE) as T
            }

            is RemoteProperty.ScreenshotEnabled -> {
                remoteConfig.getBoolean(SCREENSHOT_ENABLE_PROPERTY) as T
            }

            is RemoteProperty.CustomerSupportInfoProperty -> {
                jsonInstance.decodeFromString<CustomerSupportInfo>(
                    remoteConfig.getString(CUSTOMER_SUPPORT_INFO)
                ) as T
            }
        }
    }

    /**
     * Maps the RemoteProperty to its corresponding key in Firebase Remote Config.
     */
    private fun RemoteProperty<*>.mapToKey(): String {
        return when (this) {
            is RemoteProperty.LastBuildNumber -> LAST_BUILD_NUMBER_PROPERTY
            is RemoteProperty.ForceUpdateMessage -> FORCE_UPDATE_MESSAGE
            is RemoteProperty.ScreenshotEnabled -> SCREENSHOT_ENABLE_PROPERTY
            is RemoteProperty.CustomerSupportInfoProperty -> CUSTOMER_SUPPORT_INFO
        }
    }

    /**
     * Companion object to hold constants used in the remote config.
     * These constants are used for property names and default values.
     */
    companion object {
        private const val MINIMUM_FETCH_INTERVAL_PRD = 6L // in hours
        private const val MINIMUM_FETCH_INTERVAL_DEBUG = 30L // in seconds
        private const val FETCH_TIME_OUT = 30L
        private const val SCREENSHOT_ENABLE_PROPERTY = "screen_capture_enabled"
        private const val LAST_BUILD_NUMBER_PROPERTY = "latest_build_number_android"
        private const val FORCE_UPDATE_MESSAGE = "force_update_message"
        private const val CUSTOMER_SUPPORT_INFO = "customer_support_info_json"

        // Default customer support info
        private const val DEFAULT_UPDATE_APP_MESSAGE = "Se encuentra disponible una nueva version de la app en la tienda. Favor descargar para continuar utilizando la app"
        private const val CUSTOMER_SUPPORT_EMAIL = "<EMAIL>"
        private const val CUSTOMER_SUPPORT_PHONE = "********"
        private const val CUSTOMER_SUPPORT_PRIVACY_POLICY_URL = "https://one.bancoatlantida.com.sv/terminos-y-condiciones/politica-de-privacidad/"
        private const val E_BANKING_URL = "https://aolebanking.bancoatlantida.com.sv/personas"

        private val DEFAULT_CUSTOMER_SUPPORT_INFO = CustomerSupportInfo(
            contactEmail = CUSTOMER_SUPPORT_EMAIL,
            contactPhone = CUSTOMER_SUPPORT_PHONE,
            privacyPolicyURL = CUSTOMER_SUPPORT_PRIVACY_POLICY_URL,
            eBankingURL = E_BANKING_URL
        )
    }
}
