package sv.com.tarjetaone.core.utils

import android.content.Intent
import android.net.Uri
import android.provider.Settings
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import sv.com.tarjetaone.BuildConfig
import sv.com.tarjetaone.common.utils.AppConstants.URI_SCHEME_PACKAGE

/**
 * Extension function to open app settings
 * @param action action to open app settings
 * @param uri uri to open app settings
 * @return Intent to open app settings with given action and uri.
 */
fun openAppSettings(
    action: String = Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
    uri: Uri? = Uri.fromParts(URI_SCHEME_PACKAGE, BuildConfig.APPLICATION_ID, null)
) = uri?.let { Intent(action, uri) } ?: Intent(action)

/**
 * Executes a given action after a specified delay.
 *
 * @param delayMillis The delay time in milliseconds before executing the action.
 * @param action A lambda function to execute after the delay.
 */
fun CoroutineScope.delayThenExecute(delayMillis: Long, action: () -> Unit) {
    launch {
        delay(delayMillis)
        action()
    }
}

/**
 * Helper function to check if a value is null, empty, or zero.
 */
fun <T : Any> isNullOrEmptyOrZero(value: T?): Boolean {
    return when (value) {
        is String -> value.isEmpty()
        is Number -> value.toDouble() == 0.0
        else -> value == null
    }
}
