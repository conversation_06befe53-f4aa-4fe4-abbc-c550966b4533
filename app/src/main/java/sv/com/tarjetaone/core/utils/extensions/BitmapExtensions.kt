package sv.com.tarjetaone.core.utils.extensions

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.util.Base64
import android.view.View
import androidx.annotation.FontRes
import sv.com.tarjetaone.common.utils.helpers.CryptoHelper
import java.io.ByteArrayOutputStream
import sv.com.tarjetaone.common.R
import sv.com.tarjetaone.common.utils.AppConstants.BITMAP_COMPRESS_QUALITY
import sv.com.tarjetaone.common.utils.AppConstants.BITMAP_DEF_HEIGHT
import sv.com.tarjetaone.common.utils.AppConstants.BITMAP_DEF_WIDTH
import sv.com.tarjetaone.common.utils.AppConstants.BITMAP_DST_HEIGHT
import sv.com.tarjetaone.common.utils.AppConstants.BITMAP_DST_WIDTH
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.common.utils.extensions.convertTextToBitmap

fun Bitmap.toBase64(
    compressType: Bitmap.CompressFormat = Bitmap.CompressFormat.PNG,
    quality: Int = BITMAP_COMPRESS_QUALITY
): String {
    return try {
        val byteArrayOutputStream = ByteArrayOutputStream()
        this.compress(compressType, quality, byteArrayOutputStream)
        val byteArray: ByteArray = byteArrayOutputStream.toByteArray()
        CryptoHelper.encryptBASE64(byteArray)
    } catch (e: Exception) {
        EMPTY_STRING
    }
}

fun Bitmap.bitmapToBase64(
    compressType: Bitmap.CompressFormat = Bitmap.CompressFormat.PNG,
    quality: Int = BITMAP_COMPRESS_QUALITY
): String {
    val resizeBitmap = resizeBitmap(this)
    val byteArrayOutputStream = ByteArrayOutputStream()

    resizeBitmap.compress(compressType, quality, byteArrayOutputStream)

    val byteArray: ByteArray = byteArrayOutputStream.toByteArray()
    return Base64.encodeToString(byteArray, Base64.DEFAULT)
}

fun resizeBitmap(
    bitmap: Bitmap,
    width: Int = BITMAP_DST_WIDTH,
    height: Int = BITMAP_DST_HEIGHT
): Bitmap {
    return Bitmap.createScaledBitmap(bitmap, width, height, true)
}

fun String.convertTextToImage(context: Context, @FontRes fontId: Int): Bitmap {
    return convertTextToBitmap(
        size = 37f,
        font = context.resources.getFont(fontId),
        textColor = context.resources.getColor(R.color.black, null),
        backgroundColor = context.resources.getColor(R.color.white, null)
    )
}

fun View.toBitmap(): Bitmap? {
    try {
        val width = if (this.width == ZERO_VALUE) {
            BITMAP_DEF_WIDTH
        } else {
            this.width
        }

        val height = if (this.height == ZERO_VALUE) {
            BITMAP_DEF_HEIGHT
        } else {
            this.height
        }

        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        val bgDrawable = this.background

        if (bgDrawable != null) {
            bgDrawable.draw(canvas)
        } else {
            canvas.drawColor(Color.WHITE)
        }

        this.draw(canvas)
        return bitmap
    } catch (ex: Exception) {
        return null
    }
}
