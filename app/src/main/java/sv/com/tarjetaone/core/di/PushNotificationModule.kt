package sv.com.tarjetaone.core.di

import com.google.firebase.messaging.FirebaseMessaging
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton
import sv.com.tarjetaone.core.utils.notification.PushNotificationsIdGenerator
import sv.com.tarjetaone.core.utils.notification.PushNotificationsIdGeneratorImpl

@Module
@InstallIn(SingletonComponent::class)
object PushNotificationModule {

    @Provides
    @Singleton
    fun providePushNotificationModule(firebaseMessaging: FirebaseMessaging): PushNotificationsIdGenerator {
        return PushNotificationsIdGeneratorImpl(firebaseMessaging)
    }

    @Provides
    @Singleton
    fun providesPushServiceInstance(): FirebaseMessaging {
        return FirebaseMessaging.getInstance()
    }
}
