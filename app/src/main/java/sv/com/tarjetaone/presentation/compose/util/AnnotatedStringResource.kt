package sv.com.tarjetaone.presentation.compose.util

import androidx.annotation.StringRes
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.fromHtml

@Composable
@ReadOnlyComposable
private fun getTextFromResource(@StringRes id: Int): String {
    return LocalContext.current.resources.getText(id).toString()
}

/**
 * Converts a string resource to an annotated string to displayed in a Text component.
 * Useful for displaying HTML formatted strings.
 */
@Composable
@ReadOnlyComposable
fun annotatedStringResource(@StringRes id: Int): AnnotatedString {
    return AnnotatedString.fromHtml(getTextFromResource(id))
}

/**
 * Converts a string resource to an annotated string to displayed in a Text component.
 * Substitutes format specifiers in the format string with the provided arguments.
 * Useful for displaying HTML formatted strings.
 */
@Composable
@ReadOnlyComposable
fun annotatedStringResource(@StringRes id: Int, vararg args: Any): AnnotatedString {
    return AnnotatedString.fromHtml(getTextFromResource(id).format(*args))
}
