package sv.com.tarjetaone.presentation.view.common.management.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp

@Composable
fun DetailManagementCardItem(
    modifier: Modifier = Modifier,
    title: String,
    description: String,
    descriptionStyle: TextStyle? = null,
    showDivider: Boolean = false
) {
    Column(
        modifier = modifier
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodySmall.copy(
                lineHeight = MaterialTheme.customDimensSp.sp24,
                fontWeight = FontWeight.SemiBold
            ),
            color = MaterialTheme.customColors.gray5
        )
        Text(
            modifier = Modifier.padding(top = MaterialTheme.customDimens.dimen4),
            text = description,
            style = descriptionStyle ?: MaterialTheme.typography.bodySmall.copy(
                fontSize = MaterialTheme.customDimensSp.sp15,
            ),
            color = MaterialTheme.colorScheme.onSurface
        )
        if (showDivider) {
            HorizontalDivider(
                modifier = Modifier.padding(top = MaterialTheme.customDimens.dimen16),
                color = MaterialTheme.customColors.gray200
            )
        }
    }
}

@Preview
@Composable
fun DetailManagementCardItemPreview() {
    OneAppTheme {
        DetailManagementCardItem(
            modifier = Modifier
                .background(MaterialTheme.colorScheme.surface)
                .padding(MaterialTheme.customDimens.dimen32),
            title = "Nombre de la gestión",
            description = "Gestión de desactivación de protección"
        )
    }
}