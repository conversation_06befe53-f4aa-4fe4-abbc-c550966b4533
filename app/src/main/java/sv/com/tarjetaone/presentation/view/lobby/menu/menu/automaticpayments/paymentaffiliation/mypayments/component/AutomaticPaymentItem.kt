package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.mypayments.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.DASH_STRING
import sv.com.tarjetaone.common.utils.AppConstants.DAY_MONTH_YEAR_WITH_SLASH
import sv.com.tarjetaone.common.utils.AppConstants.YEAR_MONTH_DAY_NO_SPACES
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.contractslist.RecurringChargeContractUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.ButtonSize
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.mypayments.MyPaymentsDummyData

@Composable
fun AutomaticPaymentItem(
    contract: RecurringChargeContractUI,
    onViewDetailsClick: () -> Unit,
    onCancelClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    ElevatedCard(
        modifier = modifier,
        colors = CardDefaults.elevatedCardColors(
            containerColor = MaterialTheme.customColors.defaultSurface,
            contentColor = MaterialTheme.customColors.onDefaultSurface
        ),
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.customDimens.dimen16,
                    vertical = MaterialTheme.customDimens.dimen8
                )
        ) {
            Text(
                text = contract.serviceName,
                style = MaterialTheme.typography.bodyLarge.copy(fontWeight = FontWeight.SemiBold),
                color = MaterialTheme.colorScheme.secondary
            )
            Spacer8()
            if (contract.maxAmount.toDoubleOrNull() != 0.0) {
                InfoLine(
                    label = stringResource(R.string.my_payments_max_amount),
                    value = stringResource(R.string.money_string_format, contract.maxAmount),
                    modifier = Modifier.fillMaxWidth()
                )
                Spacer8()
            }
            InfoLine(
                label = stringResource(R.string.my_payments_last_charge),
                value = stringResource(R.string.money_string_format, contract.applicationAmount),
                modifier = Modifier.fillMaxWidth()
            )
            Spacer8()
            InfoLine(
                label = stringResource(R.string.my_payments_date_last_charge),
                value = contract.formattedLastApplicationDate(),
                modifier = Modifier.fillMaxWidth()
            )
            Spacer8()
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier.fillMaxWidth()
            ) {
                OneButton(
                    text = stringResource(R.string.my_payments_view_details),
                    onClick = onViewDetailsClick,
                    size = ButtonSize.SMALL
                )
                OneButton(
                    text = stringResource(R.string.cancel),
                    onClick = onCancelClick,
                    buttonVariant = ButtonVariant.SECONDARY_VARIANT,
                    size = ButtonSize.SMALL
                )
            }
        }
    }
}

fun RecurringChargeContractUI.formattedLastApplicationDate(): String {
    return if (lastApplicationDate != NO_LAST_APPLICATION_DATE) {
        lastApplicationDate.getFormattedDateFromTo(YEAR_MONTH_DAY_NO_SPACES, DAY_MONTH_YEAR_WITH_SLASH)
    } else {
        DASH_STRING
    }
}

private const val NO_LAST_APPLICATION_DATE = "00000"

@Preview
@Composable
private fun AutomaticPaymentItemPreview() {
    OneAppTheme {
        AutomaticPaymentItem(
            contract = MyPaymentsDummyData.recurringChargeContract,
            onViewDetailsClick = {},
            onCancelClick = {}
        )
    }
}
