package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.Spacer8

@Composable
fun AboutUsComponent(modifier: Modifier = Modifier) {
    val labelSmallStyle = MaterialTheme.typography.labelSmall.copy(
        color = MaterialTheme.customColors.gray500
    )
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Text(
            text = stringResource(id = R.string.about_us_title),
            style = labelSmallStyle.copy(fontWeight = FontWeight.Bold),
            color = MaterialTheme.customColors.secondaryDark
        )
        Text(
            text = stringResource(id = R.string.about_us_company_name),
            style = labelSmallStyle
        )
        Text(
            text = stringResource(id = R.string.about_us_company_phone_number),
            style = labelSmallStyle
        )
        Text(
            text = stringResource(id = R.string.about_us_company_nit),
            style = labelSmallStyle
        )
        Text(
            text = stringResource(id = R.string.about_us_company_email),
            style = labelSmallStyle
        )
        Text(
            text = stringResource(id = R.string.about_us_company_address),
            style = labelSmallStyle,
            textAlign = TextAlign.Center
        )
        Spacer8()
        Image(
            painter = painterResource(id = R.drawable.digicert),
            contentDescription = null,
            modifier = Modifier.size(
                width = MaterialTheme.customDimens.dimen100,
                height = MaterialTheme.customDimens.dimen52
            )
        )
    }
}

@Preview
@Composable
private fun AboutUsComponentPreview() {
    OneAppTheme {
        Surface {
            AboutUsComponent(modifier = Modifier.padding(MaterialTheme.customDimens.dimen32))
        }
    }
}
