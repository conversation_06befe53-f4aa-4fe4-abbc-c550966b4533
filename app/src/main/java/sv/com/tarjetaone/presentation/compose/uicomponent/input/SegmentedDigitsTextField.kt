package sv.com.tarjetaone.presentation.compose.uicomponent.input

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusManager
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.InfoStatus
import sv.com.tarjetaone.presentation.helpers.filterDigits

@Suppress("S3776")
@Composable
fun SegmentedDigitsTextField(
    modifier: Modifier = Modifier,
    length: Int = 6,
    onValueChange: (String) -> Unit,
    focusManager: FocusManager = LocalFocusManager.current,
    infoStatus: InfoStatus = InfoStatus.None
) {
    val numbers = remember {
        mutableStateListOf<Int?>().also { it.addAll(List(length) { null }) }
    }
    val focusRequesters = remember {
        List(length) { FocusRequester() }
    }
    var focusedIndex: Int? by remember { mutableStateOf(null) }
    LaunchedEffect(focusedIndex) {
        focusedIndex?.let { focusRequesters[it].requestFocus() }
    }
    LaunchedEffect(numbers.toList()) {
        if (numbers.none { it == null }) {
            focusRequesters.forEach { it.freeFocus() }
            focusManager.clearFocus()
        }
        onValueChange(
            numbers.filterNotNull().joinToString(separator = EMPTY_STRING) { it.toString() }
        )
    }
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(
            space = MaterialTheme.customDimens.dimen4,
            alignment = Alignment.CenterHorizontally
        )
    ) {
        repeat(length) { index ->
            DigitInputField(
                number = numbers.getOrNull(index),
                infoStatus = infoStatus,
                focusRequester = focusRequesters[index],
                onFocusChange = {
                    if (it) focusedIndex = index
                },
                onNumberChange = { number ->
                    number?.let { focusRequesters[index].freeFocus() }
                    focusedIndex = if (number == null || numbers[index] != null) {
                        focusedIndex
                    } else {
                        getNextFocusedTextFieldIndex(numbers, focusedIndex)
                    }
                    numbers[index] = number
                },
                onValueChange = { newText ->
                    if (newText.length > ONE_VALUE) {
                        val newNumber = newText.filterDigits().take(length)
                        if (newNumber.isNotEmpty()) {
                            numbers.clear()
                            numbers.addAll(List(length) { newNumber.getOrNull(it)?.digitToInt() })
                        }
                    }
                },
                onKeyboardBack = {
                    getPreviousFocusedIndex(focusedIndex)?.let {
                        numbers[it] = null
                        focusedIndex = it
                    }
                }
            )
        }
    }
}

private fun getPreviousFocusedIndex(currentIndex: Int?): Int? {
    return currentIndex?.minus(1)?.coerceAtLeast(0)
}

private fun getFirstEmptyFieldIndexAfterFocusedIndex(numbers: List<Int?>, focusedIndex: Int): Int {
    numbers.forEachIndexed { index, number ->
        if (index <= focusedIndex) return@forEachIndexed
        if (number == null) return index
    }
    return focusedIndex
}

private fun getNextFocusedTextFieldIndex(numbers: List<Int?>, focusedIndex: Int?): Int? {
    return focusedIndex?.let {
        if (it == numbers.lastIndex) return it
        getFirstEmptyFieldIndexAfterFocusedIndex(numbers, it)
    }
}

@Preview
@Composable
private fun SegmentedDigitsTextFieldPreview() {
    OneAppTheme {
        SegmentedDigitsTextField(
            onValueChange = {},
        )
    }
}
