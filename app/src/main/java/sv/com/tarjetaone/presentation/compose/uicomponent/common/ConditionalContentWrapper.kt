package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme

/**
 * A composable wrapper that conditionally displays its content based on the given condition.
 *
 * @param condition If true, the content composable will be displayed; otherwise, nothing is rendered.
 * @param content The composable content to display when the condition is true.
 */
@Composable
fun ConditionalContentWrapper(
    condition: Boolean = false,
    content: @Composable () -> Unit
) {
    if (condition) {
        content()
    }
}

@Preview(showBackground = true)
@Composable
private fun ConditionalContentWrapperPreview() {
    OneAppTheme {
        ConditionalContentWrapper(
            condition = true
        ) {
            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(8.dp),
                text = "value"
            )
        }
    }
}
