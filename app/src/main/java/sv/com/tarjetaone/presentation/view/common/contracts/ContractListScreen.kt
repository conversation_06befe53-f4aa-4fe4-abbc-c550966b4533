package sv.com.tarjetaone.presentation.view.common.contracts

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.domain.entities.response.DataContractUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.checkbox.CheckBoxWithText
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.view.request_card_forms.card_configuration.contracts.list.component.ContractItem

@Composable
fun ContractListScreen(
    viewModel: BaseContractsViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LaunchedEffect(Unit) {
        viewModel.onEvent(ContractListUiEvent.OnStart)
    }

    SideEffectHandler(
        events = viewModel.sideEffects
    ) {
        ContractListScreenContent(
            uiState = uiState,
            onEvent = viewModel::onEvent
        )
    }
}

@Composable
fun ContractListScreenContent(
    uiState: ContractListUiState = ContractListUiState(),
    onEvent: (ContractListUiEvent) -> Unit = {}
) {
    ScreenWithTopAppBar(
        title = stringResource(R.string.sign_to_finish),
        isLeftButtonVisible = uiState.isBackEnabled,
        onLeftButtonClick = {
            onEvent(ContractListUiEvent.OnBackClick)
        },
        onRightButtonClick = {
            onEvent(ContractListUiEvent.OnTwilioClick)
        }
    ) {
        Column(
            modifier = Modifier
                .background(MaterialTheme.colorScheme.surface)
                .padding(
                    horizontal = MaterialTheme.customDimens.dimen36,
                    vertical = MaterialTheme.customDimens.dimen16
                )
                .fillMaxSize()
        ) {
            Text(
                buildAnnotatedString {
                    append(stringResource(id = R.string.unsigned_contracts_description))
                    withStyle(style = SpanStyle(fontWeight = FontWeight.Bold)) {
                        append(stringResource(id = R.string.my_documents_label))
                    }
                },
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface
            )
            Spacer16()
            Text(
                text = stringResource(id = R.string.your_credit_card_one),
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.Bold,
                ),
                modifier = Modifier.align(Alignment.CenterHorizontally)
            )
            Spacer32()
            ContractsList(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(ONE_FLOAT_VALUE),
                contracts = uiState.contracts,
                onClickReadContract = { idContract ->
                    onEvent(ContractListUiEvent.OnClickReadContract(idContract))
                },
                onCheckContract = { idContract ->
                    onEvent(ContractListUiEvent.OnContractChecked(idContract))
                }
            )
            Spacer24()
            CheckBoxWithText(
                text = stringResource(id = R.string.contracts_ive_read_all),
                color = MaterialTheme.colorScheme.onSecondaryContainer,
                isChecked = uiState.areDocumentsRead,
                onCheckedChanged = {
                    onEvent(ContractListUiEvent.OnCheckAllDocuments)
                }
            )
            OneButton(
                modifier = Modifier.fillMaxWidth(),
                text = uiState.primaryButtonText.asString(),
                enabled = uiState.areDocumentChecked,
                onClick = {
                    onEvent(ContractListUiEvent.OnPrimaryButtonClick)
                }
            )
        }
    }
}

@Composable
fun ContractsList(
    modifier: Modifier = Modifier,
    contracts: List<DataContractUI> = emptyList(),
    onClickReadContract: (Int) -> Unit = {},
    onCheckContract: (Int) -> Unit = {}
) {
    LazyColumn(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen22)
    ) {
        items(contracts) { item ->
            ContractItem(
                data = item,
                onClickReadContract = onClickReadContract,
                onCheckContract = { documentId, _ ->
                    onCheckContract(documentId)
                }
            )
            HorizontalDivider(
                modifier = Modifier.padding(top = MaterialTheme.customDimens.dimen16),
                thickness = MaterialTheme.customDimens.dimen2,
                color = MaterialTheme.customColors.gray200
            )
        }
    }
}

@Preview(device = "id:Nexus 5")
@Composable
fun ContractUnsignedScreenPreview() {
    OneAppTheme {
        ContractListScreenContent(
            uiState = ContractListUiState(
                contracts = listOf(
                    DataContractUI(
                        documentTypeNameApp = "Contrato 1",
                        description = "Descripción Contrato 1",
                    ),
                    DataContractUI(
                        documentTypeNameApp = "Contrato 2",
                        description = "Descripción Contrato 2",
                    ),
                    DataContractUI(
                        documentTypeNameApp = "Contrato 3",
                        description = "Descripción Contrato 3",
                    ),
                    DataContractUI(
                        documentTypeNameApp = "Contrato 4",
                        description = "Descripción Contrato 4",
                    ),
                )
            )
        )
    }
}