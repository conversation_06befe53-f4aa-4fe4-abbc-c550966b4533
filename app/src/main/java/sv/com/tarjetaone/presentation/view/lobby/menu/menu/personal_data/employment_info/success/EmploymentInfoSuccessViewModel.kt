package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.employment_info.success

import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import javax.inject.Inject

@HiltViewModel
class EmploymentInfoSuccessViewModel @Inject constructor() : BaseViewModel() {
    private fun onContinueClick() {
        sendEvent(UiEvent.NavigateBackTo(R.id.employmentInfoOverviewFragment))
    }

    fun onEvent(event: EmploymentInfoSuccessUiEvent) {
        when (event) {
            EmploymentInfoSuccessUiEvent.OnContinueButtonClick -> onContinueClick()
        }
    }
}
