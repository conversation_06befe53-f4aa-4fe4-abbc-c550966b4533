package sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.deactivate_program_protection.confirmation

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.deactivate_program_protection.actived_program_protection.DeactivateProgramProtectionCard

@Composable
fun DeactivateProgramProtectionConfirmationScreen(
    viewModel: DeactivateProgramProtectionConfirmViewModel
) {
    DeactivateProgramProtectionConfirmationContent(
        onEvent = viewModel::onEvent
    )
}

@Composable
fun DeactivateProgramProtectionConfirmationContent(
    onEvent: (DeactivateProgramProtectionConfirmationUiEvent) -> Unit = {}
) {
    ScreenWithTopAppBar(
        title = stringResource(id = R.string.protection_plan),
        onLeftButtonClick = {
            onEvent(DeactivateProgramProtectionConfirmationUiEvent.OnBackClick)
        },
        onRightButtonClick = {
            onEvent(DeactivateProgramProtectionConfirmationUiEvent.OnSupportClick)
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = MaterialTheme.customDimens.dimen32),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = stringResource(id = R.string.do_you_want_to_deactivate_program_protection),
                style = MaterialTheme.typography.displaySmall.copy(
                    fontWeight = FontWeight.SemiBold
                ),
                color = MaterialTheme.colorScheme.onSurface,
                textAlign = TextAlign.Center,
            )
            Spacer1f()
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                DeactivateProgramProtectionCard(
                    iconId = R.drawable.ic_shield_warning,
                    titleCard = stringResource(id = R.string.if_you_deactivate_program_protection),
                ) {
                    Text(
                        text = stringResource(id = R.string.without_this_protection_program_you_have_disclaimer),
                        fontSize = MaterialTheme.customDimensSp.sp13,
                        modifier = Modifier.padding(top = MaterialTheme.customDimens.dimen16)
                    )
                    Text(
                        text = stringResource(id = R.string.with_this_protection_program_you_have_options),
                        fontSize = MaterialTheme.customDimensSp.sp13
                    )
                }
            }
            Spacer1f()
            OneButton(
                text = stringResource(id = R.string.keep_my_credit_card_safe),
                onClick = { onEvent(DeactivateProgramProtectionConfirmationUiEvent.OnKeepClick) },
                modifier = Modifier.fillMaxWidth()
            )
            OneButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = MaterialTheme.customDimens.dimen16),
                text = stringResource(id = R.string.deactivate_program_protection),
                buttonVariant = ButtonVariant.SECONDARY_VARIANT,
                onClick = {
                    onEvent(DeactivateProgramProtectionConfirmationUiEvent.OnDeactivateClick)
                },
            )
        }
    }
}

@Preview
@Composable
fun DeactivateProgramProtectionConfirmationScreenPreview() {
    OneAppTheme {
        DeactivateProgramProtectionConfirmationContent()
    }
}
