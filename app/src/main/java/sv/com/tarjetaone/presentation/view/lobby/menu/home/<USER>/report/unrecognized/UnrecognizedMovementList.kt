package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.report.unrecognized

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.paging.LoadState
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.items
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.domain.entities.response.UnrecognizedTransactionUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleLoadingIndicator
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component.EmptyTransactionsItem

@Composable
fun CardUnrecognizedMovementListContent(
    modifier: Modifier,
    maskedCard: String,
    transactionItems: LazyPagingItems<UnrecognizedTransactionUI>?,
    selectedItems: List<UnrecognizedTransactionUI>,
    onTransactionAction: (UnrecognizedTransactionUI) -> Unit,
    onAppendError: (() -> Unit) -> Unit
) {
    if (transactionItems == null || transactionItems.itemCount == ZERO_VALUE) {
        EmptyTransactionsItem(
            modifier = modifier.fillMaxSize(),
            image = R.drawable.ic_empty_movement,
            subtitle = stringResource(R.string.unrecognized_purchase_empty)
        )
    } else {
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen4),
            contentPadding = PaddingValues(
                horizontal = MaterialTheme.customDimens.dimen16,
                vertical = MaterialTheme.customDimens.dimen10
            ),
            modifier = modifier.fillMaxSize()
        ) {
            items(items = transactionItems, key = { it.referenceNumber.orEmpty() }) { transaction ->
                transaction?.let {
                    CardSwitchableTransactionItem(
                        maskedCard = maskedCard,
                        transaction = it,
                        isSelected = selectedItems.any { selectedItem ->
                            selectedItem.referenceNumber == it.referenceNumber
                        },
                        onTransactionAction = onTransactionAction,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
            when (transactionItems.loadState.append) {
                is LoadState.Loading -> item {
                    SimpleLoadingIndicator(modifier = Modifier.fillMaxWidth())
                }

                is LoadState.Error -> {
                    onAppendError { transactionItems.retry() }
                }

                else -> Unit
            }
        }
    }
}

@Composable
@Preview
fun UnrecognizedMovementListPreview() {
    OneAppTheme {
        CardUnrecognizedMovementListContent(
            modifier = Modifier,
            maskedCard = "****, 123",
            transactionItems = null,
            selectedItems = listOf(),
            onTransactionAction = {},
            onAppendError = {}
        )
    }
}