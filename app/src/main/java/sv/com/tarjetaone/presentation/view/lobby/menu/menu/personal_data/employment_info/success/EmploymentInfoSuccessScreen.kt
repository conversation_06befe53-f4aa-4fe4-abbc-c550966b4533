package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.employment_info.success

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.OneBackHandler
import sv.com.tarjetaone.presentation.view.common.confirmation_gradient.SuccessGradientScreen

@Composable
fun EmploymentInfoScreen(viewModel: EmploymentInfoSuccessViewModel) {
    OneBackHandler {
        viewModel.onEvent(EmploymentInfoSuccessUiEvent.OnContinueButtonClick)
    }
    SuccessGradientScreen(
        message = stringResource(id = R.string.employment_success_update_message),
        buttonText = stringResource(id = R.string.done_button),
        onButtonClick = {
            viewModel.onEvent(EmploymentInfoSuccessUiEvent.OnContinueButtonClick)
        }
    )
}
