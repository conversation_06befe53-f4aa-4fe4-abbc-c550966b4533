package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.cardpay.paymentselection

import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.response.CardPaymentAccountsDebitDataUI
import sv.com.tarjetaone.presentation.helpers.removeComma

data class PaymentSelectionUiState(
    val minAmount: Double = 0.0,
    val fullAmount: Double = 0.0,
    val otherAmount: String = "",
    val finalAmount: Double = 0.0,
    val selectedPayment: PaymentType? = null,
    val cardNumber: String = "",
    val isSelectingAccount: Boolean = false,
    val accountTypes: List<String> = emptyList(),
    val selectedAccountType: String? = null,
    val accounts: List<CardPaymentAccountsDebitDataUI> = emptyList(),
    val filteredAccounts: List<CardPaymentAccountsDebitDataUI> = emptyList(),
    val selectedAccount: CardPaymentAccountsDebitDataUI? = null,
    val paymentDescription: String = ""
) {
    val hasEnoughBalance = selectedAccount?.available?.removeComma()?.toDoubleOrNull().orZero() >= finalAmount

    private fun getValidMinAmount(): Boolean {
        return selectedPayment == PaymentType.MIN && minAmount > ZERO_VALUE
    }

    private fun getValidFullAmount(): Boolean {
        return selectedPayment == PaymentType.FULL && fullAmount > ZERO_VALUE
    }

    private fun getValidOtherAmount(): Boolean {
        return selectedPayment == PaymentType.OTHER && otherAmount.isNotEmpty()
    }

    fun isValidPayment(): Boolean {
        return getValidMinAmount() || getValidFullAmount() || getValidOtherAmount()
    }
}

enum class PaymentType {
    MIN,
    FULL,
    OTHER
}
