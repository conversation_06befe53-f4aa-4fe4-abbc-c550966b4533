package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.employment_info.employment_form

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.SolidLargeButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.PhoneTextField
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.Spacer32

@Composable
fun EmploymentContactPhoneScreen(
    viewModel: EmploymentInfoFormOverviewViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    EmploymentContactPhoneContent(
        uiState = uiState,
        onEvent = viewModel::onScreenUiEvent
    )
}

@Composable
private fun EmploymentContactPhoneContent(
    uiState: EmploymentContactPhoneUiState = EmploymentContactPhoneUiState(),
    onEvent: (EmploymentContactPhoneUiEvent) -> Unit
) {
    ScreenWithTopAppBar(
        onLeftButtonClick = {},
        onRightButtonClick = { onEvent(EmploymentContactPhoneUiEvent.OnTwilioClick) },
        isLeftButtonVisible = false,
        title = stringResource(id = R.string.contact_office)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = MaterialTheme.customDimens.dimen32)
        ) {
            PhoneTextField(
                value = uiState.contactPhone,
                onValueChange = { onEvent(EmploymentContactPhoneUiEvent.OnContactPhoneChange(it))},
                label = stringResource(id = R.string.type_phone_number_company_label),
                hasError = !uiState.phoneIsValid && !uiState.isValidatingPhone &&
                        uiState.contactPhone.isNotEmpty(),
                error = uiState.phoneError?.asString(),
                trailingIcon = if (uiState.isValidatingPhone) {
                    {
                        CircularProgressIndicator(
                            modifier = Modifier.size(MaterialTheme.customDimens.dimen12),
                            strokeWidth = MaterialTheme.customDimens.dimen2
                        )
                    }
                } else null
            )
            Spacer32()
            SolidLargeButton(
                text = stringResource(id = R.string.continue_button_label),
                enabled = uiState.phoneIsValid && !uiState.isValidatingPhone,
                onClick = { onEvent(EmploymentContactPhoneUiEvent.OnContinueButtonClick) }
            )
        }
    }
}

@Preview
@Composable
private fun EmploymentContactPhoneScreenPreview() {
    OneAppTheme {
        EmploymentContactPhoneContent(
            uiState = EmploymentContactPhoneUiState(),
            onEvent = {}
        )
    }
}
