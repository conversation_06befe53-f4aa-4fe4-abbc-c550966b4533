package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.catalog

sealed class ServiceCatalogUiEvent {
    data object OnStart: ServiceCatalogUiEvent()
    data object OnBack: ServiceCatalogUiEvent()
    data object OnTwilioClick: ServiceCatalogUiEvent()
    data class OnCategoryClick(val categoryId: Int, val isExpanded: Boolean): ServiceCatalogUiEvent()
    data class OnServiceClick(val service: ServiceItem): ServiceCatalogUiEvent()
    data class OnChangeSearchQuery(val searchServiceName: String): ServiceCatalogUiEvent()
    data class OnFetchServiceName(val query: String): ServiceCatalogUiEvent()
}
