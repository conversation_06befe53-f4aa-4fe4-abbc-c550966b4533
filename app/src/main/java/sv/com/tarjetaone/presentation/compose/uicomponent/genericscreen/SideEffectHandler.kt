package sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen

import android.widget.Toast
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import kotlinx.coroutines.flow.Flow
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.presentation.compose.uicomponent.common.ObserveFlowAsEvents
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleLoadingIndicator
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.OneDialog
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.OneDialogParams
import sv.com.tarjetaone.presentation.compose.util.shareContent

@Composable
fun SideEffectHandler(
    events: Flow<SideEffect>,
    content: @Composable () -> Unit,
) {
    var isLoading by rememberSaveable { mutableStateOf(false) }
    var showDialog by rememberSaveable { mutableStateOf(false) }
    var dialogParams by rememberSaveable { mutableStateOf(OneDialogParams()) }
    val context = LocalContext.current
    ObserveFlowAsEvents(events) { event ->
        when (event) {
            is SideEffect.Loading -> {
                isLoading = event.isLoading
            }
            is SideEffect.ShowOneDialog -> {
                showDialog = true
                dialogParams = event.params
            }
            is SideEffect.ShowToast -> {
                Toast.makeText(context, event.message.asString(context), Toast.LENGTH_SHORT).show()
            }
            is SideEffect.ShareContent -> shareContent(context, event.contentUri, event.contentType)
            is SideEffect.StartIntent -> context.startActivity(event.intent)
        }
    }
    // Disable back gestures when loading to avoid accidental navigation and invalid states.
    // If content lambda has a BackHandler, it will control the back handling behavior. To prevent
    // this, LocalBackHandlerEnabled composition local must be consumed to enable/disable back handling
    BackHandler(enabled = isLoading) {  }
    if (showDialog) {
        OneDialog(
            params = dialogParams,
            onDismissRequest = {
                showDialog = false
            }
        )
    }
    Box(modifier = Modifier.fillMaxSize()) {
        // Provide a local composition to control back handler behavior based on loading state
        CompositionLocalProvider(LocalBackHandlerEnabled provides !isLoading) {
            content()
        }
        AnimatedVisibility(
            visible = isLoading,
            enter = fadeIn(),
            exit = fadeOut(),
        ) {
            SimpleLoadingIndicator(
                modifier = Modifier
                    .fillMaxSize()
                    .background(MaterialTheme.colorScheme.surface)
            )
        }
    }
}

/**
 * This BackHandler must be used instead of the standard [BackHandler] for screens that require
 * custom back handling behavior and are wrapped in the [SideEffectHandler] composable.
 *
 * Functions as a wrapper around the standard [BackHandler] to avoid having to pass the
 * [LocalBackHandlerEnabled] state every time you want to use a back handler.
 *
 * @param enabled Whether the back handler is enabled or not. Defaults to true.
 * @param onBack The action to perform when the back button is pressed. Defaults to an empty lambda.
 */
@Composable
fun OneBackHandler(enabled: Boolean = true, onBack: () -> Unit = {}) {
    BackHandler(enabled && LocalBackHandlerEnabled.current, onBack)
}

val LocalBackHandlerEnabled = compositionLocalOf { true }
