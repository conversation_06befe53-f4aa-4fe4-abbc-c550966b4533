package sv.com.tarjetaone.presentation.view.lobby.menu.menu.account_deletion.delete_account_home

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.AppConstants.SUCCESS_RESPONSE_CODE
import sv.com.tarjetaone.core.remoteconfig.RemoteConfigManager
import sv.com.tarjetaone.core.remoteconfig.RemoteProperty
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.request.ManCloseAccountRequestUI
import sv.com.tarjetaone.domain.usecases.deleteAccount.CloseAccountManagementUseCase
import sv.com.tarjetaone.domain.usecases.deleteAccount.DeleteAccountUseCase
import sv.com.tarjetaone.presentation.helpers.DeleteAccountDetailType
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.account_deletion.common.DeleteAccountUiEvent

@HiltViewModel
class DeleteAccountHomeViewModel @Inject constructor(
    private val sharedPrefsRepo: SecureSharedPreferencesRepository,
    private val deleteAccountUseCase: DeleteAccountUseCase,
    private val closeAccountManagementUseCase: CloseAccountManagementUseCase,
    remoteConfigManager: RemoteConfigManager
) : BaseViewModel() {
    private val _privacyPolicy = MutableStateFlow(
        remoteConfigManager.getProperty(
            RemoteProperty.CustomerSupportInfoProperty
        )?.privacyPolicyURL.orEmpty()
    )
    val privacyPolicy: StateFlow<String> = _privacyPolicy.asStateFlow()

    private fun onCreateCloseAccountManagement() {
        viewModelScope.launch {
            sendEvent(SideEffect.Loading(true))

            closeAccountManagementUseCase(
                manCloseAccountRequest = ManCloseAccountRequestUI(
                    customerId = sharedPrefsRepo.getCustomerId().orZero()
                )
            ).executeUseCase { response ->
                // TODO UPDATE IN A LATER TICKET WITH ENCAPSULATED RESPONSE
                if (response.statusResponse?.responseStatus?.code != SUCCESS_RESPONSE_CODE) {
                    showUpsErrorMessage()
                    return@executeUseCase
                }

                deleteAccountUseCase().executeUseCase { _ ->
                    sendEvent(SideEffect.Loading(false))
                    sendEvent(
                        UiEvent.Navigate(
                            DeleteAccountHomeFragmentDirections
                                .deleteAccountHomeFragmentToDeleteAccountDetailFragment(
                                    deleteAccountDetail = response.managementCloseAccount?.management,
                                    deleteAccountDetailType = DeleteAccountDetailType.ACCOUNT_DELETED
                                )
                        )
                    )
                }
            }
        }
    }

    fun onEvent(event: DeleteAccountUiEvent) {
        when (event) {
            is DeleteAccountUiEvent.OnPrimaryButtonClick -> sendEvent(UiEvent.NavigateBack)
            is DeleteAccountUiEvent.OnSecondaryButtonClick -> onCreateCloseAccountManagement()
            DeleteAccountUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
        }
    }
}
