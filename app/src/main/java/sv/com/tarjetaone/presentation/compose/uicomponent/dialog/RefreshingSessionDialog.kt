package sv.com.tarjetaone.presentation.compose.uicomponent.dialog

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleLoadingIndicator
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer8

@Composable
fun RefreshingSessionDialog(
    @StringRes title: Int,
    @DrawableRes icon: Int,
    iconTint: Color,
    modifier: Modifier = Modifier,
    onDismissRequest: () -> Unit = {}
) {
    Dialog(
        onDismissRequest = onDismissRequest,
        properties = DialogProperties(
            dismissOnBackPress = false,
            dismissOnClickOutside = false
        )
    ) {
        Box(
            modifier = modifier
                .fillMaxSize()
                .wrapContentHeight()
                .padding(
                    horizontal = MaterialTheme.customDimens.dimen16,
                    vertical = MaterialTheme.customDimens.dimen1
                )
        ) {
            Card(
                colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
                modifier = modifier
                    .heightIn(min = MaterialTheme.customDimens.dimen189)
                    .wrapContentWidth()
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            vertical = MaterialTheme.customDimens.dimen16,
                            horizontal = MaterialTheme.customDimens.dimen16
                        ),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        modifier = Modifier.size(
                            width = MaterialTheme.customDimens.dimen56,
                            height = MaterialTheme.customDimens.dimen50
                        ),
                        painter = painterResource(id = icon),
                        tint = iconTint,
                        contentDescription = stringResource(id = title),
                    )
                    Spacer8()
                    Text(
                        text = stringResource(id = title),
                        style = MaterialTheme.typography.headlineSmall,
                        color = MaterialTheme.colorScheme.onSurface,
                        textAlign = TextAlign.Center
                    )
                    Spacer16()
                    SimpleLoadingIndicator(
                        modifier = Modifier
                            .width(MaterialTheme.customDimens.dimen24)
                            .height(MaterialTheme.customDimens.dimen24)
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun CommonComposeDialogPreview() {
    OneAppTheme {
        RefreshingSessionDialog(
            title = R.string.refreshing_ob_session_title,
            icon = R.drawable.ic_dialog_session_timeout,
            iconTint = MaterialTheme.customColors.alertVariant
        )
    }
}
