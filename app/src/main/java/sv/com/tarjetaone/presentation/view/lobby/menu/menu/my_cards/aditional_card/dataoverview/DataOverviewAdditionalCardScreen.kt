package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.dataoverview

import android.Manifest
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.rememberPermissionState
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.EIGHT_VALUE_PERCENT
import sv.com.tarjetaone.core.utils.facephi.CaptureType
import sv.com.tarjetaone.core.utils.facephi.rememberFacephiWidgetLauncher
import sv.com.tarjetaone.core.utils.shouldShowRationale
import sv.com.tarjetaone.domain.entities.response.CustomerDataUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.SolidLargeButton
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer12
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.generaldata.dataoverview.PersonalDataOverviewSharedContent

@Composable
fun DataOverviewAdditionalCardScreen(
    viewModel: DataOverviewAdditionalCardViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LaunchedEffect(Unit) {
        viewModel.onEvent(
            DataOverviewAdditionalCardUiEvent.OnStart
        )
    }

    DataOverviewAdditionalCardContent(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun DataOverviewAdditionalCardContent(
    modifier: Modifier = Modifier,
    uiState: DataOverviewAdditionalCardUiState,
    onEvent: (DataOverviewAdditionalCardUiEvent) -> Unit = {}
) {
    val cameraPermission = Manifest.permission.CAMERA
    val context = LocalContext.current
    val facephiWidgetLauncher = rememberFacephiWidgetLauncher(CaptureType.Document()) {
        onEvent(DataOverviewAdditionalCardUiEvent.OnDocumentCapture(it))
    }
    val cameraPermissionState = rememberPermissionState(cameraPermission) { isGranted ->
        if (isGranted)
            facephiWidgetLauncher.initWidget(context)
        else
            onEvent(
                DataOverviewAdditionalCardUiEvent.OnCameraPermissionDenied(
                    showRationale = context.shouldShowRationale(cameraPermission)
                )
            )
    }
    ScreenWithTopAppBar(
        contentPadding = PaddingValues(MaterialTheme.customDimens.dimen24),
        title = stringResource(R.string.personal_data),
        onLeftButtonClick = {
            onEvent(DataOverviewAdditionalCardUiEvent.OnBackClick)
        },
        onRightButtonClick = {
            onEvent(DataOverviewAdditionalCardUiEvent.OnTwilioButtonClick)
        },
        isProgressbarVisible = true,
        progress = EIGHT_VALUE_PERCENT
    ) {
        Column(
            modifier = modifier
                .fillMaxSize()
                .padding(horizontal = MaterialTheme.customDimens.dimen32)
                .verticalScroll(rememberScrollState())
        ) {
            PersonalDataOverviewSharedContent(
                customerData = uiState.customerData,
                addressLabel = null,
                canEditInformation = true,
                onEditButtonClick = {
                    onEvent(DataOverviewAdditionalCardUiEvent.OnEditClick)
                }
            )
            Spacer1f()
            Text(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(id = R.string.validate_personal_data_overview_confirm_title),
                style = MaterialTheme.typography.displaySmall.copy(
                    fontWeight = FontWeight.SemiBold
                ),
                textAlign = TextAlign.Center
            )
            Spacer12()
            SolidLargeButton(
                text = stringResource(id = R.string.validate_personal_data_overview_confirm_button),
                onClick = {
                    onEvent(DataOverviewAdditionalCardUiEvent.OnConfirmButtonClick)
                }
            )
            Spacer8()
            SolidLargeButton(
                text = stringResource(id = R.string.capture_document_again_label),
                buttonVariant = ButtonVariant.SECONDARY_VARIANT,
                onClick = {
                    onEvent(
                        DataOverviewAdditionalCardUiEvent.OnWrongDataButtonClick(
                            requestPermission = {
                                cameraPermissionState.launchPermissionRequest()
                            }
                        )
                    )
                }
            )
            Spacer32()
        }
    }
}

@Preview
@Composable
private fun DataOverviewAdditionalCardScreenPreview() {
    OneAppTheme {
        DataOverviewAdditionalCardContent(
            uiState = DataOverviewAdditionalCardUiState(
                customerData = CustomerDataUI(
                    customerId = 0,
                    fullName = "David Alejandro Massana Zacatares",
                    genderCode = "Masculino",
                    dui = "0000000-0",
                    duiExpirationDate = "01 jun 2024",
                    birthDate = "01 julio 1990",
                )
            )
        )
    }
}