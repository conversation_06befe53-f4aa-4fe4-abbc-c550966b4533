package sv.com.tarjetaone.presentation.compose.uicomponent.radiobutton

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.radiobutton.GradientConstants.CIRCLE_WIDTH_STROKE
import sv.com.tarjetaone.presentation.compose.uicomponent.radiobutton.GradientConstants.EXTERNAL_CIRCLE_RADIUS
import sv.com.tarjetaone.presentation.compose.uicomponent.radiobutton.GradientConstants.INNER_CIRCLE_RADIUS
import sv.com.tarjetaone.presentation.compose.util.Spacer8

@Composable
fun GradientRadioButton(
    modifier: Modifier = Modifier,
    text: String? = null,
    textStyle: TextStyle = MaterialTheme.typography.bodySmall.copy(
        fontWeight = FontWeight.Medium
    ),
    selected: Boolean,
    size: Dp = MaterialTheme.customDimens.dimen24,
    onClick: () -> Unit
) {
    val gradient = Brush.linearGradient(
        MaterialTheme.customColors.primaryGradient.reversed(),
        start = Offset(0f, Float.POSITIVE_INFINITY),
        end = Offset(Float.POSITIVE_INFINITY, 0f)
    )
    val disableColor = MaterialTheme.customColors.disabledGray

    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        Box(
            modifier = modifier
                .clip(CircleShape)
                .selectable(
                    selected = selected,
                    onClick = onClick
                )
                .size(size),
            contentAlignment = Alignment.Center
        ) {
            Canvas(modifier = Modifier.size(size)) {
                if (selected) {
                    drawCircle(
                        brush = gradient,
                        radius = size.toPx() / EXTERNAL_CIRCLE_RADIUS,
                        style = Stroke(
                            width = CIRCLE_WIDTH_STROKE
                        )
                    )
                    drawCircle(
                        brush = gradient,
                        radius = size.toPx() / INNER_CIRCLE_RADIUS,
                        center = Offset(
                            x = size.toPx() / EXTERNAL_CIRCLE_RADIUS,
                            y = size.toPx() / EXTERNAL_CIRCLE_RADIUS
                        )
                    )
                } else {
                    drawCircle(
                        color = disableColor,
                        radius = size.toPx() / EXTERNAL_CIRCLE_RADIUS,
                        style = Stroke(
                            width = CIRCLE_WIDTH_STROKE
                        )
                    )
                }
            }
        }
        text?.let {
            Spacer8()
            Text(
                it,
                style = textStyle,
                color = MaterialTheme.customColors.gray700
            )
        }
    }
}

object GradientConstants {
    const val EXTERNAL_CIRCLE_RADIUS = 2
    const val INNER_CIRCLE_RADIUS = 4
    const val CIRCLE_WIDTH_STROKE = 10.0f
}

@Preview
@Composable
private fun GradientRadioButtonActivePreview() {
    OneAppTheme {
        Surface(
            color = MaterialTheme.colorScheme.inverseSurface,
            modifier = Modifier.size(MaterialTheme.customDimens.dimen80)
        ) {
            GradientRadioButton(
                text = "Si",
                selected = true,
                onClick = {}
            )
        }
    }
}

@Preview
@Composable
private fun GradientRadioButtonDeactivatedPreview() {
    OneAppTheme {
        Surface(
            color = MaterialTheme.colorScheme.inverseSurface,
            modifier = Modifier.size(MaterialTheme.customDimens.dimen80)
        ) {
            GradientRadioButton(
                text = "No",
                selected = false,
                onClick = {}
            )
        }
    }
}
