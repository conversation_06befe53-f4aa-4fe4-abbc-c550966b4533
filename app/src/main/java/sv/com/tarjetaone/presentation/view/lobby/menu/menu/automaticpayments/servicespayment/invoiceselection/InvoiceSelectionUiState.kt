package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.invoiceselection

import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.domain.entities.request.automaticpayments.servicespayment.PxPaymentInformation
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFormUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.invoiceselection.BalanceClientAutomaticResponseUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.invoiceselection.infoproducts.PXPaymentMethodUI
import sv.com.tarjetaone.domain.utils.automaticpayments.servicespayment.invoiceselection.ProductTypeCode
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.invoiceselection.util.Invoice

data class InvoiceSelectionUiState(
    val serviceName: String = EMPTY_STRING,
    val serviceCategoryName: String? = null,
    val invoices: List<Invoice> = emptyList(),
    val selectedInvoice: Int? = null,
    val userProducts: List<PXPaymentMethodUI> = emptyList(),
    val userProductsTypes: List<ProductTypeCode> = emptyList(),
    val userProductsFiltered: List<PXPaymentMethodUI> = emptyList(),
    val selectedProduct: PXPaymentMethodUI? = null,
    val selectedProductType: ProductTypeCode? = null,
) {
    fun canSendInvoice(): Boolean {
        return selectedInvoice?.let { selectedInvoiceIndex ->
            invoices[selectedInvoiceIndex].isValidInvoice()
        } == true && selectedProduct != null
    }

    private fun getSelectedPaymentMethod(): String? {
        return if (selectedProduct != null && selectedProductType != null) {
            "${selectedProduct.productType.orEmpty()} - ${selectedProduct.productCode.orEmpty()}"
        } else null
    }

    fun toPxPaymentInformation(
        selectedInvoiceIndex: Int,
        balanceClient: BalanceClientAutomaticResponseUI,
        isFromScanning: Boolean
    ): PxPaymentInformation {
        val invoice = invoices[selectedInvoiceIndex]
        val fields = invoice.getUpdatedFields()
        return PxPaymentInformation(
            requestData = balanceClient.copy(
                service = balanceClient.service.copy(
                    dataSets = balanceClient.service.dataSets.copy(
                        data = mapOf(
                            invoice.formKey to PXFormUI(
                                type = invoice.type,
                                fields = fields
                            )
                        )
                    )
                )
            ),
            paymentNumber = if (selectedProduct?.productTypeCode == ProductTypeCode.CreditCards) {
                selectedProduct.contractTypeID.orEmpty()
            } else {
                selectedProduct?.productCode.orEmpty()
            },
            paymentMethodType = selectedProductType?.value.orEmpty(),
            transactionNumber = balanceClient.transactionNumber.orEmpty(),
            selectedPaymentMethod = getSelectedPaymentMethod(),
            isFromScanning = isFromScanning
        )
    }
}

fun List<PXPaymentMethodUI>.filterProductsByCode(productTypeCode: ProductTypeCode): List<PXPaymentMethodUI> {
    return this.filter { item ->
        item.productTypeCode == productTypeCode
    }
}
