package sv.com.tarjetaone.presentation.compose.uicomponent.common.topbar.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.vectorResource
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens

@Composable
fun BackActionButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
            .size(MaterialTheme.customDimens.dimen48)
            .clip(CircleShape)
            .clickable(onClick = onClick)
    ) {
        Image(
            modifier = Modifier.size(MaterialTheme.customDimens.dimen28),
            painter = painterResource(R.drawable.ic_new_back),
            contentDescription = null
        )
    }
}

@Composable
fun SupportActionButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
            .size(MaterialTheme.customDimens.dimen48)
            .clip(CircleShape)
            .background(MaterialTheme.customColors.successContainer)
            .clickable(onClick = onClick)
    ) {
        Image(
            modifier = Modifier.size(MaterialTheme.customDimens.dimen24),
            painter = painterResource(R.drawable.ic_chat),
            contentDescription = null
        )
    }
}

@Composable
fun CancelActionButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
            .size(MaterialTheme.customDimens.dimen38)
            .clip(CircleShape)
            .border(MaterialTheme.customDimens.dimen2, Color.White, CircleShape)
            .clickable(onClick = onClick)
    ) {
        Image(
            modifier = Modifier.size(MaterialTheme.customDimens.dimen16),
            painter = painterResource(R.drawable.ic_group_close),
            contentDescription = null
        )
    }
}

@Composable
fun CloseActionButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
            .size(MaterialTheme.customDimens.dimen48)
            .clip(CircleShape)
            .clickable { onClick() }
    ) {
        Image(
            modifier = Modifier.size(MaterialTheme.customDimens.dimen32),
            imageVector = ImageVector.vectorResource(R.drawable.ic_close_icon),
            contentDescription = null
        )
    }
}
