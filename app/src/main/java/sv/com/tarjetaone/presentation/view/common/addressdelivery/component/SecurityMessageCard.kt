package sv.com.tarjetaone.presentation.view.common.addressdelivery.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.THREE_VALUE
import sv.com.tarjetaone.presentation.compose.theme.LocalCustomColors
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SelectableCardMainContainer
import sv.com.tarjetaone.presentation.compose.util.Spacer16

@Composable
fun SecurityMessageCard(
    modifier: Modifier = Modifier,
    onDismissSecurityAlert: () -> Unit
) {
    SelectableCardMainContainer(
        modifier = modifier,
        content = {
            Row(
                modifier = Modifier
                    .wrapContentSize()
                    .background(LocalCustomColors.current.cardLightBackground)
                    .padding(
                        horizontal = MaterialTheme.customDimens.dimen24,
                        vertical = MaterialTheme.customDimens.dimen18
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    modifier = Modifier.wrapContentSize(),
                    imageVector = ImageVector.vectorResource(R.drawable.ic_check_circle_white),
                    contentDescription = null
                )
                Spacer16()
                Text(
                    text = stringResource(id = R.string.delivery_address_warning),
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.customColors.onSuccess,
                    maxLines = THREE_VALUE,
                    modifier = Modifier.weight(1f)
                )
                Image(
                    modifier = Modifier
                        .padding(start = MaterialTheme.customDimens.dimen8)
                        .clickable { onDismissSecurityAlert() },
                    imageVector = ImageVector.vectorResource(R.drawable.ic_close_alert),
                    contentDescription = null
                )
            }
        }
    )
}

@Preview
@Composable
fun SecurityMessageCardPreview() {
    OneAppTheme {
        SecurityMessageCard(
            onDismissSecurityAlert = {}
        )
    }
}
