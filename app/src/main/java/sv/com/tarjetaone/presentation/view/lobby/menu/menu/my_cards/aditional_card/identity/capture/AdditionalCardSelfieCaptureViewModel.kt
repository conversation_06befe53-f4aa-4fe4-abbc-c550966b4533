package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.identity.capture

import android.graphics.Bitmap
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.common.selfiecapture.SelfieCaptureBaseViewModel
import javax.inject.Inject

@HiltViewModel
class AdditionalCardSelfieCaptureViewModel @Inject constructor(
    facephiResultHandler: FacephiResultHandler
) : SelfieCaptureBaseViewModel(facephiResultHandler) {

    override fun onStart() {
        super.onStart()
        _uiState.update {
            it.copy(
                description = UiText.StringResource(
                    R.string.photo_verification_additional_card
                )
            )
        }
    }

    override fun onSuccessfulSelfieCapture(selfie: Bitmap) {
        sendEvent(
            UiEvent.Navigate(
                AdditionalCardSelfieCaptureFragmentDirections
                    .actionPrepareForAdditionalCardFragmentToSelfieConfirmationAdditionalCard()
            )
        )
    }
}
