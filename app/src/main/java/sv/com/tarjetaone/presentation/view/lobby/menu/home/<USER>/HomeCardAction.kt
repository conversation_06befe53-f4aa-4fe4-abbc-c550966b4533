package sv.com.tarjetaone.presentation.view.lobby.menu.home.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ROTATION_90
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.Spacer8

@Composable
fun HomeCardAction(
    modifier: Modifier = Modifier,
    label: String,
    onClick: () -> Unit,
    isExpanded: Boolean = false,
    content: @Composable (() -> Unit)? = null
) {
    val rotation by animateFloatAsState(
        targetValue = if (isExpanded) ROTATION_90 else 0f,
        label = "icon_rotation"
    )
    ElevatedCard(
        modifier = modifier,
        colors = CardDefaults.elevatedCardColors(
            containerColor = MaterialTheme.customColors.defaultSurface,
            contentColor = MaterialTheme.colorScheme.secondary
        )
    ) {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(min = MaterialTheme.customDimens.dimen56)
                    .clickable(onClick = onClick)
                    .padding(horizontal = MaterialTheme.customDimens.dimen16)
            ) {
                Text(
                    text = label,
                    modifier = Modifier.weight(1f),
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.SemiBold
                    )
                )
                Spacer8()
                Icon(
                    painter = painterResource(id = R.drawable.ic_arrow_right_stroke),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(end = MaterialTheme.customDimens.dimen4)
                        .rotate(rotation)
                )
            }
            AnimatedVisibility(isExpanded) {
                content?.invoke()
            }
        }
    }
}

@Preview
@Composable
private fun HomeCardActionPreview() {
    OneAppTheme {
        HomeCardAction(
            label = "Detalles de mi tarjeta",
            onClick = {}
        )
    }
}
