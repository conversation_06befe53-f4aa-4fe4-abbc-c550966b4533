package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.paymentdetail

import android.graphics.Bitmap

sealed class PaymentAffiliationDetailUiEvent {
    data object OnStart : PaymentAffiliationDetailUiEvent()
    data object OnTwilioClick : PaymentAffiliationDetailUiEvent()
    data object OnContinueClick : PaymentAffiliationDetailUiEvent()
    data class OnStoragePermissionGranted(val bitmap: Bitmap?) : PaymentAffiliationDetailUiEvent()
    data class OnStoragePermissionDenied(val showRationale: Boolean) : PaymentAffiliationDetailUiEvent()
    data class OnShareVoucherClick(
        val requestPermissionCallback: () -> Unit
    ) : PaymentAffiliationDetailUiEvent()
    data object OnHideComponentBeforeCapture : PaymentAffiliationDetailUiEvent()
}
