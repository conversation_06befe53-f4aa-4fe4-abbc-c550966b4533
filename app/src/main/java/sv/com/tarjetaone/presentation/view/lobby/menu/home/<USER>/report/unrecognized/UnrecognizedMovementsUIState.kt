package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.report.unrecognized

import androidx.compose.runtime.Stable
import androidx.paging.PagingData
import kotlinx.coroutines.flow.Flow
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.TWELVE_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_FLOAT_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.domain.entities.response.UnrecognizedTransactionUI

@Stable
data class UnrecognizedMovementsUIState(
    val selectedUnrecognizedTransactionUI: UnrecognizedTransactionUI? = null,
    val cardStatusId: String = EMPTY_STRING,
    val maskedCard: String = EMPTY_STRING,
    val unrecognizedTransactionUIPaging: Flow<PagingData<UnrecognizedTransactionUI>>? = null,
    val unrecognizedTransactionSelected: List<UnrecognizedTransactionUI> = listOf(),
    val cCardId: Int = ZERO_VALUE,
    val referenceNumber: String = EMPTY_STRING,
    val authorizationCode: String = EMPTY_STRING,
    val valueDate: String = EMPTY_STRING,
    val amount: Float = ZERO_FLOAT_VALUE,
    val pageSize: Int = TWELVE_VALUE,
    val description: String = EMPTY_STRING,
    val pageNumber: Int = ZERO_VALUE
)