package sv.com.tarjetaone.presentation.view.common.tutorial.component

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.Spacer32

@Composable
fun TipVisualReference(
    modifier: Modifier = Modifier,
    @DrawableRes referenceImage: Int,
    @DrawableRes bottomIcon: Int,
) {
    Box(modifier = modifier.height(IntrinsicSize.Min)) {
        Column(
            modifier = Modifier.fillMaxHeight(VISUAL_REFERENCE_WEIGHT)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        color = MaterialTheme.colorScheme.tertiary,
                        shape = MaterialTheme.shapes.medium
                    ),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Spacer32()
                Image(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(min = MaterialTheme.customDimens.dimen264)
                        .padding(horizontal = MaterialTheme.customDimens.dimen8),
                    painter = painterResource(id = referenceImage),
                    contentDescription = null,
                    contentScale = ContentScale.Fit,
                    alignment = Alignment.BottomCenter
                )
            }
        }
        Icon(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .size(MaterialTheme.customDimens.dimen32),
            imageVector = ImageVector.vectorResource(id = bottomIcon),
            tint = Color.Unspecified,
            contentDescription = null,
        )
    }
}

@Preview
@Composable
private fun TipVisualReferencePreview() {
    OneAppTheme {
        TipVisualReference(
            referenceImage = R.drawable.selfie_tutorial_correct_photo,
            bottomIcon = R.drawable.ic_check_green,
        )
    }
}

private const val VISUAL_REFERENCE_WEIGHT = 0.95f
