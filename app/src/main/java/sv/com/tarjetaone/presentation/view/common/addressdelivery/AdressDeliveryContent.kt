package sv.com.tarjetaone.presentation.view.common.addressdelivery

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_STRING
import sv.com.tarjetaone.domain.entities.response.AddressDeliveryUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.OneAppSnackBar
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.view.common.addressdelivery.component.DeliveryAddressesList

@Composable
fun AddressDeliveryContent(
    addresses: List<AddressDeliveryUI>,
    selectedAddress: AddressDeliveryUI?,
    onSelectAddress: (AddressDeliveryUI) -> Unit,
    alertVisibility: Boolean,
    onDismissSecurityAlert: () -> Unit,
    onContinueClick: () -> Unit,
    isContinueButtonEnabled: Boolean
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .background(MaterialTheme.colorScheme.surface)
            .fillMaxWidth()
    ) {
        Box(
            modifier = Modifier.weight(ONE_FLOAT_VALUE)
        ) {
            DeliveryAddressesList(
                addresses = addresses,
                selectedItem = selectedAddress ?: AddressDeliveryUI(),
                onSelectChange = { item ->
                    onSelectAddress(item)
                }
            )
        }
        AnimatedVisibility(
            visible = alertVisibility,
            exit = fadeOut()
        ) {
            OneAppSnackBar(
                modifier = Modifier.padding(
                    horizontal = MaterialTheme.customDimens.dimen35,
                ),
                icon = R.drawable.ic_check_circle_white,
                containerColor = MaterialTheme.customColors.cardLightBackground,
                message = stringResource(id = R.string.delivery_address_warning),
                messageStyle = MaterialTheme.typography.labelMedium,
                onCancelClick = onDismissSecurityAlert
            )
        }
        Spacer16()
        OneButton(
            modifier = Modifier
                .padding(
                    top = MaterialTheme.customDimens.dimen2,
                    bottom = MaterialTheme.customDimens.dimen16,
                    end = MaterialTheme.customDimens.dimen35,
                    start = MaterialTheme.customDimens.dimen35
                )
                .fillMaxWidth(),
            text = stringResource(id = R.string.continue_button_label),
            enabled = isContinueButtonEnabled,
            onClick = onContinueClick
        )
        Spacer16()
    }
}

@Preview
@Composable
fun AddressDeliveryContentPreview() {
    OneAppTheme {
        AddressDeliveryContent(
            addresses = listOf(
                AddressDeliveryUI(
                    addressTypeName = "Enviar a mi casa",
                    address = "Santa Maria 2670, Providencia, Región Metropolitana de Santiago, Chile Carrera 11 #95-37 Bogota Londres 247, Del Carmen, Ciudad de Mexico",
                    isValid = true
                ),
                AddressDeliveryUI(
                    addressTypeName = "Otra dirección",
                    address = "san salvador san salvador 134",
                    isValid = false
                ),
                AddressDeliveryUI(
                    addressId = ZERO_STRING,
                    isValid = true,
                    addressTypeName = "Agregar una nueva dirección"
                )
            ),
            selectedAddress = AddressDeliveryUI(),
            onSelectAddress = { },
            alertVisibility = true,
            onDismissSecurityAlert = { },
            onContinueClick = { },
            isContinueButtonEnabled = true
        )
    }
}