package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Row
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.util.Spacer8

@Composable
fun InfoLabel(
    text: String,
    modifier: Modifier = Modifier,
    @DrawableRes iconResId: Int = R.drawable.ic_info_2,
    textColor: Color = MaterialTheme.customColors.disclaimer,
    textStyle: TextStyle = MaterialTheme.typography.labelMedium,
    verticalAlignment: Alignment.Vertical = Alignment.CenterVertically
) {
    Row(
        verticalAlignment = verticalAlignment,
        modifier = modifier
    ) {
        Image(
            imageVector = ImageVector.vectorResource(id = iconResId),
            contentDescription = null
        )
        Spacer8()
        Text(
            text = text,
            style = textStyle,
            color = textColor
        )
    }
}

@Preview
@Composable
private fun InfoLabelPreview() {
    OneAppTheme {
        InfoLabel(text = "Hello world")
    }
}
