package sv.com.tarjetaone.presentation.view.common.documentpreview

import android.graphics.BitmapFactory
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.facephi.CaptureType
import sv.com.tarjetaone.core.utils.facephi.rememberFacephiWidgetLauncher
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.SolidLargeButton
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer8

@Composable
fun DocumentPreviewScreen(
    viewModel: DocumentPreviewViewModel,
    isBackVisible: Boolean = true,
    isProgressbarVisible: Boolean = false
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    DocumentPreviewScreen(
        isBackVisible = isBackVisible,
        isProgressbarVisible = isProgressbarVisible,
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@Composable
fun DocumentPreviewScreen(
    isBackVisible: Boolean = true,
    isProgressbarVisible: Boolean = false,
    uiState: DocumentPreviewUiState,
    onEvent: (DocumentPreviewUiEvent) -> Unit
) {
    val context = LocalContext.current
    val facephiLauncher = rememberFacephiWidgetLauncher(
        type = CaptureType.Document(),
        onFailure = {
            onEvent(DocumentPreviewUiEvent.OnDocumentCaptureFailed(it))
        },
    ) {
        onEvent(DocumentPreviewUiEvent.OnDocumentCaptured(it))
    }
    LaunchedEffect(Unit) {
        onEvent(DocumentPreviewUiEvent.OnStart)
    }
    ScreenWithTopAppBar(
        onLeftButtonClick = { onEvent(DocumentPreviewUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(DocumentPreviewUiEvent.OnTwilioClick) },
        isLeftButtonVisible = isBackVisible,
        title = stringResource(R.string.dui_capture_label_aditional_card),
        isProgressbarVisible = isProgressbarVisible,
        progress = 0.1f,
        contentPadding = PaddingValues(
            start = MaterialTheme.customDimens.dimen30,
            end = MaterialTheme.customDimens.dimen30,
            top = MaterialTheme.customDimens.dimen30
        )
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(horizontal = MaterialTheme.customDimens.dimen30)
        ) {
            if (uiState.documentFront != null && uiState.documentBack != null) {
                Spacer8()
                ElevatedCard(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = MaterialTheme.customDimens.dimen20)
                ) {
                    Image(
                        bitmap = uiState.documentFront.asImageBitmap(),
                        contentDescription = null,
                        contentScale = ContentScale.FillWidth,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
                Spacer8()
                ElevatedCard(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = MaterialTheme.customDimens.dimen20)
                ) {
                    Image(
                        bitmap = uiState.documentBack.asImageBitmap(),
                        contentDescription = null,
                        contentScale = ContentScale.FillWidth,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
                Spacer8()
                Text(
                    text = stringResource(R.string.confirm_readability),
                    style = MaterialTheme.typography.titleMedium
                )
                Spacer8()
                Text(
                    text = stringResource(R.string.check_readability_dui),
                    style = MaterialTheme.typography.bodyMedium
                )
                Spacer8()
            }
            Spacer1f()
            SolidLargeButton(
                text = stringResource(R.string.yes_continue_button_label),
                onClick = { onEvent(DocumentPreviewUiEvent.OnContinueClick) },
                enabled = uiState.documentFront != null && uiState.documentBack != null
            )
            Spacer16()
            SolidLargeButton(
                text = stringResource(R.string.shoot_a_new_photo),
                buttonVariant = ButtonVariant.SECONDARY_VARIANT,
                onClick = {
                    onEvent(DocumentPreviewUiEvent.OnRetakeClick)
                    facephiLauncher.initWidget(context)
                }
            )
            Spacer24()
        }
    }
}

@Preview
@Composable
private fun DocumentPreviewScreenPreview() {
    val resources = LocalContext.current.resources
    val uiState = DocumentPreviewUiState(
        documentFront = BitmapFactory.decodeResource(resources, R.drawable.dui_sample),
        documentBack = BitmapFactory.decodeResource(resources, R.drawable.dui_back)
    )
    OneAppTheme {
        DocumentPreviewScreen(
            uiState = uiState,
            onEvent = {}
        )
    }
}
