package sv.com.tarjetaone.presentation.view.lobby.menu.cardpin.identity.preview

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.presentation.view.common.selfiepreview.SelfiePreviewViewModel
import javax.inject.Inject

@HiltViewModel
class CardPinPreviewSelfieViewModel @Inject constructor(
    imageUtils: ImageUtils,
    facephiResultHandler: FacephiResultHandler,
    savedStateHandle: SavedStateHandle
) : SelfiePreviewViewModel(imageUtils, facephiResultHandler) {
    private val args = CardPinPreviewSelfieFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onContinueClick() {
        sendEvent(
            UiEvent.Navigate(
                CardPinPreviewSelfieFragmentDirections
                    .actionCardPinPreviewSelfieFragmentToIdentityValidationPinChangeFragment(pin = args.pin)
            )
        )
    }
}
