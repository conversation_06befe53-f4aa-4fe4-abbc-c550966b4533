package sv.com.tarjetaone.presentation.view.experimental

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.OneDialogParams
import sv.com.tarjetaone.presentation.helpers.UiText
import javax.inject.Inject

@HiltViewModel
class ExperimentalViewModel @Inject constructor() : BaseViewModel() {

    fun showDialog(params: OneDialogParams) {
        sendEvent(SideEffect.ShowOneDialog(params))
    }

    fun showToast() {
        sendEvent(SideEffect.ShowToast(UiText.DynamicString("This is a toast message!")))
    }

    fun loadStuff() {
        viewModelScope.launch {
            sendEvent(SideEffect.Loading(true))
            // Simulate loading
            delay(2000)
            sendEvent(SideEffect.Loading(false))
        }
    }
}
