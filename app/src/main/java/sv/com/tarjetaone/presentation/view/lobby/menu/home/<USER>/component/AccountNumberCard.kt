package sv.com.tarjetaone.presentation.view.lobby.menu.home.account_status.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.Spacer16

@Composable
fun AccountNumberCard(
    modifier: Modifier = Modifier,
    accountNumber: String
) {
    ElevatedCard(
        modifier = modifier.fillMaxWidth(),
        shape = MaterialTheme.shapes.large,
        colors = CardDefaults.elevatedCardColors(
            containerColor = MaterialTheme.colorScheme.background
        )
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
                .padding(MaterialTheme.customDimens.dimen16)
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_visa2),
                contentDescription = null,
            )
            Spacer16()
            Column(
                horizontalAlignment = Alignment.Start
            ) {
                Text(
                    text = stringResource(id = R.string.account_status_card_description),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.customColors.bodyLightGray
                )
                Text(
                    text = accountNumber,
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.customColors.headingDark
                )
            }
        }
    }
}

@Preview
@Composable
private fun AccountNumberCardPreview() {
    OneAppTheme {
        AccountNumberCard(
            accountNumber = "**********"
        )
    }
}