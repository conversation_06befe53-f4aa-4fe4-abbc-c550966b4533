package sv.com.tarjetaone.presentation.compose.theme

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Shapes
import androidx.compose.ui.unit.dp

/**
 * Adjust shape values if needed, right now we are adapting them as the design system.
 * @see https://www.figma.com/file/CDyZxGeX03JM7WTtnaNGGt/Banco-Atlantida---Atlantis---Design-system-v1.0-(Copy)?node-id=4086%3A20931&mode=dev
 */
val Shapes = Shapes(
    extraSmall = RoundedCornerShape(4.dp),
    small = RoundedCornerShape(8.dp),
    medium = RoundedCornerShape(12.dp),
    large = RoundedCornerShape(16.dp),
    extraLarge = RoundedCornerShape(32.dp)
)
