package sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.SetStatusBarAppearance
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.helpers.UiText

/**
 * Generic screen to show error message as a full screen format.
 *
 * @param title the main error title.
 * @param description it will be placed right below the text.
 * @param buttonVariant each caller screen can decide to change the button variant as needed.
 * @param onButtonClick this action will be invoked on the caller screen/fragment
 * to handel its own logic when pressing the button.
 */
@Composable
fun FailureFullScreen(
    title: UiText? = UiText.StringResource(R.string.were_sorry_for_what_happened),
    subtitle: UiText? = null,
    description: UiText? = UiText.StringResource(R.string.you_can_tried_later),
    textButton: UiText = UiText.StringResource(R.string.ok_message),
    buttonVariant: ButtonVariant = ButtonVariant.SECONDARY_VARIANT,
    onButtonClick: () -> Unit
) {
    SetStatusBarAppearance(lightAppearance = false)
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.secondary)
            .safeDrawingPadding()
    ) {
        Column(
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.customDimens.dimen25)
                .weight(1f)
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_rejectoffer),
                contentDescription = null
            )
            title?.asString()?.let {
                Spacer16()
                Text(
                    text = it,
                    style = MaterialTheme.typography.displayMedium,
                    color = MaterialTheme.colorScheme.onPrimary,
                    textAlign = TextAlign.Center
                )
            }
            subtitle?.asString()?.let {
                Text(
                    text = it,
                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.SemiBold),
                    color = MaterialTheme.colorScheme.onPrimary,
                    textAlign = TextAlign.Center,
                )
            }
            description?.asString()?.let {
                Spacer16()
                Text(
                    text = it,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimary,
                    textAlign = TextAlign.Center
                )
            }
        }
        OneButton(
            text = textButton.asString(),
            onClick = onButtonClick,
            buttonVariant = buttonVariant,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.customDimens.dimen40)
        )
        Spacer32()
    }
}

@Preview
@Composable
fun FailureFullScreenPreview() {
    OneAppTheme {
        FailureFullScreen(
            title = UiText.StringResource(R.string.were_sorry_for_what_happened),
            description = UiText.StringResource(R.string.you_can_tried_later),
            textButton = UiText.StringResource(R.string.understood_label)
        ) {}
    }
}
