package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.paymentdetail

import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.paymentdetail.PxPaymentServiceDetailUI

data class PxPaymentServiceDetailUiState(
    val isCapturingVoucherView: Boolean = false,
    val isCardPaymentMethod: Boolean = false,
    val isFromScanning: Boolean = false,
    val selectedPaymentMethod: String? = "",
    val pxPaymentServiceDetail: PxPaymentServiceDetailUI = PxPaymentServiceDetailUI(
        subject = "",
        referenceNumberCore = "",
        customerName = "",
        name = "",
        date = "",
        collectorName = "",
        collector = "",
        digits = "",
        totalPayment = 0.0,
        amount = 0.0
    )
)
