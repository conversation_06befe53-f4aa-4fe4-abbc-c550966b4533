package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import sv.com.tarjetaone.common.utils.AppConstants.TEN_VALUE
import sv.com.tarjetaone.presentation.compose.theme.LocalCustomColors
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.Shapes

/**
 * A reusable ProgressBar component for displaying progress information on every screen.
 *
 * @param modifier Modifier to apply desired modifications (e.g., fillMaxWidth, background, etc.).
 * @param progress The current progress as an integer value. Internally, it will be converted
 *  * to a Float, as the ProgressBar component expects a Float type. Additionally, the value
 *  * is divided by 10, assuming the maximum value is 10.
 */
@Composable
fun HorizontalProgressIndicator(
    modifier: Modifier = Modifier,
    progress: Int? = null,
) {
    val customColors = LocalCustomColors.current
    val progressModifier = modifier
        .fillMaxWidth()
        .height(8.dp)
        .background(customColors.gray200, Shapes.extraSmall)

    val color = MaterialTheme.colorScheme.primary
    val backgroundColor = Color.Transparent
    val strokeCap = StrokeCap.Round

    // If progress is null, then it means the LinearProgressIndicator is Indeterminate.
    progress?.let {
        LinearProgressIndicator(
            progress = { progress.toFloat().div(TEN_VALUE) },
            modifier = progressModifier,
            color = color,
            trackColor = backgroundColor,
            strokeCap = strokeCap
        )
    } ?: run {
        LinearProgressIndicator(
            modifier = progressModifier,
            color = color,
            trackColor = backgroundColor,
            strokeCap = strokeCap
        )
    }
}

@Preview
@Composable
fun HorizontalProgressIndicatorPreview() {
    OneAppTheme {
        HorizontalProgressIndicator(
            modifier = Modifier.fillMaxWidth(),
            progress = 6,
        )
    }
}
