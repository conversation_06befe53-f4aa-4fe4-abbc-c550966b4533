package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.contact_channel

import sv.com.tarjetaone.domain.entities.request.ContactNotificationType
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.contact_channel.component.ContactItemUiState

sealed class ContactChannelsUiEvent {
    data object OnBackClick : ContactChannelsUiEvent()
    data object OnSupportClick : ContactChannelsUiEvent()
    data class OnModifyClick(
        val contactItem: ContactItemUiState
    ) : ContactChannelsUiEvent()
    data class OnToggleNotification(
        val isActive: Boolean,
        val contactIndex: Int,
        val contactItem: ContactItemUiState,
        val notificationType: ContactNotificationType,
    ) : ContactChannelsUiEvent()
}
