package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.invoiceselection.util

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import sv.com.tarjetaone.common.utils.extensions.formatToTwoDecimals
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldValueType
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFormType

data class Invoice(
    val type: PXFormType?,
    val formKey: String,
    val invoiceFields: SnapshotStateList<InvoicePXFieldUiState> = mutableStateListOf(),
) {
    fun isValidInvoice(): Boolean {
        return invoiceFields.all { it.isValidField }
    }

    fun updateValue(fieldIndex: Int, value: String) {
        val invoiceField = this.invoiceFields[fieldIndex]
        this.invoiceFields[fieldIndex] = invoiceField.copy(
            value = value,
            hasError = value.isEmpty() && invoiceField.pxField.required
        )
    }

    fun getUpdatedFields(): Map<String, PXFieldUI> {
        val invoiceFields = this.invoiceFields.associate {
            it.fieldKey to it.toPXFieldUI()
        }.toMutableMap()
        val totalPaymentField = invoiceFields.entries.find { it.key == TOTAL_PAYMENT_FIELD_KEY }
        val totalSum = invoiceFields.filter {
            it.key != TOTAL_PAYMENT_FIELD_KEY && it.value.value.asValueString()?.type == PXFieldValueType.Money
        }.values.sumOf {
            it.value.asValueString()?.value?.toDoubleOrNull().orZero()
        }.formatToTwoDecimals()

        totalPaymentField?.let { field ->
            invoiceFields[TOTAL_PAYMENT_FIELD_KEY] = field.value.copy(
                value = field.value.valueString?.copy(
                    value = totalSum
                ) ?: field.value.value
            )
        }

        return invoiceFields.toMap()
    }

    companion object {
        const val TOTAL_PAYMENT_FIELD_KEY = "total_payment"
    }
}
