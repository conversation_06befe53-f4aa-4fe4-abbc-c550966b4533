package sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.deactivate_program_protection.deactivated

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.deactivate_program_protection.deactivated.component.ProgramProtectionDeactivatedInfoCard

@Composable
fun ProgramProtectionDeactivatedScreen(
    viewModel: ProgramProtectionDeactivatedViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    DeactivatedProgramProtectionContent(
        uiState = uiState,
        uiEvent = viewModel::onEvent
    )
}

@Composable
fun DeactivatedProgramProtectionContent(
    uiState: ProgramProtectionDeactivatedUiState,
    uiEvent: (ProgramProtectionDeactivatedUiEvent) -> Unit = {}
) {
    ScreenWithTopAppBar(
        title = stringResource(id = R.string.protection_plan),
        isLeftButtonVisible = false,
        onLeftButtonClick = {},
        onRightButtonClick = { uiEvent(ProgramProtectionDeactivatedUiEvent.OnSupportClick) }
    ) {
        Column(
            Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.surface)
                .padding(horizontal = MaterialTheme.customDimens.dimen32),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_shield_canceled),
                contentDescription = null,
                modifier = Modifier.padding(
                    top = MaterialTheme.customDimens.dimen32,
                    bottom = MaterialTheme.customDimens.dimen16
                )
            )
            Text(
                text = stringResource(id = R.string.deactivated_program_protection),
                style = MaterialTheme.typography.bodyMedium.copy(
                    lineHeight = MaterialTheme.customDimensSp.sp25,
                    fontWeight = FontWeight.SemiBold
                ),
                color = MaterialTheme.colorScheme.primary,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.weight(ONE_FLOAT_VALUE))
            ProgramProtectionDeactivatedInfoCard(
                deactivationDate = uiState.deactivationDate,
                chargeAmount = uiState.chargeAmount
            )
            Spacer(modifier = Modifier.weight(ONE_FLOAT_VALUE))
            OneButton(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(id = R.string.continue_button_label),
                onClick = { uiEvent(ProgramProtectionDeactivatedUiEvent.OnContinueClick) }
            )
            Spacer32()
        }
    }
}

@Preview
@Composable
private fun DeactivatedProgramProtectionScreenPreview() {
    OneAppTheme {
        DeactivatedProgramProtectionContent(
            uiState = ProgramProtectionDeactivatedUiState(
                deactivationDate = "19 de septiembre",
                chargeAmount = "$1.99"
            )
        )
    }
}
