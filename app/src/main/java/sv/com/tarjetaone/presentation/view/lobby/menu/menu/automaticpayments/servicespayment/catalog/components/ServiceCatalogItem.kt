package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.catalog.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.VALUE_0F
import sv.com.tarjetaone.common.utils.AppConstants.VALUE_90_F
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.card.HorizontalIconWithText
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.helpers.capitalize
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.catalog.ServiceCategory
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.catalog.ServiceItem
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.catalog.iconRes

@Composable
fun ServiceCatalogItem(
    modifier: Modifier = Modifier,
    category: ServiceCategory,
    onCategoryClick: (Int, Boolean) -> Unit,
    onServiceClick: (ServiceItem) -> Unit
) {
    ElevatedCard(
        modifier = modifier,
        shape = MaterialTheme.shapes.small,
        colors = CardDefaults.elevatedCardColors(
            containerColor = MaterialTheme.colorScheme.secondaryContainer
        )
    ) {
        CollectorsCategorySection(
            category = category,
            onClick = onCategoryClick
        )
        AnimatedVisibility(category.isExpanded) {
            CollectorsListSection(
                servicesList = category.serviceItems,
                onClick = onServiceClick
            )
        }
    }
}

@Composable
private fun CollectorsCategorySection(
    modifier: Modifier = Modifier,
    category: ServiceCategory,
    onClick: (Int, Boolean) -> Unit = { _, _ -> }
) {
    val rotation by animateFloatAsState(
        targetValue = if (category.isExpanded) VALUE_90_F else VALUE_0F,
        label = "arrow_rotation"
    )

    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable {
                onClick(category.categoryId, category.isExpanded)
            }
            .padding(MaterialTheme.customDimens.dimen16),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        HorizontalIconWithText(
            leadingIcon = category.iconRes(),
            iconSize = MaterialTheme.customDimens.dimen24,
            iconTextSpacing = MaterialTheme.customDimens.dimen8,
            text = UiText.DynamicString(category.categoryName.lowercase().capitalize()),
            textColor = MaterialTheme.colorScheme.onSurface,
            style = MaterialTheme.typography.bodySmall.copy(
                fontWeight = FontWeight.SemiBold,
                lineHeight = MaterialTheme.customDimensSp.sp14
            )
        )
        Icon(
            imageVector = ImageVector.vectorResource(R.drawable.ic_chevron_right),
            contentDescription = null,
            tint = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.rotate(rotation)
        )
    }
}

@Composable
private fun CollectorsListSection(
    modifier: Modifier = Modifier,
    servicesList: List<ServiceItem>,
    onClick: (ServiceItem) -> Unit = {}
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.customDimens.dimen35),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        repeat(servicesList.size) { index ->
            CollectorService(
                paymentServiceData = servicesList[index],
                onClick = onClick
            )
            if (index < servicesList.lastIndex) {
                HorizontalDivider(
                    thickness = MaterialTheme.customDimens.dimenZeroDotFive,
                    color = MaterialTheme.customColors.divider
                )
            }
        }
    }
}

@Composable
private fun CollectorService(
    modifier: Modifier = Modifier,
    paymentServiceData: ServiceItem,
    onClick: (ServiceItem) -> Unit = {}
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick(paymentServiceData) }
            .padding(
                vertical = MaterialTheme.customDimens.dimen8,
                horizontal = MaterialTheme.customDimens.dimen16
            )
    ) {
        Text(
            text = paymentServiceData.serviceName,
            color = MaterialTheme.colorScheme.onSurface,
            style = MaterialTheme.typography.headlineSmall.copy(
                fontWeight = FontWeight.Normal
            )
        )
        Text(
            text = paymentServiceData.categoriesName,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            style = MaterialTheme.typography.labelMedium.copy(
                fontWeight = FontWeight.Normal,
                lineHeight = MaterialTheme.customDimensSp.sp14
            )
        )
        Spacer8()
    }
}

@Preview
@Composable
private fun ServiceCatalogItemCollapsedPreview() {
    OneAppTheme {
        ServiceCatalogItem(
            category = ServiceCategory(
                categoryId = 1,
                categoryName = "Agua y electricidad",
                categoryIcon = "agua",
                isExpanded = false,
                serviceItems = emptyList()
            ),
            onCategoryClick = { _, _ -> },
            onServiceClick = {}
        )
    }
}

@Preview
@Composable
private fun ServiceCatalogItemExpandedPreview() {
    OneAppTheme {
        ServiceCatalogItem(
            category = ServiceCategory(
                categoryId = 1,
                categoryName = "Educación",
                categoryIcon = "icon_educacion",
                isExpanded = true,
                serviceItems = listOf(
                    ServiceItem(
                        catalogServiceCode = "010800201",
                        categoriesName = "Colegio",
                        serviceName = "COLEGIO CEFAS",
                        isManualCapture = false,
                        isAutomaticPayment = true,
                        fieldAutomatic = null,
                        serviceLabel = null
                    ),
                    ServiceItem(
                        catalogServiceCode = "010800202",
                        categoriesName = "Universidad",
                        serviceName = "UDB",
                        isManualCapture = false,
                        isAutomaticPayment = true,
                        fieldAutomatic = null,
                        serviceLabel = null
                    ),
                    ServiceItem(
                        catalogServiceCode = "010800203",
                        categoriesName = "Diplomado",
                        serviceName = "CURSO DE INGLES",
                        isManualCapture = false,
                        isAutomaticPayment = true,
                        fieldAutomatic = null,
                        serviceLabel = null
                    )
                ),
            ),
            onCategoryClick = { _, _ -> },
            onServiceClick = {}
        )
    }
}
