package sv.com.tarjetaone.presentation.view.lobby.menu.menu.account_deletion.delete_account_menu

import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import sv.com.tarjetaone.core.remoteconfig.RemoteConfigManager
import sv.com.tarjetaone.core.remoteconfig.RemoteProperty
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.account_deletion.common.DeleteAccountUiEvent

@HiltViewModel
class DeleteAccountMenuViewModel @Inject constructor(
    remoteConfigManager: RemoteConfigManager
) : BaseViewModel() {

    private val _privacyPolicy = MutableStateFlow(
        remoteConfigManager.getProperty(
            RemoteProperty.CustomerSupportInfoProperty
        )?.privacyPolicyURL.orEmpty()
    )
    val privacyPolicy: StateFlow<String> = _privacyPolicy.asStateFlow()

    fun onEvent(event: DeleteAccountUiEvent) {
        when (event) {
            is DeleteAccountUiEvent.OnPrimaryButtonClick -> sendEvent(UiEvent.NavigateBack)
            is DeleteAccountUiEvent.OnSecondaryButtonClick -> {
                sendEvent(
                    UiEvent.Navigate(
                        DeleteAccountMenuFragmentDirections
                            .deleteAccountMenuFragmentToCancelCardFragment()
                    )
                )
            }

            DeleteAccountUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
        }
    }
}
