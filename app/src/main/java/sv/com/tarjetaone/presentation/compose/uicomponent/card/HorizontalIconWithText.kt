package sv.com.tarjetaone.presentation.compose.uicomponent.card

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.helpers.UiText

/**
 * Reusable composable Row component.
 *
 * @param modifier to apply modifications from the caller function.
 * @param leadingImageIcon the icon to be displayed at the beginning.
 * @param leadingIcon the icon to be displayed at the beginning.
 * @param iconTextSpacing the space between the icon and the text.
 * @param text the text next to the icon.
 * @param textColor represents the main text color.
 * @param style the proper Text Style to reuse it according to each screen needs.
 */
@Suppress("kotlin:S107")
@Composable
fun HorizontalIconWithText(
    modifier: Modifier = Modifier,
    @DrawableRes leadingImageIcon: Int? = null,
    @DrawableRes leadingIcon: Int? = null,
    iconSize: Dp? = null,
    iconTextSpacing: Dp = MaterialTheme.customDimens.dimen20,
    tint: Color = Color.Unspecified,
    leadingIconAlignment: Alignment.Vertical = Alignment.CenterVertically,
    text: UiText? = null,
    annotatedString: AnnotatedString? = null,
    textColor: Color = Color.Unspecified,
    style: TextStyle = MaterialTheme.typography.bodySmall
) {

    Row(
        modifier = modifier,
        verticalAlignment = Alignment.Top
    ) {
        leadingImageIcon?.let {
            Image(
                painter = painterResource(id = it),
                contentDescription = null,
                modifier = Modifier.align(leadingIconAlignment)
            )
        }
        leadingIcon?.let {
            Icon(
                modifier = Modifier
                    .align(leadingIconAlignment)
                    .then(if (iconSize != null) Modifier.size(iconSize) else Modifier),
                painter = painterResource(id = it),
                contentDescription = null,
                tint = tint
            )
        }
        Spacer(modifier = Modifier.width(iconTextSpacing))
        text?.let {
            Text(
                text = it.asString(),
                style = style,
                color = textColor
            )
        }
        annotatedString?.let {
            Text(
                text = it,
                style = style,
                color = textColor
            )
        }
    }
}

@Preview
@Composable
fun HorizontalIconWithTextComponentPreview() {
    OneAppTheme {
        HorizontalIconWithText(
            leadingIcon = R.drawable.ic_sunglasses,
            text = UiText.StringResource(R.string.benefits_you_prefer),
        )
    }
}
