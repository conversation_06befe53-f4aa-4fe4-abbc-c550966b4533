package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import sv.com.tarjetaone.core.utils.isNullOrEmptyOrZero
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme

/**
 * A composable wrapper that ensures a non-empty value is passed to a child composable.
 * If primaryValue is empty or null, it falls back to secondaryValue.
 * If both values are null or empty, the component is not displayed.
 *
 * @param primaryValue The main value to be used.
 * @param secondaryValue A fallback value if primaryValue is null or empty.
 * @param content The composable that will receive the resolved non-empty value.
 */
@Composable
fun <T : Any> NonEmptyValueWrapper(
    primaryValue: T? = null,
    secondaryValue: T? = null,
    content: @Composable (T) -> Unit
) {
    val resolvedValue = primaryValue.takeUnless { isNullOrEmptyOrZero(it) } ?: secondaryValue
    resolvedValue?.let { content(it) }
}

@Preview(showBackground = true)
@Composable
private fun NonEmptyValueWrapperPreview() {
    OneAppTheme {
        NonEmptyValueWrapper(
            primaryValue = "",
            secondaryValue = "One"
        ) { value ->
            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(8.dp),
                text = value
            )
        }
    }
}
