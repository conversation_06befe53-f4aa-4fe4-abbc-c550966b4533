package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.paymentdetail

import android.view.View
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.extensions.configCurrencyWithFractions
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.detail.PaymentAffiliationDetailUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.CaptureAndroidViewWrapper
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.button.ShareVoucherButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.NonEmptyValueWrapper
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.Spacer12
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.handleShareVoucherCallback
import sv.com.tarjetaone.presentation.compose.util.handleVoucherSharingPermission
import sv.com.tarjetaone.presentation.view.common.management.component.DetailManagementCardItem
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.common.paymentdetail.PaymentServiceDetailHeader

@Composable
fun PaymentAffiliationDetailScreen(viewModel: PaymentAffiliationDetailViewModel) {

    LaunchedEffect(Unit) {
        viewModel.onEvent(PaymentAffiliationDetailUiEvent.OnStart)
    }

    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    PaymentAffiliationDetailContent(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun PaymentAffiliationDetailContent(
    uiState: PaymentAffiliationDetailUiState,
    onEvent: (PaymentAffiliationDetailUiEvent) -> Unit
) {
    ScreenWithTopAppBar(
        contentPadding = PaddingValues(
            start = MaterialTheme.customDimens.dimen24,
            end = MaterialTheme.customDimens.dimen24,
            top = MaterialTheme.customDimens.dimen24,
            bottom = MaterialTheme.customDimens.dimen4
        ),
        isLeftButtonVisible = false,
        onLeftButtonClick = { },
        onRightButtonClick = { onEvent(PaymentAffiliationDetailUiEvent.OnTwilioClick) },
    ) {

        // Voucher view
        var voucherView by remember { mutableStateOf<View?>(null) }
        val context = LocalContext.current

        val storagePermissionState = handleVoucherSharingPermission(
            voucherView = voucherView,
            onHideComponentBeforeCapture = {
                PaymentAffiliationDetailUiEvent.OnHideComponentBeforeCapture
            },
            onPermissionGranted = { bitmap ->
                PaymentAffiliationDetailUiEvent.OnStoragePermissionGranted(bitmap)
            },
            onPermissionDenied = { showRationale ->
                PaymentAffiliationDetailUiEvent.OnStoragePermissionDenied(showRationale)
            },
            onEvent = onEvent
        )

        val shareVoucherCallback = handleShareVoucherCallback(
            context = context,
            voucherView = voucherView,
            storagePermissionState = storagePermissionState,
            onEvent = onEvent,
            hideBeforeCaptureEvent = PaymentAffiliationDetailUiEvent.OnHideComponentBeforeCapture,
            onGrantedEvent = { bitmap ->
                PaymentAffiliationDetailUiEvent.OnStoragePermissionGranted(bitmap)
            },
            onShareVoucherClickEvent = { requestPermissionCallback ->
                PaymentAffiliationDetailUiEvent.OnShareVoucherClick(requestPermissionCallback)
            }
        )

        Column(modifier = Modifier.fillMaxSize()) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .weight(0.9f)
                    .verticalScroll(rememberScrollState())
            ) {
                CaptureAndroidViewWrapper(view = { voucherView = it }) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(MaterialTheme.colorScheme.surface)
                    ) {
                        Spacer12()
                        PaymentServiceDetailHeader(
                            modifier = Modifier.align(Alignment.CenterHorizontally),
                            referenceNumber = uiState.paymentAffiliationDetailUI.contractNumber.orEmpty()
                        )
                        Spacer16()
                        PaymentServiceDetailCard(
                            uiState = uiState,
                            onShareVoucherClick = shareVoucherCallback
                        )
                    }
                }
            }

            OneButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.customDimens.dimen32),
                text = stringResource(id = R.string.back_to_my_account),
                onClick = { onEvent(PaymentAffiliationDetailUiEvent.OnContinueClick) },
            )
            Spacer16()
        }
    }
}

@Composable
fun PaymentServiceDetailCard(
    modifier: Modifier = Modifier,
    uiState: PaymentAffiliationDetailUiState,
    onShareVoucherClick: () -> Unit,
) {
    Card(
        modifier = modifier
            .fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.onPrimaryContainer
        ),
        shape = RoundedCornerShape(MaterialTheme.customDimens.dimen30)
    ) {
        Column(
            modifier = modifier
                .padding(
                    top = MaterialTheme.customDimens.dimen32,
                    start = MaterialTheme.customDimens.dimen32,
                    end = MaterialTheme.customDimens.dimen32,
                    bottom = MaterialTheme.customDimens.dimen16
                ),
            verticalArrangement = Arrangement.spacedBy(
                space = MaterialTheme.customDimens.dimen8
            )
        ) {

            DetailManagementCardItem(
                title = stringResource(id = R.string.name_of_request),
                description = stringResource(id = uiState.paymentAffiliationDetailUI.contractManagementName),
                showDivider = true
            )

            NonEmptyValueWrapper(uiState.paymentAffiliationDetailUI.customerName) { value ->
                DetailManagementCardItem(
                    title = stringResource(id = R.string.client_name),
                    description = value,
                    showDivider = true
                )
            }

            val paymentMethod = if (uiState.isCardPaymentMethod) {
                stringResource(id = R.string.payment_service_tc_label)
            } else {
                 uiState.paymentAffiliationDetailUI.selectedPaymentMethod
            }

            NonEmptyValueWrapper(paymentMethod) { value ->
                DetailManagementCardItem(
                    title = stringResource(id = R.string.payment_method_label),
                    description = value,
                    showDivider = true
                )
            }

            NonEmptyValueWrapper(uiState.paymentAffiliationDetailUI.serviceName) { value ->
                DetailManagementCardItem(
                    title = stringResource(id = R.string.payment_method_service_label),
                    description = value,
                    showDivider = true
                )
            }

            NonEmptyValueWrapper(uiState.paymentAffiliationDetailUI.identifier) { value ->
                DetailManagementCardItem(
                    title = stringResource(id = R.string.payment_method_reference_number_label),
                    description = value,
                    showDivider = true
                )
            }

            NonEmptyValueWrapper(uiState.paymentAffiliationDetailUI.contractMaxAmount) { value ->
                DetailManagementCardItem(
                    modifier = Modifier.padding(top = MaterialTheme.customDimens.dimen8),
                    title = stringResource(id = R.string.maximum_collection_amount_label),
                    description = value.configCurrencyWithFractions(),
                    showDivider = uiState.isCapturingVoucherView.not()
                )
            }

            if (uiState.isCapturingVoucherView.not()) {
                ShareVoucherButton(
                    modifier = Modifier
                        .align(Alignment.CenterHorizontally)
                        .padding(vertical = MaterialTheme.customDimens.dimen8),
                    onClick = onShareVoucherClick
                )
            }
        }
    }
}

@Preview
@Composable
private fun PaymentAffiliationDetailScreenPreview() {
    OneAppTheme {
        PaymentAffiliationDetailContent(
            uiState = PaymentAffiliationDetailUiState(
                isCardPaymentMethod = true,
                paymentAffiliationDetailUI = PaymentAffiliationDetailUI(
                    contractNumber = "234",
                    contractManagementName = R.string.payment_affiliation_detail_create_automatic_payment,
                    customerName = "Juan Perez",
                    contractProductType = "",
                    serviceName = "SIMAN",
                    identifier = "****************",
                    contractMaxAmount = 0.00,
                    selectedPaymentMethod = "Cuentas de Ahorro PN - 5801..."
                ),
            ),
            onEvent = {}
        )
    }
}
