package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.contact_channel.successupdate

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.entities.response.ContactType
import javax.inject.Inject

@HiltViewModel
class SuccessUpdateContactViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {
    private val args = SuccessUpdateContactFragmentArgs.fromSavedStateHandle(savedStateHandle)
    private val _uiState =
        MutableStateFlow(
            SuccessUpdateContactUiState(
                contactType = args.contactType ?: ContactType.Email
            )
        )
    val uiState = _uiState.asStateFlow()

    private fun onDoneClick() {
        sendEvent(
            UiEvent.Navigate(
                SuccessUpdateContactFragmentDirections
                    .actionSuccessUpdateContactToContactChannelOverviewFragment()
            )
        )
    }

    fun onEvent(event: SuccessUpdateContactUiEvent) {
        when (event) {
            is SuccessUpdateContactUiEvent.OnDoneClick -> onDoneClick()
        }
    }
}
