package sv.com.tarjetaone.presentation.view.lobby.menu.menu.component

import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.domain.entities.alerts.AlertBodyUI
import sv.com.tarjetaone.domain.entities.alerts.AlertCloseConfigUI
import sv.com.tarjetaone.domain.entities.alerts.AlertStyleUI
import sv.com.tarjetaone.domain.entities.alerts.AlertTypeUI
import sv.com.tarjetaone.domain.entities.alerts.AlertUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.skeleton.SkeletonContainer
import sv.com.tarjetaone.presentation.compose.uicomponent.skeleton.SkeletonState

@Composable
fun AlertBannerCarousel(
    modifier: Modifier = Modifier,
    pagerState: PagerState,
    banners: List<AlertUI>,
    state: SkeletonState,
    onBannerClick: (AlertUI) -> Unit,
    onCloseBannerClick: (AlertUI) -> Unit,
    onRetry: () -> Unit,
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .animateContentSize(animationSpec = tween(durationMillis = SKELETON_HIDE_ANIMATION_TIME))
    ) {
        if (!(state is SkeletonState.Success && banners.isEmpty())) {
            SkeletonContainer(
                modifier = Modifier.heightIn(min = MaterialTheme.customDimens.dimen74),
                state = state,
                customSkeletonContent = { CustomAlertBannerSkeleton() },
                errorContent = {
                    AlertErrorStateCard(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = MaterialTheme.customDimens.dimen24),
                        onRetry = onRetry
                    )
                }
            ) {
                HorizontalPager(
                    modifier = Modifier.fillMaxWidth(),
                    state = pagerState,
                    pageSpacing = MaterialTheme.customDimens.dimen24,
                    contentPadding = PaddingValues(
                        horizontal = MaterialTheme.customDimens.dimen24
                    ),
                ) { page ->
                    val alert = banners[page]
                    AlertBanner(
                        alert = alert,
                        onClick = { onBannerClick(alert) },
                        onCloseClick = { onCloseBannerClick(alert) }
                    )
                }
            }
        }
    }
}

@Preview
@Composable
fun AlertBannerCarouselPreview() {
    val pagerState = rememberPagerState(
        pageCount = { TWO_VALUE } // Dummy page count for preview,
    )
    OneAppTheme {
        AlertBannerCarousel(
            pagerState = pagerState,
            banners = listOf(
                AlertUI(
                    id = "alert_12345",
                    code = "TRACKING",
                    type = AlertTypeUI.BANNER,
                    cCardId = 1,
                    customerId = 2,
                    isAdditionalCard = false,
                    body = AlertBodyUI(
                        icon = "tarjeta-visa",
                        title = "Sample Banner",
                        description = "This is a sample banner description."
                    ),
                    closeConfig = AlertCloseConfigUI(
                        allowGesture = true,
                        showButton = true,
                        canRemove = false
                    ),
                    style = AlertStyleUI(
                        backgroundColor = "#FFEBEE",
                        contentColor = "#B71C1C"
                    )
                ),
                AlertUI(
                    id = "alert_BANNER_12345",
                    code = "DELIVERY",
                    type = AlertTypeUI.BANNER,
                    cCardId = 1,
                    customerId = 2,
                    isAdditionalCard = false,
                    body = AlertBodyUI(
                        icon = "file-post-red",
                        title = "Sample Banner 2",
                        description = "This is a sample banner description 2."
                    ),
                    closeConfig = AlertCloseConfigUI(
                        allowGesture = true,
                        showButton = true,
                        canRemove = true
                    ),
                    style = AlertStyleUI(
                        backgroundColor = "#FFEBEE",
                        contentColor = "#B71C1C"
                    )
                )
            ),
            state = SkeletonState.Loading,
            onBannerClick = { },
            onCloseBannerClick = { },
            onRetry = { }
        )
    }
}

private const val SKELETON_HIDE_ANIMATION_TIME = 300