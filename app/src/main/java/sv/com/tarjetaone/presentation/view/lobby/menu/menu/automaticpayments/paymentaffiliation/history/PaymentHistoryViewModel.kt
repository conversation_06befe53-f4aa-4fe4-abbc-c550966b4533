package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.history

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import java.util.Calendar
import java.util.Date
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.CalendarFormat.DAY_MONTH_YEAR
import sv.com.tarjetaone.core.utils.extensions.addDays
import sv.com.tarjetaone.core.utils.extensions.formatDate
import sv.com.tarjetaone.core.utils.extensions.getCurrentDate
import sv.com.tarjetaone.core.utils.extensions.getPastDate
import sv.com.tarjetaone.domain.entities.request.automaticpayments.paymentaffiliation.transactionlist.RecurringTransactionListRequestUI
import sv.com.tarjetaone.domain.usecases.automaticpayments.paymentaffiliation.transaction.GetRecurringTransactionsUseCase

@HiltViewModel
class PaymentHistoryViewModel @Inject constructor(
    private val getRecurringTransactionsUseCase: GetRecurringTransactionsUseCase,
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {

    private val _uiState: MutableStateFlow<PaymentHistoryUiState> = MutableStateFlow(
        PaymentHistoryUiState(
            startDate = getDefaultDate().first,
            endDate = getDefaultDate().second
        )
    )
    val uiState: StateFlow<PaymentHistoryUiState> = _uiState.asStateFlow()

    private val args = PaymentHistoryFragmentArgs.fromSavedStateHandle(savedStateHandle)

    private fun onStart() {
        _uiState.update { it.copy(serviceName = args.serviceName) }
        fetchRecurringTransactions()
    }

    private fun getDefaultDate(): Pair<String, String> {
        val endDate = Calendar.getInstance().getCurrentDate(AppConstants.DAY_MONTH_YEAR_WITH_SLASH)
        val startDate = Calendar.getInstance().getPastDate(
            monthsAgo = TRANSACTION_HISTORY_FROM_MONTHS,
            format = DAY_MONTH_YEAR
        )
        return Pair(startDate, endDate)
    }

    private fun fetchRecurringTransactions() {
        sendEvent(UiEvent.Loading(true))
        viewModelScope.launch {
            val request = RecurringTransactionListRequestUI(
                clientCode = args.clientCode,
                contractNumber = args.contractNumber,
                transactionStartDate = _uiState.value.startDate,
                transactionEndDate = _uiState.value.endDate
            )
            getRecurringTransactionsUseCase(request)
                .executeUseCase(
                    onApiErrorAction = { _, error, _, _ ->
                        _uiState.update { it.copy(transactionList = emptyList()) }
                        sendEvent(UiEvent.Loading(false))
                        if (error?.code?.toIntOrNull() != EMPTY_TRANSACTIONS_CODE) {
                            showUpsErrorMessage(false) { sendEvent(UiEvent.NavigateBack) }
                        }
                    },
                    onSuccessAction = { transactions ->
                        sendEvent(UiEvent.Loading(false))
                        _uiState.update {
                            it.copy(transactionList = transactions)
                        }
                    }
                )
        }
    }

    private fun onDateInputClick(type: PaymentHistoryDateType) {
        _uiState.update { it.copy(openDialog = true, dateInputTypeSelected = type) }
    }

    private fun onCloseDialog() {
        _uiState.update { it.copy(openDialog = false, dateInputTypeSelected = null) }
    }

    private fun onStartDateSelected(startDate: Long?) {
        startDate?.let { date ->
            _uiState.update {
                it.copy(
                    startDate = Date(date).addDays(DAYS_TO_ADD_CALENDAR).formatDate(DAY_MONTH_YEAR),
                )
            }
        }
    }

    private fun onEndDateSelected(endDate: Long?) {
        endDate?.let { date ->
            _uiState.update {
                it.copy(
                    endDate = Date(date).addDays(DAYS_TO_ADD_CALENDAR).formatDate(DAY_MONTH_YEAR)
                )
            }
        }
    }

    private fun onSearchPaymentsClick() {
        // Set the custom date as selected to show empty state if there are no transactions
        _uiState.update { it.copy(customDateIsSelected = true) }

        fetchRecurringTransactions()
    }

    fun onEvent(event: PaymentHistoryUiEvent) {
        when (event) {
            PaymentHistoryUiEvent.OnStart -> onStart()
            PaymentHistoryUiEvent.OnBack -> sendEvent(UiEvent.NavigateBack)
            PaymentHistoryUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            is PaymentHistoryUiEvent.OnDateInputClick -> onDateInputClick(event.type)
            PaymentHistoryUiEvent.OnCloseDialog -> onCloseDialog()
            is PaymentHistoryUiEvent.OnStartDateSelected -> onStartDateSelected(event.date)
            is PaymentHistoryUiEvent.OnEndDateSelected -> onEndDateSelected(event.date)
            PaymentHistoryUiEvent.OnSearchPaymentsClick -> onSearchPaymentsClick()
        }
    }

    companion object {
        const val TRANSACTION_HISTORY_FROM_MONTHS = 6
        const val DAYS_TO_ADD_CALENDAR = 1
        const val EMPTY_TRANSACTIONS_CODE = 26
    }
}
