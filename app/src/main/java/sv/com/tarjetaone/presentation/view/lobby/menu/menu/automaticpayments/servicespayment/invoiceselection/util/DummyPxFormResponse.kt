package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.invoiceselection.util

import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldMaskType
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldType
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldValueType
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldValueUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFormType
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFormUI

object DummyPxFormResponse {
    private val exampleForm = PXFormUI(
        type = PXFormType.SimpleForm,
        fields = mapOf(
            "field5" to PXFieldUI(
                index = 3,
                label = "Field 5",
                mask = null,
                name = "field5",
                required = true,
                value = PXFieldValueUI.FieldValueStringUI(
                    type = PXFieldValueType.Money,
                    mask = PXFieldMaskType.Money2D,
                    editable = true,
                    value = ""
                ),
                reference = false,
                tooltip = "Field 5",
                selected = false,
                type = PXFieldType.Input,
            ),
            "field1" to PXFieldUI(
                index = 1,
                label = "Field 1",
                mask = null,
                name = "field1",
                required = true,
                value = PXFieldValueUI.FieldValueStringUI(
                    type = PXFieldValueType.Text,
                    mask = null,
                    editable = false,
                    value = "Sample Text"
                ),
                reference = false,
                tooltip = null,
                selected = false,
                type = PXFieldType.Output,
            ),
            "field2" to PXFieldUI(
                index = 2,
                label = "Field 2",
                mask = null,
                name = "field2",
                required = true,
                value = PXFieldValueUI.FieldValueStringUI(
                    type = PXFieldValueType.Text,
                    mask = null,
                    editable = true,
                    value = ""
                ),
                reference = false,
                tooltip = "Field 2...",
                selected = false,
                type = PXFieldType.Input,
            ),
            "field3" to PXFieldUI(
                index = 5,
                label = "Field 3",
                mask = null,
                name = "field3",
                required = true,
                value = PXFieldValueUI.FieldValueStringUI(
                    type = PXFieldValueType.Money,
                    mask = PXFieldMaskType.Money2D,
                    editable = false,
                    value = "54.0"
                ),
                reference = false,
                tooltip = "Field 3...",
                selected = false,
                type = PXFieldType.Output,
            ),
            "field4" to PXFieldUI(
                index = 4,
                label = "Field 4",
                mask = null,
                name = "field4",
                required = true,
                value = PXFieldValueUI.FieldValueListUI(
                    mask = null,
                    type = PXFieldValueType.List,
                    editable = false,
                    value = mapOf(
                       "option1" to PXFieldUI(
                            index = 1,
                            label = "Option 1",
                            mask = null,
                            name = "option1",
                            required = true,
                            value = PXFieldValueUI.FieldValueStringUI(
                                type = PXFieldValueType.Text,
                                mask = PXFieldMaskType.NoMask,
                                editable = false,
                                value = ""
                            ),
                            reference = false,
                            tooltip = null,
                            selected = false,
                            type = PXFieldType.Input,
                        ),
                        "option2" to PXFieldUI(
                            index = 2,
                            label = "Option 2",
                            mask = null,
                            name = "option2",
                            required = true,
                            value = PXFieldValueUI.FieldValueStringUI(
                                type = PXFieldValueType.Text,
                                mask = PXFieldMaskType.NoMask,
                                editable = false,
                                value = ""
                            ),
                            reference = false,
                            tooltip = null,
                            selected = true,
                            type = PXFieldType.Input,
                        ),
                        "option3" to PXFieldUI(
                            index = 3,
                            label = "Option 3",
                            mask = null,
                            name = "option3",
                            required = true,
                            value = PXFieldValueUI.FieldValueStringUI(
                                type = PXFieldValueType.Text,
                                mask = PXFieldMaskType.NoMask,
                                editable = false,
                                value = ""
                            ),
                            reference = false,
                            tooltip = null,
                            selected = false,
                            type = PXFieldType.Input,
                        ),
                    ),
                ),
                reference = false,
                tooltip = "Select an option",
                selected = false,
                type = PXFieldType.SelectBox,
            ),
            "total_payment" to PXFieldUI(
                index = 7,
                label = "Total payment",
                mask = null,
                name = "total_payment",
                required = true,
                value = PXFieldValueUI.FieldValueStringUI(
                    type = PXFieldValueType.Money,
                    mask = null,
                    editable = false,
                    value = "0.00"
                ),
                reference = false,
                tooltip = null,
                selected = false,
                type = PXFieldType.Hidden,
            ),
        )
    )

    val dummySimpleFormResponse = mapOf(
        "form1" to exampleForm
    )

    val dummyMultipleFormResponse = mapOf(
        "form1" to exampleForm,
        "form2" to exampleForm,
    )
}
