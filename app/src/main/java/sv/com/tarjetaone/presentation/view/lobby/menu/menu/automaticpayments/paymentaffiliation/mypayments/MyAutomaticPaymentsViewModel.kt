package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.mypayments

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.entities.request.automaticpayments.paymentaffiliation.contract.ContractAffiliationAction
import sv.com.tarjetaone.domain.entities.request.automaticpayments.paymentaffiliation.contract.DeleteContractRequestUI
import sv.com.tarjetaone.domain.entities.request.automaticpayments.paymentaffiliation.contractslist.RecurringChargeListRequestUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.contractslist.RecurringChargeContractUI
import sv.com.tarjetaone.domain.usecases.automaticpayments.paymentaffiliation.contracts.GetRecurringChargeContractsUseCase

@HiltViewModel
class MyAutomaticPaymentsViewModel @Inject constructor(
    private val getRecurringChargeContracts: GetRecurringChargeContractsUseCase
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(MyAutomaticPaymentsUiState())
    val uiState = _uiState.asStateFlow()

    private fun onStart() {
        if (uiState.value.contracts.isNotEmpty()) return
        viewModelScope.launch {
            sendEvent(UiEvent.Loading(true))
            getRecurringChargeContracts(
                RecurringChargeListRequestUI(baseSharedPrefs.dui())
            ).executeUseCase(
                onApiErrorAction = { _, error, _, _ ->
                    sendEvent(UiEvent.Loading(false))
                    if (error?.code?.toIntOrNull() != EMPTY_CONTRACTS) {
                        showUpsErrorMessage()
                    }
                }
            ) { contracts ->
                sendEvent(UiEvent.Loading(false))
                _uiState.update { it.copy(contracts = contracts) }
            }
        }
    }

    private fun onViewContractClick(contract: RecurringChargeContractUI) {
        _uiState.update { it.copy(currentContract = contract) }
    }

    private fun onCancelContract(contract: RecurringChargeContractUI) {
        _uiState.update { it.copy(contractToCancel = contract) }
    }

    private fun onAcceptCancelContract() {
        _uiState.value.contractToCancel?.let { contract ->
            // Reset value to avoid reopen dialog when user enter screen from back
            _uiState.update { it.copy(contractToCancel = null) }
            sendEvent(
                UiEvent.Navigate(
                    MyAutomaticPaymentsFragmentDirections
                        .actionMyAutomaticPaymentsFragmentsToPaymentAffPrepareForPictureFragment(
                            contractAction = ContractAffiliationAction.Cancel(
                                contract = contract,
                                request = DeleteContractRequestUI(contractNumber = contract.contractNumber)
                            )
                        )
                )
            )
        }
    }

    private fun onDismissContractDetails() {
        _uiState.update { it.copy(currentContract = null) }
    }

    private fun onDismissCancelContract() {
        _uiState.update { it.copy(contractToCancel = null) }
    }

    private fun onAddContractClick() {
        sendEvent(
            UiEvent.Navigate(
                MyAutomaticPaymentsFragmentDirections
                    .actionMyAutomaticPaymentsFragmentsToServiceCatalogFragment(
                        isAutomaticPayment = true
                    )
            )
        )
    }

    private fun onEditRecurringCharge(contract: RecurringChargeContractUI) {
        onDismissContractDetails()
        sendEvent(
            UiEvent.Navigate(
                MyAutomaticPaymentsFragmentDirections
                    .navigateToUpdateAffiliationFragment(
                        contract = contract
                    )
            )
        )
    }

    private fun onViewPaymentHistory(contract: RecurringChargeContractUI) {
        onDismissContractDetails()
        sendEvent(
            UiEvent.Navigate(
                MyAutomaticPaymentsFragmentDirections
                    .navigateToPaymentHistoryFragment(
                        serviceName = contract.serviceName,
                        clientCode = contract.clientCode,
                        contractNumber = contract.contractNumber
                    )
            )
        )
    }

    fun onEvent(event: MyAutomaticPaymentsUiEvent) {
        when (event) {
            MyAutomaticPaymentsUiEvent.OnStart -> onStart()
            MyAutomaticPaymentsUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            MyAutomaticPaymentsUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            is MyAutomaticPaymentsUiEvent.OnEditRecurringCharge -> onEditRecurringCharge(event.contract)
            is MyAutomaticPaymentsUiEvent.OnViewPaymentHistory -> onViewPaymentHistory(event.contract)
            is MyAutomaticPaymentsUiEvent.OnViewContractClick -> onViewContractClick(event.contract)
            MyAutomaticPaymentsUiEvent.OnDismissContractDetails -> onDismissContractDetails()
            is MyAutomaticPaymentsUiEvent.OnCancelContractClick -> onCancelContract(event.contract)
            MyAutomaticPaymentsUiEvent.OnAcceptCancelContractClick -> onAcceptCancelContract()
            MyAutomaticPaymentsUiEvent.OnDismissCancelContractClick -> onDismissCancelContract()
            MyAutomaticPaymentsUiEvent.OnAddContractClick -> onAddContractClick()
        }
    }

    companion object {
        const val EMPTY_CONTRACTS = 5
    }
}
