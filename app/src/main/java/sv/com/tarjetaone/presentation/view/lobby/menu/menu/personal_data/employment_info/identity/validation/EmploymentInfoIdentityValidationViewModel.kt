package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.employment_info.identity.validation

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.usecases.auth.FaceAuthenticationUseCase
import sv.com.tarjetaone.presentation.view.common.identity_validation.IdentityValidationBaseViewModel
import javax.inject.Inject

@HiltViewModel
class EmploymentInfoIdentityValidationViewModel @Inject constructor(
    faceAuthenticationUseCase: FaceAuthenticationUseCase,
    sharedPreferences: SecureSharedPreferencesRepository,
    savedStateHandle: SavedStateHandle
) : IdentityValidationBaseViewModel(faceAuthenticationUseCase, sharedPreferences) {
    private val args = EmploymentInfoIdentityValidationFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override val onBlockedAction = {
        sendEvent(UiEvent.NavigateBackTo(R.id.navigation_home))
    }

    override fun operation(biometryId: Int) {
        sendEvent(
            UiEvent.Navigate(
                EmploymentInfoIdentityValidationFragmentDirections
                    .actionEmploymentInfoIdentityValidationToEmploymentInfoFormOverviewFragment(
                        jobId = args.jobId.orZero(),
                        contactId = args.contactId.orZero(),
                        contactTypeCode = args.contactTypeCode.orEmpty(),
                        biometryId = biometryId
                    )
            )
        )
    }

    override fun onFailedIdentityValidation(resultCode: String?) {
        sendEvent(UiEvent.NavigateBackTo(R.id.prepareForPictureEmploymentInfoFragment))
    }
}
