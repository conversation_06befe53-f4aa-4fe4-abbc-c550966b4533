package sv.com.tarjetaone.presentation.view.common.reference_form.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.platform.SoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.onEach
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.SEARCH_QUERY_DEBOUNCE_TIMER
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.button.SolidLargeButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.PhoneTextField
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedDropdown
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.compose.util.FieldDecorationType
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.helpers.hasInvalidPhoneNumber
import sv.com.tarjetaone.presentation.view.common.reference_form.ReferenceFormEvent
import sv.com.tarjetaone.presentation.view.common.reference_form.ReferenceFormUiState
import sv.com.tarjetaone.presentation.view.common.reference_form.getPhoneErrorMessage
import sv.com.tarjetaone.presentation.view.common.reference_form.isContinueButtonEnabled

@OptIn(FlowPreview::class)
@Composable
fun ReferenceFormContent(
    uiState: ReferenceFormUiState,
    onEvent: (ReferenceFormEvent) -> Unit
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    LaunchedEffect(uiState.referencePhoneNumber) {
        snapshotFlow { uiState.referencePhoneNumber }
            .debounce(SEARCH_QUERY_DEBOUNCE_TIMER)
            .filter { it.hasInvalidPhoneNumber().not() }
            .onEach {
                onEvent(ReferenceFormEvent.OnValidatePhoneNumber(it))
            }
            .collect()
    }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(
                horizontal = MaterialTheme.customDimens.dimen32,
            ),
    ) {
        ReferenceFormHeader()
        Spacer16()
        ReferenceFormBody(
            uiState = uiState,
            keyboardController = keyboardController,
            onEvent = onEvent
        )
        Spacer1f()
        SolidLargeButton(
            modifier = Modifier.padding(vertical = MaterialTheme.customDimens.dimen32),
            enabled = uiState.isContinueButtonEnabled(),
            text = stringResource(id = R.string.continue_button_label)
        ) {
            onEvent(ReferenceFormEvent.OnContinueButtonClicked)
        }
    }
}

@Composable
fun ReferenceFormHeader() {
    Column(
        modifier = Modifier
            .padding(top = MaterialTheme.customDimens.dimen16)
            .fillMaxWidth()
    ) {
        Text(
            text = stringResource(id = R.string.this_reference_help_us_label),
            style = MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.customColors.bodyLightGray
            ),
        )
    }
}

@Composable
fun ReferenceFormBody(
    uiState: ReferenceFormUiState,
    keyboardController: SoftwareKeyboardController? = null,
    onEvent: (ReferenceFormEvent) -> Unit
) {
    Column {
        Text(
            text = stringResource(id = uiState.referenceTypeLabel),
            style = MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.customColors.bodyLightGray,
                fontWeight = FontWeight.SemiBold
            ),
        )
        SimpleElevatedDropdown(
            items = uiState.referencesList,
            itemLabel = { item -> item.name.orEmpty() },
            value = uiState.referenceValueSelected,
            onValueChange = { value -> onEvent(ReferenceFormEvent.OnReferenceValueChanged(value)) },
            enabled = uiState.isDropdownEnabled,
            decorationType = FieldDecorationType.OUTLINED
        )
        Spacer16()
        SimpleElevatedTextField(
            label = stringResource(id = R.string.name_and_last_name_label),
            labelStyle = MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.customColors.bodyLightGray,
                fontWeight = FontWeight.SemiBold
            ),
            value = uiState.referenceName,
            onValueChange = { name -> onEvent(ReferenceFormEvent.OnNameChanged(name)) },
            textStyle = MaterialTheme.typography.bodyLarge.copy(
                color = MaterialTheme.customColors.baseText,
                lineHeight = MaterialTheme.customDimensSp.sp18
            ),
            decorationType = FieldDecorationType.OUTLINED,
            hasError = uiState.referenceNameError,
            error = stringResource(id = R.string.field_is_mandatory),
            keyboardActions = KeyboardActions(
                onDone = { keyboardController?.hide() }
            ),
        )
        Spacer16()
        PhoneTextField(
            label = stringResource(id = R.string.phone_number_label),
            placeholder = stringResource(id = R.string.phone_hint),
            labelStyle = MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.customColors.bodyLightGray,
                fontWeight = FontWeight.SemiBold
            ),
            value = uiState.referencePhoneNumber,
            onValueChange = { phoneNumber ->
                onEvent(ReferenceFormEvent.OnPhoneNumberChanged(phoneNumber))
            },
            decorationType = FieldDecorationType.OUTLINED,
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Phone,
                imeAction = ImeAction.Done
            ),
            keyboardActions = KeyboardActions(
                onDone = { keyboardController?.hide() }
            ),
            hasError = uiState.referencePhoneNumberError && !uiState.isValidatingPhone &&
                    uiState.referencePhoneNumber.isNotEmpty(),
            error = uiState.getPhoneErrorMessage().asString(),
            trailingIcon = if (uiState.isValidatingPhone) {
                {
                    CircularProgressIndicator(
                        modifier = Modifier.size(MaterialTheme.customDimens.dimen12),
                        strokeWidth = MaterialTheme.customDimens.dimen2
                    )
                }
            } else null
        )
    }
}

@Preview(
    showBackground = true,
    showSystemUi = true
)
@Composable
fun ReferenceFormScreenPreview() {
    OneAppTheme {
        ReferenceFormContent(
            uiState = ReferenceFormUiState(),
            onEvent = { }
        )
    }
}
