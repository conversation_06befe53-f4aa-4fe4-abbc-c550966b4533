package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.pay_with_points.success

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.OneBackHandler
import sv.com.tarjetaone.presentation.view.common.confirmation_gradient.SuccessGradientScreen

@Composable
fun PayWithPointsSuccessScreen(viewModel: PayWithPointsSuccessViewModel) {
    OneBackHandler()
    SuccessGradientScreen(
        message = stringResource(id = R.string.pay_with_points_success_msg),
        buttonText = stringResource(id = R.string.understood),
        onButtonClick = { viewModel.onEvent(PayWithPointsSuccessUiEvent.OnContinueClick) }
    )
}
