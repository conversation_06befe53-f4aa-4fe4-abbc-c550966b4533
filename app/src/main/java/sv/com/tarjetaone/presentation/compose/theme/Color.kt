package sv.com.tarjetaone.presentation.compose.theme

import androidx.compose.ui.graphics.Color

/**
 * Design System colors according to Figma:
 * @see https://www.figma.com/file/CDyZxGeX03JM7WTtnaNGGt/Banco-Atlantida---Atlantis---Design-system-v1.0-(Copy)?type=design&node-id=4040%3A36957&mode=dev
 */
val md_theme_light_primary = Color(0xFFFF2A4A)
val md_theme_light_onPrimary = Color(0xFFFFFFFF)
val md_theme_light_primaryContainer = Color(0xFFBF002E)
val md_theme_light_onPrimaryContainer = Color(0xFFFFFFFF)
val md_theme_light_secondary = Color(0xFF1F1E36)
val md_theme_light_onSecondary = Color(0xFFFFFFFF)
val md_theme_light_secondaryContainer = Color(0xFFFFFFFF)
val md_theme_light_onSecondaryContainer = Color(0xFFA0AEC0)
val md_theme_light_tertiary = Color(0xFFE4ECF7)
val md_theme_light_onTertiary = Color(0xFF1F1E36)
val md_theme_light_tertiaryContainer = Color(0xFFCDE5FF)
val md_theme_light_onTertiaryContainer = Color(0xFF001D32)
val md_theme_light_error = Color(0xFFFF3D00)
val md_theme_light_errorContainer = Color(0xFFFF3D00)
val md_theme_light_onError = Color(0xFFFFFFFF)
val md_theme_light_onErrorContainer = Color(0xFFFF3D00)
val md_theme_light_background = Color(0xFFFFFFFF)
val md_theme_light_onBackground = Color(0xFF79789D)
val md_theme_light_surface = Color(0xFFF7FAFC)
val md_theme_light_onSurface = Color(0xFF1F1E36)
val md_theme_light_surfaceVariant = Color(0xFFF7FAFC)
val md_theme_light_onSurfaceVariant = Color(0xFF000000)
val md_theme_light_outline = Color(0xFFFF2A4A)
val md_theme_light_inverseSurface = Color(0xFF1F1E36)
val md_theme_light_inverseOnSurface = Color(0xFFFFFFFF)
val md_theme_light_inversePrimary = Color(0xFFFFB3B3)
val md_theme_light_shadow = Color(0xFF000000)
val md_theme_light_surfaceTint = Color(0xFFBF002E)
val md_theme_light_outlineVariant = Color(0xFFF7FAFC)
val md_theme_light_scrim = Color(0xFF000000)
val seed = Color(0xFFFF2A4A)
val md_theme_light_selected = Color(0xFFFFA29B)
val md_theme_light_headerText = Color(0xFF63656A)

// Custom colors
val md_theme_light_successContainer = Color(0xFF51DEA3)
val md_theme_light_successLightContainer = Color(0xFF4BCA95)
val md_theme_light_onSuccess = Color(0xFFFFFFFF)
val md_theme_light_alertContainer = Color(0xFFFFE600)
val md_theme_light_alertDark = Color(0xFFF3DE22)
val md_theme_light_alertVariant = Color(0xFFFFC700)
val md_theme_light_onAlert = Color(0xFF1F1E36)
val md_theme_light_infoContainer = Color(0xFF8845FB)
val md_theme_light_cardBackground = Color(0xFFC8B3EA)
val md_theme_light_onInfo = Color(0xFFFFFFFF)
val md_theme_light_onHeadingTextDark = Color(0xFF27272E)
val md_theme_light_secondary_soft = Color(0xFFE1E8FF)
val md_theme_light_disabled_gray_placeholder = Color(0xFFB5B5BD)
val md_theme_light_disabled_gray = Color(0xFFC9CED6)
val md_theme_light_gray_text_surface_variant = Color(0xFFF7F8FA)
val md_theme_light_body_light_gray = Color(0xFF425466)
val md_theme_light_secondary_light = Color(0xFF79789D)
val md_theme_light_secondary_dark = Color(0xFF4C4B75)
val md_theme_light_disclaimer = Color(0xFF9681B9)
val md_theme_light_base_text = Color(0xFF494949)
val md_theme_text_body_light = Color(0xFF425466)
val md_theme_light_tertiary_dark = Color(0xFFA6B7D4)
val md_theme_light_heading_dark = Color(0xFF27272E)
val md_theme_light_progress = Color(0xFFFF7F92)
val md_theme_light_soft_violet_gray = Color(0xFFBFBED6)
val md_theme_light_arctic_veil = Color(0xFFF4FFFF)

// Primary gradient colors
val md_theme_light_primary_gradient_1 = Color(0xFFFF642D)
val md_theme_light_primary_gradient_2 = Color(0xFFFF2A4A)
val md_theme_light_primary_gradient_3 = Color(0xFFCA3387)
val md_theme_light_primary_gradient_4 = Color(0xFF6645FB)

// Not in design system
val md_theme_card_red = Color(0xFFD9272E)
val md_theme_polygon_red = Color(0x38FD2A4D)
val md_theme_divider = Color(0xFFB1B9BC)
val md_theme_light_blue_background = Color(0xFFF1F8FF)

// Gray colors
val md_theme_gray_5 = Color(0xFF30363C)
val md_theme_gray_50 = Color(0xFFFAFAFA)
val md_theme_gray_100 = Color(0xFFF7FAFC)
val md_theme_gray_200 = Color(0xFFEDF2F7)
val md_theme_gray_300 = Color(0xFFE2E8F0)
val md_theme_gray_400 = Color(0xFFCBD5E0)
val md_theme_gray_500 = Color(0xFFA0AEC0)
val md_theme_gray_600 = Color(0xFF718096)
val md_theme_gray_700 = Color(0xFF4A5568)
val md_theme_gray_800 = Color(0xFF2D3748)
val md_theme_gray_900 = Color(0xFF1A202C)
val md_theme_steel_gray = Color(0xFF7A828A)

val md_theme_light_gray = Color(0xFFF8F7FA)
val md_theme_white_background_progress = Color(0xFFE7EBEF)
val md_theme_soft_primary = Color(0xFFFFEEF1)

// Shimmer colors
val md_skeleton_start_color = Color(0xFFEDF2F7)
val md_skeleton_middle_color = Color(0xFFEDF6FA)
val md_skeleton_end_color = Color(0xFFEDF2F7)

// Banner colors
val md_theme_banner_text = Color(0xFF2F363D)
val md_theme_tracking_banner = Color(0xFFFFEEF1)
val md_theme_alert_banner = Color(0XFFFFC700)
val md_theme_alert_banner_soft = Color(0xFFFFF7B1)
