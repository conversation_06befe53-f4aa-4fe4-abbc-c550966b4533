package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.report.success

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.extensions.takeIfNotBlank
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.OneBackHandler
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.view.common.confirmation_gradient.SuccessGradientScreen

@Composable
fun ReportPurchaseSuccessScreen(viewModel: ReportPurchaseSuccessViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LaunchedEffect(Unit) {
        viewModel.onEvent(ReportPurchaseSuccessUiEvent.OnStart)
    }
    OneBackHandler()
    SuccessGradientScreen(
        title = stringResource(id = R.string.done_label),
        buttonText = stringResource(id = R.string.lock_card_confirmation_button_text),
        buttonVariant = ButtonVariant.SECONDARY_VARIANT,
        message = buildAnnotatedString {
            withStyle(SpanStyle(fontWeight = FontWeight.SemiBold)) {
                append(stringResource(id = R.string.success_report_subtitle))
            }
            append(
                stringResource(
                    id = R.string.success_report_subtitle_detail,
                    uiState.managementId,
                    uiState.cardBlockId.takeIfNotBlank() ?: stringResource(R.string.not_applicable)
                )
            )
        },
        messageStyle = MaterialTheme.typography.bodyMedium,
        onButtonClick = { viewModel.onEvent(ReportPurchaseSuccessUiEvent.OnSelectCardADeliveryClick) },
        showCloseButton = true,
        onCloseClick = { viewModel.onEvent(ReportPurchaseSuccessUiEvent.OnCloseClick) }
    )
}
