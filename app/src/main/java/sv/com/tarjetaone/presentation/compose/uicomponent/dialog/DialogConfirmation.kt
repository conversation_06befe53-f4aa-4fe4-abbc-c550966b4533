package sv.com.tarjetaone.presentation.compose.uicomponent.dialog

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.helpers.UiText

@Suppress("kotlin:S107")
@Composable
fun DialogConfirmation(
    title: UiText? = null,
    titleStyle: TextStyle = MaterialTheme.typography.headlineSmall.copy(
        color = MaterialTheme.colorScheme.secondary,
    ),
    message: UiText,
    primaryButtonText: UiText,
    secondaryButtonText: UiText,
    messageStyle: TextStyle = MaterialTheme.typography.labelSmall,
    messagePadding: PaddingValues = PaddingValues(bottom = MaterialTheme.customDimens.dimen32),
    onPositiveClick: () -> Unit,
    onNegativeClick: () -> Unit,
    positiveButtonVariant: ButtonVariant = ButtonVariant.PRIMARY_VARIANT,
    negativeButtonVariant: ButtonVariant = ButtonVariant.TERTIARY_VARIANT,
    onDismissRequest: (() -> Unit)? = null,
    properties: DialogProperties = DialogProperties()
) {
    Dialog(
        onDismissRequest = onDismissRequest ?: onNegativeClick,
        properties = properties
    ) {
        Card(
            colors = CardDefaults.cardColors().copy(
                containerColor = MaterialTheme.colorScheme.background,
            ),
            modifier = Modifier
                .heightIn(min = MaterialTheme.customDimens.dimen189)
                .width(MaterialTheme.customDimens.dimen252)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        vertical = MaterialTheme.customDimens.dimen16,
                        horizontal = MaterialTheme.customDimens.dimen24
                    ),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                title?.let {
                    Spacer8()
                    Text(
                        text = title.asString(),
                        style = titleStyle,
                        color = MaterialTheme.colorScheme.onSurface,
                        textAlign = TextAlign.Center
                    )
                }
                Spacer8()
                Text(
                    modifier = Modifier.padding(paddingValues = messagePadding),
                    text = message.asString(),
                    style = messageStyle,
                    color = MaterialTheme.colorScheme.onSurface,
                    textAlign = TextAlign.Center
                )
                ConfirmationDialogButton(
                    text = primaryButtonText.asString(),
                    onClick = onPositiveClick,
                    buttonVariant = positiveButtonVariant
                )
                Spacer8()
                ConfirmationDialogButton(
                    text = secondaryButtonText.asString(),
                    onClick = onNegativeClick,
                    buttonVariant = negativeButtonVariant
                )
            }
        }
    }
}

@Preview
@Composable
fun DialogConfirmationPreview() {
    OneAppTheme {
        DialogConfirmation(
            message = UiText.StringResource(R.string.sure_delete),
            primaryButtonText = UiText.StringResource(R.string.delete_label),
            secondaryButtonText = UiText.StringResource(R.string.cancel),
            onPositiveClick = {},
            onNegativeClick = {}
        )
    }
}

@Preview
@Composable
fun DialogConfirmationWithTitlePreview() {
    OneAppTheme {
        DialogConfirmation(
            title = UiText.StringResource(R.string.ups_card_activation),
            message = UiText.StringResource(R.string.would_you_like_assistance),
            primaryButtonText = UiText.StringResource(R.string.facephi_error_dialog_by_phone),
            secondaryButtonText = UiText.StringResource(R.string.try_again_label),
            positiveButtonVariant = ButtonVariant.TERTIARY_VARIANT,
            negativeButtonVariant = ButtonVariant.PRIMARY_VARIANT,
            onPositiveClick = {},
            onNegativeClick = {}
        )
    }
}
