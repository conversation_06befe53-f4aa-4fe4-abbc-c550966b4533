package sv.com.tarjetaone.presentation.view.common.locationconfirmation

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.google.android.gms.maps.model.CameraPosition
import com.google.android.gms.maps.model.LatLng
import com.google.maps.android.compose.GoogleMap
import com.google.maps.android.compose.MapUiSettings
import com.google.maps.android.compose.Marker
import com.google.maps.android.compose.rememberCameraPositionState
import com.google.maps.android.compose.rememberMarkerState
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.SV_DEFAULT_LOCATION_LAT
import sv.com.tarjetaone.common.utils.AppConstants.SV_DEFAULT_LOCATION_LNG
import sv.com.tarjetaone.core.utils.vectorToBitmap
import sv.com.tarjetaone.domain.entities.request.UserLocationUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.helpers.capitalizeAllWords

@Composable
fun LocationConfirmationScreen(viewModel: LocationConfirmationBaseViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    LaunchedEffect(Unit) {
        viewModel.onEvent(LocationConfirmationUiEvent.OnStart)
    }
    LocationConfirmationContent(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@Composable
fun LocationConfirmationContent(
    uiState: LocationConfirmationUiState,
    onEvent: (LocationConfirmationUiEvent) -> Unit
) {
    ScreenWithTopAppBar(
        onLeftButtonClick = { onEvent(LocationConfirmationUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(LocationConfirmationUiEvent.OnTwilioClick) },
        title = stringResource(id = R.string.address_location),
        isProgressbarVisible = uiState.showProgress,
        progress = 0.5f
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.customDimens.dimen50)
            ) {
                Text(
                    text = uiState.userLocation?.address?.capitalizeAllWords().orEmpty(),
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.secondary
                )
                Text(
                    text = stringResource(id = R.string.address_label),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.secondary
                )
                Spacer32()
                Text(
                    text = uiState.userLocation?.department?.capitalizeAllWords().orEmpty(),
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.secondary
                )
                Text(
                    text = stringResource(id = R.string.state_label),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.secondary
                )
                Spacer32()
                Text(
                    text = uiState.userLocation?.municipio?.capitalizeAllWords().orEmpty(),
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.secondary
                )
                Text(
                    text = stringResource(id = R.string.municipality_label),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.secondary
                )
                Spacer16()

                val userLat = uiState.userLocation?.latitude?.toDoubleOrNull()
                val userLng = uiState.userLocation?.longitude?.toDoubleOrNull()
                val userLatLng = if (userLat != null && userLng != null) {
                    LatLng(userLat, userLng)
                } else {
                    LatLng(SV_DEFAULT_LOCATION_LAT, SV_DEFAULT_LOCATION_LNG)
                }
                val cameraPositionState = rememberCameraPositionState {
                    position = CameraPosition.fromLatLngZoom(userLatLng, MINIMAP_ZOOM)
                }
                GoogleMap(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(MaterialTheme.customDimens.dimen200)
                        .clip(MaterialTheme.shapes.medium)
                        .background(MaterialTheme.customColors.gray500),
                    cameraPositionState = cameraPositionState,
                    uiSettings = MapUiSettings(
                        compassEnabled = false,
                        mapToolbarEnabled = false,
                        myLocationButtonEnabled = false,
                        rotationGesturesEnabled = false,
                        scrollGesturesEnabled = false,
                        zoomControlsEnabled = false,
                        zoomGesturesEnabled = false,
                        tiltGesturesEnabled = false
                    )
                ) {
                    Marker(
                        state = rememberMarkerState(position = userLatLng),
                        draggable = false,
                        icon = LocalContext.current.vectorToBitmap(R.drawable.ic_location)
                    )
                }
                Spacer(length = MaterialTheme.customDimens.dimen92)
            }
            Text(
                text = stringResource(id = R.string.address_matches_pin),
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.secondary,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(horizontal = MaterialTheme.customDimens.dimen16)
            )
            Spacer16()
            OneButton(
                text = stringResource(id = R.string.yes_continue_button_label),
                onClick = { onEvent(LocationConfirmationUiEvent.OnContinueClick) },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.customDimens.dimen50)
            )
            Spacer8()
            OneButton(
                text = stringResource(
                    id = R.string.no_change_address.takeIf {
                        uiState.isModifyingAddress
                    } ?: R.string.no_change_pin
                ),
                onClick = { onEvent(LocationConfirmationUiEvent.OnChangeAddressClick(uiState.isModifyingAddress)) },
                buttonVariant = ButtonVariant.SECONDARY_VARIANT,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.customDimens.dimen50)
            )
            Spacer32()
        }
    }
}

private const val MINIMAP_ZOOM = 15f

@Preview
@Composable
private fun LocationConfirmationScreenPreview() {
    OneAppTheme {
        LocationConfirmationContent(
            LocationConfirmationUiState(
                userLocation = UserLocationUI(
                    address = "Calle El Mirador, Colonia Escalon",
                    department = "La Libertad",
                    municipio = "Santa Tecla",
                    residential = "Calle El Mirador, Colonia Escalon",
                    latitude = "13.69438175464387",
                    longitude = "-89.23072161223457"
                ),
                isModifyingAddress = false,
                showProgress = true
            ),
            onEvent = {}
        )
    }
}
