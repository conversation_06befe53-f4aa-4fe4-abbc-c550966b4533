package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.cardpay.productselection

import sv.com.tarjetaone.domain.entities.response.CardPaymentMethodsDataUI

sealed class ProductPaymentUiEvent {
    data object OnStart : ProductPaymentUiEvent()
    data object OnBackClick : ProductPaymentUiEvent()
    data object OnTwilioClick : ProductPaymentUiEvent()
    data object OnContinueClick : ProductPaymentUiEvent()
    data class OnSelectProduct(val product: CardPaymentMethodsDataUI) : ProductPaymentUiEvent()
}
