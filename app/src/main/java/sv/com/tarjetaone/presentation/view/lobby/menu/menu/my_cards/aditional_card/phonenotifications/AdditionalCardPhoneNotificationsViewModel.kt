package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.phonenotifications

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.AppConstants.SEND_OTP_ERROR_CODES
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.CommonDialogWithIconParams
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.response.OtpMethod
import sv.com.tarjetaone.domain.entities.response.OtpType
import sv.com.tarjetaone.domain.usecases.requestcard.otpvalidation.SendOtpCodeUseCase
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.locationreceive.LocationReceiveCardAction
import javax.inject.Inject

@HiltViewModel
class AdditionalCardPhoneNotificationsViewModel @Inject constructor(
    private val sendOtpCodeUseCase: SendOtpCodeUseCase,
    private val sharedPrefsRepo: SecureSharedPreferencesRepository
) : BaseViewModel() {

    fun onEvent(event: AdditionalCardPhoneNotificationsUiEvent) {
        when (event) {
            is AdditionalCardPhoneNotificationsUiEvent.OnSelectOtpMethod -> sendOtp(event.method)
            AdditionalCardPhoneNotificationsUiEvent.OnDenyNotificationsButtonClick -> onDenyNotifications()
            AdditionalCardPhoneNotificationsUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            AdditionalCardPhoneNotificationsUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
        }
    }

    private fun sendOtp(method: OtpMethod) {
        val phoneNumber =
            sharedPrefsRepo.userAdditionalCardData?.customer?.phoneNumber.orEmpty()
        sendEvent(UiEvent.Loading(true))
        viewModelScope.launch {
            sendOtpCodeUseCase(OtpType.PHONE, phoneNumber, method).executeUseCase(
                onSuccessAction = {
                    sendEvent(UiEvent.Loading(false))
                    if (SEND_OTP_ERROR_CODES.contains(it.code)) {
                        sendEvent(
                            UiEvent.ShowCommonDialog(
                                CommonDialogWithIconParams(
                                    showIcon = false,
                                    showTitle = false
                                )
                            )
                        )
                    } else {
                        setNotificationsAnswer(true)
                        sendEvent(
                            UiEvent.Navigate(
                                AdditionalCardPhoneNotificationsFragmentDirections
                                    .navigateToOtpValidationAdditionalCardFragment(
                                        phoneNumber = phoneNumber,
                                        sharedKey = it.sharedKey.orEmpty(),
                                    )
                            )
                        )
                    }
                },
                onApiErrorAction = { _, _, _, _ ->
                    sendEvent(UiEvent.Loading(false))
                    showUpsErrorMessage()
                }
            )
        }
    }

    private fun onDenyNotifications() {
        setNotificationsAnswer(false)
        sendEvent(
            UiEvent.Navigate(
                AdditionalCardPhoneNotificationsFragmentDirections
                    .actionAdditionalCardPhoneNotificationsToLocationToReceiveRequestCardFragment(
                        cardAction = LocationReceiveCardAction.AdditionalCard
                    )
            )
        )
    }

    private fun setNotificationsAnswer(value: Boolean) {
        sharedPrefsRepo.userAdditionalCardData?.let { userAdditionalCard ->
            sharedPrefsRepo.userAdditionalCardData = userAdditionalCard.copy(
                customer = userAdditionalCard.customer.copy(
                    isNotificationTran = value
                )
            )
        }
    }
}