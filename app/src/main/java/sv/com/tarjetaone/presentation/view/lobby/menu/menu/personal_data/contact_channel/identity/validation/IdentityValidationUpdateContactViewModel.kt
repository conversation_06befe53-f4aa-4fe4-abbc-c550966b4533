package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.contact_channel.identity.validation

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.usecases.auth.FaceAuthenticationUseCase
import sv.com.tarjetaone.presentation.view.common.identity_validation.IdentityValidationBaseViewModel
import javax.inject.Inject

@HiltViewModel
class IdentityValidationUpdateContactViewModel @Inject constructor(
    faceAuthenticationUseCase: FaceAuthenticationUseCase,
    sharedPrefs: SecureSharedPreferencesRepository,
    savedStateHandle: SavedStateHandle
) : IdentityValidationBaseViewModel(faceAuthenticationUseCase, sharedPrefs) {
    private val args =
        IdentityValidationUpdateContactFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override val onBlockedAction = {
        sendEvent(UiEvent.NavigateBackTo(R.id.navigation_home))
    }

    override fun operation(biometryId: Int) {
        sendEvent(
            UiEvent.Navigate(
                IdentityValidationUpdateContactFragmentDirections
                    .actionIdentityValidationUpdateContactFragmentToUpdateContactFragment(
                        contactType = args.contactType,
                        contactId = args.contactId.orZero(),
                        biometryId = biometryId
                    )
            )
        )
    }

    override fun onFailedIdentityValidation(resultCode: String?) {
        sendEvent(UiEvent.NavigateBackTo(R.id.prepareForPictureUpdateContactFragment))
    }
}
