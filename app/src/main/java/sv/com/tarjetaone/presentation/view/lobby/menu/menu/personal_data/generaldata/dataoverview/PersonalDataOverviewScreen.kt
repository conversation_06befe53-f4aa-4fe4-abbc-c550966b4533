package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.generaldata.dataoverview

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.extensions.setGender
import sv.com.tarjetaone.domain.entities.response.CustomerDataUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.HyperLinkTextButton
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.Spacer32

@Composable
fun PersonalDataOverviewScreen(viewModel: PersonalDataOverviewViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    PersonalDataOverviewContent(
        uiState = uiState,
        uiEvent = viewModel::onScreenUiEvent
    )
}

@Composable
fun PersonalDataOverviewContent(
    uiState: PersonalDataOverviewUiState,
    uiEvent: (PersonalDataOverviewUiEvent) -> Unit
) {
    LaunchedEffect(Unit) {
        uiEvent(PersonalDataOverviewUiEvent.OnStart)
    }
    ScreenWithTopAppBar(
        title = stringResource(R.string.general_data),
        onLeftButtonClick = { uiEvent(PersonalDataOverviewUiEvent.OnBackClick) },
        onRightButtonClick = { uiEvent(PersonalDataOverviewUiEvent.OnSupportClick) },
        contentPadding = PaddingValues(
            start = MaterialTheme.customDimens.dimen32,
            end = MaterialTheme.customDimens.dimen32,
            top = MaterialTheme.customDimens.dimen32,
        )
    ) {
        Column(
            Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(horizontal = MaterialTheme.customDimens.dimen32),
        ) {
            PersonalDataOverviewSharedContent(uiState.customerData)
            HyperLinkTextButton(
                text = stringResource(id = R.string.personal_overview_data_update_your_document),
                onClick = { uiEvent(PersonalDataOverviewUiEvent.OnUpdateYourDocumentClick) }
            )
            Spacer32()
        }
    }
}

@Preview(showSystemUi = true)
@Composable
fun PersonalDataOverviewPreview() {
    OneAppTheme {
        PersonalDataOverviewContent(
            uiState = PersonalDataOverviewUiState(
                customerData = CustomerDataUI(
                    customerId = 0,
                    fullName = "Juan Daniel Perez Smith",
                    genderCode = "F".setGender(),
                    dui = "0000000-0",
                    duiExpirationDate = "01 jun 2024",
                    birthDate = "01 julio 1990",
                    address = "Col. Escalón, Calle Francisco Gavidia Casa #4, San Salvador, San Salvador"
                )
            ),
            uiEvent = {}
        )
    }
}
