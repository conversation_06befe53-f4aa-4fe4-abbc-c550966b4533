package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.cashback.detail

sealed class MyCashbackUiEvent {
    data object OnBackClick : MyCashbackUiEvent()
    data object OnSupportClick : MyCashbackUiEvent()
    data object OnStart : MyCashbackUiEvent()
    data class OnTransactionsLoadError(val onRetryAction: () -> Unit) : MyCashbackUiEvent()
    data class OnHowToUseClick(val visibility: Boolean): MyCashbackUiEvent()
    data object OnEBankingClick : MyCashbackUiEvent()
}
