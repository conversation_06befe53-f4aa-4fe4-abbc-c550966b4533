package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.employment_info

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING

data class EmploymentInfoOverviewUiState(
    val employmentInfoList: SnapshotStateList<EmploymentInfo> = mutableStateListOf()
)

data class EmploymentInfo(
    val id: Int = 0,
    val contactId: Int = 0,
    val contactTypeCode: String = EMPTY_STRING,
    val incomeType: String = EMPTY_STRING,
    val fixedIncome: String = EMPTY_STRING,
    val variableIncome: String = EMPTY_STRING,
    val companyName: String = EMPTY_STRING,
    val sinceDate: String = EMPTY_STRING,
    val hiringType: String = EMPTY_STRING,
    val occupation: String = EMPTY_STRING,
    val address: String = EMPTY_STRING,
    val phoneNumber: String = EMPTY_STRING,
)

enum class IncomeType(val type: String) {
    SALARIED("Asalariado")
}
