package sv.com.tarjetaone.presentation.view.common.confirmation_gradient

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.presentation.compose.modifier.diagonalGradientBackground
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.SetStatusBarAppearance
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.OneBackHandler
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Corner
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer32

@Suppress("kotlin:S107")
@Composable
fun SuccessGradientScreen(
    title: String? = null,
    message: String? = null,
    messageStyle: TextStyle = MaterialTheme.typography.bodyLarge.copy(
        fontWeight = FontWeight.SemiBold
    ),
    buttonText: String = stringResource(id = R.string.continue_button_label),
    onButtonClick: () -> Unit
) {
    OneBackHandler()
    SetStatusBarAppearance()
    Column(
        modifier = Modifier
            .fillMaxSize()
            .diagonalGradientBackground(
                colors = MaterialTheme.customColors.primaryGradient,
                startingPoint = Corner.TOP_RIGHT
            )
            .safeDrawingPadding()
    ) {
        Column(
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.customDimens.dimen24)
                .weight(ONE_FLOAT_VALUE)
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_check_green),
                contentDescription = null
            )
            title?.let {
                Spacer16()
                Text(
                    text = it,
                    style = MaterialTheme.typography.displayMedium,
                    color = MaterialTheme.colorScheme.onPrimary
                )
            }
            message?.let {
                if (title == null) Spacer32() else Spacer16()
                Text(
                    text = it,
                    style = messageStyle,
                    color = MaterialTheme.colorScheme.onPrimary,
                    textAlign = TextAlign.Center
                )
            }
        }
        OneButton(
            text = buttonText,
            onClick = onButtonClick,
            buttonVariant = ButtonVariant.INFO_VARIANT,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.customDimens.dimen24)
        )
        Spacer32()
    }
}

/**
 * Success screen with a gradient background.
 * @param message The message to display. Flexible to allow multiple span styles within the message.
 * @param messageStyle The global style to apply to the message.
 */
@Composable
fun SuccessGradientScreen(
    title: String? = null,
    message: AnnotatedString? = null,
    messageStyle: TextStyle = MaterialTheme.typography.bodyLarge.copy(
        fontWeight = FontWeight.SemiBold
    ),
    buttonText: String = stringResource(id = R.string.continue_button_label),
    buttonVariant: ButtonVariant = ButtonVariant.INFO_VARIANT,
    onButtonClick: () -> Unit,
    showCloseButton: Boolean = false,
    onCloseClick: () -> Unit = {}
) {
    OneBackHandler()
    SetStatusBarAppearance()
    Box {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .diagonalGradientBackground(
                    colors = MaterialTheme.customColors.primaryGradient,
                    startingPoint = Corner.TOP_RIGHT
                )
                .safeDrawingPadding()
        ) {
            Column(
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.customDimens.dimen40)
                    .weight(1f)
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_check_green),
                    contentDescription = null
                )
                title?.let {
                    Text(
                        text = it,
                        style = MaterialTheme.typography.displayMedium,
                        color = MaterialTheme.colorScheme.onPrimary,
                        modifier = Modifier.padding(vertical = MaterialTheme.customDimens.dimen16)
                    )
                } ?: Spacer32()
                message?.let {
                    Text(
                        text = it,
                        color = MaterialTheme.colorScheme.onPrimary,
                        textAlign = TextAlign.Center,
                        style = messageStyle
                    )
                }
            }
            OneButton(
                text = buttonText,
                onClick = onButtonClick,
                buttonVariant = buttonVariant,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.customDimens.dimen30)
            )
            Spacer32()
        }
        if (showCloseButton) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .padding(MaterialTheme.customDimens.dimen16)
                    .size(MaterialTheme.customDimens.dimen32)
                    .clip(CircleShape)
                    .clickable(onClick = onCloseClick)
                    .align(Alignment.TopStart)
            ) {
                Image(
                    modifier = Modifier.size(MaterialTheme.customDimens.dimen16),
                    painter = painterResource(R.drawable.ic_close_sign),
                    contentDescription = null
                )
            }
        }
    }
}

@Preview
@Composable
private fun SuccessGradientScreenPreview() {
    OneAppTheme {
        SuccessGradientScreen(
            title = stringResource(id = R.string.done_label),
            message = stringResource(id = R.string.claim_complete_message),
            buttonText = stringResource(id = R.string.continue_button_label),
            onButtonClick = {}
        )
    }
}

@Preview
@Composable
private fun SuccessGradientScreenPreview2() {
    OneAppTheme {
        SuccessGradientScreen(
            title = stringResource(id = R.string.done_label),
            buttonText = stringResource(id = R.string.lock_card_confirmation_button_text),
            buttonVariant = ButtonVariant.SECONDARY_VARIANT,
            onButtonClick = {},
            messageStyle = MaterialTheme.typography.bodyMedium,
            message = buildAnnotatedString {
                withStyle(SpanStyle(fontWeight = FontWeight.SemiBold)) {
                    append(stringResource(id = R.string.success_report_subtitle))
                }
                append(stringResource(id = R.string.success_report_subtitle_detail, "123456", 1234))
            },
            showCloseButton = true
        )
    }
}
