package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Slider
import androidx.compose.material3.SliderColors
import androidx.compose.material3.SliderDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors

/**
 * Reusable Slider component, it receives the min..max values to display the slider value range.
 * @param isActive will be used to tint the active/inactive color.
 */
@Suppress("kotlin:S107")
@Composable
fun SliderComponent(
    modifier: Modifier = Modifier,
    isActive: Boolean = true,
    sliderPosition: Float,
    minLimit: Float,
    maxLimit: Float,
    stepSize: Int = 1,
    onValueChange: (Float) -> Unit = {},
    onValueChangeFinished: () -> Unit = {}
) {

    val sliderColor = if (isActive) MaterialTheme.customColors.successLightContainer
    else MaterialTheme.colorScheme.errorContainer

    val thumbColor = if (isActive) MaterialTheme.customColors.successLightContainer
    else MaterialTheme.customColors.defaultSurface

    val steps = ((maxLimit - minLimit) / stepSize).toInt() - 1

    val colors: SliderColors = SliderDefaults.colors(
        activeTrackColor = sliderColor,
        inactiveTickColor = MaterialTheme.colorScheme.onSecondaryContainer,
        activeTickColor = sliderColor,
        thumbColor = thumbColor,
    )

    Slider(
        modifier = modifier,
        colors = colors,
        value = sliderPosition,
        valueRange = minLimit..maxLimit,
        steps = if (steps > 0) steps else 1,
        onValueChange = onValueChange,
        onValueChangeFinished = onValueChangeFinished
    )
}

@Preview
@Composable
fun SliderComponentPreview() {
    OneAppTheme {
        SliderComponent(
            isActive = true,
            sliderPosition = 500f,
            stepSize = 1,
            minLimit = 100f,
            maxLimit = 1000f
        )
    }
}
