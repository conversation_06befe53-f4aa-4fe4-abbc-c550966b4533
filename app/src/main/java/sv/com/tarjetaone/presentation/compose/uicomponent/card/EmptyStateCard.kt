package sv.com.tarjetaone.presentation.compose.uicomponent.card

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.Spacer12

@Composable
fun EmptyStateCard(
    modifier: Modifier = Modifier,
    variant: EmptyStateCardVariant = EmptyStateCardVariant.PRIMARY,
) {
    when (variant) {
        EmptyStateCardVariant.PRIMARY -> {
            PrimaryEmptyStateCard(
                modifier = modifier.background(
                    color = MaterialTheme.colorScheme.tertiary,
                    shape = MaterialTheme.shapes.large,
                ),
            )
        }

        EmptyStateCardVariant.SECONDARY -> {
            SecondaryEmptyStateCard(
                modifier = modifier.background(
                    color = MaterialTheme.customColors.gray500,
                    shape = MaterialTheme.shapes.large,
                ),
            )
        }
    }
}

@Composable
private fun PrimaryEmptyStateCard(
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier) {
        Icon(
            modifier = Modifier
                .padding(
                    start = MaterialTheme.customDimens.dimen16,
                    top = MaterialTheme.customDimens.dimen16
                )
                .size(
                    width = MaterialTheme.customDimens.dimen64,
                    height = MaterialTheme.customDimens.dimen14,
                )
                .align(Alignment.TopStart),
            imageVector = ImageVector.vectorResource(id = R.drawable.ic_atlantida_logo),
            contentDescription = null,
            tint = MaterialTheme.customColors.softVioletGray,
        )
        Icon(
            modifier = Modifier
                .size(
                    width = MaterialTheme.customDimens.dimen48,
                    height = MaterialTheme.customDimens.dimen66,
                )
                .align(Alignment.Center),
            imageVector = ImageVector.vectorResource(id = R.drawable.ic_logo_one),
            contentDescription = null,
            tint = MaterialTheme.customColors.softVioletGray,
        )
        Icon(
            modifier = Modifier
                .padding(
                    start = MaterialTheme.customDimens.dimen16,
                    bottom = MaterialTheme.customDimens.dimen16
                )
                .size(
                    width = MaterialTheme.customDimens.dimen14,
                    height = MaterialTheme.customDimens.dimen14,
                )
                .align(Alignment.BottomStart),
            imageVector = ImageVector.vectorResource(id = R.drawable.ic_signal_card),
            contentDescription = null,
            tint = MaterialTheme.customColors.softVioletGray,
        )
        Icon(
            modifier = Modifier
                .padding(
                    end = MaterialTheme.customDimens.dimen16,
                    bottom = MaterialTheme.customDimens.dimen16
                )
                .size(
                    width = MaterialTheme.customDimens.dimen56,
                    height = MaterialTheme.customDimens.dimen18,
                )
                .align(Alignment.BottomEnd),
            imageVector = ImageVector.vectorResource(id = R.drawable.ic_visa_icon),
            contentDescription = null,
            tint = MaterialTheme.customColors.softVioletGray,
        )
    }
}

@Composable
private fun SecondaryEmptyStateCard(
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier) {
        Column(
            modifier = Modifier.align(Alignment.Center),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Icon(
                imageVector = ImageVector.vectorResource(id = R.drawable.ic_shield_fill_minus),
                contentDescription = null,
                tint = MaterialTheme.customColors.arcticVeil,
            )
            Spacer12()
            Text(
                text = stringResource(id = R.string.empty_state_card_secondary_variant_card_title),
                style = MaterialTheme.typography.bodySmall.copy(
                    color = MaterialTheme.customColors.arcticVeil,
                    fontWeight = FontWeight.SemiBold,
                )
            )
        }
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    start = MaterialTheme.customDimens.dimen16,
                    end = MaterialTheme.customDimens.dimen16,
                    bottom = MaterialTheme.customDimens.dimen16
                )
                .align(Alignment.BottomCenter)
        ) {
            Icon(
                modifier = Modifier.size(
                    width = MaterialTheme.customDimens.dimen64,
                    height = MaterialTheme.customDimens.dimen14,
                ),
                imageVector = ImageVector.vectorResource(id = R.drawable.ic_atlantida_logo),
                contentDescription = null,
                tint = MaterialTheme.customColors.arcticVeil
            )
            Icon(
                modifier = Modifier.size(
                    width = MaterialTheme.customDimens.dimen42,
                    height = MaterialTheme.customDimens.dimen14,
                ),
                imageVector = ImageVector.vectorResource(id = R.drawable.ic_visa_icon_2),
                contentDescription = null,
                tint = MaterialTheme.customColors.arcticVeil
            )
        }
    }
}

@Preview
@Composable
private fun PrimaryEmptyStateCardPreview() {
    OneAppTheme {
        EmptyStateCard(
            modifier = Modifier.size(
                width = MaterialTheme.customDimens.dimen175,
                height = MaterialTheme.customDimens.dimen260,
            ),
            variant = EmptyStateCardVariant.PRIMARY,
        )
    }
}

@Preview
@Composable
private fun SecondaryEmptyStateCardPreview() {
    OneAppTheme {
        EmptyStateCard(
            modifier = Modifier.size(
                width = MaterialTheme.customDimens.dimen175,
                height = MaterialTheme.customDimens.dimen260,
            ),
            variant = EmptyStateCardVariant.SECONDARY,
        )
    }
}

enum class EmptyStateCardVariant {
    PRIMARY,
    SECONDARY;
}
