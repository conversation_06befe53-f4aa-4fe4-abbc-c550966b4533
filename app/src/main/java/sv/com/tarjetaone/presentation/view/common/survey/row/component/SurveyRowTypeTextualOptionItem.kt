package sv.com.tarjetaone.presentation.view.common.survey.row.component

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CornerSize
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SelectableCardMainContainer

@Composable
fun TextualSurveyOptionItem(
    modifier: Modifier = Modifier,
    text: String,
    selected: Boolean = false,
    onItemSelected: () -> Unit = {}
) {
    SelectableCardMainContainer(
        modifier = modifier,
        selected = selected,
        elevation = CardDefaults.cardElevation(MaterialTheme.customDimens.dimenZero),
        border = BorderStroke(
            MaterialTheme.customDimens.dimen1,
            if (selected) MaterialTheme.customColors.successContainer else MaterialTheme.colorScheme.tertiary
        ),
        shape = MaterialTheme.shapes.extraSmall.copy(all = CornerSize(MaterialTheme.customDimens.dimen6)),
        content = {
            Text(
                modifier = Modifier
                    .padding(
                        vertical = MaterialTheme.customDimens.dimen10,
                        horizontal = MaterialTheme.customDimens.dimen14
                    )
                    .wrapContentWidth(),
                text = text,
                style = MaterialTheme.typography.labelSmall,
                color = if (selected) MaterialTheme.colorScheme.onPrimary
                else MaterialTheme.colorScheme.onSurfaceVariant
            )
        },
        onClick = onItemSelected
    )
}