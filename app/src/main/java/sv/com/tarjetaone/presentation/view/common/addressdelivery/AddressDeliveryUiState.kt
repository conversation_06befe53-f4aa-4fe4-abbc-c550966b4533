package sv.com.tarjetaone.presentation.view.common.addressdelivery

import androidx.compose.runtime.Stable
import sv.com.tarjetaone.domain.entities.response.AddressDeliveryUI

@Stable
data class AddressDeliveryUiState(
    val addresses: List<AddressDeliveryUI> = emptyList(),
    val selectedAddress: AddressDeliveryUI? = null,
    val alertVisibility: Boolean = true,
    val progressBarValue: Float? = null,
    val isBackButtonVisible: Boolean = true
) {
    val hasLoadedData: Boolean = addresses.isNotEmpty()
    val isContinueButtonEnabled: Boolean = selectedAddress != null
}