package sv.com.tarjetaone.presentation.view.common.documentpreview

import com.facephi.fphiselphidwidgetcore.WidgetSelphIDResult
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.core.interfaces.DuiValidation
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.CommonDialogWithIconParams
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.core.utils.extensions.toDate
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.core.utils.facephi.getDialogParamsForException
import sv.com.tarjetaone.presentation.helpers.UiText
import java.util.Calendar

abstract class DocumentPreviewViewModel(
    private val duiValidation: DuiValidation,
    private val imgUtils: ImageUtils,
    private val facephiResultHandler: FacephiResultHandler,
) : BaseViewModel() {

    protected val _uiState = MutableStateFlow(DocumentPreviewUiState())
    val uiState = _uiState.asStateFlow()

    protected abstract fun onValidDocument()

    /**
     * Action to be performed on the onClick button event of the invalid document dialog
     */
    protected abstract fun onInvalidDocument()

    protected open fun onStart() {
        _uiState.update { state ->
            state.copy(
                documentFront = baseSharedPrefs.getDocFrontFileName()?.let {
                    imgUtils.getTempImage(it)
                },
                documentBack = baseSharedPrefs.getDocBackFileName()?.let {
                    imgUtils.getTempImage(it)
                }
            )
        }
    }

    private fun onContinueClick() {
        getValidationMessage()?.let {
            sendEvent(
                UiEvent.ShowCommonDialog(
                    CommonDialogWithIconParams(
                        icon = R.drawable.ic_error_yellow,
                        title = UiText.StringResource(it.first),
                        message = UiText.StringResource(it.second),
                        buttonText = UiText.StringResource(R.string.accept_label),
                        buttonColor = R.color.yellow_alert_button,
                        onButtonClick = ::onInvalidDocument
                    )
                )
            )
        } ?: run {
            onValidDocument()
        }
    }

    protected open fun onRetakeClick() = Unit

    private fun onDocumentCaptured(result: WidgetSelphIDResult) {
        val doc = facephiResultHandler.onDocumentCaptured(result)
        if (doc != null) {
            _uiState.update {
                it.copy(
                    documentFront = doc.front,
                    documentBack = doc.back
                )
            }
        } else {
            sendEvent(
                UiEvent.ShowCommonDialog(
                    CommonDialogWithIconParams(
                        isDismissible = false,
                        icon = R.drawable.ic_error_yellow,
                        buttonColor = R.color.yellow_alert_button,
                        title = UiText.StringResource(R.string.error_doc_picture_title),
                        message = UiText.StringResource(R.string.error_doc_picture_desc),
                        buttonText = UiText.StringResource(R.string.take_picture)
                    )
                )
            )
        }
    }

    private fun onDocumentCaptureFailed(result: WidgetSelphIDResult) {
        facephiResultHandler.onDocumentCaptureFailed(result) {
            sendEvent(
                UiEvent.ShowCommonDialog(
                    getDialogParamsForException(it, ::onCaptureFailureDialogAction)
                )
            )
        }
    }

    protected open fun onCaptureFailureDialogAction() = Unit

    /**
     * Validates the captured OCR data and returns a title and respective message if the criteria
     * are not met
     */
    private fun getValidationMessage(): Pair<Int, Int>? {
        val ocr = baseSharedPrefs.getOcr()
        val matchingSideScore = ocr?.matchingSidesScore?.toFloat()
        val expiryDate = ocr?.dateOfExpiry?.toDate(AppConstants.DAY_MONTH_YEAR_WITH_SLASH)

        return when {
            // Expired document
            expiryDate == null || expiryDate.before(Calendar.getInstance().time) -> {
                R.string.expired_dui_title_label to R.string.expired_dui_description_label
            }

            // The sides do not belong to the same document
            matchingSideScore.orZero() < SIDE_MATCH_THRESHOLD -> {
                R.string.invalid_dui_title_label to R.string.non_matching_sides
            }

            // Read document number is not valid
            !duiValidation.checkIfDuiIsValid(ocr.frontMLDocumentNumber.orEmpty()) -> {
                R.string.invalid_dui_title_label to R.string.invalid_document
            }
            else -> null
        }
    }

    fun onEvent(event: DocumentPreviewUiEvent) {
        when (event) {
            is DocumentPreviewUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            is DocumentPreviewUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            is DocumentPreviewUiEvent.OnStart -> onStart()
            is DocumentPreviewUiEvent.OnContinueClick -> onContinueClick()
            is DocumentPreviewUiEvent.OnRetakeClick -> onRetakeClick()
            is DocumentPreviewUiEvent.OnDocumentCaptured -> onDocumentCaptured(event.result)
            is DocumentPreviewUiEvent.OnDocumentCaptureFailed -> onDocumentCaptureFailed(event.result)
        }
    }

    companion object {
        private const val SIDE_MATCH_THRESHOLD = 0.70f
    }
}
