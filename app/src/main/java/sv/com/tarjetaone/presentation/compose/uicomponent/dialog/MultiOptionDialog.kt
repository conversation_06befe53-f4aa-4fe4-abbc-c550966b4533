package sv.com.tarjetaone.presentation.compose.uicomponent.dialog

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Card
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant

@Composable
fun MultiOptionDialog(
    modifier: Modifier = Modifier,
    title: String,
    subTitle: String,
    dynamicButtons: List<DynamicDialogButtonParam>,
    onClickButton: (buttonId: String) -> Unit
) {
    Dialog(
        onDismissRequest = {},
        properties = DialogProperties(
            dismissOnBackPress = false,
            dismissOnClickOutside = false
        )
    ) {
        Box(
            modifier = modifier
                .fillMaxSize()
                .wrapContentHeight()
                .padding(
                    horizontal = MaterialTheme.customDimens.dimen16,
                    vertical = MaterialTheme.customDimens.dimen1
                )
        ) {
            Card(
                modifier = modifier
                    .heightIn(min = MaterialTheme.customDimens.dimen189)
                    .wrapContentWidth()
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            vertical = MaterialTheme.customDimens.dimen16,
                            horizontal = MaterialTheme.customDimens.dimen16
                        ),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleSmall
                    )
                    Text(
                        text = subTitle,
                        style = MaterialTheme.typography.bodySmall
                    )

                    LazyColumn(
                        contentPadding = PaddingValues(
                            horizontal = MaterialTheme.customDimens.dimen35,
                            vertical = MaterialTheme.customDimens.dimen10
                        ),
                        modifier = Modifier.wrapContentSize()
                    ) {
                        items(dynamicButtons) {
                            ConfirmationDialogButton(
                                modifier = Modifier
                                    .padding(vertical = MaterialTheme.customDimens.dimen5)
                                    .fillMaxWidth(),
                                text = it.title,
                                buttonVariant = it.buttonVariant,
                                onClick = { onClickButton(it.id) }
                            )
                        }
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun DynamicDialogPreview() {
    val dialogTitle = "No te preocupes"
    val dialogSubTitle = "Aun cuentas con tu tarjeta?"
    val buttonTitle = "Si, tengo mi tarjeta"

    OneAppTheme {
        MultiOptionDialog(
            title = dialogTitle,
            subTitle = dialogSubTitle,
            dynamicButtons = listOf(
                DynamicDialogButtonParam(
                    id = "1",
                    title = buttonTitle,
                    buttonVariant = ButtonVariant.PRIMARY_VARIANT
                ),
                DynamicDialogButtonParam(
                    id = "2",
                    title = buttonTitle,
                    buttonVariant = ButtonVariant.SECONDARY_VARIANT
                ),
                DynamicDialogButtonParam(
                    id = "3",
                    title = buttonTitle,
                    buttonVariant = ButtonVariant.SECONDARY_VARIANT
                )
            )
        ) {}
    }
}
