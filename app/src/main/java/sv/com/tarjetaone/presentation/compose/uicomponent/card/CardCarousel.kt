package sv.com.tarjetaone.presentation.compose.uicomponent.card

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PageSize
import androidx.compose.foundation.pager.PagerDefaults
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.common.CustomDotsIndicator
import sv.com.tarjetaone.presentation.compose.uicomponent.personalization.CardPersonalizationDummyData

/**
 * Composable to display a carousel of cards.
 * @param cards List of items to display in the carousel.
 * @param cardWidth Width of each card. Used to calculate the padding of the cards so that the first
 * and last card appear centered
 * @param onCardChange Callback to handle when the currently selected card changes.
 * Alternatively, pass a [pagerState] parameter and handle it's `currentPage` property.
 * @param preloadCardCount Cards to compose and layout before and after the list of visible cards.
 * @param cardContent Composable to display the content of each card.
 */
@Composable
fun <T> CardCarousel(
    modifier: Modifier = Modifier,
    cards: List<T>,
    cardWidth: Dp,
    onCardChange: (Int) -> Unit = {},
    pagerState: PagerState = rememberPagerState(
        pageCount = { cards.size }
    ),
    preloadCardCount: Int = PagerDefaults.BeyondViewportPageCount,
    cardContent: @Composable (Int) -> Unit
) {
    LaunchedEffect(pagerState.currentPage) {
        onCardChange(pagerState.currentPage)
    }
    BoxWithConstraints {
        // Calculating padding to begin the cards in the center of the screen
        val horizontalPadding = (maxWidth - cardWidth) / 2
        Column(
            modifier = modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            HorizontalPager(
                state = pagerState,
                pageSize = PageSize.Fixed(cardWidth),
                pageSpacing = MaterialTheme.customDimens.dimen24,
                contentPadding = PaddingValues(
                    vertical = MaterialTheme.customDimens.dimen16,
                    horizontal = horizontalPadding
                ),
                beyondViewportPageCount = preloadCardCount
            ) {
                cardContent(it)
            }
            CustomDotsIndicator(
                totalDots = cards.size,
                activeIndex = pagerState.currentPage,
                activeColor = MaterialTheme.colorScheme.primary,
                inactiveColor = MaterialTheme.customColors.gray300,
                dotSize = MaterialTheme.customDimens.dimen8
            )
        }
    }
}

@Preview
@Composable
private fun CardCarouselPreview() {
    OneAppTheme {
        val cards = listOf(1, 2, 3, 4, 5)
        CardCarousel(
            cards = cards,
            pagerState = rememberPagerState(
                pageCount = { cards.size }
            ),
            cardWidth = MaterialTheme.customDimens.dimen230,
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)
                .padding(bottom = MaterialTheme.customDimens.dimen16),
        ) {
            HomeCreditCard(
                availableCredit = "2,000.00",
                usedCredit = "500.00",
                cardNumMasked = "xxxx xxxx xxxx 1234",
                holderName = "John Doe",
                isMainCard = true,
                cardColors = CardPersonalizationDummyData.colors.first()
            )
        }
    }
}
