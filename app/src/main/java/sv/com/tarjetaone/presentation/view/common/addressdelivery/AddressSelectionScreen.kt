package sv.com.tarjetaone.presentation.view.common.addressdelivery

import android.Manifest
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.rememberPermissionState
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_STRING
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.core.utils.shouldShowRationale
import sv.com.tarjetaone.domain.entities.response.AddressDeliveryUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler

@Composable
fun AddressSelectionScreen(
    viewModel: AddressDeliveryBaseViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    SideEffectHandler(viewModel.sideEffects) {
        AddressSelectionScreenContent(
            uiState = uiState,
            onEvent = viewModel::onEvent
        )
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
private fun AddressSelectionScreenContent(
    uiState: AddressDeliveryUiState,
    onEvent: (AddressDeliveryUiEvent) -> Unit
) {
    val context = LocalContext.current
    val locationPermission = Manifest.permission.ACCESS_FINE_LOCATION
    val locationPermissionState = rememberPermissionState(locationPermission) { isGranted ->
        if (isGranted) {
            onEvent(AddressDeliveryUiEvent.OnLocationPermissionGranted)
        } else {
            onEvent(
                AddressDeliveryUiEvent.OnLocationPermissionDenied(
                    showRationale = context.shouldShowRationale(locationPermission)
                )
            )
        }
    }
    LaunchedEffect(Unit) {
        onEvent(AddressDeliveryUiEvent.OnStart)
    }
    ScreenWithTopAppBar(
        title = stringResource(id = R.string.where_do_u_want_receive_the_card),
        isProgressbarVisible = uiState.progressBarValue != null,
        progress = uiState.progressBarValue.orZero(),
        isLeftButtonVisible = uiState.isBackButtonVisible,
        onLeftButtonClick = { onEvent(AddressDeliveryUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(AddressDeliveryUiEvent.OnTwilioClick) },
    ) {
        val addresses by remember(uiState.addresses) {
            derivedStateOf {
                uiState.addresses + addAddressOption(
                    displayText = context.getString(R.string.add_new_address)
                )
            }
        }
        AddressDeliveryContent(
            addresses = addresses,
            selectedAddress = uiState.selectedAddress,
            onSelectAddress = { item ->
                onEvent(AddressDeliveryUiEvent.OnSelectAddress(item))
            },
            alertVisibility = uiState.alertVisibility,
            onDismissSecurityAlert = { onEvent(AddressDeliveryUiEvent.OnDismissSecurityAlert) },
            onContinueClick = {
                if (uiState.selectedAddress?.addressId?.toIntOrNull() == ZERO_VALUE) {
                    locationPermissionState.launchPermissionRequest()
                } else {
                    onEvent(AddressDeliveryUiEvent.OnContinueClick)
                }
            },
            isContinueButtonEnabled = uiState.isContinueButtonEnabled
        )
    }
}

@Preview
@Composable
private fun AddressSelectionScreenPreview() {
    OneAppTheme {
        AddressSelectionScreenContent(
            uiState = AddressDeliveryUiState(
                addresses = listOf(
                    AddressDeliveryUI(
                        addressTypeName = "Enviar a mi casa",
                        address = "Santa Maria 2670, Providencia, Región Metropolitana de Santiago, Chile Carrera 11 #95-37 Bogota Londres 247, Del Carmen, Ciudad de Mexico",
                        isValid = true
                    ),
                    AddressDeliveryUI(
                        addressTypeName = "Otra dirección",
                        address = "San Salvador, San Salvador 134",
                        isValid = false
                    )
                ),
                selectedAddress = AddressDeliveryUI(),
                alertVisibility = true
            ),
            onEvent = { }
        )
    }
}

private fun addAddressOption(displayText: String) = AddressDeliveryUI(
    addressId = ZERO_STRING,
    isValid = true,
    addressTypeName = displayText
)