package sv.com.tarjetaone.presentation.view.common.contactinput

import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.response.ContactType

sealed class ContactValidationError(val message: UiText?) {
    data object None : ContactValidationError(null)
    data class InvalidFormat(val type: ContactType) :
        ContactValidationError(
            when (type) {
                ContactType.Phone -> UiText.StringResource(res = R.string.invalid_phone_number)
                ContactType.Email -> UiText.StringResource(res = R.string.email_format_error)
            }
        )

    data object InvalidCharacters :
        ContactValidationError(UiText.StringResource(res = R.string.contact_input_format_error))

    data object Empty :
        ContactValidationError(UiText.StringResource(res = R.string.contact_input_empty_error))

    data object TooShort :
        ContactValidationError(UiText.StringResource(res = R.string.contact_input_is_too_short))

    data class ApiError(val errorMessage: String) :
        ContactValidationError(UiText.DynamicString(errorMessage))
}
