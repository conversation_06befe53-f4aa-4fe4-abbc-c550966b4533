package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.contact_channel.otpvalidation

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.AppConstants.SUCCESS_RESPONSE_CODE
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.request.CustomerContactRequestUI
import sv.com.tarjetaone.domain.entities.request.CustomerContactUI
import sv.com.tarjetaone.domain.entities.request.ValidateOTPRequestUI
import sv.com.tarjetaone.domain.entities.response.ContactType
import sv.com.tarjetaone.domain.entities.response.OtpMethod
import sv.com.tarjetaone.domain.entities.response.OtpType
import sv.com.tarjetaone.domain.usecases.personalData.contacts.UpdateCustomerContactUseCase
import sv.com.tarjetaone.domain.usecases.requestcard.otpvalidation.SendOtpCodeUseCase
import sv.com.tarjetaone.domain.usecases.requestcard.otpvalidation.ValidateOtpCodeUseCase
import sv.com.tarjetaone.presentation.compose.util.InfoStatus
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.common.otpvalidation.OtpValidationBaseViewModel
import javax.inject.Inject

@HiltViewModel
class UpdateContactOtpValidationViewModel @Inject constructor(
    private val sendOtpCodeUseCase: SendOtpCodeUseCase,
    private val validateOtpCodeUseCase: ValidateOtpCodeUseCase,
    private val updateCustomerContactUseCase: UpdateCustomerContactUseCase,
    private val sharedPrefs: SecureSharedPreferencesRepository,
    savedStateHandle: SavedStateHandle,
) : OtpValidationBaseViewModel() {

    private val args = UpdateContactOtpValidationFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onStart() {
        super.onStart()
        _uiState.update {
            it.copy(
                contactType = args.contactType ?: ContactType.Phone,
                contact = args.contact,
                sharedKey = args.sharedKey.orEmpty(),
                showProgressBar = false
            )
        }
    }

    override fun resendOtp(type: OtpType, method: OtpMethod) {
        sendEvent(UiEvent.Loading(true))
        viewModelScope.launch {
            sendOtpCodeUseCase(
                otpType = type,
                value = _uiState.value.contact,
                otpChannel = method,
            ).executeUseCase(
                onApiErrorAction = { _, error, _, _ ->
                    sendEvent(UiEvent.Loading(false))
                    val errorCode = error?.code?.toIntOrNull()
                    val otpStatus = when (errorCode) {
                        MAX_REQUEST_OTP,
                        MAX_REQUEST_OTP_BAES -> InfoStatus.Error

                        else -> null
                    }
                    _uiState.update { state ->
                        state.copy(
                            otpMessage = error?.result?.let { UiText.DynamicString(it) },
                            otpStatus = otpStatus ?: state.otpStatus
                        )
                    }
                },
                onSuccessAction = {
                    sendEvent(UiEvent.Loading(false))
                    it.sharedKey?.let { sharedKey ->
                        _uiState.update { state -> state.copy(sharedKey = sharedKey) }
                    }
                    startOtpCountdown()
                    showSnackBar()
                },
                onNetworkErrorAction = {
                    showUpsErrorMessage()
                }
            )
        }
    }

    override fun onValidateOtp(code: String) {
        sendEvent(UiEvent.Loading(true))
        viewModelScope.launch {
            validateOtpCodeUseCase(
                request = ValidateOTPRequestUI(
                    otpCode = code,
                    sharedKey = _uiState.value.sharedKey
                )
            ).executeUseCase(
                onApiErrorAction = { _, error, _, _ ->
                    sendEvent(UiEvent.Loading(false))
                    val errorCode = error?.code?.toIntOrNull()
                    val otpStatus = when (errorCode) {
                        MAX_VALIDATION_ATTEMPTS_CODE -> InfoStatus.Error
                        INVALID_OTP_CODE -> InfoStatus.Error
                        EXPIRED_OTP_CODE -> InfoStatus.Warning
                        else -> InfoStatus.Error
                    }
                    _uiState.update { state ->
                        state.copy(
                            otpStatus = otpStatus,
                            otpMessage = if (errorCode == MAX_VALIDATION_ATTEMPTS_CODE) {
                                error.result?.let { UiText.DynamicString(it) }
                            } else null
                        )
                    }
                },
                onNetworkErrorAction = {
                    showUpsErrorMessage()
                },
                onSuccessAction = {
                    _uiState.update { it.copy(otpStatus = InfoStatus.Success) }
                    updateCustomerContact()
                }
            )
        }
    }

    private suspend fun updateCustomerContact() {
        updateCustomerContactUseCase(
            request = CustomerContactRequestUI(
                customerId = sharedPrefs.getCustomerId().orZero(),
                customerContact = CustomerContactUI(
                    contactId = args.contactId,
                    contactTypeCode = args.contactType?.type,
                    contactValue = args.contact,
                ),
                biometryId = args.biometryId.orZero()
            )
        ).executeUseCase(
            onApiErrorAction = { _, _, _, _ ->
                sendEvent(UiEvent.Loading(false))
                showUpsErrorMessage()
            },
            onNetworkErrorAction = {
                sendEvent(UiEvent.Loading(false))
                showUpsErrorMessage()
            },
            onSuccessAction = {
                sendEvent(UiEvent.Loading(false))
                if (it.statusResponse?.responseStatus?.code == SUCCESS_RESPONSE_CODE) {
                    sendEvent(
                        UiEvent.Navigate(
                            UpdateContactOtpValidationFragmentDirections
                                .actionUpdateContactOtpValidationFragmentToSuccessUpdateContact(
                                    contactType = args.contactType
                                )
                        )
                    )
                } else {
                    showUpsErrorMessage()
                }
            }
        )
    }

    companion object {
        private const val MAX_VALIDATION_ATTEMPTS_CODE = 6
    }
}
