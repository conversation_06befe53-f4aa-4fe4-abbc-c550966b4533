package sv.com.tarjetaone.presentation.view.common.data_overview

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.analytics.dynatrace.DynatraceEvent
import sv.com.tarjetaone.analytics.dynatrace.DynatraceManager
import sv.com.tarjetaone.common.utils.AppConstants.BLANK_SPACE
import sv.com.tarjetaone.common.utils.AppConstants.DAY_FULL_MONTH_NAME_YEAR_WITH_SPACES
import sv.com.tarjetaone.common.utils.AppConstants.DAY_MONTH_YEAR_WITH_SLASH
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.IMAGE_CODE_DUI
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.SUCCESS_RESPONSE_CODE
import sv.com.tarjetaone.common.utils.AppConstants.YEAR_MONTH_DAY_NO_SPACES
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.UserUtils
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.core.utils.extensions.setGender
import sv.com.tarjetaone.core.utils.extensions.takeIfNotBlank
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.request.IdentityDocumentsUI
import sv.com.tarjetaone.domain.entities.request.UserLocationUI
import sv.com.tarjetaone.domain.entities.request.ValidateOCRRequestUI
import sv.com.tarjetaone.domain.entities.response.CustomerDataUI
import sv.com.tarjetaone.domain.entities.response.ValidateOCRContentUI
import sv.com.tarjetaone.domain.usecases.requestcard.validateocr.ValidateOcrUseCase
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.DialogAction
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.MessageParams
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.OneDialogParams
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.helpers.addDashDui
import sv.com.tarjetaone.presentation.helpers.capitalizeAllWords
import sv.com.tarjetaone.presentation.helpers.removeDash
import sv.com.tarjetaone.presentation.helpers.splitAndGet
import sv.com.tarjetaone.presentation.helpers.splitAndGetFirst
import sv.com.tarjetaone.presentation.view.request_card_flow.data_overview.DataOverviewRequestCardFragmentDirections
import sv.com.tarjetaone.presentation.view.request_card_flow.data_overview.DataOverviewRequestCardUiEvent
import sv.com.tarjetaone.presentation.view.request_card_flow.data_overview.DataOverviewRequestCardUiState
import javax.inject.Inject

@HiltViewModel
class DataOverviewViewModel @Inject constructor(
    private val validateOcrUseCase: ValidateOcrUseCase,
    private val sharedPrefsRepo: SecureSharedPreferencesRepository,
    private val userUtils: UserUtils,
    private val dynatraceManager: DynatraceManager
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(DataOverviewRequestCardUiState())
    val uiState = _uiState.asStateFlow()

    //TODO IN A LATER TICKET CONSIDER MOVING TO LAUNCHED EFFECT
    init {
        onEvent(DataOverviewRequestCardUiEvent.OnStart)
    }

    private fun onStart() {
        dynatraceManager.sendInAppEvent(DynatraceEvent.ShortCapture.ViewPersonalData)
        validateOcr()
    }

    private fun validateOcr() {
        sendEvent(SideEffect.Loading(true))
        viewModelScope.launch {
            val request = createValidateOcrRequest() ?: run {
                showSimpleErrorMessage(EMPTY_STRING)
                return@launch
            }
            validateOcrUseCase(request).executeUseCase(
                onSuccessAction = { response ->
                    //TODO IN A LATER TICKET UPDATE WITH ENCAPSULATED RESPONSE

                    val responseCode = response.statusResponse?.responseStatus?.code
                    if (responseCode != SUCCESS_RESPONSE_CODE) {
                        showSimpleErrorMessage(response.statusResponse?.responseStatus?.message.orEmpty())
                    } else {
                        response.data?.let {
                            sharedPrefsRepo.putValidatedOcr(it)
                            updateUserData(it)
                            setUserAddressFromDui(it)
                        }
                        sendEvent(SideEffect.Loading(false))
                    }
                }
            )
        }
    }

    private fun createValidateOcrRequest(): ValidateOCRRequestUI? {
        return sharedPrefsRepo.getOcr()?.let { ocrData ->
            ValidateOCRRequestUI(
                name = ocrData.frontMLFirstName,
                birthDate = ocrData.frontMLDateOfBirth?.getFormattedDateFromTo(
                    DAY_MONTH_YEAR_WITH_SLASH,
                    YEAR_MONTH_DAY_NO_SPACES
                ),
                knownBy = ocrData.frontMLFirstName,
                lastName = ocrData.frontMLLastName,
                genderCode = ocrData.gender,
                marriedName = ocrData.marriedName,
                matchingSidesScore = ocrData.matchingSidesScore?.toDouble(),
                identityDocuments = IdentityDocumentsUI(
                    document = ocrData.frontMLDocumentNumber?.removeDash(),
                    issuedDate = ocrData.frontMLDateOfIssue
                        ?.getFormattedDateFromTo(
                            DAY_MONTH_YEAR_WITH_SLASH,
                            YEAR_MONTH_DAY_NO_SPACES
                        ),
                    expirationDate = ocrData.frontMLDateOfExpiry
                        ?.getFormattedDateFromTo(
                            DAY_MONTH_YEAR_WITH_SLASH,
                            YEAR_MONTH_DAY_NO_SPACES
                        ),
                    idTypeCode = EMPTY_STRING
                )
            )
        }
    }

    private fun updateUserData(data: ValidateOCRContentUI) {
        val names = data.name?.capitalizeAllWords()
        val lastnames = data.lastName?.capitalizeAllWords()
        val marriedName = data.marriedName?.capitalizeAllWords()
        val fullName = listOfNotNull(names, lastnames, marriedName)
            .filter { it.isNotBlank() }
            .joinToString(separator = BLANK_SPACE)

        userUtils.userProfile.apply {
            firstSurname = lastnames?.splitAndGetFirst(BLANK_SPACE)
            secondSurname = lastnames?.splitAndGet(BLANK_SPACE, ONE_VALUE)
            firstName = names?.splitAndGetFirst(BLANK_SPACE)
            secondName = names?.splitAndGet(BLANK_SPACE, ONE_VALUE)
            knowBy = data.knownBy?.capitalizeAllWords()
            this.fullName = fullName
            birthDate = data.birthDate
            duiDueDate = data.identityDocuments?.expirationDate
            duiIssueDate = data.identityDocuments?.issuedDate
            marriedSurname = marriedName
            gender = data.genderCode?.setGender()
        }

        // update customerData to be displayed on the UI.
        _uiState.update {
            it.copy(
                customerData = CustomerDataUI(
                    fullName = fullName,
                    genderCode = data.genderCode?.setGender(),
                    dui = data.identityDocuments?.document?.addDashDui().orEmpty(),
                    duiExpirationDate = data.identityDocuments?.expirationDate
                        ?.getFormattedDateFromTo(
                            YEAR_MONTH_DAY_NO_SPACES,
                            DAY_FULL_MONTH_NAME_YEAR_WITH_SPACES
                        ).orEmpty(),
                    birthDate = data.birthDate
                        ?.getFormattedDateFromTo(
                            YEAR_MONTH_DAY_NO_SPACES,
                            DAY_FULL_MONTH_NAME_YEAR_WITH_SPACES
                        ).orEmpty(),
                )
            )
        }
    }

    private fun setUserAddressFromDui(data: ValidateOCRContentUI) {
        userUtils.clearUserLocation()
        userUtils.addUserLocation(
            UserLocationUI(
                address = data.address,
                addressType = IMAGE_CODE_DUI,
                department = data.department?.capitalizeAllWords(),
                municipio = data.municipality?.capitalizeAllWords(),
                idMunicipio = data.municipalityId?.toString(),
                residential = data.address
            )
        )
    }

    private fun showSimpleErrorMessage(message: String) {
        sendEvent(SideEffect.Loading(false))
        showOneDialog(
            params = OneDialogParams(
                message = MessageParams(
                    text = message.takeIfNotBlank()?.let { UiText.DynamicString(it) }
                        ?: UiText.StringResource(R.string.something_went_wrong_simple)
                ),
                isDismissible = false,
                primaryAction = DialogAction(
                    text = UiText.StringResource(R.string.ok_message),
                    onClick = {
                        sendEvent(
                            UiEvent.Navigate(
                                DataOverviewRequestCardFragmentDirections
                                    .actionDataOverviewFragmentToWelcomeFragment()
                            )
                        )
                    }
                )
            )
        )
    }

    private fun onConfirmButtonClicked() {
        dynatraceManager.sendInAppEvent(DynatraceEvent.ShortCapture.ConfirmPersonalData)
        sendEvent(
            UiEvent.Navigate(
                DataOverviewRequestCardFragmentDirections
                    .actionDataOverviewFragmentToLocationOverviewFragment()
            )
        )
    }

    private fun onWrongDataButtonClicked() {
        dynatraceManager.sendInAppEvent(DynatraceEvent.ShortCapture.WrongPersonalData)
        showOneDialog(
            params = OneDialogParams(
                icon =  R.drawable.ic_match_error,
                title = UiText.StringResource(R.string.personal_information_dialog_title),
                message = MessageParams(
                    text = UiText.StringResource(R.string.personal_information_dialog_subtitle)
                ),
                isDismissible = false,
                primaryAction = DialogAction(
                    actionType = DialogAction.ActionType.ERROR,
                    text = UiText.StringResource(R.string.accept_label),
                    onClick = {
                        dynatraceManager.sendInAppEvent(
                            DynatraceEvent.ShortCapture.ReportPersonalData
                        )
                        sendEvent(
                            UiEvent.Navigate(
                                DataOverviewRequestCardFragmentDirections
                                    .actionDataOverviewFragmentToWelcomeFragment()
                            )
                        )
                    }
                )
            )
        )
    }

    fun onEvent(event: DataOverviewRequestCardUiEvent) {
        when (event) {
            DataOverviewRequestCardUiEvent.OnStart -> onStart()
            DataOverviewRequestCardUiEvent.OnConfirmButtonClicked -> onConfirmButtonClicked()
            DataOverviewRequestCardUiEvent.OnWrongDataButtonClicked -> onWrongDataButtonClicked()
            DataOverviewRequestCardUiEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
        }
    }
}
