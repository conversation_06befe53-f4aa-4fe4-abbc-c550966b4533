package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_FLOAT_VALUE
import sv.com.tarjetaone.presentation.compose.theme.LocalCustomColors
import sv.com.tarjetaone.presentation.compose.uicomponent.common.TimelineNodeConstants.Y_DEFAULT_OFFSET
import sv.com.tarjetaone.presentation.compose.uicomponent.common.TimelineNodeConstants.Y_END_LINE_OFFSET
import sv.com.tarjetaone.presentation.compose.uicomponent.common.TimelineNodeConstants.Y_START_LINE_OFFSET
import sv.com.tarjetaone.presentation.compose.uicomponent.common.TimelineNodeConstants.Y_START_OFFSET

@Composable
fun TimelineNode(
    position: TimelineNodePosition,
    contentStartOffset: Dp = 24.dp,
    circleParams: CircleParams = CircleParams(
        radius = 8.dp,
        backgroundColor = LocalCustomColors.current.gray500
    ),
    lineParameters: LineParams = LineParams(
        strokeWidth = 1.dp,
        color = LocalCustomColors.current.gray500
    ),
    timelineNodeMode: TimelineMode = TimelineMode.START_END_DATES,
    content: @Composable BoxScope.(modifier: Modifier) -> Unit
) {
    
    var contentHeight by remember { mutableFloatStateOf(ZERO_FLOAT_VALUE) }

    Box(
        modifier = Modifier
            .onGloballyPositioned { coordinates ->
                contentHeight = coordinates.size.height.toFloat()
            }
            .wrapContentSize()
            .drawBehind {
                val circleRadiusInPx = circleParams.radius.toPx()
                drawCircle(
                    color = circleParams.backgroundColor,
                    radius = circleRadiusInPx,
                    center = Offset(
                        x = circleRadiusInPx,
                        y = if (position == TimelineNodePosition.START && timelineNodeMode == TimelineMode.START_DATE_ONLY) {
                            contentHeight / Y_DEFAULT_OFFSET
                        } else if (position == TimelineNodePosition.START) {
                            contentHeight / Y_START_OFFSET
                        } else {
                            contentHeight / Y_DEFAULT_OFFSET
                        }
                    )
                )
                if (position != TimelineNodePosition.LAST && timelineNodeMode == TimelineMode.START_END_DATES) {
                    drawLine(
                        color = lineParameters.color,
                        start = Offset(
                            x = circleRadiusInPx, y = circleRadiusInPx * Y_START_LINE_OFFSET
                        ),
                        end = Offset(
                            x = circleRadiusInPx, y = this.size.height + (this.size.height * Y_END_LINE_OFFSET)
                        ),
                        strokeWidth = lineParameters.strokeWidth.toPx()
                    )
                }
            }
    ) {
        content(Modifier.padding(start = contentStartOffset))
    }
}

@Preview
@Composable
fun TimelineNodePreview() {
    Column(
        modifier = Modifier
            .background(MaterialTheme.colorScheme.surface)
            .fillMaxWidth()
            .padding(32.dp)
    ) {
        TimelineNode(
            position = TimelineNodePosition.START
        ) { modifier ->
            Text(text = "Inicio", modifier.padding(bottom = 32.dp))
        }
        TimelineNode(
            position = TimelineNodePosition.LAST
        ) { modifier ->
            Text(text = "Final", modifier)
        }
    }
}

@Preview
@Composable
fun TimelineNodeStartOnlyPreview() {
    Column(
        modifier = Modifier
            .background(MaterialTheme.colorScheme.surface)
            .fillMaxWidth()
            .padding(32.dp)
    ) {
        TimelineNode(
            position = TimelineNodePosition.START,
            timelineNodeMode = TimelineMode.START_DATE_ONLY
        ) { modifier ->
            Text(text = "Inicio", modifier.padding(bottom = 32.dp))
        }
    }
}

object TimelineNodeConstants {
    const val Y_START_OFFSET = 4f
    const val Y_DEFAULT_OFFSET = 2.5f
    const val Y_START_LINE_OFFSET = 2
    const val Y_END_LINE_OFFSET = 0.25f
}

enum class TimelineNodePosition {
    START,
    LAST
}

enum class TimelineMode {
    START_DATE_ONLY,
    START_END_DATES
}

data class CircleParams(
    val radius: Dp,
    val backgroundColor: Color
)

data class LineParams(
    val strokeWidth: Dp,
    val color: Color
)
