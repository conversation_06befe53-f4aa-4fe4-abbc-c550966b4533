package sv.com.tarjetaone.presentation.compose.util

import android.os.Build
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.res.painterResource
import coil.compose.AsyncImage
import coil.decode.GifDecoder
import coil.decode.ImageDecoderDecoder
import coil.request.ImageRequest
import sv.com.tarjetaone.R

/**
 * composable function to load an image or gif from external URL or internal resources.
 */
@Composable
fun LoadAsyncImage(
    modifier: Modifier = Modifier,
    url: Any,
    iconTintColor: Color? = null,
    placeholderPreview: Int = R.drawable.ic_auth_advice,
    contentScale: ContentScale = ContentScale.Fit
) {

    var colorFilter: ColorFilter? = null
    iconTintColor?.let { colorFilter = ColorFilter.tint(it) }

    val decoderFactory = if (Build.VERSION.SDK_INT > Build.VERSION_CODES.P)
        ImageDecoderDecoder.Factory() else GifDecoder.Factory()

    val imageRequest = ImageRequest.Builder(LocalContext.current)
        .data(url)
        .decoderFactory(decoderFactory)
        .crossfade(true)
        .build()

    AsyncImage(
        modifier = modifier,
        model = imageRequest,
        placeholder = mockPlaceholder(resource = placeholderPreview),
        contentScale = contentScale,
        contentDescription = null,
        colorFilter = colorFilter
    )
}

@Composable
fun mockPlaceholder(resource: Int): Painter? =
    if (LocalInspectionMode.current) {
        painterResource(id = resource)
    } else {
        null
    }
