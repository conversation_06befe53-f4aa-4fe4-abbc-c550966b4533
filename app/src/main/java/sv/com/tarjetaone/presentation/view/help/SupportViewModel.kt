package sv.com.tarjetaone.presentation.view.help

import android.content.Intent
import androidx.core.net.toUri
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.common.utils.AppConstants.INTENT_DIAL_PREFIX
import sv.com.tarjetaone.core.remoteconfig.RemoteConfigManager
import sv.com.tarjetaone.core.remoteconfig.RemoteProperty
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.SideEffect
import javax.inject.Inject
import sv.com.tarjetaone.core.utils.UiEvent

/**
 * <AUTHOR> on 7/2/22.
 * Copyright (c) 2022 Atlantida. All rights reserved.
 **/
@HiltViewModel
class SupportViewModel @Inject constructor(
    private val remoteConfigManager: RemoteConfigManager
) : BaseViewModel() {

    fun onEvent(event: SupportUiEvent) {
        when (event) {
            SupportUiEvent.OnCallClick -> callToSupport()
            SupportUiEvent.OnCloseClick -> sendEvent(UiEvent.NavigateBack)
        }
    }

    private fun callToSupport() {
        val phone = remoteConfigManager.getProperty(
            RemoteProperty.CustomerSupportInfoProperty
        )?.contactPhone

        phone?.let {
            sendEvent(
                SideEffect.StartIntent(
                    Intent(Intent.ACTION_DIAL, "${INTENT_DIAL_PREFIX}$it".toUri())
                )
            )
        }
    }
}
