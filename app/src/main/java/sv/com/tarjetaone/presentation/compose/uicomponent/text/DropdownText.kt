package sv.com.tarjetaone.presentation.compose.uicomponent.text

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.presentation.compose.modifier.fieldDecoration
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.FieldDecorationType

@Suppress("kotlin:S107")
@Composable
fun DropdownOverflowText(
    modifier: Modifier = Modifier,
    value: String,
    placeholder: String,
    textStyle: TextStyle = MaterialTheme.typography.bodyMedium,
    fieldDecorationType: FieldDecorationType = FieldDecorationType.ELEVATED,
    trailingIcon: @Composable (() -> Unit)? = null,
    enabled: Boolean = true,
    hasError: Boolean = false,
    onClick: (() -> Unit)? = null,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .heightIn(MaterialTheme.customDimens.dimen48)
            .fieldDecoration(
                decorationType = fieldDecorationType,
                hasError = hasError,
                enabled = enabled
            )
            .then(
                if (enabled && onClick != null) Modifier.clickable { onClick() } else Modifier
            )
            .background(
                if (enabled) Color.White else MaterialTheme.customColors.gray200,
                MaterialTheme.shapes.small,
            )
            .padding(
                start = MaterialTheme.customDimens.dimen16,
                end = MaterialTheme.customDimens.dimen16
            ),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            modifier = Modifier.weight(1f),
            text = value.ifEmpty { placeholder },
            style = textStyle.copy(
                color = when {
                    hasError -> MaterialTheme.colorScheme.error
                    !enabled -> MaterialTheme.customColors.steelGray
                    value.isEmpty() -> MaterialTheme.colorScheme.onSecondaryContainer
                    else -> MaterialTheme.customColors.textBodyLight
                },
            ),
            maxLines = ONE_VALUE,
            overflow = TextOverflow.Ellipsis,
        )
        trailingIcon?.invoke()
    }
}

@Preview
@Composable
fun PreviewOverflowBasicTextField() {
    DropdownOverflowText(
        modifier = Modifier,
        placeholder = PLACEHOLDER_TEXT,
        value = PLACEHOLDER_VALUE,
        trailingIcon = {
            Icon(
                painter = painterResource(id = R.drawable.ic_arrow_down_filled),
                contentDescription = null,
                tint = Color.Gray,
                modifier = Modifier.padding(start = MaterialTheme.customDimens.dimen8)
            )
        },
    )
}

@Preview
@Composable
fun PreviewErrorOverflowBasicTextField() {
    DropdownOverflowText(
        modifier = Modifier,
        placeholder = PLACEHOLDER_TEXT,
        value = PLACEHOLDER_VALUE,
        trailingIcon = {
            Icon(
                painter = painterResource(id = R.drawable.ic_arrow_down_filled),
                contentDescription = null,
                tint = Color.Gray,
                modifier = Modifier.padding(start = MaterialTheme.customDimens.dimen8)
            )
        },
        hasError = true,
    )
}

@Preview
@Composable
fun PreviewDisabledOverflowBasicTextField() {
    DropdownOverflowText(
        modifier = Modifier,
        placeholder = PLACEHOLDER_TEXT,
        value = PLACEHOLDER_VALUE,
        trailingIcon = {
            Icon(
                painter = painterResource(id = R.drawable.ic_arrow_down_filled),
                contentDescription = null,
                tint = Color.Gray,
                modifier = Modifier.padding(start = MaterialTheme.customDimens.dimen8)
            )
        },
        enabled = false,
    )
}

private const val PLACEHOLDER_TEXT = "Select an option"
private const val PLACEHOLDER_VALUE =
    "This is a very long text that should be truncated if it exceeds the width of the screen"
