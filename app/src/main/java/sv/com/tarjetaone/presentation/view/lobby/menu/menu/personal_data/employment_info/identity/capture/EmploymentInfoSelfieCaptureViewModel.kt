package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.employment_info.identity.capture

import android.graphics.Bitmap
import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.presentation.view.common.selfiecapture.SelfieCaptureBaseViewModel
import javax.inject.Inject

@HiltViewModel
class EmploymentInfoSelfieCaptureViewModel @Inject constructor(
    facephiResultHandler: FacephiResultHandler,
    savedStateHandle: SavedStateHandle
) : SelfieCaptureBaseViewModel(facephiResultHandler) {

    private val args = EmploymentInfoSelfieCaptureFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onSuccessfulSelfieCapture(selfie: Bitmap) {
        sendEvent(
            UiEvent.Navigate(
                EmploymentInfoSelfieCaptureFragmentDirections
                    .actionPrepareForPictureToSelfieConfirmationEmploymentInfoFragment(
                        jobId = args.jobId,
                        contactId = args.contactId,
                        contactTypeCode = args.contactTypeCode
                    )
            )
        )
    }
}
