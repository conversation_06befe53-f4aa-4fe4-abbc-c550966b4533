package sv.com.tarjetaone.presentation.compose.uicomponent.input

import android.view.KeyEvent
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.input.key.onKeyEvent
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.core.text.isDigitsOnly
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.InfoStatus
import sv.com.tarjetaone.presentation.compose.util.statusColor

/**
 * A composable function that represents a single digit input field.
 *
 * To ensure a consistent capture of user input, it is guaranteed that only one of the two value
 * related callbacks will be invoked at a time.
 *
 * @param onNumberChange Callback invoked when a single digit is entered or deleted.
 * @param onValueChange Callback invoked when the new text is longer than 1 character and this field
 * is empty ([number] is `null`).
 */
@Suppress("kotlin:S107")
@Composable
fun DigitInputField(
    number: Int?,
    focusRequester: FocusRequester,
    onFocusChange: (Boolean) -> Unit,
    onNumberChange: (Int?) -> Unit,
    onValueChange: (String) -> Unit,
    onKeyboardBack: () -> Unit,
    modifier: Modifier = Modifier,
    infoStatus: InfoStatus = InfoStatus.None
) {
    val value by remember(number) {
        mutableStateOf(
            TextFieldValue(
                text = number?.toString().orEmpty(),
                selection = TextRange(index = if (number != null) 1 else 0) // Set the cursor at the end of the text
            )
        )
    }
    var isFocused by remember { mutableStateOf(false) }
    BasicTextField(
        modifier = modifier
            .width(MaterialTheme.customDimens.dimen45)
            .height(MaterialTheme.customDimens.dimen48)
            .focusRequester(focusRequester)
            .onFocusChanged {
                isFocused = it.isFocused
                onFocusChange(it.isFocused)
            }
            .onKeyEvent { event ->
                val deleteWasPressed = event.nativeKeyEvent.keyCode == KeyEvent.KEYCODE_DEL
                if (deleteWasPressed && number == null) {
                    onKeyboardBack()
                }
                false
            },
        value = value,
        onValueChange = { newText ->
            val newNumber = newText.text
            when {
                newNumber.length > ONE_VALUE && number == null -> {
                    // If the new text is longer than 1 character and this field's number is null,
                    // it can be assumed it came from a paste action
                    onValueChange(newNumber)
                }
                newNumber.length <= ONE_VALUE && newNumber.isDigitsOnly() -> {
                    onNumberChange(newNumber.toIntOrNull())
                }
            }
        },
        textStyle = MaterialTheme.typography.displayMedium.copy(
            color = MaterialTheme.colorScheme.secondary,
            textAlign = TextAlign.Center
        ),
        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
        decorationBox = { innerTextField ->
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(MaterialTheme.customDimens.dimen1)
                    .border(
                        width = MaterialTheme.customDimens.dimen1,
                        color = when {
                            infoStatus == InfoStatus.None && number != null -> {
                                MaterialTheme.colorScheme.secondary
                            }
                            infoStatus == InfoStatus.None && isFocused -> {
                                MaterialTheme.colorScheme.onSecondaryContainer
                            }
                            else -> {
                                infoStatus.statusColor()
                            }
                        },
                        shape = MaterialTheme.shapes.small
                    )
                    .clip(MaterialTheme.shapes.small)
                    .background(MaterialTheme.customColors.defaultSurface)
            ) {
                innerTextField()
            }
        }
    )
}

@Preview
@Composable
private fun DigitInputFieldPreview() {
    OneAppTheme {
        DigitInputField(
            number = 1,
            focusRequester = remember { FocusRequester() },
            onFocusChange = {},
            onNumberChange = {},
            onKeyboardBack = {},
            onValueChange = {},
        )
    }
}
