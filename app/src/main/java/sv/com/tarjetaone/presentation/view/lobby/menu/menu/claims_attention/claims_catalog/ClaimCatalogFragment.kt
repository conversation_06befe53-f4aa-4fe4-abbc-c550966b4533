package sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.claims_catalog

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import dagger.hilt.android.AndroidEntryPoint
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.base.BaseFragment
import sv.com.tarjetaone.databinding.FragmentComposeBinding
import sv.com.tarjetaone.presentation.view.utils.requireLobbyActivity

@AndroidEntryPoint
class ClaimCatalogFragment : BaseFragment<FragmentComposeBinding>(
    R.layout.fragment_compose
) {
    override val viewModel: ClaimsCatalogViewModel by viewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requireLobbyActivity().hideNavigationBottom()
        binding.composeView.setContentScreen {
            val claimItems by viewModel.claimsList.collectAsStateWithLifecycle()
            ClaimsCatalogScreen(
                claimItems = claimItems,
                onEvent = viewModel::onEvent
            )
        }
    }
}
