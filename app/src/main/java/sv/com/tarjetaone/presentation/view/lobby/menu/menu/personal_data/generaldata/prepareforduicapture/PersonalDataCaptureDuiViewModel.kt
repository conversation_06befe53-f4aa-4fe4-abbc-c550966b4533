package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.generaldata.prepareforduicapture

import androidx.lifecycle.SavedStateHandle
import com.facephi.fphiselphidwidgetcore.WidgetSelphIDResult
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.CommonDialogWithIconParams
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.core.utils.facephi.getDialogParamsForException
import sv.com.tarjetaone.core.utils.openAppSettings
import sv.com.tarjetaone.presentation.helpers.UiText
import javax.inject.Inject

@HiltViewModel
class PersonalDataCaptureDuiViewModel @Inject constructor(
    private val facephiResultHandler: FacephiResultHandler,
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {

    private val args = PersonalDataCaptureDuiFragmentArgs.fromSavedStateHandle(savedStateHandle)

    private fun onCameraPermissionDenied(showRationale: Boolean) {
        sendEvent(
            UiEvent.ShowCommonDialog(
                CommonDialogWithIconParams(
                    title = UiText.StringResource(R.string.camera_access),
                    message = UiText.StringResource(R.string.camera_rationale_document),
                    buttonText = UiText.StringResource(
                        if (showRationale) R.string.accept_label else R.string.settings
                    ),
                    onButtonClick = {
                        if (!showRationale) sendEvent(UiEvent.StartIntent(openAppSettings()))
                    }
                )
            )
        )
    }

    private fun onDocumentCaptureFailed(result: WidgetSelphIDResult) {
        facephiResultHandler.onDocumentCaptureFailed(result) {
            sendEvent(UiEvent.ShowCommonDialog(getDialogParamsForException(it)))
        }
    }

    private fun onDocumentCaptured(result: WidgetSelphIDResult) {
        if (facephiResultHandler.onDocumentCaptured(result) != null) {
            sendEvent(
                UiEvent.Navigate(
                    PersonalDataCaptureDuiFragmentDirections
                        .actionPersonalDataCaptureDuiFragmentToReadabilityConfirmationPersonalDataFragment(
                            biometryProcessId = args.biometryProcessId
                        )
                )
            )
        } else {
            sendEvent(
                UiEvent.ShowCommonDialog(
                    CommonDialogWithIconParams(
                        isDismissible = false,
                        icon = R.drawable.ic_error_yellow,
                        buttonColor = R.color.yellow_alert_button,
                        title = UiText.StringResource(R.string.error_doc_picture_title),
                        message = UiText.StringResource(R.string.error_doc_picture_desc),
                        buttonText = UiText.StringResource(R.string.facephi_dialog_agree)
                    )
                )
            )
        }
    }

    fun onEvent(event: PersonalDataCaptureDuiUiEvent) {
        when (event) {
            PersonalDataCaptureDuiUiEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
            is PersonalDataCaptureDuiUiEvent.OnCameraPermissionDenied -> onCameraPermissionDenied(event.showRationale)
            is PersonalDataCaptureDuiUiEvent.OnDocumentCaptureFailed -> onDocumentCaptureFailed(event.result)
            is PersonalDataCaptureDuiUiEvent.OnDocumentCaptured -> onDocumentCaptured(event.result)
        }
    }
}
