package sv.com.tarjetaone.presentation.view.lobby.menu.home.account_status

import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.domain.entities.response.DataAccountStateUI

data class AccountStatusUiState(
    val accountNumber: String = EMPTY_STRING,
    val accountStates: List<DataAccountStateUI> = emptyList(),
    val accountStateSelected: DataAccountStateUI? = null,
)