package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.util.Spacer8

@Composable
fun PasswordCondition(contentTitle: String, conditionIcon: Boolean) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.padding(
            vertical = MaterialTheme.customDimens.dimen3
        )
    ) {
        Icon(
            modifier = Modifier.size(MaterialTheme.customDimens.dimen14),
            painter = painterResource(
                id = R.drawable.ic_baseline_check_24
            ),
            contentDescription = EMPTY_STRING,
            tint = if (conditionIcon) {
                MaterialTheme.customColors.successLightContainer
            } else {
                MaterialTheme.customColors.secondarySolid
            }
        )
        Spacer8()
        Text(
            text = contentTitle,
            style = MaterialTheme.typography.labelSmall.copy(
                fontSize = MaterialTheme.customDimensSp.sp10
            )
        )
    }
}

@Preview
@Composable
fun PasswordConditionPreview() {
    PasswordCondition(
        contentTitle = stringResource(
            id = R.string.change_password_condition_one
        ),
        conditionIcon = false
    )
}