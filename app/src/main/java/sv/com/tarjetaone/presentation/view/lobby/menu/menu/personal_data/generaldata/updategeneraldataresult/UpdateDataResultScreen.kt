package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.generaldata.updategeneraldataresult

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.FailureFullScreen
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.OneBackHandler
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.common.confirmation_gradient.SuccessGradientScreen

@Composable
fun UpdateDataResultScreen(viewModel: UpdateDataResultViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    UpdateDataResultScreenContent(
        isSuccessResult = uiState.isSuccessResult,
        onEvent = viewModel::onEvent
    )
}

@Composable
fun UpdateDataResultScreenContent(
    isSuccessResult: Boolean,
    onEvent: (UpdateDataResultUiEvent) -> Unit = {}
) {
    OneBackHandler()
    if (isSuccessResult) {
        SuccessGradientScreen(
            message = stringResource(id = R.string.successful_document_update),
            buttonText = stringResource(id = R.string.done_button),
            onButtonClick = { onEvent(UpdateDataResultUiEvent.OnDoneClick) }
        )
    } else {
        FailureFullScreen(
            title = UiText.StringResource(R.string.failure_update_data_title),
            subtitle = UiText.StringResource(R.string.failure_update_data_subtitle),
            description = UiText.StringResource(R.string.failure_update_data_description),
            textButton = UiText.StringResource(R.string.ok_message),
            buttonVariant = ButtonVariant.SECONDARY_VARIANT,
            onButtonClick = { onEvent(UpdateDataResultUiEvent.OnDoneClick) }
        )
    }
}

@Preview
@Composable
fun UpdateDataResultScreenPreview() {
    OneAppTheme {
        UpdateDataResultScreenContent(isSuccessResult = true)
    }
}
