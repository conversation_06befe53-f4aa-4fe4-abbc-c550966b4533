package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.report.identity.capture

import android.os.Bundle
import android.view.View
import androidx.fragment.app.viewModels
import dagger.hilt.android.AndroidEntryPoint
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.base.BaseFragment
import sv.com.tarjetaone.databinding.FragmentComposeBinding
import sv.com.tarjetaone.presentation.view.common.selfiecapture.SelfieCaptureScreen

@AndroidEntryPoint
class ReportCaptureSelfieFragment : BaseFragment<FragmentComposeBinding>(
    R.layout.fragment_compose
) {
    override val viewModel: ReportCaptureSelfieViewModel by viewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.composeView.setContentScreen {
            SelfieCaptureScreen(viewModel)
        }
    }
}
