package sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.claims_catalog

import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.AppConstants.SUCCESS_RESPONSE_CODE
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.response.CatalogType
import sv.com.tarjetaone.domain.usecases.catalog.GetCatalogUseCase
import sv.com.tarjetaone.presentation.helpers.catalogIcon
import sv.com.tarjetaone.presentation.helpers.drawableResFromString
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.util.ClaimCatalogMenuItem
import javax.inject.Inject

@HiltViewModel
class ClaimsCatalogViewModel @Inject constructor(
    private val getCatalogUseCase: GetCatalogUseCase,
    private val gson: Gson
) : BaseViewModel() {
    private val _claimsList: MutableStateFlow<List<ClaimCatalogMenuItem>> = MutableStateFlow(emptyList())
    val claimsList = _claimsList.asStateFlow()

    init {
        fetchClaimsCatalog()
    }

    private fun fetchClaimsCatalog() {
        viewModelScope.launch {
            sendEvent(UiEvent.Loading(true))
            getCatalogUseCase(CatalogType.MANAGEMENT_GROUP).executeUseCase { response ->
                sendEvent(UiEvent.Loading(false))
                if (response.statusCatalog?.responseStatus?.code == SUCCESS_RESPONSE_CODE) {
                    _claimsList.value = response.dataCatalog?.catalogItemsCollection?.map {
                        ClaimCatalogMenuItem(
                            id = it.id.toIntOrNull().orZero(),
                            text = it.name.orEmpty(),
                            icon = drawableResFromString(it.catalogIcon(gson)?.icon.orEmpty())
                        )
                    } ?: emptyList()
                } else {
                    showUpsErrorMessage(false) { sendEvent(UiEvent.NavigateBack) }
                }
            }
        }
    }

    private fun onMenuItemClick(item: ClaimCatalogMenuItem) {
        sendEvent(
            UiEvent.Navigate(
                ClaimCatalogFragmentDirections
                    .actionClaimCatalogFragmentToOptionsClaimFragment(item.id, item.text)
            )
        )
    }

    fun onEvent(event: ClaimCatalogUiEvent) {
        when (event) {
            is ClaimCatalogUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            is ClaimCatalogUiEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
            is ClaimCatalogUiEvent.OnMenuItemClick -> onMenuItemClick(event.item)
        }
    }
}
