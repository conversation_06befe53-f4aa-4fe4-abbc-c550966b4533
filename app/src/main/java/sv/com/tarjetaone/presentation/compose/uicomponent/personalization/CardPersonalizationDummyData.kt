package sv.com.tarjetaone.presentation.compose.uicomponent.personalization

import androidx.compose.runtime.mutableStateListOf
import sv.com.tarjetaone.common.utils.AppConstants.BLANK_SPACE
import sv.com.tarjetaone.common.utils.CardColor
import sv.com.tarjetaone.presentation.view.utils.NamesCard

@Suppress("S1192")
object CardPersonalizationDummyData {

    private const val WHITE_COLOR = "#FFFFFF"

    val names = mutableStateListOf(
        NamesCard("<PERSON>", selected = true),
        NamesCard("<PERSON>", selected = false),
        NamesCard("<PERSON>", selected = false),
        NamesCard("Solis", selected = false),
        NamesCard("Otorrinolaringologo", selected = true)
    )
    val creditCardName = names.filter { it.selected }.joinToString(BLANK_SPACE) { it.name }
    val colors = listOf(
        CardColor(
            id = "1",
            iconColor = WHITE_COLOR,
            textColor = WHITE_COLOR,
            colorName = "Color 1",
            colors = listOf(
                "#FF642D",
                "#FF2A4A",
                "#CA3387",
                "#6645FB"
            )
        ),
        CardColor(
            id = "2",
            iconColor = WHITE_COLOR,
            textColor = WHITE_COLOR,
            colorName = "Color 2",
            colors = listOf(
                "#F5F47E",
                "#34EECB",
            )
        ),
        CardColor(
            id = "3",
            iconColor = WHITE_COLOR,
            textColor = WHITE_COLOR,
            colorName = "Color 3",
            colors = listOf(
                WHITE_COLOR,
            )
        ),
        CardColor(
            id = "4",
            iconColor = WHITE_COLOR,
            textColor = WHITE_COLOR,
            colorName = "Color 4",
            colors = listOf(
                "#F6A7D7"
            )
        ),
        CardColor(
            id = "5",
            textColor = WHITE_COLOR,
            iconColor = WHITE_COLOR,
            colorName = "Color 5",
            colors = listOf(
                "#E31E30"
            )
        ),
        CardColor(
            id = "6",
            textColor = WHITE_COLOR,
            iconColor = WHITE_COLOR,
            colorName = "Color 6",
            colors = listOf(
                "#1F1E36"
            )
        )
    )
}
