package sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.contract.sign

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.bitmap.BitmapUtils
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.presentation.view.common.sign.SignContractViewModel
import javax.inject.Inject

@HiltViewModel
class ProgramProtectionSignViewModel @Inject constructor(
    imageUtils: ImageUtils,
    bitmapUtils: BitmapUtils,
    savedStateHandle: SavedStateHandle,
) : SignContractViewModel(bitmapUtils, imageUtils) {
    private val args = ProgramProtectionSignFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onSignClickAction(signFileName: String) {
        args.fraud?.let {
            sendEvent(
                UiEvent.Navigate(
                    ProgramProtectionSignFragmentDirections
                        .actionSignProtectionProgramFragmentToProtectionIdentityCaptureFragment(
                            fProtectionId = it.fraudProtectionId.orZero(),
                            signFileName = signFileName,
                            cardId = it.creditCardId.orZero()
                        )
                )
            )
        }
    }

    override fun getCustomerName(): String = baseSharedPrefs.getUserProfile()?.let {
        "${it.firstName.orEmpty()} ${it.firstSurname.orEmpty()}"
    }.orEmpty()
}