package sv.com.tarjetaone.presentation.compose.uicomponent.dialog

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.DatePicker
import androidx.compose.material3.DatePickerDefaults
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors

/**
 * Composable that displays a dialog with a single date picker based on the Material3 library.
 * The dialog has two buttons, one to confirm the selected date and another to cancel the selection.
 *
 * @param confirmButtonText The text to display in the confirm button.
 * @param cancelButtonText The text to display in the cancel button.
 * @param onDismissRequest The callback to be invoked when the dialog is dismissed.
 * @param onConfirmButtonClick The callback to be invoked when the confirm button is clicked.
 * @param onCancelButtonClick The callback to be invoked when the cancel button is clicked.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SingleDatePickerDialog(
    initialDate: Long? = null,
    confirmButtonText: String,
    cancelButtonText: String,
    onDismissRequest: () -> Unit = {},
    onConfirmButtonClick: (Long?) -> Unit = {},
    onCancelButtonClick: () -> Unit = {}
) {
    val datePickerState = rememberDatePickerState(initialSelectedDateMillis = initialDate)
    val confirmEnabled = remember {
        derivedStateOf { datePickerState.selectedDateMillis != null }
    }
    
    DatePickerDialog(
        shape = MaterialTheme.shapes.large,
        colors = DatePickerDefaults.colors(containerColor = MaterialTheme.colorScheme.background),
        dismissButton = {
            Button(
                modifier = Modifier.fillMaxWidth(0.45f),
                shape = MaterialTheme.shapes.small,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.customColors.lightGray,
                    contentColor = MaterialTheme.colorScheme.secondary
                ),
                onClick = onCancelButtonClick
            ) {
                Text(text = cancelButtonText)
            }
        },
        confirmButton = {
            Button(
                modifier = Modifier.fillMaxWidth(0.5f),
                shape = MaterialTheme.shapes.small,
                enabled = confirmEnabled.value,
                onClick = {
                    onConfirmButtonClick(datePickerState.selectedDateMillis)
                    onDismissRequest()
                }
            ) {
                Text(text = confirmButtonText)
            }
        },
        onDismissRequest = onDismissRequest
    ) {
        DatePicker(
            state = datePickerState,
            title = null,
            headline = null,
            showModeToggle = false,
            colors = DatePickerDefaults.colors(
                containerColor = MaterialTheme.colorScheme.background,
                navigationContentColor = MaterialTheme.colorScheme.primary,
                selectedDayContentColor = MaterialTheme.colorScheme.onPrimary,
                selectedDayContainerColor = MaterialTheme.colorScheme.primary,
                todayContentColor = MaterialTheme.colorScheme.primary,
                todayDateBorderColor = MaterialTheme.customColors.lightGray
            )
        )
    }
}

@Preview
@Composable
private fun SingleDatePickerDialogPreview() {
    OneAppTheme {
        SingleDatePickerDialog(
            confirmButtonText = "Aceptar",
            cancelButtonText = "Salir"
        )
    }
}
