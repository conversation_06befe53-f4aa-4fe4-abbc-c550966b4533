package sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen

import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.SetStatusBarAppearance
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.common.TopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.common.topbar.component.TopBarAction

/**
 * Simple screen composable to create a full size screen with a top app bar and avoid some
 * boilerplate code.
 */
@Suppress("kotlin:S107")
@Composable
fun ScreenWithTopAppBar(
    backgroundColor: Color = MaterialTheme.colorScheme.surface,
    topBarBackgroundColor: Color = backgroundColor,
    textColor: Color = MaterialTheme.colorScheme.onSurface,
    @DrawableRes headerIcon: Int = R.drawable.one_header_icon,
    contentPadding: PaddingValues = PaddingValues(MaterialTheme.customDimens.dimen32),
    isLeftButtonVisible: Boolean = true,
    isRightButtonVisible: Boolean = true,
    leftActionButton: TopBarAction = TopBarAction.BACK,
    rightActionButton: TopBarAction = TopBarAction.SUPPORT,
    isProgressbarVisible: Boolean = false,
    progress: Float = 0f,
    title: String? = null,
    onLeftButtonClick: () -> Unit,
    onRightButtonClick: () -> Unit,
    content: @Composable () -> Unit
) {
    OneBackHandler {
        if (isLeftButtonVisible) {
            onLeftButtonClick()
        }
    }
    SetStatusBarAppearance()
    Column(
        modifier = Modifier
            .background(backgroundColor)
            .fillMaxSize()
            .navigationBarsPadding()
    ) {
        TopAppBar(
            backgroundColor = topBarBackgroundColor,
            textColor = textColor,
            headerIcon = headerIcon,
            contentPadding = contentPadding,
            isLeftButtonVisible = isLeftButtonVisible,
            isRightButtonVisible = isRightButtonVisible,
            leftActionButton = leftActionButton,
            rightActionButton = rightActionButton,
            isProgressbarVisible = isProgressbarVisible,
            progress = progress,
            title = title,
            onLeftButtonClick = onLeftButtonClick,
            onRightButtonClick = onRightButtonClick
        )
        content()
    }
}

@Preview
@Composable
private fun ScreenWithTopAppBarPreview() {
    OneAppTheme {
        ScreenWithTopAppBar(
            title = "Screen wrapper with top app bar",
            onLeftButtonClick = {},
            onRightButtonClick = {}
        ) {
            // Content goes here
        }
    }
}
