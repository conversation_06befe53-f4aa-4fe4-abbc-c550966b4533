package sv.com.tarjetaone.presentation.compose.modifier

import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.ClipOp
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.DefaultShadowColor
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.addOutline
import androidx.compose.ui.graphics.drawscope.clipPath
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import sv.com.tarjetaone.common.utils.AppConstants.TWO_DOT_TWO_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_FLOAT_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE

/**
 * A modifier that applies a shadow only to the bottom side of a view avoiding
 * unwanted behaviours with native shadow.
 *
 * @param elevation The elevation of the shadow.
 * @param shape The shape of the shadow.
 * @param clip Whether to clip the content to the shape.
 * @param ambientColor The color of the ambient shadow.
 * @param spotColor The color of the spot shadow.
 */
fun Modifier.shadowWithClipIntersect(
    elevation: Dp,
    shape: Shape = RectangleShape,
    clip: Boolean = elevation > ZERO_VALUE.dp,
    ambientColor: Color = DefaultShadowColor,
    spotColor: Color = DefaultShadowColor,
): Modifier = this
    .drawWithCache {
        //  bottom shadow offset in Px based on elevation
        val bottomOffsetPx = elevation.toPx() * TWO_DOT_TWO_VALUE
        // Adjust the size to extend the bottom by the bottom shadow offset
        val adjustedSize = Size(size.width, size.height + bottomOffsetPx)
        val outline = shape.createOutline(adjustedSize, layoutDirection, this)
        val path = Path().apply {
            moveTo(ZERO_FLOAT_VALUE, size.height / TWO_VALUE)
            addOutline(outline)
        }
        onDrawWithContent {
            clipPath(path, ClipOp.Intersect) {
                <EMAIL>()
            }
        }
    }
    .shadow(elevation, shape, clip, ambientColor, spotColor)