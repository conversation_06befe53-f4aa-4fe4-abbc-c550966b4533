package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.references

import androidx.compose.runtime.Stable
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.response.ReferenceUI
import sv.com.tarjetaone.domain.entities.response.ReferencesTypesUI
import sv.com.tarjetaone.presentation.helpers.ReferenceType

@Stable
data class ReferencesUiState(
    val references: List<ReferencesTypesUI> = emptyList(),
    val referenceSelected: ReferenceUI? = null,
    val showDeleteConfirmationDialog: Boolean = false,
    val isSnackBarVisible: Boolean = false
)

fun ReferencesUiState.getFamilyReferences(): List<ReferenceUI> =
    references.find { it.globalReferenceTypeName == ReferenceType.FAMILY.value }?.reference.orEmpty()

fun ReferencesUiState.getPersonalReferences(): List<ReferenceUI> =
    references.find { it.globalReferenceTypeName == ReferenceType.PERSONAL.value }?.reference.orEmpty()

fun ReferencesUiState.getMaxFamilyReferences(): Int =
    references.find { it.globalReferenceTypeName == ReferenceType.FAMILY.value }?.maxActiveRows.orZero()

fun ReferencesUiState.getMaxPersonalReferences(): Int =
    references.find { it.globalReferenceTypeName == ReferenceType.PERSONAL.value }?.maxActiveRows.orZero()
