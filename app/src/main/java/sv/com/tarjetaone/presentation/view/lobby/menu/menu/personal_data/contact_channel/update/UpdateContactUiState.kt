package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.contact_channel.update

import sv.com.tarjetaone.domain.entities.response.ContactType
import sv.com.tarjetaone.presentation.helpers.UiText

data class UpdateContactUiState(
    val contactType: ContactType = ContactType.Email,
    val contact: String = "",
    val isValidContact: Boolean = false,
    val isValidatingContact: Boolean = false,
    val errorMessage: UiText? = null,
    val showOtpMethodDialog: Boolean = false,
    val contactError: UiText? = null
) {
    fun hasError(): <PERSON><PERSON>an {
        return isValidContact.not()
                && !isValidatingContact
                && contact.isNotEmpty()
    }

    fun shouldEnableContinueButton(): Boolean {
        return isValidContact && !isValidatingContact
    }
}
