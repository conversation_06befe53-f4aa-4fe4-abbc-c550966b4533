package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.queryform

import androidx.compose.runtime.toMutableStateList
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldsResponseUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFormType
import sv.com.tarjetaone.domain.usecases.automaticpayments.servicespayment.fields.PXFieldsUseCase
import sv.com.tarjetaone.domain.usecases.automaticpayments.servicespayment.invoiceselection.BalanceClientUseCase
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.queryform.component.PXFieldUiState
import javax.inject.Inject

@HiltViewModel
class ServiceQueryFormViewModel @Inject constructor(
    private val pxFields: PXFieldsUseCase,
    private val balanceClient: BalanceClientUseCase,
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(ServiceQueryFormUiState())
    val uiState = _uiState.asStateFlow()

    private val args = ServiceQueryFormFragmentArgs.fromSavedStateHandle(savedStateHandle)
    private var fieldsResponse: PXFieldsResponseUI? = null

    private fun onStart() {
        if (fieldsResponse != null) return // avoid loading fields again
        viewModelScope.launch {
            sendEvent(UiEvent.Loading(true))
            pxFields(args.service.catalogServiceCode).executeUseCase(
                onApiErrorAction = { _, _, _, _ ->
                    showUpsErrorMessage(false) {
                        sendEvent(UiEvent.NavigateBack)
                    }
                },
                onSuccessAction = { response ->
                    fieldsResponse = response
                    sendEvent(UiEvent.Loading(false))
                    val form = response.dataSets.data.values.firstOrNull()
                    _uiState.update { state ->
                        state.copy(
                            serviceName = args.service.serviceName,
                            categoryName = args.service.categoriesName,
                            formType = form?.type,
                            fields = form?.fields
                                ?.map { PXFieldUiState(it.key, it.value) }
                                .orEmpty()
                                .toMutableStateList(),
                            queryOptions = if (form?.type == PXFormType.SelectOne) {
                                form.fields.entries.map { it.toPair() }
                            } else emptyList()
                        )
                    }
                }
            )
        }
    }

    private fun onPXFieldChange(
        index: Int,
        typedValue: String,
        selectedValue: Pair<String, PXFieldUI>?
    ) {
        _uiState.value.fields.apply {
            this[index] = this[index].copy(
                typedValue = typedValue,
                selectedValue = selectedValue
            )
        }
        _uiState.update { it.copy(queryFormError = null) }
    }

    private fun onQueryOptionChange(queryOption: Pair<String, PXFieldUI>) {
        _uiState.update {
            it.copy(
                selectedQueryOption = queryOption,
                reference = if (queryOption == it.selectedQueryOption) it.reference else EMPTY_STRING,
                queryFormError = null
            )
        }
    }

    private fun onReferenceChange(reference: String) {
        _uiState.update {
            it.copy(
                reference = reference,
                queryFormError = null
            )
        }
    }

    private fun onContinueClick() {
        viewModelScope.launch {
            fieldsResponse?.let { fields ->
                val request = uiState.value.toBalanceClientRequest(fields)
                sendEvent(UiEvent.Loading(true))
                balanceClient(request).executeUseCase(
                    onApiErrorAction = { _, error, _, _ ->
                        sendEvent(UiEvent.Loading(false))
                        if (error?.code in NON_FRIENDLY_ERROR_CODES) {
                            showUpsErrorMessage()
                        } else {
                            _uiState.update { it.copy(queryFormError = error?.result) }
                        }
                    },
                    onSuccessAction = {
                        sendEvent(UiEvent.Loading(false))
                        sendEvent(
                            UiEvent.Navigate(
                                ServiceQueryFormFragmentDirections
                                    .actionServiceQueryFragmentToInvoiceSelectionFragment(
                                        balanceClient = it,
                                        serviceCategoryName = uiState.value.categoryName,
                                    )
                            )
                        )
                    }
                )
            }
        }
    }

    fun onEvent(event: ServiceQueryFormUiEvent) {
        when (event) {
            ServiceQueryFormUiEvent.OnStart -> onStart()
            ServiceQueryFormUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            ServiceQueryFormUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            is ServiceQueryFormUiEvent.OnPXFieldChange -> {
                onPXFieldChange(event.index, event.typedValue, event.selectedValue)
            }
            ServiceQueryFormUiEvent.OnContinueClick -> onContinueClick()
            is ServiceQueryFormUiEvent.OnQueryOptionChange -> onQueryOptionChange(event.selectedQueryOption)
            is ServiceQueryFormUiEvent.OnReferenceChange -> onReferenceChange(event.reference)
        }
    }

    companion object {
        private val NON_FRIENDLY_ERROR_CODES = listOf("500")
    }
}
