package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform.update

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.ONE_HUNDRED_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE_DOUBLE
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.request.automaticpayments.paymentaffiliation.contract.ContractAffiliationAction
import sv.com.tarjetaone.domain.usecases.automaticpayments.paymentaffiliation.affiliationform.NotificationChannelsUseCase
import sv.com.tarjetaone.domain.usecases.automaticpayments.servicespayment.invoiceselection.infoproducts.InfoProductsUseCase
import sv.com.tarjetaone.domain.utils.automaticpayments.servicespayment.invoiceselection.ProductTypeCode
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform.AffiliationFormViewModel

@HiltViewModel
class UpdateAffiliationViewModel @Inject constructor(
    infoProductsUseCase: InfoProductsUseCase,
    notificationChannelsUseCase: NotificationChannelsUseCase,
    savedStateHandle: SavedStateHandle,
) : AffiliationFormViewModel(
    infoProductsUseCase,
    notificationChannelsUseCase
) {

    private val args = UpdateAffiliationFragmentArgs.fromSavedStateHandle(savedStateHandle)
    private val contract = args.contract

    override fun updateUiStateOnInit() {
        val maxAmountEnabled = contract.maxAmount.toDoubleOrNull().orZero() > ZERO_VALUE_DOUBLE
        val product = contract.contractProduct.let { product ->
            if (contract.contractProductType == ProductTypeCode.CreditCards.value) {
                _uiState.value.userProducts.firstOrNull { it.contractTypeID == product }
            } else {
                _uiState.value.userProducts.firstOrNull { it.productCode == product }
            }
        }

        _uiState.update { state ->
            state.copy(
                isMaxAmountEnabled = maxAmountEnabled,
                maxChargeAmount = if (maxAmountEnabled)
                    contract.maxAmount.toDoubleOrNull()
                        ?.times(ONE_HUNDRED_VALUE)
                        ?.toInt()
                        ?.toString()
                        .orEmpty()
                else EMPTY_STRING,
                selectedProduct = product,
                selectedProductType = product?.let {
                    state.userProducts.firstOrNull { productItem ->
                        productItem.productTypeCode.value == it.productTypeCode.value
                    }?.productTypeCode
                },
                selectedNotificationChannel = state.notificationChannels.firstOrNull { channel ->
                    channel.type.aliases.contains(contract.parameters.find {
                        it.category.toBoolean()
                    }?.value.orEmpty())
                },
                showTermsAndConditions = false,
            )
        }
    }

    override fun navigateToAffiliationTerms() {
        sendEvent(
            UiEvent.Navigate(
                UpdateAffiliationFragmentDirections
                    .updateAffiliationFragmentToPaymentAffiliationTermsFragment()
            )
        )
    }

    override fun onContinueClick() {
        sendEvent(
            UiEvent.Navigate(
                UpdateAffiliationFragmentDirections.updateAffiliationFragmentToPaymentAffPrepareForPictureFragment(
                    selectedPaymentMethod = _uiState.value.getSelectedPaymentMethod(),
                    contractAction = ContractAffiliationAction.Update(
                        request = _uiState.value.toUpdateAffiliationContractRequestUI(
                            contract = contract
                        )
                    )
                )
            )
        )
    }
}
