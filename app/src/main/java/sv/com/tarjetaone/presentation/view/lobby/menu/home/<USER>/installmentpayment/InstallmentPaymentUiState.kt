package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.installmentpayment

import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE_DOUBLE
import sv.com.tarjetaone.domain.entities.response.InstallmentTermsUI

data class InstallmentPaymentUiState(
    val storeName: String = EMPTY_STRING,
    val installments: List<InstallmentTermsUI> = emptyList(),
    val selectedInstallment: InstallmentTermsUI = InstallmentTermsUI(),
    val totalAmount: Double = ZERO_VALUE_DOUBLE,
    val availableInstallmentSlots: Int = ZERO_VALUE,
    val showBottomSheet: Boolean = false,
    val bottomSheetType: InstallmentBottomSheetType? = null
)

sealed class InstallmentBottomSheetType {
    data object Info : InstallmentBottomSheetType()
    data object Confirmation : InstallmentBottomSheetType()
}
