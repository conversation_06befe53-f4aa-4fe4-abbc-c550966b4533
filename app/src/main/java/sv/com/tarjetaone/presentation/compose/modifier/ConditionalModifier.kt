package sv.com.tarjetaone.presentation.compose.modifier

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier

@Composable
fun Modifier.conditional(
    condition: <PERSON><PERSON>an,
    onTrue: @Composable Modifier.() -> Modifier = { this },
    onFalse: @Composable Modifier.() -> Modifier = { this }
): Modifier {
    return if (condition) {
        this.then(onTrue(Modifier))
    } else {
        this.then(onFalse(Modifier))
    }
}
