package sv.com.tarjetaone.presentation.view.common.contracts

sealed interface ContractListUiEvent {
    data object OnStart: ContractListUiEvent
    data object OnBackClick: ContractListUiEvent
    data object OnTwilioClick: ContractListUiEvent
    data class OnContractChecked(val documentId: Int): ContractListUiEvent
    data object OnPrimaryButtonClick: ContractListUiEvent
    data class OnClickReadContract(val documentId: Int): ContractListUiEvent
    data object OnCheckAllDocuments: ContractListUiEvent
}