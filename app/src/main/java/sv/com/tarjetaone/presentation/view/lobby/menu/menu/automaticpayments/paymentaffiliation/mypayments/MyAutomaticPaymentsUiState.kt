package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.mypayments

import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.affiliationform.NotificationChannelType
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.contractslist.IdentifierUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.contractslist.PXStatus
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.contractslist.ParameterUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.contractslist.RecurringChargeContractUI
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.mypayments.component.getLabel

data class MyAutomaticPaymentsUiState(
    val contracts: List<RecurringChargeContractUI> = emptyList(),
    val currentContract: RecurringChargeContractUI? = null,
    val contractToCancel: RecurringChargeContractUI? = null
) {

    val notificationChannelLabel: UiText?
        get() = currentContract
            ?.parameters
            ?.find { it.category.toBoolean() }
            ?.value
            ?.let { NotificationChannelType.fromString(it).getLabel() }
}

object MyPaymentsDummyData {
    val recurringChargeContract = RecurringChargeContractUI(
        companyCode = "*********",
        serviceTypeCode = "1",
        serviceCode = "1",
        serviceName = "Applaudo",
        clientCode = "1",
        contractNumber = "1",
        contractCorrelative = "1",
        openingDate = "********",
        nextApplicationDate = "********",
        lastApplicationDate = "00000",
        contractFrequency = "1",
        contractCurrency = "1",
        contractBank = "1",
        contractProductType = "CT",
        contractProduct = "*************",
        productCurrency = "USD",
        applicationAmount = "17.50",
        maxAmount = "30.00",
        commentary = "",
        alias = "",
        status = PXStatus.Active,
        identifiers = listOf(
            IdentifierUI(
                number = "1",
                name = "reference",
                required = "true",
                identifier = "123456",
                length = "6",
                type = ""
            )
        ),
        parameters = listOf(
            ParameterUI(
                line = "1",
                type = "NOT",
                category = "",
                value = "NOTWHATS"
            )
        )
    )
}
