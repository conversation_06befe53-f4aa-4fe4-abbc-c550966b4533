package sv.com.tarjetaone.presentation.helpers

import android.content.Context
import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.StringRes
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.fromHtml
import androidx.core.text.parseAsHtml
import androidx.core.text.toHtml
import androidx.core.text.toSpanned
import kotlinx.parcelize.Parceler
import kotlinx.parcelize.Parcelize
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.presentation.compose.util.annotatedStringResource

/**
 * Helper class to handle both dynamic strings and string resources for texts in the UI
 */
@Parcelize
sealed class UiText : Parcelable {
    @Parcelize
    data class DynamicString(val text: String) : UiText()

    @Parcelize
    data class BulletPointString(
        @StringRes val titleRes: Int,
        @StringRes val itemResList: List<Int>
    ) : UiText()

    class StringResource(
        @StringRes val res: Int,
        vararg val args: Any
    ) : UiText() {
        companion object : Parceler<StringResource> {
            override fun create(parcel: Parcel): StringResource {
                val resId = parcel.readInt()
                val size = parcel.readInt()
                val args = Array<Any>(size) {
                    when (val type = parcel.readString()) {
                        String::class.simpleName -> parcel.readString().orEmpty()
                        Int::class.simpleName -> parcel.readInt()
                        Long::class.simpleName -> parcel.readLong()
                        Float::class.simpleName -> parcel.readFloat()
                        Double::class.simpleName -> parcel.readDouble()
                        Boolean::class.simpleName -> parcel.readInt() != 0
                        else -> throw IllegalArgumentException("Unsupported type: $type")
                    }
                }
                return StringResource(resId, *args)
            }

            override fun StringResource.write(parcel: Parcel, flags: Int) {
                parcel.apply {
                    writeInt(res)
                    writeInt(args.size)

                    args.forEach {
                        when (it) {
                            is String -> {
                                writeString(String::class.simpleName)
                                writeString(it)
                            }

                            is Int -> {
                                writeString(Int::class.simpleName)
                                writeInt(it)
                            }

                            is Float -> {
                                writeString(Float::class.simpleName)
                                writeFloat(it)
                            }

                            is Double -> {
                                writeString(Double::class.simpleName)
                                writeDouble(it)
                            }

                            is Long -> {
                                writeString(Long::class.simpleName)
                                writeLong(it)
                            }

                            is Boolean -> {
                                writeString(Boolean::class.simpleName)
                                writeInt(if (it) 1 else 0)
                            }

                            else -> throw IllegalArgumentException("Unsupported type: ${it::class}")
                        }
                    }
                }
            }
        }
    }

    fun asString(context: Context): String {
        return when (this) {
            is DynamicString -> text
            is StringResource -> context.getString(res, *args)
            is BulletPointString -> EMPTY_STRING // Empty on purpose due to this case not being used
        }
    }

    fun asCharSequence(context: Context): CharSequence {
        return when (this) {
            is DynamicString -> text
            is StringResource -> context.getText(res).toSpanned().toHtml().format(*args)
                .parseAsHtml().trim()

            is BulletPointString -> EMPTY_STRING // Empty on purpose due to this case not being used
        }
    }

    @Composable
    fun asString(): String {
        return when (this) {
            is DynamicString -> text
            is StringResource -> stringResource(res, *args)
            is BulletPointString -> EMPTY_STRING // Empty on purpose due to this case not being used
        }
    }

    @Composable
    fun asAnnotatedString(): AnnotatedString {
        return when (this) {
            is DynamicString -> AnnotatedString.fromHtml(text)
            is StringResource -> annotatedStringResource(res, *args)
            is BulletPointString -> AnnotatedString(EMPTY_STRING) // Empty on purpose due to this case not being used
        }
    }
}
