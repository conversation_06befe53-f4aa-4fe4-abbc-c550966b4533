package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.requestdocument

import com.facephi.fphiselphidwidgetcore.WidgetSelphIDResult

sealed class RequestDocumentAdditionalCardUiEvent {

    data class OnBack(val prevDestId: Int?) : RequestDocumentAdditionalCardUiEvent()
    data object OnTwilio : RequestDocumentAdditionalCardUiEvent()
    data class OnConfirm(val requestPermission: () -> Unit) : RequestDocumentAdditionalCardUiEvent()
    data object OnDismiss : RequestDocumentAdditionalCardUiEvent()
    data class OnOptionSelected(val option: AnswerOption) : RequestDocumentAdditionalCardUiEvent()
    data class OnDocumentCaptured(val result: WidgetSelphIDResult) :
        RequestDocumentAdditionalCardUiEvent()

    data class OnCameraPermissionDenied(
        val shouldShowRationale: Boolean
    ) : RequestDocumentAdditionalCardUiEvent()
}