package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements

import sv.com.tarjetaone.domain.entities.response.CustomerCCardUI
import sv.com.tarjetaone.domain.entities.response.InstallmentTermsUI
import sv.com.tarjetaone.domain.entities.response.TransactionsUI

sealed class MyMovementsEvent {
    data object OnBackClick : MyMovementsEvent()
    data object OnSupportClick : MyMovementsEvent()
    data object OnStart : MyMovementsEvent()
    data class OnCardChange(val card: CustomerCCardUI) : MyMovementsEvent()
    data class OnSearchQueryToggle(val value: Boolean) : MyMovementsEvent()
    data class OnCalendarQueryToggle(val value: Boolean) : MyMovementsEvent()
    data class OnSearchQueryChange(val query: String) : MyMovementsEvent()
    data class OnPerformSearchQuery(val query: String) : MyMovementsEvent()
    data object OnDateRangeClick : MyMovementsEvent()
    data class OnDateRangeChange(val start: Long, val end: Long) : MyMovementsEvent()
    data object OnDateRangeDialogDismiss : MyMovementsEvent()
    data class OnStatusFilterClick(val status: MovementStatus) : MyMovementsEvent()
    data class OnTransactionItemAction(
        val action: TransactionItemAction,
        val transaction: TransactionsUI
    ) : MyMovementsEvent()

    data class OnInstallmentBottomSheetAction(
        val action: InstallmentBottomSheetEvent,
    ) : MyMovementsEvent()

    data class OnTransactionsLoadError(val onRetryAction: () -> Unit) : MyMovementsEvent()
    data object OnDismissInstallmentsTutorial: MyMovementsEvent()
    data object OnTutorialCompleted: MyMovementsEvent()
}

sealed class TransactionItemAction {
    data object OnToggleDetails : TransactionItemAction()
    data object OnUnrecognizedPurchaseClick : TransactionItemAction()
    data object OnPayInInstallmentsClick : TransactionItemAction()
    data object OnPayWithPointsClick : TransactionItemAction()
}

sealed class InstallmentBottomSheetEvent {
    data object OnDismiss : InstallmentBottomSheetEvent()
    data class OnSelectInstallment(val installment: InstallmentTermsUI) :
        InstallmentBottomSheetEvent()

    data object OnContinueClick : InstallmentBottomSheetEvent()
}