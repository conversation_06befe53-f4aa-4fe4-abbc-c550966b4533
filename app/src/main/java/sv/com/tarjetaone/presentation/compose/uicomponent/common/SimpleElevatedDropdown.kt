package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.MenuAnchorType
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.PopupProperties
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.text.DropdownOverflowText
import sv.com.tarjetaone.presentation.compose.util.FieldDecorationType
import sv.com.tarjetaone.presentation.compose.util.Spacer4

/**
 * Reusable component for a simple elevated dropdown.
 * @param modifier Modifier for the dropdown.
 * @param items List of items to be displayed in the dropdown.
 * @param itemLabel Function to map an item to a string to be displayed as option.
 * @param value The selected value.
 * @param onValueChange Callback for when the value changes.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Suppress("kotlin:S107")
@Composable
fun <T> SimpleElevatedDropdown(
    modifier: Modifier = Modifier,
    label: String? = null,
    labelStyle: TextStyle? = null,
    hint: String? = null,
    hintStyle: TextStyle? = null,
    placeholder: String? = null,
    items: List<T>,
    itemLabel: @Composable (T) -> String,
    value: T?,
    onValueChange: (T) -> Unit,
    showTrailingIcon: Boolean = true,
    hasError: Boolean = false,
    error: String? = null,
    enabled: Boolean = true,
    decorationType: FieldDecorationType = FieldDecorationType.ELEVATED
) {
    val dimen48 = MaterialTheme.customDimens.dimen48
    var size by remember { mutableStateOf(IntSize.Zero) }
    val sizeOfOneItem by remember { mutableStateOf(dimen48) }
    val configuration = LocalConfiguration.current
    val screenHeight50 by remember {
        val screenHeight = configuration.screenHeightDp.dp
        // Assuming the DropDown menu anchor is in middle of the screen. This is the maximum height that popup menu can take.
        mutableStateOf(screenHeight / AppConstants.TWO_VALUE)
    }
    var expanded by remember { mutableStateOf(false) }
    Column(modifier = modifier) {
        label?.let {
            Text(
                text = it,
                style = labelStyle ?: MaterialTheme.typography.bodySmall.copy(
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.customColors.gray700,
                ),
            )
            Spacer(modifier = Modifier.height(4.dp))
        }
        ExposedDropdownMenuBox(
            expanded = expanded,
            onExpandedChange = {
                if (enabled) expanded = !expanded
            },
            modifier = Modifier.onSizeChanged { size = it },
        ) {
            DropdownOverflowText(
                modifier = Modifier.menuAnchor(
                    type = MenuAnchorType.PrimaryNotEditable,
                    enabled = enabled,
                ),
                value = value?.let { itemLabel(it) }.orEmpty(),
                placeholder = placeholder.orEmpty(),
                fieldDecorationType = decorationType,
                trailingIcon = {
                    if (showTrailingIcon) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_arrow_down_filled),
                            contentDescription = null,
                            tint = MaterialTheme.customColors.disabledPlaceholder,
                            modifier = Modifier.padding(start = MaterialTheme.customDimens.dimen8)
                        )
                    }
                },
                enabled = enabled,
                hasError = hasError,
                onClick = { expanded = true },
            )
            val height by remember(items.size) {
                val itemsSize = sizeOfOneItem * items.size
                mutableStateOf(minOf(itemsSize, screenHeight50))
            }
            DropdownMenu(
                modifier = Modifier
                    .background(Color.White)
                    .exposedDropdownSize(true),
                expanded = expanded,
                properties = PopupProperties(focusable = false),
                onDismissRequest = { expanded = false },
            ) {
                Box(
                    modifier = Modifier.then(
                        with(LocalDensity.current) {
                            Modifier.size(
                                width = size.width.toDp(),
                                height = height
                            )
                        }
                    )
                ) {
                    LazyColumn {
                        items(items) {
                            DropdownMenuItem(
                                text = {
                                    Text(
                                        text = itemLabel(it),
                                        style = MaterialTheme.typography.labelLarge.copy(
                                            fontWeight = FontWeight.Normal
                                        ),
                                    )
                                },
                                onClick = {
                                    expanded = false
                                    onValueChange(it)
                                }
                            )
                        }
                    }
                }
            }
        }
        hint?.let {
            Spacer4()
            Text(
                text = it,
                style = hintStyle ?: MaterialTheme.typography.labelMedium.copy(
                    color = MaterialTheme.customColors.textBodyLight,
                ),
            )
        }
        if (hasError && error.isNullOrEmpty().not()) {
            Spacer4()
            Text(
                text = error.orEmpty(),
                style = MaterialTheme.typography.labelMedium.copy(
                    color = MaterialTheme.colorScheme.error
                )
            )
        }
    }
}

@Preview
@Composable
private fun SimpleElevatedDropdownPreview() {
    OneAppTheme {
        SimpleElevatedDropdown(
            items = listOf(),
            value = "BAES",
            onValueChange = {},
            itemLabel = { it },
            modifier = Modifier.fillMaxWidth()
        )
    }
}

@Preview
@Composable
private fun SimpleOutlinedDropdownPreview() {
    OneAppTheme {
        SimpleElevatedDropdown(
            items = listOf(),
            value = "BAES",
            onValueChange = {},
            itemLabel = { it },
            modifier = Modifier.fillMaxWidth(),
            decorationType = FieldDecorationType.OUTLINED
        )
    }
}

@Preview
@Composable
private fun SimpleElevatedDropdownDisabledPreview() {
    OneAppTheme {
        SimpleElevatedDropdown(
            items = listOf(),
            value = "BAES",
            onValueChange = {},
            itemLabel = { it },
            modifier = Modifier.fillMaxWidth(),
            enabled = false
        )
    }
}

@Preview
@Composable
private fun SimpleElevatedDropdownWithErrorPreview() {
    OneAppTheme {
        SimpleElevatedDropdown(
            items = listOf(),
            value = "BAES",
            onValueChange = {},
            itemLabel = { it },
            modifier = Modifier.fillMaxWidth(),
            enabled = true,
            hasError = true,
            error = "Este es un mensaje de error",
        )
    }
}
