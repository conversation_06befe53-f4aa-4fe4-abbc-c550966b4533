package sv.com.tarjetaone.presentation.view.lobby.menu.home.skeleton

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.presentation.compose.modifier.shimmerEffect
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.skeleton.SkeletonContainer
import sv.com.tarjetaone.presentation.compose.uicomponent.skeleton.SkeletonState
import sv.com.tarjetaone.presentation.compose.util.ShimmerSize
import sv.com.tarjetaone.presentation.compose.util.ShimmerStyle
import sv.com.tarjetaone.presentation.compose.util.Spacer12
import sv.com.tarjetaone.presentation.compose.util.Spacer14

/**
 * Custom Points overview card skeleton composable.
 */
@Composable
fun PointsOverviewCardSkeleton(
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .background(
                color = MaterialTheme.customColors.gray300,
                shape = MaterialTheme.shapes.medium
            )
            .fillMaxWidth()
            .padding(
                top = MaterialTheme.customDimens.dimen25,
                bottom = MaterialTheme.customDimens.dimen20,
                start = MaterialTheme.customDimens.dimen20,
                end = MaterialTheme.customDimens.dimen36
            )
    ) {
        Row {
            Box(
                modifier = Modifier
                    .background(
                        color = MaterialTheme.customColors.gray400,
                        shape = MaterialTheme.shapes.medium
                    )
                    .size(MaterialTheme.customDimens.dimen48)
                    .shimmerEffect(
                        shape = MaterialTheme.shapes.medium,
                        shimmerSize = ShimmerSize.LARGE,
                        shimmerStyle = ShimmerStyle.DARK_BACKGROUND,
                    )
            )
            Spacer14()
            Column {
                Box(
                    modifier = Modifier
                        .background(
                            color = MaterialTheme.customColors.gray400,
                            shape = MaterialTheme.shapes.extraSmall
                        )
                        .fillMaxWidth()
                        .height(MaterialTheme.customDimens.dimen48)
                        .shimmerEffect(shimmerStyle = ShimmerStyle.DARK_BACKGROUND)
                )
                Spacer12()
                Box(
                    modifier = Modifier
                        .background(
                            color = MaterialTheme.customColors.gray400,
                            shape = MaterialTheme.shapes.extraSmall
                        )
                        .width(MaterialTheme.customDimens.dimen110)
                        .height(MaterialTheme.customDimens.dimen16)
                        .shimmerEffect(shimmerStyle = ShimmerStyle.DARK_BACKGROUND)
                )
            }
        }
    }
}

// TODO: this is just another alternative, decide which one to keep. (Remove the unused)
@Composable
fun PointsOverviewCardSkeleton(
    modifier: Modifier = Modifier,
    state: SkeletonState = SkeletonState.Success,
) {
    Column(
        modifier = modifier
            .background(
                color = MaterialTheme.customColors.gray300,
                shape = MaterialTheme.shapes.medium
            )
            .fillMaxWidth()
            .padding(
                top = MaterialTheme.customDimens.dimen25,
                bottom = MaterialTheme.customDimens.dimen20,
                start = MaterialTheme.customDimens.dimen20,
                end = MaterialTheme.customDimens.dimen36
            )
    ) {
        Row {
            SkeletonContainer(
                modifier = Modifier,
                state = state,
                shimmerStyle = ShimmerStyle.DARK_BACKGROUND,
                backgroundColor = MaterialTheme.customColors.gray400,
            ) {
                Box(
                    modifier = Modifier
                        .size(MaterialTheme.customDimens.dimen48)
                )
            }
            Spacer14()
            Column {
                SkeletonContainer(
                    modifier = Modifier,
                    state = state,
                    shape = MaterialTheme.shapes.extraSmall,
                    shimmerStyle = ShimmerStyle.DARK_BACKGROUND,
                    backgroundColor = MaterialTheme.customColors.gray400,
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(MaterialTheme.customDimens.dimen48)
                    )
                }
                Spacer12()
                SkeletonContainer(
                    modifier = Modifier,
                    state = state,
                    shape = MaterialTheme.shapes.extraSmall,
                    shimmerStyle = ShimmerStyle.DARK_BACKGROUND,
                    backgroundColor = MaterialTheme.customColors.gray400,
                ) {
                    Box(
                        modifier = Modifier
                            .width(MaterialTheme.customDimens.dimen110)
                            .height(MaterialTheme.customDimens.dimen16)
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun PointsOverviewCardSkeletonPreview() {
    OneAppTheme {
        PointsOverviewCardSkeleton(modifier = Modifier)
    }
}
