package sv.com.tarjetaone.presentation.view.common.contactinput

import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.domain.entities.response.ContactType

data class ContactInputUiState(
    val contactType: ContactType = ContactType.Phone,
    val contact: String = EMPTY_STRING,
    val showPhoneActions: Boolean = false,
    val validationError: ContactValidationError = ContactValidationError.None
) {
    val formIsValid = validationError == ContactValidationError.None
}
