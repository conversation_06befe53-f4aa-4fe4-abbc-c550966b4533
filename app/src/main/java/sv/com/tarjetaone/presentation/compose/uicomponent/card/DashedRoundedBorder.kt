package sv.com.tarjetaone.presentation.compose.uicomponent.card

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.unit.Dp
import sv.com.tarjetaone.presentation.compose.theme.customDimens

/**
 * A reusable UI component that displays a dashed, rounded border around any given content.
 *
 * This composable is useful for highlighting actions (e.g., "Add" buttons),
 * placeholder areas, or guiding user interaction within a UI.
 *
 * @param modifier Modifier to be applied to the outer container.
 * @param borderColor Color of the dashed border.
 * @param borderWidth Thickness of the border stroke.
 * @param cornerRadius Radius applied to the rounded corners of the border.
 * @param onClick Lambda executed when the component is tapped.
 * @param content Composable content to be placed inside the bordered box.
 */
@Composable
fun DashedRoundedBorder(
    modifier: Modifier = Modifier,
    borderColor: Color,
    borderWidth: Dp = MaterialTheme.customDimens.dimen3,
    cornerRadius: Dp = MaterialTheme.customDimens.dimen16,
    onClick: () -> Unit = {},
    content: @Composable BoxScope.() -> Unit
) {

    val dashLength: Dp = MaterialTheme.customDimens.dimen24
    val dashSpacing: Dp = MaterialTheme.customDimens.dimen8

    Box(
        modifier = modifier
            .clip(RoundedCornerShape(MaterialTheme.customDimens.dimen16))
            .clickable(onClick = onClick)
            .drawWithContent {
                val borderRect = Rect(BORDER_OFFSET, BORDER_OFFSET, size.width, size.height)
                drawRoundRect(
                    color = borderColor,
                    style = Stroke(
                        width = borderWidth.toPx(),
                        pathEffect = PathEffect.dashPathEffect(
                            intervals = floatArrayOf(dashLength.toPx(), dashSpacing.toPx()),
                            phase = DASH_PHASE_OFFSET
                        )
                    ),
                    topLeft = borderRect.topLeft,
                    size = borderRect.size,
                    cornerRadius = CornerRadius(cornerRadius.toPx(), cornerRadius.toPx())
                )
                drawContent()
            },
        contentAlignment = Alignment.Center
    ) {
        content()
    }
}

// Offset to position the border from the top-left corner of the box
const val BORDER_OFFSET = 0f

// Dash phase offset to control the starting point of the dashed path.
const val DASH_PHASE_OFFSET = 0f
