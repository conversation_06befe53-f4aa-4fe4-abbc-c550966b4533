package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.installmentpayment.identity.validation

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.AppConstants.CHA_APP
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE_DOUBLE
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.request.PayInstallmentRequestUI
import sv.com.tarjetaone.domain.usecases.auth.FaceAuthenticationUseCase
import sv.com.tarjetaone.domain.usecases.installmentpayment.PayInInstallmentsUseCase
import sv.com.tarjetaone.presentation.view.common.identity_validation.IdentityValidationBaseViewModel
import javax.inject.Inject

@HiltViewModel
class InstallmentIdentityValidationViewModel @Inject constructor(
    faceAuthenticationUseCase: FaceAuthenticationUseCase,
    sharedPrefs: SecureSharedPreferencesRepository,
    savedStateHandle: SavedStateHandle,
    private val payInInstallmentsUseCase: PayInInstallmentsUseCase,
) : IdentityValidationBaseViewModel(
    faceAuthenticationUseCase,
    sharedPrefs,
) {
    private val args = InstallmentIdentityValidationFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun operation(biometryId: Int) {
        viewModelScope.launch {
            payInInstallmentsUseCase(
                payInstallmentRequestUI = PayInstallmentRequestUI(
                    cCardID = sharedPrefs.mainCard?.creditCardId,
                    amount = args.totalAmount.toDouble(),
                    biometryProcessID = biometryId,
                    channelCode = CHA_APP,
                    installmentNumbers = args.selectedInstallment.installmentNumbers,
                    installmentAmount = args.selectedInstallment.amount,
                    rate = ZERO_VALUE_DOUBLE,
                    isBiometryMode = true,
                    description = args.transaction.description,
                    authorizationCode = args.transaction.authorizationCode,
                    referenceNumber = args.transaction.referenceNumber,
                    valueDate = args.transaction.valueDate,
                )
            ).executeUseCase(
                onApiErrorAction = { _, _, _, _ ->
                    showUpsErrorMessage(isDismissible = false) {
                        sendEvent(UiEvent.NavigateBack)
                    }
                }
            ) { response ->
                sendEvent(
                    UiEvent.Navigate(
                        InstallmentIdentityValidationFragmentDirections
                            .navigateToInstallmentPaymentDetailsFragment(
                                response = response,
                            )
                    )
                )
            }
        }
    }

    override fun onFailedIdentityValidation(resultCode: String?) =
        sendEvent(UiEvent.NavigateBack)

}
