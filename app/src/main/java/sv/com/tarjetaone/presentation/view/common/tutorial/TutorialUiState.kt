package sv.com.tarjetaone.presentation.view.common.tutorial

import androidx.annotation.DrawableRes
import sv.com.tarjetaone.presentation.helpers.UiText

data class TutorialUiState(
    val items: List<TutorialItem> = emptyList(),
    val tutorialType: TutorialType = TutorialType.Document,
    val canGoBack: Boolean = false,
    val isCloseButtonVisible: Boolean = false,
)

data class TutorialItem(
    val title: UiText,
    @DrawableRes val incorrectImageTip: Int,
    val tips: List<UiText> = emptyList(),
)
