package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.annotation.DrawableRes
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.Spacer8

@Suppress("kotlin:S107")
@Composable
fun ToggleableItem(
    modifier: Modifier = Modifier,
    cardElevation: Dp = MaterialTheme.customDimens.dimenZero,
    @DrawableRes icon: Int,
    iconTint: Color = MaterialTheme.colorScheme.primary,
    title: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    description: String? = null,
    extraContent: @Composable (() -> Unit)? = null,
    showExtraContent: Boolean = false
) {
    Column(
        modifier = modifier
    ) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.customColors.defaultSurface,
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = cardElevation
            )
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(MaterialTheme.customDimens.dimen16)
            ) {
                Icon(
                    imageVector = ImageVector.vectorResource(id = icon),
                    contentDescription = null,
                    tint = iconTint
                )
                Spacer8()
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.weight(ONE_FLOAT_VALUE),
                    color = MaterialTheme.colorScheme.secondary
                )
                Spacer8()
                OneSwitch(checked = checked, onCheckedChange = onCheckedChange)
            }
        }
        description?.let {
            Spacer8()
            Text(
                text = it,
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onBackground,
                modifier = Modifier.fillMaxWidth()
            )
        }
        AnimatedVisibility(
            visible = showExtraContent,
            modifier = Modifier.fillMaxWidth()
        ) {
            extraContent?.invoke()
        }
    }
}

@Preview
@Composable
private fun ToggleableItemPreview() {
    OneAppTheme {
        ToggleableItem(
            title = stringResource(id = R.string.international_purchases_option),
            icon = R.drawable.ic_option_card_online_international_payment_icon,
            description = stringResource(id = R.string.international_purchases_option_description),
            checked = true,
            onCheckedChange = { }
        )
    }
}
