package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.mypayments

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.contractslist.RecurringChargeContractUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.DialogConfirmation
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer48
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.mypayments.component.AutomaticPaymentItem
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.mypayments.component.ContractDetailsDialog

@Composable
fun MyAutomaticPaymentsScreen(
    viewModel: MyAutomaticPaymentsViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    LaunchedEffect(Unit) {
        viewModel.onEvent(MyAutomaticPaymentsUiEvent.OnStart)
    }
    MyAutomaticPaymentsScreen(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@Composable
fun MyAutomaticPaymentsScreen(
    uiState: MyAutomaticPaymentsUiState,
    onEvent: (MyAutomaticPaymentsUiEvent) -> Unit
) {
    ScreenWithTopAppBar(
        onLeftButtonClick = { onEvent(MyAutomaticPaymentsUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(MyAutomaticPaymentsUiEvent.OnTwilioClick) },
        title = stringResource(R.string.service_payment_title),
        contentPadding = PaddingValues(MaterialTheme.customDimens.dimen24)
    ) {
        uiState.currentContract?.let {
            ContractDetailsDialog(
                contract = it,
                notificationChannelLabel = uiState.notificationChannelLabel,
                onEditRecurringCharge = { onEvent(MyAutomaticPaymentsUiEvent.OnEditRecurringCharge(it)) },
                onViewPaymentHistory = { onEvent(MyAutomaticPaymentsUiEvent.OnViewPaymentHistory(it)) },
                onDismissRequest = { onEvent(MyAutomaticPaymentsUiEvent.OnDismissContractDetails) }
            )
        }
        uiState.contractToCancel?.let {
            DialogConfirmation(
                message = UiText.StringResource(R.string.my_payments_delete_message),
                messageStyle = MaterialTheme.typography.bodySmall.copy(
                    color = MaterialTheme.customColors.onAlert
                ),
                messagePadding = PaddingValues(bottom = MaterialTheme.customDimens.dimen16),
                primaryButtonText = UiText.StringResource(R.string.my_payments_delete_positive_button),
                secondaryButtonText = UiText.StringResource(R.string.exit_label),
                onPositiveClick = { onEvent(MyAutomaticPaymentsUiEvent.OnAcceptCancelContractClick) },
                onNegativeClick = { onEvent(MyAutomaticPaymentsUiEvent.OnDismissCancelContractClick) }
            )
        }

        ElevatedCard(
            colors = CardDefaults.elevatedCardColors(
                containerColor = MaterialTheme.customColors.defaultSurface,
                contentColor = MaterialTheme.customColors.onDefaultSurface
            ),
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = MaterialTheme.customDimens.dimen24)
                .padding(bottom = MaterialTheme.customDimens.dimen16)
        ) {
            Spacer16()
            Text(
                text = stringResource(R.string.my_payments_title),
                style = MaterialTheme.typography.bodyLarge.copy(fontWeight = FontWeight.SemiBold),
                textAlign = TextAlign.Center,
                modifier = Modifier.align(Alignment.CenterHorizontally),
            )
            Box(
                modifier = Modifier
                    .weight(1f)
                    .padding(MaterialTheme.customDimens.dimen16)
            ) {
                ContractsList(
                    contracts = uiState.contracts,
                    onViewDetailsClick = { onEvent(MyAutomaticPaymentsUiEvent.OnViewContractClick(it)) },
                    onCancelClick = { onEvent(MyAutomaticPaymentsUiEvent.OnCancelContractClick(it)) }
                )
                OneButton(
                    text = stringResource(R.string.my_payments_add_service),
                    onClick = { onEvent(MyAutomaticPaymentsUiEvent.OnAddContractClick) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.BottomCenter)
                )
            }
        }
    }
}

@Composable
private fun ContractsList(
    contracts: List<RecurringChargeContractUI>,
    onViewDetailsClick: (RecurringChargeContractUI) -> Unit,
    onCancelClick: (RecurringChargeContractUI) -> Unit,
) {
    if (contracts.isEmpty()) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.fillMaxSize()
        ) {
            Image(
                imageVector = ImageVector.vectorResource(R.drawable.ic_empty_movement),
                contentDescription = null,
            )
            Spacer16()
            Text(
                text = stringResource(R.string.my_payments_empty),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.padding(horizontal = MaterialTheme.customDimens.dimen16)
            )
        }
    } else {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen16)
        ) {
            items(items = contracts) {
                AutomaticPaymentItem(
                    modifier = Modifier.fillMaxWidth(),
                    contract = it,
                    onViewDetailsClick = { onViewDetailsClick(it) },
                    onCancelClick = { onCancelClick(it) }
                )
            }
            item { Spacer48() }
        }
    }
}

@Preview
@Composable
private fun MyAutomaticPaymentsScreenPreview() {
    OneAppTheme {
        MyAutomaticPaymentsScreen(
            uiState = MyAutomaticPaymentsUiState(
                contracts = listOf(
                    MyPaymentsDummyData.recurringChargeContract.copy(
                        lastApplicationDate = "20241201",
                        maxAmount = "0.0"
                    ),
                    MyPaymentsDummyData.recurringChargeContract,
                    MyPaymentsDummyData.recurringChargeContract,
                )
            ),
            onEvent = {}
        )
    }
}
