package sv.com.tarjetaone.presentation.view.experimental

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.openAppSettings
import sv.com.tarjetaone.presentation.compose.uicomponent.button.ButtonSize
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.DialogAction
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.MessageParams
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.OneDialogParams
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.helpers.UiText

@Composable
fun ExperimentalScreen(
    viewModel: ExperimentalViewModel
) {
    SideEffectHandler(viewModel.sideEffects) {
        Column(
            verticalArrangement = Arrangement.spacedBy(16.dp, Alignment.CenterVertically),
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.fillMaxSize()
        ) {
            OneButton(
                text = "Show XML dialog",
                size = ButtonSize.MEDIUM,
                onClick = {
                    viewModel.showUpsErrorMessage()
                }
            )
            OneButton(
                text = "Show dialog with message with args",
                size = ButtonSize.MEDIUM,
                onClick = {
                    viewModel.showDialog(
                        OneDialogParams(
                            icon = R.drawable.circle_error_icon,
                            message = MessageParams(
                                text = UiText.StringResource(R.string.greeting_home, "string arg")
                            ),
                            primaryAction = DialogAction(
                                text = UiText.DynamicString("Goodbye"),
                                onClick = { viewModel.showToast() }
                            ),
                            showCloseButton = true
                        )
                    )
                }
            )
            OneButton(
                text = "Show dynamic html dialog",
                size = ButtonSize.MEDIUM,
                onClick = {
                    viewModel.showDialog(
                        OneDialogParams(
                            icon = R.drawable.ic_success,
                            message = MessageParams(
                                text = UiText.DynamicString(AppConstants.MOCK_HTML_FORMATTED_TEXT)
                            )
                        )
                    )
                }
            )
            OneButton(
                text = "Show dialog with html res string",
                size = ButtonSize.MEDIUM,
                onClick = {
                    viewModel.showDialog(
                        OneDialogParams(
                            isDismissible = false,
                            icon = R.drawable.ic_info_purple,
                            message = MessageParams(
                                text = UiText.StringResource(R.string.selfie_description_additional_card)
                            )
                        )
                    )
                }
            )
            OneButton(
                text = "Show Toast",
                size = ButtonSize.MEDIUM,
                onClick = { viewModel.showToast() }
            )
            OneButton(
                text = "Load stuff",
                size = ButtonSize.MEDIUM,
                onClick = { viewModel.loadStuff() }
            )
            OneButton(
                text = "Start Intent to settings",
                size = ButtonSize.MEDIUM,
                onClick = { viewModel.sendEvent(SideEffect.StartIntent(openAppSettings())) }
            )
        }
    }
}