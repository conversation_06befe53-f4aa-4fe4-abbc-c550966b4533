package sv.com.tarjetaone.presentation.compose.theme

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

@Suppress("MagicNumber")
data class CustomDimensions(
    val dimenZero: Dp = 0.dp,
    val dimenZeroDotTwo: Dp = 0.2.dp,
    val dimenZeroDotFive: Dp = 0.5.dp,
    val dimen1: Dp = 1.dp,
    val dimen2: Dp = 2.dp,
    val dimen3: Dp = 3.dp,
    val dimen4: Dp = 4.dp,
    val dimen5: Dp = 5.dp,
    val dimen6: Dp = 6.dp,
    val dimen8: Dp = 8.dp,
    val dimen10: Dp = 10.dp,
    val dimen12: Dp = 12.dp,
    val dimen13: Dp = 13.dp,
    val dimen14: Dp = 14.dp,
    val dimen15: Dp = 15.dp,
    val dimen16: Dp = 16.dp,
    val dimen19: Dp = 19.dp,
    val dimen18: Dp = 18.dp,
    val dimen20: Dp = 20.dp,
    val dimen22: Dp = 22.dp,
    val dimen24: Dp = 24.dp,
    val dimen25: Dp = 25.dp,
    val dimen26: Dp = 26.dp,
    val dimen28: Dp = 28.dp,
    val dimen30: Dp = 30.dp,
    val dimen32: Dp = 32.dp,
    val dimen35: Dp = 35.dp,
    val dimen36: Dp = 36.dp,
    val dimen38: Dp = 38.dp,
    val dimen39: Dp = 39.dp,
    val dimen40: Dp = 40.dp,
    val dimen42: Dp = 42.dp,
    val dimen44: Dp = 44.dp,
    val dimen45: Dp = 45.dp,
    val dimen46: Dp = 46.dp,
    val dimen48: Dp = 48.dp,
    val dimen50: Dp = 50.dp,
    val dimen52: Dp = 52.dp,
    val dimen56: Dp = 56.dp,
    val dimen60: Dp = 60.dp,
    val dimen64: Dp = 64.dp,
    val dimen66: Dp = 66.dp,
    val dimen70: Dp = 70.dp,
    val dimen72: Dp = 72.dp,
    val dimen74: Dp = 74.dp,
    val dimen80: Dp = 80.dp,
    val dimen85: Dp = 85.dp,
    val dimen88: Dp = 88.dp,
    val dimen90: Dp = 90.dp,
    val dimen92: Dp = 92.dp,
    val dimen93: Dp = 93.dp,
    val dimen98: Dp = 98.dp,
    val dimen100: Dp = 100.dp,
    val dimen108: Dp = 108.dp,
    val dimen110: Dp = 110.dp,
    val dimen120: Dp = 120.dp,
    val dimen128: Dp = 128.dp,
    val dimen132: Dp = 132.dp,
    val dimen140: Dp = 140.dp,
    val dimen146: Dp = 146.dp,
    val dimen157: Dp = 157.dp,
    val dimen160: Dp = 160.dp,
    val dimen168: Dp = 168.dp,
    val dimen175: Dp = 175.dp,
    val dimen184: Dp = 184.dp,
    val dimen189: Dp = 189.dp,
    val dimen190: Dp = 190.dp,
    val dimen192: Dp = 192.dp,
    val dimen200: Dp = 200.dp,
    val dimen220: Dp = 220.dp,
    val dimen230: Dp = 230.dp,
    val dimen240: Dp = 240.dp,
    val dimen243: Dp = 243.dp,
    val dimen250: Dp = 250.dp,
    val dimen252: Dp = 252.dp,
    val dimen260: Dp = 260.dp,
    val dimen264: Dp = 264.dp,
    val dimen285: Dp = 285.dp,
    val dimen292: Dp = 292.dp,
    val dimen310: Dp = 310.dp,
    val dimen330: Dp = 330.dp,
    val dimen325: Dp = 325.dp,
    val dimen360: Dp = 360.dp,
    val dimen370: Dp = 370.dp,
    val dimen462: Dp = 462.dp,
    val dimen550: Dp = 550.dp,
    val dimen585: Dp = 585.dp,
    val defaultCardElevation: Dp = 1.dp
)

val LocalCustomDimensions = compositionLocalOf { CustomDimensions() }

val MaterialTheme.customDimens: CustomDimensions
    @Composable
    @ReadOnlyComposable
    get() = LocalCustomDimensions.current
