package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.installmentpayment

import sv.com.tarjetaone.domain.entities.response.InstallmentTermsUI

sealed class InstallmentPaymentUiEvent {
    data object OnStart : InstallmentPaymentUiEvent()
    data class OnSelectInstallment(val installment: InstallmentTermsUI) :
        InstallmentPaymentUiEvent()

    data class OnInfoBottomSheetEvent(val action: InfoBottomSheetUiEvent) :
        InstallmentPaymentUiEvent()

    data class OnConfirmationBottomSheetEvent(val action: ConfirmationBottomSheetUiEvent) :
        InstallmentPaymentUiEvent()

    data object OnDismissBottomSheet : InstallmentPaymentUiEvent()
    data class OnShowBottomSheet(val type: InstallmentBottomSheetType) :
        InstallmentPaymentUiEvent()
    data object OnKnowMoreClick : InstallmentPaymentUiEvent()
    data object OnContinueClick : InstallmentPaymentUiEvent()
    data object OnBackClick : InstallmentPaymentUiEvent()
    data object OnTwilioClick : InstallmentPaymentUiEvent()
}

sealed class InfoBottomSheetUiEvent {
    data object OnAgreeClick : InfoBottomSheetUiEvent()
}

sealed class ConfirmationBottomSheetUiEvent {
    data object OnContinueClick : ConfirmationBottomSheetUiEvent()
}
