package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.presentation.compose.util.StaticMenuItem
import javax.inject.Inject

@HiltViewModel
class PersonalDataMenuViewModel @Inject constructor() : BaseViewModel() {
    private val _uiState = MutableStateFlow(PersonalDataMenuUiState(items = createMenuOptions()))
    val uiState = _uiState.asStateFlow()

    private fun createMenuOptions(): List<StaticMenuItem> = listOf(
        StaticMenuItem(
            icon = R.drawable.ic_people_circle,
            label = R.string.general_data,
            destination = PersonalDataFragmentDirections.actionPersonalDataFragmentToPersonalDataOverviewFragment()
        ),
        StaticMenuItem(
            icon = R.drawable.ic_house_new,
            label = R.string.address_location,
            destination = PersonalDataFragmentDirections.actionPersonalDataFragmentToAddresLocation()
        ),
        StaticMenuItem(
            icon = R.drawable.ic_briefcase,
            label = R.string.employment_info,
            destination = PersonalDataFragmentDirections
                .actionPersonalDataFragmentToEmploymentInfoOverviewFragment()
        ),
        StaticMenuItem(
            icon = R.drawable.ic_p_phone,
            label = R.string.contacts_channel,
            destination = PersonalDataFragmentDirections
                .actionPersonalDataFragmentToContactChannelOverviewFragment()
        ),
        StaticMenuItem(
            icon = R.drawable.ic_references,
            label = R.string.references,
            destination = PersonalDataFragmentDirections
                .actionPersonalDataFragmentToReferencesOverviewFragment()
        )
    )

    private fun onItemClick(item: StaticMenuItem) {
        item.destination?.let {
            sendEvent(UiEvent.Navigate(it))
        }
    }

    private fun onBackClick() {
        sendEvent(
            UiEvent.Navigate(
                PersonalDataFragmentDirections.actionPersonalDataFragmentToNavigationMenu()
            )
        )
    }

    fun onEvent(event: PersonalDataMenuUiEvent) {
        when (event) {
            PersonalDataMenuUiEvent.OnBackClick -> onBackClick()
            PersonalDataMenuUiEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
            is PersonalDataMenuUiEvent.OnItemClick -> onItemClick(event.item)
        }
    }
}
