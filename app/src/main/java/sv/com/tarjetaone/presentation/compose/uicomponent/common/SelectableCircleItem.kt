package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import sv.com.tarjetaone.presentation.compose.modifier.noRippleClickable
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.md_theme_light_disabled_gray_placeholder
import sv.com.tarjetaone.presentation.compose.theme.montserratFamily

/**
 * Reusable component designed to be displayed as selectable circular items, showcasing the given colors.
 * The selected logic is expected to be handled by the caller function, making this component stateless.
 *
 * @param modifier Used to apply desired modifications, such as size or padding.
 * @param containerColor Represents the background color of the circular container.
 * @param contentColor Represents the color of the inner UI, specifically the text color.
 * @param strokeColor Intended to change dynamically based on the selected state.
 * @param text The string displayed inside the circular container.
 * @param onItemSelected Returns the action triggered when the component is selected.
 */
@Suppress("kotlin:S107")
@Composable
fun SelectableCircleItem(
    modifier: Modifier = Modifier,
    innerModifier: Modifier = Modifier,
    containerColor: Color = MaterialTheme.colorScheme.background,
    contentColor: Color = md_theme_light_disabled_gray_placeholder,
    strokeColor: Color = md_theme_light_disabled_gray_placeholder,
    text: String,
    bottomContent: @Composable () -> Unit = {},
    onItemSelected: () -> Unit = {}
) {

    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(modifier = modifier) {
            Box(
                modifier = innerModifier
                    .size(50.dp)
                    .background(containerColor, shape = CircleShape)
                    .border(
                        BorderStroke(
                            width = 1.dp,
                            color = strokeColor
                        ),
                        shape = CircleShape
                    )
                    .noRippleClickable { onItemSelected() }
            ) {
                Text(
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.Center),
                    text = text,
                    color = contentColor,
                    textAlign = TextAlign.Center,
                    style = TextStyle(
                        fontFamily = montserratFamily,
                        fontWeight = FontWeight.SemiBold
                    )
                )
            }
        }
        bottomContent()
    }
}

@Preview(showBackground = true)
@Composable
fun SelectableCircleItemPreview() {
    OneAppTheme {
        SelectableCircleItem(
            modifier = Modifier.size(50.dp),
            text = "3"
        )
    }
}
