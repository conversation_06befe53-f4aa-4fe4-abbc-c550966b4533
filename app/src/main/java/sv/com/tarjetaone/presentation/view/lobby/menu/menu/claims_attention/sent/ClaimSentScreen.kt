package sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.sent

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.FailureFullScreen
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.common.confirmation_gradient.SuccessGradientScreen

@Composable
fun ClaimSentScreen(viewModel: ClaimSentViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    LaunchedEffect(Unit) {
        viewModel.onEvent(ClaimSentUiEvent.OnStart)
    }
    if (uiState.isPriority) {
        FailureFullScreen(
            description = UiText.StringResource(R.string.about_your_claim),
            onButtonClick = { viewModel.onEvent(ClaimSentUiEvent.OnClaimDetailClick) },
            buttonVariant = ButtonVariant.PRIMARY_VARIANT,
            textButton = UiText.StringResource(R.string.understood)
        )
    } else {
        SuccessGradientScreen(
            title = stringResource(id = R.string.done_label),
            message = stringResource(id = R.string.claim_complete_message),
            buttonText = stringResource(id = R.string.continue_button_label),
            onButtonClick = { viewModel.onEvent(ClaimSentUiEvent.OnClaimDetailClick) }
        )
    }
}
