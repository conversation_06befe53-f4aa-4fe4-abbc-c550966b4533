package sv.com.tarjetaone.presentation.compose.uicomponent.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.getContainerColor
import sv.com.tarjetaone.presentation.compose.util.getContentColor

/**
 * Custom button for the confirmation dialog.
 * Has less internal padding than the regular M3 button.
 */
@Composable
fun ConfirmationDialogButton(
    modifier: Modifier = Modifier,
    text: String,
    buttonVariant: ButtonVariant = ButtonVariant.PRIMARY_VARIANT,
    onClick: () -> Unit,
) {
    Box(
        modifier = modifier
            .sizeIn(
                minHeight = MaterialTheme.customDimens.dimen40,
                minWidth = MaterialTheme.customDimens.dimen140
            )
            .clip(RoundedCornerShape(MaterialTheme.customDimens.dimen6))
            .then(
                if (buttonVariant == ButtonVariant.TERTIARY_VARIANT) {
                    // Tertiary has transparent background
                    Modifier
                        .padding(MaterialTheme.customDimens.dimen1)
                        .border(
                            width = MaterialTheme.customDimens.dimen1,
                            color = buttonVariant.getContentColor(),
                            shape = RoundedCornerShape(MaterialTheme.customDimens.dimen6)
                        )
                } else {
                    // Other variants have solid background
                    Modifier.background(buttonVariant.getContainerColor())
                }
            )
            .clickable(onClick = onClick),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall.copy(
                fontWeight = FontWeight.SemiBold,
                color = buttonVariant.getContentColor(),
            ),
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(MaterialTheme.customDimens.dimen8)
        )
    }
}

@Preview
@Composable
private fun ConfirmationDialogButtonPreview() {
    OneAppTheme {
        ConfirmationDialogButton(
            text = "Mensaje de texto",
            onClick = {},
            buttonVariant = ButtonVariant.TERTIARY_VARIANT
        )
    }
}
