package sv.com.tarjetaone.presentation.view.common.otpvalidation

import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.data.utils.countDownFlow
import sv.com.tarjetaone.domain.entities.response.ContactType
import sv.com.tarjetaone.domain.entities.response.OtpMethod
import sv.com.tarjetaone.domain.entities.response.OtpType
import sv.com.tarjetaone.presentation.compose.util.InfoStatus

abstract class OtpValidationBaseViewModel : BaseViewModel() {
    protected val _uiState = MutableStateFlow(OtpValidationUiState())
    val uiState = _uiState.asStateFlow()

    private var timerJob: Job? = null

    /**
     * Starts the OTP countdown timer. Cancels any existing timer job before starting a new one.
     */
    protected fun startOtpCountdown() {
        timerJob?.cancel()
        timerJob = viewModelScope.launch {
            _uiState.update { it.copy(otpStatus = InfoStatus.None) }
            countDownFlow(OTP_LIFE_TIME_MS, OTP_INTERVAL).collectLatest { remaining ->
                _uiState.update { it.copy(otpLifeTime = (remaining / OTP_INTERVAL).toInt()) }
            }
        }.apply {
            invokeOnCompletion { cause ->
                if (cause != null) return@invokeOnCompletion
                _uiState.update {
                    it.copy(
                        otpLifeTime = ZERO_VALUE,
                        otpStatus = InfoStatus.Warning,
                    )
                }
            }
        }
    }

    protected open fun onStart() {
        startOtpCountdown()
    }

    abstract fun resendOtp(type: OtpType, method: OtpMethod)

    abstract fun onValidateOtp(code: String)

    private fun onResendOtpClick() {
        if (uiState.value.contactType == ContactType.Phone) {
            _uiState.update { it.copy(showOtpMethodDialog = true) }
        } else {
            resendOtp(OtpType.EMAIL, OtpMethod.EMAIL)
        }
    }

    private fun onSelectOtpMethod(method: OtpMethod) {
        _uiState.update { it.copy(showOtpMethodDialog = false) }
        resendOtp(OtpType.PHONE, method)
    }

    private fun onCodeChange(code: String) {
        _uiState.update {
            it.copy(
                code = code,
                otpMessage = null,
                otpStatus = InfoStatus.None
            )
        }
    }

    private fun onDismissSelectOtpMethod() {
        _uiState.update { it.copy(showOtpMethodDialog = false) }
    }

    private fun onHideSnackBar() = _uiState.update { it.copy(isSnackBarVisible = false) }

    protected fun showSnackBar() = _uiState.update { it.copy(isSnackBarVisible = true) }

    fun onEvent(event: OtpValidationUiEvent) {
        when (event) {
            OtpValidationUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            OtpValidationUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            is OtpValidationUiEvent.OnStart -> onStart()

            is OtpValidationUiEvent.OnCodeChange -> onCodeChange(event.code)
            is OtpValidationUiEvent.OnValidateOtp -> onValidateOtp(event.code)
            OtpValidationUiEvent.OnResendOtpClick -> onResendOtpClick()
            OtpValidationUiEvent.OnDismissSelectOtpMethod -> onDismissSelectOtpMethod()
            is OtpValidationUiEvent.OnSelectOtpMethod -> onSelectOtpMethod(event.method)
            OtpValidationUiEvent.OnHideSnackBar -> onHideSnackBar()
        }
    }

    companion object {
        private const val OTP_LIFE_TIME_MS = 60000L
        private const val OTP_INTERVAL = 1000L

        // Resend otp error codes
        const val MAX_REQUEST_OTP = 3
        const val MAX_REQUEST_OTP_BAES = 2

        // Otp validation error codes
        const val MAX_VALIDATION_ATTEMPTS_CODE = 7
        const val INVALID_OTP_CODE = 2
        const val EXPIRED_OTP_CODE = 4
    }
}
