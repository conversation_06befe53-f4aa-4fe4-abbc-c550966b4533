package sv.com.tarjetaone.presentation.compose.uicomponent.text

import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.presentation.compose.theme.customColors

@Composable
@Preview
fun GradientTextPreview() {
    Text(
        modifier = Modifier.textBrush(
            brush = Brush.linearGradient(
                colors = MaterialTheme.customColors.primaryGradient.reversed()
            )
        ),
        text = "Example Gradient Text",
        style = MaterialTheme.typography.bodySmall
    )
}

/**
 * Custom modifier to apply a gradient to a text.
 * @param brush The gradient brush to apply to the text.
 */
fun Modifier.textBrush(brush: Brush) = this
    .graphicsLayer(alpha = ALPHA_LAYER)
    .drawWithCache {
        onDrawWithContent {
            drawContent()
            drawRect(brush, blendMode = BlendMode.SrcAtop)
        }
    }

/**
 * Controls the opacity of the gradient applied to the text.
 * The lower the value, the more opaque the gradient will be.
 * */
const val ALPHA_LAYER = 0.99f