package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.installmentpayment

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.SheetState
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withLink
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.THREE_FLOAT_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.TWO_FLOAT_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.core.utils.extensions.configCurrencyWithFractions
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.response.InstallmentTermsUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.card.SimpleCardComponent
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedDropdown
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer4
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.installmentpayment.InstallmentPaymentViewModel.Companion.INSTALLMENT_PAYMENT_MORE_INFO
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.installmentpayment.InstallmentPaymentViewModel.Companion.ZERO_INTEREST_RATE_STRING
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.installmentpayment.component.InstallmentPaymentBottomSheet
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.mypayments.component.InfoLine

@Composable
fun InstallmentPaymentScreen(
    viewModel: InstallmentPaymentViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    InstallmentPaymentScreen(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun InstallmentPaymentScreen(
    uiState: InstallmentPaymentUiState,
    onEvent: (InstallmentPaymentUiEvent) -> Unit,
) {
    val bottomSheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = true,
    )
    LaunchedEffect(Unit) {
        onEvent(InstallmentPaymentUiEvent.OnStart)
    }
    ScreenWithTopAppBar(
        onLeftButtonClick = { onEvent(InstallmentPaymentUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(InstallmentPaymentUiEvent.OnTwilioClick) },
        title = stringResource(id = R.string.installment_payment_title),
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            InstallmentDetailCard(
                installments = uiState.installments,
                selectedInstallment = uiState.selectedInstallment,
                storeName = uiState.storeName,
                totalAmount = uiState.totalAmount,
                availableInstallmentSlots = uiState.availableInstallmentSlots,
                onSelectInstallment = { onEvent(InstallmentPaymentUiEvent.OnSelectInstallment(it)) },
                onKnowMoreClick = { onEvent(InstallmentPaymentUiEvent.OnKnowMoreClick) }
            )
            Spacer1f()
            OneButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        horizontal = MaterialTheme.customDimens.dimen24,
                        vertical = MaterialTheme.customDimens.dimen16
                    ),
                text = stringResource(
                    id = R.string.installment_payment_button_label,
                    uiState.selectedInstallment.installmentNumbers?.toString().orEmpty()
                ),
                onClick = { onEvent(InstallmentPaymentUiEvent.OnContinueClick) }
            )
            if (uiState.showBottomSheet) {
                InstallmentBottomSheetContainer(
                    bottomSheetState = bottomSheetState,
                    bottomSheetType = uiState.bottomSheetType,
                    onEvent = onEvent
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun InstallmentBottomSheetContainer(
    bottomSheetState: SheetState,
    bottomSheetType: InstallmentBottomSheetType? = null,
    onEvent: (InstallmentPaymentUiEvent) -> Unit,
) {
    InstallmentPaymentBottomSheet(
        bottomSheetState = bottomSheetState,
        onDismiss = { onEvent(InstallmentPaymentUiEvent.OnDismissBottomSheet) },
    ) {
        when (bottomSheetType) {
            is InstallmentBottomSheetType.Info -> {
                InfoBottomSheetContent(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = MaterialTheme.customDimens.dimen16),
                    onAgreeClick = {
                        onEvent(
                            InstallmentPaymentUiEvent.OnInfoBottomSheetEvent(
                                InfoBottomSheetUiEvent.OnAgreeClick
                            )
                        )
                    }
                )
            }

            is InstallmentBottomSheetType.Confirmation -> {
                ConfirmationBottomSheetContent(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = MaterialTheme.customDimens.dimen16),
                    onContinueClick = {
                        onEvent(
                            InstallmentPaymentUiEvent.OnConfirmationBottomSheetEvent(
                                ConfirmationBottomSheetUiEvent.OnContinueClick
                            )
                        )
                    }
                )
            }

            else -> Unit
        }
    }
}

@Composable
private fun InfoBottomSheetContent(
    modifier: Modifier = Modifier,
    onAgreeClick: () -> Unit,
) {
    Column(modifier = modifier) {
        Text(
            text = stringResource(id = R.string.installment_payment_available_slot_info),
            style = MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.colorScheme.secondary,
            ),
        )
        Spacer24()
        OneButton(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(id = R.string.ok_message),
            onClick = onAgreeClick,
            buttonVariant = ButtonVariant.SECONDARY_VARIANT
        )
    }
}

@Composable
private fun ConfirmationBottomSheetContent(
    modifier: Modifier = Modifier,
    onContinueClick: () -> Unit,
) {
    Column(modifier = modifier) {
        Text(
            text = stringResource(id = R.string.installment_payment_terms_info),
            style = MaterialTheme.typography.bodyMedium.copy(
                color = MaterialTheme.colorScheme.secondary,
            ),
            textAlign = TextAlign.Center,
        )
        Spacer24()
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .border(
                    width = MaterialTheme.customDimens.dimen1,
                    color = MaterialTheme.customColors.gray200,
                    shape = MaterialTheme.shapes.small
                )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        vertical = MaterialTheme.customDimens.dimen8,
                        horizontal = MaterialTheme.customDimens.dimen12,
                    ),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Icon(
                    imageVector = ImageVector.vectorResource(id = R.drawable.ic_patch_check_fill),
                    tint = MaterialTheme.colorScheme.primary,
                    contentDescription = null,
                )
                Spacer16()
                Text(
                    text = stringResource(id = R.string.installment_payment_no_interest_info),
                    style = MaterialTheme.typography.bodySmall.copy(
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.secondary,
                    ),
                )
            }
        }
        Spacer24()
        OneButton(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(id = R.string.continue_button_label),
            onClick = onContinueClick,
        )
    }
}

@Composable
private fun InstallmentDetailCard(
    installments: List<InstallmentTermsUI>,
    selectedInstallment: InstallmentTermsUI,
    storeName: String,
    totalAmount: Double,
    availableInstallmentSlots: Int,
    onSelectInstallment: (InstallmentTermsUI) -> Unit,
    onKnowMoreClick: () -> Unit,
) {
    SimpleCardComponent(
        modifier = Modifier.fillMaxWidth(),
        contentPadding = PaddingValues()
    ) {
        InstallmentDetailTitle()
        HorizontalDivider(color = MaterialTheme.customColors.gray200)
        InstallmentPurchaseInfo(
            storeName = storeName,
            totalAmount = totalAmount
        )
        HorizontalDivider(color = MaterialTheme.customColors.gray200)
        InstallmentPlanSelection(
            installments = installments,
            selectedInstallment = selectedInstallment,
            onSelectInstallment = onSelectInstallment
        )
        HorizontalDivider(color = MaterialTheme.customColors.gray200)
        InstallmentDetailBottomSection(
            onKnowMore = onKnowMoreClick,
            availableInstallmentSlots = availableInstallmentSlots
        )
    }
}

@Composable
private fun InstallmentDetailTitle() {
    Column(modifier = Modifier.fillMaxWidth()) {
        Text(
            modifier = Modifier
                .align(Alignment.CenterHorizontally)
                .padding(vertical = MaterialTheme.customDimens.dimen8),
            text = stringResource(id = R.string.installment_payment_detail),
            style = MaterialTheme.typography.bodyMedium.copy(
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.secondary,
            ),
        )
    }
}

@Composable
private fun InstallmentPurchaseInfo(
    storeName: String,
    totalAmount: Double,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                vertical = MaterialTheme.customDimens.dimen16,
                horizontal = MaterialTheme.customDimens.dimen24
            ),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Text(
            text = storeName,
            style = MaterialTheme.typography.bodySmall.copy(
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.secondary,
            ),
            maxLines = TWO_VALUE,
            overflow = TextOverflow.Ellipsis,
        )
        Text(
            text = totalAmount.configCurrencyWithFractions(),
            style = MaterialTheme.typography.displaySmall.copy(
                fontSize = MaterialTheme.customDimensSp.sp48,
                color = MaterialTheme.customColors.successLightContainer,
            ),
        )
        Text(
            text = stringResource(id = R.string.financed_amount),
            style = MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.colorScheme.onBackground,
            ),
            textAlign = TextAlign.Center,
        )
    }
}

@Composable
private fun InstallmentPlanSelection(
    installments: List<InstallmentTermsUI>,
    selectedInstallment: InstallmentTermsUI,
    onSelectInstallment: (InstallmentTermsUI) -> Unit,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                vertical = MaterialTheme.customDimens.dimen16,
                horizontal = MaterialTheme.customDimens.dimen24
            ),
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                modifier = Modifier.weight(THREE_FLOAT_VALUE),
                text = stringResource(id = R.string.installment_term_label),
                style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.SemiBold),
                color = MaterialTheme.customColors.gray5
            )
            SimpleElevatedDropdown(
                modifier = Modifier.weight(TWO_FLOAT_VALUE),
                items = installments,
                itemLabel = {
                    stringResource(
                        id = R.string.installments_dropdown_label,
                        it.installmentNumbers.orZero()
                    )
                },
                onValueChange = { onSelectInstallment(it) },
                value = selectedInstallment,
            )
        }
        Spacer8()
        InfoLine(
            label = stringResource(id = R.string.monthly_installment_label),
            value = selectedInstallment.amount?.configCurrencyWithFractions().orEmpty(),
            modifier = Modifier.fillMaxWidth()
        )
        Spacer8()
        InfoLine(
            label = stringResource(id = R.string.installment_interest_rate_label),
            value = ZERO_INTEREST_RATE_STRING,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

@Composable
private fun InstallmentDetailBottomSection(
    onKnowMore: () -> Unit,
    availableInstallmentSlots: Int,
) {
    val knowMoreText = buildAnnotatedString {
        withLink(
            LinkAnnotation.Clickable(tag = INSTALLMENT_PAYMENT_MORE_INFO) {
                onKnowMore()
            }
        ) {
            append(stringResource(id = R.string.know_more_label))
        }
    }
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                vertical = MaterialTheme.customDimens.dimen16,
                horizontal = MaterialTheme.customDimens.dimen24
            ),
    ) {
        InfoLine(
            label = stringResource(id = R.string.available_installment_slots),
            value = availableInstallmentSlots.toString(),
            modifier = Modifier.fillMaxWidth()
        )
        BasicText(
            text = knowMoreText,
            style = MaterialTheme.typography.labelMedium.copy(
                color = MaterialTheme.colorScheme.primary,
                textDecoration = TextDecoration.Underline
            ),
        )
        Spacer16()
        InstallmentPaymentInfo()
    }
}

@Composable
private fun InstallmentPaymentInfo() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = MaterialTheme.customColors.lightBlueBackground,
                shape = MaterialTheme.shapes.small
            )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    vertical = MaterialTheme.customDimens.dimen8,
                    horizontal = MaterialTheme.customDimens.dimen12,
                ),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                imageVector = ImageVector.vectorResource(id = R.drawable.ic_check_circle_white),
                tint = MaterialTheme.colorScheme.secondary,
                contentDescription = null,
            )
            Spacer16()
            Column(modifier = Modifier.fillMaxWidth()) {
                Text(
                    text = stringResource(id = R.string.installment_info_title),
                    style = MaterialTheme.typography.labelMedium.copy(
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.secondary,
                    ),
                )
                Spacer4()
                Text(
                    text = stringResource(id = R.string.installment_info_description),
                    style = MaterialTheme.typography.labelMedium.copy(
                        color = MaterialTheme.colorScheme.secondary,
                    ),
                )
            }
        }
    }
}

@Preview
@Composable
fun InstallmentPaymentScreenPreview() {
    OneAppTheme {
        InstallmentPaymentScreen(
            uiState = InstallmentPaymentUiState(
                storeName = "Store Name",
                totalAmount = 1500.0,
                selectedInstallment = InstallmentTermsUI(
                    installmentNumbers = 3,
                    amount = 100.0,
                ),
            ),
            onEvent = { }
        )
    }
}
