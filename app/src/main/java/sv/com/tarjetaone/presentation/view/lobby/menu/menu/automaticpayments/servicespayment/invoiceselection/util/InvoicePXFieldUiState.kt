package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.invoiceselection.util

import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.ONE_HUNDRED_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.YEAR_MONTH_DAY_WITH_SLASH
import sv.com.tarjetaone.common.utils.AppConstants.YEAR_MONTH_DAY_WITH_SLASH_AND_TIME
import sv.com.tarjetaone.common.utils.extensions.configCurrencyWithFractions
import sv.com.tarjetaone.common.utils.extensions.formatToTwoDecimals
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldType
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldValueType

data class InvoicePXFieldUiState(
    val fieldKey: String,
    val pxField: PXFieldUI,
    val value: String = EMPTY_STRING,
    val hasError: Boolean = false
) {
    val isValidField = !this.pxField.required || this.value.isNotBlank()

    fun toPXFieldUI(): PXFieldUI {
        return if (pxField.type == PXFieldType.SelectBox) {
            val selectedOption = pxField.value.asValueList()?.value?.entries?.find { it.key == this.value }
            pxField.copy(
                value = pxField.value.asValueList()?.copy(
                    value = selectedOption?.let {
                        mapOf(it.key to it.value.copy(selected = true))
                    }.orEmpty()
                ) ?: pxField.value
            )
        } else {
            pxField.copy(
                value = pxField.value.asValueString()?.copy(
                    value = formatValue()
                ) ?: pxField.value
            )
        }
    }

    private fun formatValue(): String = when (pxField.value.asValueString()?.type) {
        PXFieldValueType.Decimal,
        PXFieldValueType.Double,
        PXFieldValueType.Money -> {
            if (pxField.isOutputOrHidden().not())
                (value.toDoubleOrNull().orZero().div(ONE_HUNDRED_VALUE)).formatToTwoDecimals()
            else
                value.toDoubleOrNull()?.formatToTwoDecimals().orEmpty()
        }
        else -> value
    }

    fun getFormattedValue(): String {
        return when (this.pxField.value.asValueString()?.type) {
            PXFieldValueType.Money -> {
                this.value.toDoubleOrNull()?.configCurrencyWithFractions().orEmpty()
            }
            PXFieldValueType.Date -> {
                this.value.getFormattedDateFromTo(
                    YEAR_MONTH_DAY_WITH_SLASH_AND_TIME,
                    YEAR_MONTH_DAY_WITH_SLASH
                )
            }
            else -> this.value
        }
    }
}
