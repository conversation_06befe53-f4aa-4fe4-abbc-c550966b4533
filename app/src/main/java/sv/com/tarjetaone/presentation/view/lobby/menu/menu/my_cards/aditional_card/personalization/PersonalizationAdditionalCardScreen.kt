package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.personalization

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.common.utils.CardColor
import sv.com.tarjetaone.core.utils.facephi.CaptureType
import sv.com.tarjetaone.core.utils.facephi.rememberFacephiWidgetLauncher
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.compose.uicomponent.personalization.CARD_BACK_PAGE
import sv.com.tarjetaone.presentation.compose.uicomponent.personalization.ColorSelectorContainer
import sv.com.tarjetaone.presentation.compose.uicomponent.personalization.CreditCardComponent
import sv.com.tarjetaone.presentation.compose.uicomponent.personalization.NameSelectorComponent
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.compose.util.Spacer8

@Composable
fun PersonalizationAdditionalCardScreen(
    viewModel: PersonalizationAdditionalCardViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    SideEffectHandler(events = viewModel.sideEffects) {
        PersonalizationAdditionalCardScreenContent(
            uiState = uiState,
            onEvent = viewModel::onEvent
        )
    }
}

@Composable
fun PersonalizationAdditionalCardScreenContent(
    uiState: PersonalizationAdditionalCardUiState,
    onEvent: (PersonalizationAdditionalCardUiEvent) -> Unit = {}
) {
    val context = LocalContext.current
    val facephiWidgetLauncher = rememberFacephiWidgetLauncher(CaptureType.Document()) {
        onEvent(PersonalizationAdditionalCardUiEvent.OnDocumentCapture(it))
    }
    val defaultPadding = MaterialTheme.customDimens.dimen32
    val pagerState = rememberPagerState(
        pageCount = { TWO_VALUE }
    )

    LaunchedEffect(Unit) {
        onEvent(PersonalizationAdditionalCardUiEvent.OnStart)
    }

    LaunchedEffect(uiState) {
        if (uiState.animateToBack && pagerState.currentPage != CARD_BACK_PAGE)
            pagerState.animateScrollToPage(CARD_BACK_PAGE)
    }

    DisposableEffect(Unit) {
        onDispose {
            onEvent(PersonalizationAdditionalCardUiEvent.OnResetState)
        }
    }

    ScreenWithTopAppBar(
        title = stringResource(R.string.customize_your_card),
        isProgressbarVisible = true,
        progress = AppConstants.EIGHT_VALUE_PERCENT,
        contentPadding = PaddingValues(
            start = defaultPadding,
            end = defaultPadding,
            top = defaultPadding,
            bottom = MaterialTheme.customDimens.dimen16
        ),
        onLeftButtonClick = {
            onEvent(PersonalizationAdditionalCardUiEvent.OnNavigateBack)
        },
        onRightButtonClick = {
            onEvent(PersonalizationAdditionalCardUiEvent.OnTwilioButtonClick)
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = MaterialTheme.customDimens.dimen32)
                .background(color = MaterialTheme.colorScheme.surface)
                .verticalScroll(rememberScrollState())
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Center
            ) {
                CreditCardComponent(
                    pagerState = pagerState,
                    cardColors = uiState.selectedColor ?: CardColor(),
                    nameOnCard = uiState.nameOnCard
                )
                Spacer32()
                ColorSelectorContainer(
                    colors = uiState.colors,
                    cardColor = uiState.selectedColor ?: CardColor()
                ) {
                    onEvent(
                        PersonalizationAdditionalCardUiEvent.OnCardColorChange(it)
                    )
                }
            }
            Spacer32()
            Text(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(id = R.string.select_card_names),
                style = MaterialTheme.typography.bodySmall,
                textAlign = TextAlign.Center
            )
            Spacer8()
            NameSelectorComponent(
                names = uiState.customerNames,
                onNameSelected = {
                    onEvent(PersonalizationAdditionalCardUiEvent.OnNameSelected(it))
                }
            )
            Spacer24()
            CardLimitSliderComponent(
                uiState = uiState,
                onUiEvent = onEvent
            )
            Spacer1f()
            OneButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = MaterialTheme.customDimens.dimen16),
                text = stringResource(id = R.string.save_personalization_label),
                enabled = uiState.isFormValid,
                onClick = {
                    onEvent(PersonalizationAdditionalCardUiEvent.OnSavePersonalizationCard)
                }
            )
            Spacer16()
            OneButton(
                modifier = Modifier.fillMaxWidth(),
                buttonVariant = ButtonVariant.SECONDARY_VARIANT,
                text = stringResource(R.string.capture_document_again),
                onClick = {
                    facephiWidgetLauncher.initWidget(context)
                }
            )
            Spacer32()
        }
    }
}

@Preview
@Composable
private fun PersonalizationAdditionalCardPreview() {
    OneAppTheme {
        PersonalizationAdditionalCardScreenContent(
            uiState = PersonalizationAdditionalCardUiState()
        )
    }
}