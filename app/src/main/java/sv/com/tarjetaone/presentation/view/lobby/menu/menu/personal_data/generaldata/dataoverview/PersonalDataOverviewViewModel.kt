package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.generaldata.dataoverview

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.duiFormat
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.core.utils.extensions.nitFormat
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.core.utils.extensions.setGender
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.response.CustomerDataUI
import sv.com.tarjetaone.domain.usecases.personalData.personalinfo.GetPersonalInfoUseCase
import sv.com.tarjetaone.presentation.helpers.capitalizeAllWords
import sv.com.tarjetaone.presentation.helpers.capitalizeStringByComa
import sv.com.tarjetaone.presentation.view.utils.DAY_MONTH_YEAR_FORMAT
import sv.com.tarjetaone.presentation.view.utils.YEAR_MONTH_DAY_DASH_FORMAT

@HiltViewModel
class PersonalDataOverviewViewModel @Inject constructor(
    private val getPersonalInfoUseCase: GetPersonalInfoUseCase,
    private val sharedPrefs: SecureSharedPreferencesRepository
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(PersonalDataOverviewUiState())
    val uiState = _uiState.asStateFlow()

    private fun getPersonalInfo() {
        sendEvent(UiEvent.Loading(true))
        viewModelScope.launch {
            getPersonalInfoUseCase(sharedPrefs.getCustomerId().orZero()).executeUseCase { response ->
                onPersonalInfoFetched(response.data)
                sendEvent(UiEvent.Loading(false))
            }
        }
    }

    private fun onPersonalInfoFetched(customerData: CustomerDataUI) {
        val names = customerData.fullName?.capitalizeAllWords()
        val marriedName = customerData.marriedSurname?.capitalizeAllWords()
        _uiState.update {
            it.copy(
                customerData = customerData.copy(
                    fullName = "$names $marriedName",
                    dui = customerData.dui?.duiFormat(),
                    nit = customerData.nit?.nitFormat(),
                    duiExpirationDate = customerData.duiExpirationDate?.getFormattedDateFromTo(
                        YEAR_MONTH_DAY_DASH_FORMAT,
                        DAY_MONTH_YEAR_FORMAT
                    ),
                    birthDate = customerData.birthDate?.getFormattedDateFromTo(
                        YEAR_MONTH_DAY_DASH_FORMAT,
                        DAY_MONTH_YEAR_FORMAT
                    ),
                    address = customerData.address?.capitalizeStringByComa(),
                    genderCode = customerData.genderCode?.setGender()
                )
            )
        }
    }

    fun onScreenUiEvent(screenUiEvent: PersonalDataOverviewUiEvent) {
        when (screenUiEvent) {
            PersonalDataOverviewUiEvent.OnUpdateYourDocumentClick -> {
                sendEvent(
                    UiEvent.Navigate(
                        PersonalDataOverviewFragmentDirections.actionPersonalDataOverviewFragmentToPrepareForPicturePersonalInfoFragment()
                    )
                )
            }
            PersonalDataOverviewUiEvent.OnStart -> getPersonalInfo()
            PersonalDataOverviewUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            PersonalDataOverviewUiEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
        }
    }
}
