package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.generaldata.prepareforduicapture

import android.Manifest
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.rememberPermissionState
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.facephi.CaptureType
import sv.com.tarjetaone.core.utils.facephi.rememberFacephiWidgetLauncher
import sv.com.tarjetaone.core.utils.shouldShowRationale
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.card.HorizontalIconWithText
import sv.com.tarjetaone.presentation.compose.uicomponent.card.SimpleCardComponent
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.Spacer12
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.compose.util.Spacer8

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun PersonalDataCaptureDuiScreen(
    onEvent: (PersonalDataCaptureDuiUiEvent) -> Unit,
) {
    val cameraPermission = Manifest.permission.CAMERA
    val context = LocalContext.current
    val facephiLauncher = rememberFacephiWidgetLauncher(
        type = CaptureType.Document(),
        onFailure = {
            onEvent(PersonalDataCaptureDuiUiEvent.OnDocumentCaptureFailed(it))
        },
        onCaptured = {
            onEvent(PersonalDataCaptureDuiUiEvent.OnDocumentCaptured(it))
        }
    )
    val cameraPermissionState = rememberPermissionState(cameraPermission) { isGranted ->
        if (isGranted) {
            facephiLauncher.initWidget(context)
        } else {
            onEvent(
                PersonalDataCaptureDuiUiEvent.OnCameraPermissionDenied(
                    showRationale = context.shouldShowRationale(cameraPermission)
                )
            )
        }
    }
    ScreenWithTopAppBar(
        title = stringResource(R.string.personal_overview_capture_dui_screen_title),
        isLeftButtonVisible = false,
        onLeftButtonClick = { },
        onRightButtonClick = { onEvent(PersonalDataCaptureDuiUiEvent.OnSupportClick) },
    ) {
        Column(modifier = Modifier.padding(horizontal = MaterialTheme.customDimens.dimen32)) {
            Text(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(id = R.string.personal_overview_capture_dui_screen_subtitle),
                style = MaterialTheme.typography.bodySmall,
                textAlign = TextAlign.Center
            )
            Spacer12()
            Spacer1f()
            SimpleCardComponent(
                modifier = Modifier.fillMaxWidth(),
                shape = MaterialTheme.shapes.medium,
                elevation = CardDefaults.cardElevation(
                    defaultElevation = MaterialTheme.customDimens.dimen2
                )
            ) {
                Column {
                    Text(
                        text = stringResource(R.string.personal_overview_capture_dui_card_title),
                        style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Bold),
                        color = MaterialTheme.colorScheme.onBackground
                    )
                    Spacer8()
                    HorizontalIconWithText(
                        leadingIcon = R.drawable.ic_card_aditional_card,
                        leadingIconAlignment = Alignment.CenterVertically,
                        annotatedString = buildAnnotatedString {
                            withStyle(
                                style = SpanStyle(
                                    fontWeight = FontWeight.Normal,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                                    fontSize = MaterialTheme.customDimensSp.sp14
                                )
                            ) {
                                append(stringResource(id = R.string.personal_overview_capture_dui_sub_title))
                            }
                            append("\n")
                            withStyle(
                                style = SpanStyle(
                                    fontWeight = FontWeight.Normal,
                                    color = MaterialTheme.colorScheme.onBackground,
                                    fontSize = MaterialTheme.customDimensSp.sp12,
                                )
                            ) {
                                append(stringResource(id = R.string.personal_overview_capture_dui_description))
                            }
                        },
                        style = MaterialTheme.typography.bodySmall.copy(
                            lineHeight = MaterialTheme.customDimensSp.sp16
                        )
                    )
                }
            }
            Spacer1f()
            OneButton(
                text = stringResource(id = R.string.continue_button_label),
                onClick = { cameraPermissionState.launchPermissionRequest() },
                modifier = Modifier.fillMaxWidth(),
            )
            Spacer32()
        }
    }
}

@Preview(showSystemUi = true)
@Composable
fun PersonalDataCaptureDuiPreview() {
    OneAppTheme {
        PersonalDataCaptureDuiScreen {}
    }
}
