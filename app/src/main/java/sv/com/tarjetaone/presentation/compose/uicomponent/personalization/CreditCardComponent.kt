package sv.com.tarjetaone.presentation.compose.uicomponent.personalization

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import kotlin.math.absoluteValue
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.common.utils.CardColor
import sv.com.tarjetaone.presentation.compose.modifier.noRippleClickable
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.common.CustomDotsIndicator
import sv.com.tarjetaone.presentation.compose.uicomponent.common.ResizableText
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.flipTransition
import sv.com.tarjetaone.presentation.compose.util.setBackgroundFromList
import sv.com.tarjetaone.presentation.compose.util.toColor

@Composable
fun CreditCardComponent(
    pagerState: PagerState = rememberPagerState(
        initialPage = CARD_FRONT_PAGE,
        pageCount = { TWO_VALUE }
    ),
    cardColors: CardColor,
    nameOnCard: String,
    isClickable: Boolean = false
) {
    val coroutineScope = rememberCoroutineScope()
    val clickableModifier = if (isClickable) Modifier.noRippleClickable {
        coroutineScope.launch {
            pagerState.animateScrollToPage(
                if (pagerState.currentPage == CARD_BACK_PAGE) {
                    CARD_FRONT_PAGE
                } else {
                    CARD_BACK_PAGE
                }
            )
        }
    } else Modifier
    val scale by remember {
        derivedStateOf {
            1f - (pagerState.currentPageOffsetFraction.absoluteValue) * SCALE_TRANSFORMATION_VALUE
        }
    }
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        HorizontalPager(
            state = pagerState,
            modifier = Modifier
                .size(
                    MaterialTheme.customDimens.dimen240,
                    MaterialTheme.customDimens.dimen360
                )
                .scale(1f, scale)
                .then(clickableModifier)
        ) { page ->
            Box(
                modifier = Modifier
                    .flipTransition(page, pagerState)
                    .setBackgroundFromList(
                        colors = cardColors.colors.map { it.toColor() },
                        shape = MaterialTheme.shapes.large
                    )
            ) {
                when (page) {
                    CARD_FRONT_PAGE -> CreditCardFront(
                        iconColor = cardColors.iconColor.toColor()
                    )

                    CARD_BACK_PAGE -> CreditCardBack(
                        name = nameOnCard,
                        iconColor = cardColors.iconColor.toColor(),
                        textColor = cardColors.textColor.toColor()
                    )
                }
            }
        }
        Spacer16()
        CustomDotsIndicator(
            totalDots = TWO_VALUE,
            activeIndex = pagerState.currentPage,
            activeColor = MaterialTheme.customColors.gray500,
            inactiveColor = MaterialTheme.customColors.gray300,
            dotSize = MaterialTheme.customDimens.dimen8
        )
    }
}

@Composable
fun CreditCardBack(
    name: String,
    iconColor: Color = MaterialTheme.colorScheme.onPrimary,
    textColor: Color = MaterialTheme.colorScheme.onPrimary
) {
    Row(
        modifier = Modifier
            .fillMaxSize()
            .padding(
                start = MaterialTheme.customDimens.dimen16,
                end = MaterialTheme.customDimens.dimen8
            )
    ) {
        Column(
            modifier = Modifier
                .weight(1.5f)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        top = MaterialTheme.customDimens.dimen16
                    ),
                verticalAlignment = Alignment.Bottom,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Image(
                    painter = painterResource(id = R.drawable.logo_bird_card),
                    contentDescription = null,
                )
                Icon(
                    modifier = Modifier
                        .padding(
                            end = MaterialTheme.customDimens.dimen8
                        ),
                    painter = painterResource(id = R.drawable.logo_plus_white_card),
                    contentDescription = null,
                    tint = iconColor
                )
            }
            Spacer16()
            ResizableText(
                text = name,
                color = textColor,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        end = MaterialTheme.customDimens.dimen8
                    ),
                style = MaterialTheme.typography.bodyLarge.copy(
                    fontWeight = FontWeight.Bold
                )
            )
        }
        Column(
            modifier = Modifier
                .weight(0.5f)
                .fillMaxHeight(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                modifier = Modifier
                    .width(MaterialTheme.customDimens.dimen45)
                    .fillMaxSize()
                    .background(Color.Black)
            )
        }
    }
}

@Composable
fun CreditCardFront(
    iconColor: Color = MaterialTheme.colorScheme.onPrimary
) {
    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        Icon(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(
                    start = MaterialTheme.customDimens.dimen16,
                    top = MaterialTheme.customDimens.dimen16
                ),
            painter = painterResource(id = R.drawable.ic_atlantida_logo),
            contentDescription = null,
            tint = iconColor
        )
        Icon(
            modifier = Modifier.align(Alignment.Center),
            painter = painterResource(id = R.drawable.ic_logo_one),
            contentDescription = null,
            tint = iconColor
        )
        Icon(
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(
                    end = MaterialTheme.customDimens.dimen16,
                    bottom = MaterialTheme.customDimens.dimen16
                )
                .size(
                    width = MaterialTheme.customDimens.dimen74,
                    height = MaterialTheme.customDimens.dimen24
                ),
            painter = painterResource(id = R.drawable.ic_visa_icon),
            contentDescription = null,
            tint = iconColor
        )
        Icon(
            modifier = Modifier
                .align(Alignment.BottomStart)
                .padding(
                    start = MaterialTheme.customDimens.dimen16,
                    bottom = MaterialTheme.customDimens.dimen16
                ),
            painter = painterResource(id = R.drawable.ic_signal_card),
            contentDescription = null,
            tint = iconColor
        )
    }
}

@Preview
@Composable
fun CreditCardComponentPreview() {
    val pagerState = rememberPagerState(
        pageCount = { TWO_VALUE }
    )
    CreditCardComponent(
        pagerState = pagerState,
        cardColors = CardPersonalizationDummyData.colors.first(),
        nameOnCard = stringResource(id = R.string.example_name),
    )
}

@Preview
@Composable
fun ClickableCreditCardComponentPreview() {
    val pagerState = rememberPagerState(
        pageCount = { TWO_VALUE }
    )
    CreditCardComponent(
        pagerState = pagerState,
        cardColors = CardPersonalizationDummyData.colors.first(),
        nameOnCard = stringResource(id = R.string.example_name),
        isClickable = true
    )
}

private const val SCALE_TRANSFORMATION_VALUE = .3f
const val CARD_FRONT_PAGE = 0
const val CARD_BACK_PAGE = 1
