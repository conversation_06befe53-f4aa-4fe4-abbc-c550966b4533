package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.references

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.SUCCESS_RESPONSE_CODE
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.response.ReferenceUI
import sv.com.tarjetaone.domain.usecases.personalData.refereces.DeleteCustomerReferenceUseCase
import sv.com.tarjetaone.domain.usecases.personalData.refereces.GetCustomerReferencesUseCase
import sv.com.tarjetaone.presentation.helpers.ReferenceType
import javax.inject.Inject

@HiltViewModel
class ReferencesViewModel @Inject constructor(
    private val getCustomerReferencesUseCase: GetCustomerReferencesUseCase,
    private val deleteCustomerReferenceUseCase: DeleteCustomerReferenceUseCase,
    private val sharedPrefs: SecureSharedPreferencesRepository
) : BaseViewModel() {

    private val _uiState: MutableStateFlow<ReferencesUiState> =
        MutableStateFlow(ReferencesUiState())
    val uiState = _uiState.asStateFlow()

    private fun onStart() {
        sendEvent(UiEvent.Loading(true))
        viewModelScope.launch {
            getCustomerReferencesUseCase(
                customerId = sharedPrefs.getCustomerId().orZero()
            ).executeUseCase { response ->
                sendEvent(UiEvent.Loading(false))
                if (response.statusResponse?.responseStatus?.code == SUCCESS_RESPONSE_CODE) {
                    _uiState.update { state ->
                        state.copy(
                            references = response.data.referenceTypes
                        )
                    }
                } else {
                    showUpsErrorMessage(false) { onBackClick() }
                }
            }
        }
    }

    private fun onDeleteReference(reference: ReferenceUI) {
        _uiState.update { state ->
            state.copy(
                referenceSelected = reference,
                showDeleteConfirmationDialog = true
            )
        }
    }

    private fun onDeleteReferenceConfirmed() {
        _uiState.update { state -> state.copy(showDeleteConfirmationDialog = false) }
        val reference = _uiState.value.referenceSelected ?: return
        sendEvent(UiEvent.Loading(true))
        viewModelScope.launch {
            deleteCustomerReferenceUseCase(
                referenceId = reference.customerReferenceId.orZero(),
                customerId = sharedPrefs.getCustomerId().orZero()
            ).executeUseCase { response ->
                sendEvent(UiEvent.Loading(false))
                if (response.statusResponse?.responseStatus?.code == SUCCESS_RESPONSE_CODE) {
                    showSnackBar()
                    // Remove item from list
                    val updatedList = _uiState.value.references.map { referencesTypesUI ->
                        referencesTypesUI.copy(
                            reference = referencesTypesUI.reference.filterNot { it.customerReferenceId == reference.customerReferenceId }
                        )
                    }
                    _uiState.update { state -> state.copy(references = updatedList) }
                } else {
                    showUpsErrorMessage()
                }
            }
        }
        _uiState.update { state -> state.copy(showDeleteConfirmationDialog = false) }
    }

    private fun onModifyReference(referenceType: ReferenceType, reference: ReferenceUI) {
        sendEvent(
            UiEvent.Navigate(
                ReferencesOverviewFragmentDirections
                    .actionCustomerReferencesOverviewToPrepareForPictureReferencesFragment(
                        referenceType = referenceType,
                        referenceData = reference
                    )
            )
        )
    }

    private fun onAddReference(type: ReferenceType) {
        sendEvent(
            UiEvent.Navigate(
                ReferencesOverviewFragmentDirections
                    .actionCustomerReferencesOverviewToPrepareForPictureReferencesFragment(
                        referenceType = type
                    )
            )
        )
    }

    private fun onDismissDeleteDialog() {
        _uiState.update { state -> state.copy(showDeleteConfirmationDialog = false) }
    }

    private fun onBackClick() {
        sendEvent(UiEvent.NavigateBackTo(R.id.personalDataFragment))
    }

    private fun onHideSnackBar() =
        _uiState.update { state -> state.copy(isSnackBarVisible = false) }

    private fun showSnackBar() = _uiState.update { state -> state.copy(isSnackBarVisible = true) }

    fun onUiEvent(event: ReferencesEvent) {
        when (event) {
            is ReferencesEvent.OnBackClick -> onBackClick()
            is ReferencesEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
            is ReferencesEvent.OnStart -> onStart()
            is ReferencesEvent.OnDeleteReference -> onDeleteReference(event.reference)
            is ReferencesEvent.OnDeleteReferenceConfirmed -> onDeleteReferenceConfirmed()
            is ReferencesEvent.OnModifyReference -> onModifyReference(
                event.referenceType,
                event.reference
            )

            is ReferencesEvent.OnAddNewReference -> onAddReference(event.referenceType)
            is ReferencesEvent.OnDismissDeleteDialog -> onDismissDeleteDialog()
            ReferencesEvent.OnHideSnackBar -> onHideSnackBar()
        }
    }
}
