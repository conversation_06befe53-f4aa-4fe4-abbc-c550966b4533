package sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.deactivate_program_protection.actived_program_protection

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.montserratFamily

@Composable
fun DeactivateProgramProtectionCard(
    modifier: Modifier = Modifier,
    iconId: Int,
    titleCard: String,
    content: @Composable () -> Unit
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.onPrimaryContainer
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 20.dp
        ),
        modifier = modifier.width(330.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 32.dp)
        ) {
            Image(
                painter = painterResource(id = iconId),
                contentDescription = stringResource(id = R.string.protection_program),
                modifier = Modifier
                    .align(Alignment.CenterVertically)
                    .padding(end = 16.dp)
            )
            Column {
                Text(
                    text = titleCard,
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = MaterialTheme.colorScheme.onSurface,
                        lineHeight = 24.sp,
                        fontWeight = FontWeight.Bold
                    )
                )
                CompositionLocalProvider(
                    LocalContentColor provides MaterialTheme.colorScheme.onSurface,
                    LocalTextStyle provides TextStyle(
                        fontFamily = montserratFamily,
                        lineHeight = 14.sp,
                        fontWeight = FontWeight.Normal
                    )
                ) {
                    content()
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun DeactivateProgramProtectionCardPreview() {
    OneAppTheme {
        Surface(
            color = MaterialTheme.colorScheme.inverseSurface
        ) {
            DeactivateProgramProtectionCard(
                modifier = Modifier
                    .padding(16.dp),
                iconId = R.drawable.ic_shield_check_protection,
                titleCard = stringResource(id = R.string.protection_program),
            ) {
                Text(
                    text = stringResource(id = R.string.monthly_charge_label),
                    modifier = Modifier.padding(bottom = 16.dp),
                )
                Text(
                    text = stringResource(id = R.string.with_this_protection_program_you_have_disclaimer),
                    fontSize = 13.sp
                )
                Text(
                    text = " ${stringResource(id = R.string.with_this_protection_program_you_have_options)}",
                    fontSize = 13.sp,
                    overflow = TextOverflow.Visible,
                    modifier = Modifier.padding(start = 4.dp, top = 8.dp)
                )
            }
        }
    }
}