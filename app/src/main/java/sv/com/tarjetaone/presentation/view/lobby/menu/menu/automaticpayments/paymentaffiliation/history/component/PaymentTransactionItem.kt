package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.history.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.transactionlist.RecurringTransactionUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.history.RecurringTransactionDummyItems

@Composable
fun PaymentTransactionItem(
    modifier: Modifier = Modifier,
    transaction: RecurringTransactionUI
) {
    ElevatedCard(
        modifier = modifier.fillMaxWidth(),
        shape = MaterialTheme.shapes.small,
        colors = CardDefaults.elevatedCardColors(
            containerColor = MaterialTheme.colorScheme.background
        )
    ) {
        Row(
            modifier = Modifier.fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.customDimens.dimen16,
                    vertical = MaterialTheme.customDimens.dimen12
                ),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = transaction.transactionDate,
                color = MaterialTheme.colorScheme.onSurface,
                style = MaterialTheme.typography.headlineSmall
            )
            Text(
                text = stringResource(R.string.money_string_format, transaction.amount),
                color = MaterialTheme.colorScheme.primary,
                style = MaterialTheme.typography.headlineSmall
            )
        }
    }
}

@Preview
@Composable
private fun PaymentTransactionItemPreview() {
    OneAppTheme {
        PaymentTransactionItem(
            transaction = RecurringTransactionDummyItems.dummyTransaction
        )
    }
}
