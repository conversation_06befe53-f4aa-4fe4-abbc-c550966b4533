package sv.com.tarjetaone.presentation.compose.theme

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp

@Suppress("MagicNumber")
data class CustomDimensionSp(
    val sp0: TextUnit = 0.sp,
    val sp1: TextUnit = 1.sp,
    val sp10: TextUnit = 10.sp,
    val sp12: TextUnit = 12.sp,
    val sp13: TextUnit = 13.sp,
    val sp14: TextUnit = 14.sp,
    val sp15: TextUnit = 15.sp,
    val sp16: TextUnit = 16.sp,
    val sp17: TextUnit = 17.sp,
    val sp18: TextUnit = 18.sp,
    val sp20: TextUnit = 20.sp,
    val sp21: TextUnit = 21.sp,
    val sp22: TextUnit = 22.sp,
    val sp24: TextUnit = 24.sp,
    val sp25: TextUnit = 25.sp,
    val sp27: TextUnit = 27.sp,
    val sp32: TextUnit = 32.sp,
    val sp37: TextUnit = 37.sp,
    val sp38: TextUnit = 38.sp,
    val sp40: TextUnit = 40.sp,
    val sp44: TextUnit = 44.sp,
    val sp48: TextUnit = 48.sp,
)

val LocalCustomDimensionsSp = compositionLocalOf { CustomDimensionSp() }

val MaterialTheme.customDimensSp: CustomDimensionSp
    @Composable
    @ReadOnlyComposable
    get() = LocalCustomDimensionsSp.current
