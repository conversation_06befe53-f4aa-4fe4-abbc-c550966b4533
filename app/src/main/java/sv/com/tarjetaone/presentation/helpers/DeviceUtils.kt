package sv.com.tarjetaone.presentation.helpers

import android.content.ContentResolver
import android.os.Build
import android.provider.Settings

fun ContentResolver.getDeviceId(): String {
    return Settings.Secure.getString(this, Settings.Secure.ANDROID_ID)
}

fun getDeviceModel(): String {
    val deviceBrandModel = "${Build.MANUFACTURER.capitalize()} ${Build.MODEL}"
    val androidVersion = Build.VERSION.RELEASE
    return "$deviceBrandModel - Android $androidVersion"
}
