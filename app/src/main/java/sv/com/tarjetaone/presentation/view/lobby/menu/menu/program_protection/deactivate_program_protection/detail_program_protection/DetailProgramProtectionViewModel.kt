package sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.deactivate_program_protection.detail_program_protection

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.presentation.view.common.management.DEFAULT_STATUS_COLOR
import sv.com.tarjetaone.presentation.view.common.management.ManagementDetailBaseViewModel
import sv.com.tarjetaone.presentation.view.utils.DAY_OF_WEEK_MONTH_TIME_FORMAT
import sv.com.tarjetaone.presentation.view.utils.YEAR_MONTH_DAY_TIME_FORMAT
import javax.inject.Inject

@HiltViewModel
class DetailProgramProtectionViewModel @Inject constructor(
    imageUtils: ImageUtils,
    savedStateHandle: SavedStateHandle
) : ManagementDetailBaseViewModel(imageUtils) {
    private val args = DetailProgramProtectionFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onStart() {
        args.fraudResponse?.let { fraudArgs ->
            _uiState.update {
                it.copy(
                    managementNumber = fraudArgs.manNumberApp.orEmpty(),
                    managementStatus = fraudArgs.mrStatusNameApp.orEmpty(),
                    managementStatusColor = fraudArgs.mrStatusTextColor ?: DEFAULT_STATUS_COLOR,
                    managementName = fraudArgs.manTypeNameApp.orEmpty(),
                    customerName = fraudArgs.clientName.orEmpty(),
                    creditCardNumber = fraudArgs.cardNumMasked.orEmpty(),
                    creditCardType = fraudArgs.typeCardText.orEmpty(),
                    requestStartDate = fraudArgs.processDate?.getFormattedDateFromTo(
                        YEAR_MONTH_DAY_TIME_FORMAT,
                        DAY_OF_WEEK_MONTH_TIME_FORMAT
                    ).orEmpty(),
                    requestEndDate = fraudArgs.closeDate?.getFormattedDateFromTo(
                        YEAR_MONTH_DAY_TIME_FORMAT,
                        DAY_OF_WEEK_MONTH_TIME_FORMAT
                    ).orEmpty(),
                    managementAttributes = transformAttributesToUiTextList(
                        fraudArgs.manAttributes.orEmpty()
                    ),
                )
            }
        }
    }

    override fun onPrimaryButtonClick() {
        sendEvent(
            UiEvent.Navigate(
                DetailProgramProtectionFragmentDirections.actionHome()
            )
        )
    }
}