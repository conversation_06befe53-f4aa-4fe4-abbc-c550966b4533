package sv.com.tarjetaone.presentation.view.common.reference_form

import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.request.CustomerContactValidationRequestUI
import sv.com.tarjetaone.domain.entities.request.CustomerContactValidationType
import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI
import sv.com.tarjetaone.domain.entities.response.CustomerContactValidationResponseUI
import sv.com.tarjetaone.domain.usecases.catalog.GetCatalogUseCase
import sv.com.tarjetaone.domain.usecases.requestcard.validatecustomercontact.ValidateCustomerContactUseCase
import sv.com.tarjetaone.presentation.helpers.ReferenceType
import sv.com.tarjetaone.presentation.helpers.removeDash

abstract class ReferenceFormBaseViewModel(
    private val getCatalogUseCase: GetCatalogUseCase,
    private val validateCustomerContactUseCase: ValidateCustomerContactUseCase,
    private val sharedPrefsRepo: SecureSharedPreferencesRepository,
) : BaseViewModel() {

    private var phoneValidationJob: Job? = null

    protected fun fetchReferences(referenceType: ReferenceType) {
        sendEvent(SideEffect.Loading(true))
        viewModelScope.launch {
            getCatalogUseCase(
                catalogType = referenceType.getReferenceTypeForCatalog()
            ).executeUseCase(
                onApiErrorAction = { _, _, _, _ ->
                    onFailedFetchCatalog()
                },
                onNetworkErrorAction = {
                    onFailedFetchCatalog()
                }
            ) { response ->
                sendEvent(SideEffect.Loading(false))
                val items = response.dataCatalog?.catalogItemsCollection.orEmpty()
                onCatalogFetched(items)
            }
        }
    }

    protected fun validateCustomerContact(phoneNumber: String) {
        phoneValidationJob?.cancel()
        val request = CustomerContactValidationRequestUI(
            customerId = sharedPrefsRepo.getCustomerId().orZero(),
            contact = phoneNumber.removeDash(),
            type = CustomerContactValidationType.PHONE
        )
        phoneValidationJob = viewModelScope.launch {
            validateCustomerContactUseCase(request).executeUseCase {
                onPhoneNumberValidated(it)
            }
        }
    }

    /**
     * Cancel the current phone validation job. This is useful when the user modifies a valid phone number
     * and starts typing another one.
     * */
    protected fun cancelPhoneValidationJob() = phoneValidationJob?.cancel()

    /**
     * Action to perform when the getCatalogUseCase succeed.
     * Ex. Navigate back, navigate to other screen, etc.
     */
    protected abstract fun onCatalogFetched(referencesList: List<CatalogItemsCollectionUI>)

    /**
     * Action to perform when the getCatalogUseCase fails.
     * Ex. Navigate back, navigate to other screen, etc.
     */
    protected abstract fun onFailedFetchCatalog()

    /**
     * Action to perform when the validateCustomerContactUseCase succeed.
     * Ex. Navigate back, navigate to other screen, etc.
     */
    protected open fun onPhoneNumberValidated(response: CustomerContactValidationResponseUI) = Unit

}
