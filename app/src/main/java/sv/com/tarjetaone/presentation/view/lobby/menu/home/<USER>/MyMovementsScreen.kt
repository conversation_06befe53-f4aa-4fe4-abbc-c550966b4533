package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.paging.LoadState
import androidx.paging.compose.collectAsLazyPagingItems
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.core.utils.extensions.toBoolean
import sv.com.tarjetaone.domain.entities.response.CustomerCCardUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.ToggleableIconButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedDropdown
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleLoadingIndicator
import sv.com.tarjetaone.presentation.compose.uicomponent.common.TransactionsStateFilter
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component.CardTransactionsList
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component.InstallmentSelectionBottomSheet
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component.tutorial.InstallmentsTutorialBottomSheetContent

@Composable
fun MyMovementsScreen(viewModel: MyMovementsViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    SideEffectHandler(viewModel.sideEffects) {
        MyMovementsContent(
            uiState = uiState,
            onEvent = viewModel::onEvent
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MyMovementsContent(
    uiState: MyMovementsState,
    onEvent: (MyMovementsEvent) -> Unit
) {
    val transactionItems = uiState.transactionsPaging?.collectAsLazyPagingItems()
    val bottomSheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = true,
    )
    LaunchedEffect(Unit) {
        onEvent(MyMovementsEvent.OnStart)
    }
    ScreenWithTopAppBar(
        title = stringResource(id = R.string.my_movements),
        onLeftButtonClick = { onEvent(MyMovementsEvent.OnBackClick) },
        onRightButtonClick = { onEvent(MyMovementsEvent.OnSupportClick) },
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.fillMaxSize()
        ) {
            MovementsFiltersSection(
                uiState = uiState,
                onEvent = onEvent
            )
            Spacer16()
            when (transactionItems?.loadState?.refresh) {
                is LoadState.Loading -> SimpleLoadingIndicator(modifier = Modifier.fillMaxSize())
                is LoadState.Error -> {
                    onEvent(MyMovementsEvent.OnTransactionsLoadError { transactionItems.retry() })
                }

                else -> CardTransactionsList(
                    userPoints = uiState.userPoints,
                    searchMode = uiState.searchMode,
                    selectedTransaction = uiState.selectedTransaction,
                    transactionItems = transactionItems,
                    onTransactionAction = { action, transaction ->
                        onEvent(MyMovementsEvent.OnTransactionItemAction(action, transaction))
                    },
                    onAppendError = { onEvent(MyMovementsEvent.OnTransactionsLoadError(it)) }
                )
            }
            if (uiState.showInstallmentsBottomSheet) {
                if (uiState.isInstallmentsTutorialShown) {
                    InstallmentSelectionBottomSheet(
                        bottomSheetState = bottomSheetState,
                        installments = uiState.selectedTransaction?.installmentTerms.orEmpty(),
                        selectedInstallment = uiState.selectedInstallment,
                        onDismissRequest = {
                            onEvent(
                                MyMovementsEvent.OnInstallmentBottomSheetAction(
                                    InstallmentBottomSheetEvent.OnDismiss
                                )
                            )
                        },
                        showLastAmountDisclaimer = uiState.selectedTransaction?.installmentTerms?.any {
                            it.isLastInstallmentAdjusted.toBoolean()
                        } ?: false,
                        onSelectInstallment = {
                            onEvent(
                                MyMovementsEvent.OnInstallmentBottomSheetAction(
                                    InstallmentBottomSheetEvent.OnSelectInstallment(it)
                                )
                            )
                        },
                        onContinueClick = {
                            onEvent(
                                MyMovementsEvent.OnInstallmentBottomSheetAction(
                                    InstallmentBottomSheetEvent.OnContinueClick
                                )
                            )
                        }
                    )
                } else {
                    InstallmentsTutorialBottomSheetContent(
                        onDismiss = {
                            onEvent(MyMovementsEvent.OnDismissInstallmentsTutorial)
                        },
                        onCompleted = {
                            onEvent(MyMovementsEvent.OnTutorialCompleted)
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun MovementsFiltersSection(
    uiState: MyMovementsState,
    onEvent: (MyMovementsEvent) -> Unit
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    Column(
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen8),
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.customDimens.dimen35)
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen10),
            modifier = Modifier.wrapContentSize()
        ) {
            SimpleElevatedDropdown(
                items = uiState.userCards,
                itemLabel = {
                    it.creditCardLabelDetail
                },
                value = uiState.selectedCard,
                onValueChange = { onEvent(MyMovementsEvent.OnCardChange(it)) },
                enabled = uiState.userCards.isNotEmpty(),
                modifier = Modifier.weight(ONE_FLOAT_VALUE),
            )
            ToggleableIconButton(
                icon = R.drawable.ic_search_white,
                iconDescription = null,
                checked = uiState.isSearchQueryVisible,
                onCheckedChange = { onEvent(MyMovementsEvent.OnSearchQueryToggle(it)) },
                modifier = Modifier.size(MaterialTheme.customDimens.dimen46)
            )
            ToggleableIconButton(
                icon = R.drawable.ic_calendar_week,
                iconDescription = null,
                checked = uiState.isCalendarQueryVisible,
                onCheckedChange = { onEvent(MyMovementsEvent.OnCalendarQueryToggle(it)) },
                modifier = Modifier.size(MaterialTheme.customDimens.dimen46)
            )
        }
        AnimatedVisibility(visible = uiState.isSearchQueryVisible) {
            SimpleElevatedTextField(
                value = uiState.searchQuery,
                onValueChange = { onEvent(MyMovementsEvent.OnSearchQueryChange(it)) },
                trailingIcon = {
                    Icon(
                        imageVector = ImageVector.vectorResource(R.drawable.ic_search_white),
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.clickable {
                            onEvent(MyMovementsEvent.OnPerformSearchQuery(uiState.searchQuery))
                            keyboardController?.hide()
                        }
                    )
                },
                placeholder = stringResource(id = R.string.buscar),
                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Search),
                keyboardActions = KeyboardActions(
                    onSearch = {
                        onEvent(MyMovementsEvent.OnPerformSearchQuery(uiState.searchQuery))
                        keyboardController?.hide()
                    }
                )
            )
        }
        AnimatedVisibility(visible = uiState.isCalendarQueryVisible) {
            SimpleElevatedTextField(
                value = uiState.dateRangeDisplay.asString(),
                onValueChange = {},
                trailingIcon = {
                    Icon(
                        imageVector = ImageVector.vectorResource(R.drawable.ic_calendar_week),
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary
                    )
                },
                readOnly = true,
                placeholder = stringResource(id = R.string.date_range),
                onClick = { onEvent(MyMovementsEvent.OnDateRangeClick) }
            )
        }
        TransactionsStateFilter(
            status = uiState.statusFilter,
            firstStatus = MovementStatus.Confirmed,
            firstStatusName = R.string.my_movements_confirmed,
            secondStatus = MovementStatus.Pending,
            secondStatusName = R.string.my_movements_pending,
            onStatusChange = { onEvent(MyMovementsEvent.OnStatusFilterClick(it)) }
        )
    }
}

@Preview
@Composable
private fun MyMovementsScreenPreview() {
    OneAppTheme {
        MyMovementsContent(
            uiState = MyMovementsState(
                isSearchQueryVisible = true,
                isCalendarQueryVisible = true,
                selectedCard = CustomerCCardUI(
                    creditCardId = ZERO_VALUE,
                    creditCardMask = "1234 **** **** 5678",
                    creditCardLabelDetail = "John Doe (1234) - Activa",
                    type = EMPTY_STRING,
                    creditCardStatus = EMPTY_STRING,
                    expirationDate = EMPTY_STRING
                ),
                userCards = listOf(
                    CustomerCCardUI(
                        creditCardId = ZERO_VALUE,
                        creditCardMask = "1234 **** **** 5678",
                        creditCardLabelDetail = "John Doe (1234) - Activa",
                        type = EMPTY_STRING,
                        creditCardStatus = EMPTY_STRING,
                        expirationDate = EMPTY_STRING
                    )
                ),
            ),
            onEvent = {}
        )
    }
}
