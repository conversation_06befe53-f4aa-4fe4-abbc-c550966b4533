package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.analytics.dynatrace.DynatraceEvent
import sv.com.tarjetaone.analytics.dynatrace.DynatraceManager
import sv.com.tarjetaone.common.utils.AppConstants.DAY_MONTH_WITH_SPACES
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.YEAR_MONTH_DAY_NO_SPACES
import sv.com.tarjetaone.common.utils.AppConstants.YEAR_MONTH_DAY_WITH_DASH
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.addDays
import sv.com.tarjetaone.core.utils.extensions.formatDate
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.core.utils.extensions.takeIfNotBlank
import sv.com.tarjetaone.core.utils.extensions.takeIfNotEmpty
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.response.CustomerCCardUI
import sv.com.tarjetaone.domain.entities.response.TransactionsSearchMode
import sv.com.tarjetaone.domain.entities.response.TransactionsUI
import sv.com.tarjetaone.domain.usecases.general.CustomerCCardUseCase
import sv.com.tarjetaone.domain.usecases.transactions.GetCardTransactionsUseCase
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.DialogAction
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.MessageParams
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.OneDialogParams
import sv.com.tarjetaone.presentation.helpers.UiText
import java.util.Date
import javax.inject.Inject

@HiltViewModel
class MyMovementsViewModel @Inject constructor(
    private val getCardTransactionsUseCase: GetCardTransactionsUseCase,
    private val customerCCardUseCase: CustomerCCardUseCase,
    private val sharedPrefs: SecureSharedPreferencesRepository,
    private val dynatraceManager: DynatraceManager
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(MyMovementsState())
    val uiState = _uiState.asStateFlow()

    private val _displayDateRangePicker = MutableStateFlow(false)
    val displayDateRangePicker = _displayDateRangePicker.asStateFlow()

    private val hasSelectedCard: Boolean
        get() = uiState.value.selectedCard != null

    private fun onStart() {
        dynatraceManager.sendInAppEvent(DynatraceEvent.MyMovements.ViewTransactions)
        _uiState.update { it.copy(dateRangeDisplay = formatDefaultDateRange()) }
        getUserCreditCards()
    }

    private fun formatDefaultDateRange(): UiText {
        return sharedPrefs.lastStatementDate?.let {
            UiText.StringResource(
                R.string.my_movements_current_period,
                it.getFormattedDateFromTo(YEAR_MONTH_DAY_WITH_DASH, DAY_MONTH_WITH_SPACES)
            )
        } ?: UiText.StringResource(R.string.default_movements_date_input)
    }

    private fun getUserCreditCards() {
        sendEvent(SideEffect.Loading(true))
        viewModelScope.launch {
            customerCCardUseCase(sharedPrefs.getCustomerId().orZero()).executeUseCase { response ->
                sendEvent(SideEffect.Loading(false))
                _uiState.update {
                    it.copy(
                        userCards = response.creditCards,
                        selectedCard = response.creditCards.firstOrNull()
                    )
                }
                response.creditCards.firstOrNull()?.let {
                    requestTransactions()
                }
            }
        }
    }

    private fun requestTransactions() {
        _uiState.update { state ->
            val searchMode = getSearchMode(state)
            state.copy(
                selectedTransaction = null,
                transactionsPaging = getCardTransactionsUseCase(
                    cardId = state.selectedCard?.creditCardId,
                    mode = if (state.statusFilter == MovementStatus.Confirmed) {
                        searchMode.confirmed
                    } else {
                        searchMode.pending
                    },
                    startDate = state.startDate.takeIfNotEmpty(),
                    endDate = state.endDate.takeIfNotEmpty(),
                    queryText = state.searchQuery.takeIfNotBlank(),
                    onCurrentPoints = { userPoints ->
                        // Updating current user points based on pagination EP response
                        _uiState.update { it.copy(userPoints = userPoints) }
                    }
                ),
                searchMode = searchMode
            )
        }
        _displayDateRangePicker.value = false
    }

    private fun getSearchMode(uiState: MyMovementsState): TransactionsSearchMode {
        with(uiState) {
            val hasQueryText = isSearchQueryVisible && searchQuery.isNotBlank()
            val hasDateRange =
                isCalendarQueryVisible && startDate.isNotEmpty() && endDate.isNotEmpty()

            return when {
                hasQueryText && hasDateRange -> TransactionsSearchMode.ByDateAndText
                hasQueryText -> TransactionsSearchMode.ByText
                hasDateRange -> TransactionsSearchMode.ByDate
                else -> TransactionsSearchMode.ByCurrentPeriod
            }
        }
    }

    private fun onCardChange(card: CustomerCCardUI) {
        var cardHasChanged = false
        _uiState.update {
            cardHasChanged = it.selectedCard != card
            it.copy(selectedCard = card)
        }
        if (cardHasChanged) requestTransactions() // Update movements list only if card changes
    }

    private fun onSearchQueryToggle(value: Boolean) {
        _uiState.update {
            it.copy(
                isSearchQueryVisible = value,
                searchQuery = EMPTY_STRING
            )
        }
        // Update movements when hiding search query
        if (!value && hasSelectedCard) requestTransactions()
    }

    private fun onCalendarQueryToggle(value: Boolean) {
        _uiState.update {
            it.copy(
                isCalendarQueryVisible = value,
                startDate = EMPTY_STRING,
                endDate = EMPTY_STRING,
                dateRangeDisplay = formatDefaultDateRange()
            )
        }
        // Update movements when hiding date query
        if (!value && hasSelectedCard) requestTransactions()
    }

    private fun onSearchQueryChange(query: String) {
        _uiState.update { it.copy(searchQuery = query) }
    }

    private fun onDateRangeChange(start: Long, end: Long) {
        val startDate = Date(start).addDays(1)
        val endDate = Date(end).addDays(1)
        _uiState.update {
            it.copy(
                startDate = startDate.formatDate(YEAR_MONTH_DAY_NO_SPACES),
                endDate = endDate.formatDate(YEAR_MONTH_DAY_NO_SPACES),
                dateRangeDisplay = UiText.StringResource(
                    R.string.my_movements_date_range,
                    startDate.formatDate(DAY_MONTH_WITH_SPACES),
                    endDate.formatDate(DAY_MONTH_WITH_SPACES)
                )
            )
        }
        _displayDateRangePicker.value = false
        if (hasSelectedCard) requestTransactions()
    }

    private fun onDateRangeClick() {
        _displayDateRangePicker.value = true
    }

    private fun onDateRangeDialogDismiss() {
        _displayDateRangePicker.value = false
    }

    private fun onPerformSearchQuery() {
        if (hasSelectedCard) requestTransactions()
    }

    private fun onStatusFilterClick(status: MovementStatus) {
        var statusHasChanged = false
        _uiState.update {
            statusHasChanged = it.statusFilter != status
            it.copy(statusFilter = status)
        }
        // Update movements list only if status changes
        if (statusHasChanged && hasSelectedCard) requestTransactions()
    }

    private fun onTransactionAction(action: TransactionItemAction, transaction: TransactionsUI) {
        val direction = when (action) {
            TransactionItemAction.OnToggleDetails -> {
                _uiState.update {
                    it.copy(
                        selectedTransaction = if (transaction == it.selectedTransaction) {
                            null
                        } else {
                            dynatraceManager.sendInAppEvent(DynatraceEvent.MyMovements.TransactionDetail)
                            transaction
                        }
                    )
                }
                null // ^ no nav direction
            }

            TransactionItemAction.OnPayInInstallmentsClick -> {
                if (sharedPrefs.hasSeenInstallmentTutorial) {
                    _uiState.update {
                        it.copy(
                            showInstallmentsBottomSheet = true,
                        )
                    }
                } else {
                    onInstallmentTutorialBottomSheetVisibility(sharedPrefs.hasSeenInstallmentTutorial)
                    _uiState.update {
                        it.copy(
                            showInstallmentsBottomSheet = !sharedPrefs.hasSeenInstallmentTutorial,
                            selectedInstallment = null,
                        )
                    }
                    sharedPrefs.hasSeenInstallmentTutorial = true
                }
                null // shows bottom sheet
            }

            TransactionItemAction.OnPayWithPointsClick -> {
                dynatraceManager.sendInAppEvent(DynatraceEvent.MyMovements.TransactionPointPayment)
                MyMovementsFragmentDirections.actionMyMovementsFragmentToPayWithPointsGraph(
                    cardId = uiState.value.selectedCard?.creditCardId.orZero(),
                    transaction = transaction,
                    availablePoints = uiState.value.userPoints.amount.orZero()
                )
            }

            TransactionItemAction.OnUnrecognizedPurchaseClick -> {
                dynatraceManager.sendInAppEvent(DynatraceEvent.MyMovements.TransactionReport)
                MyMovementsFragmentDirections.actionMyMovementsFragmentToReportPurchaseFragment(
                    cardId = _uiState.value.selectedCard?.creditCardId.orZero(),
                    transaction = transaction
                )
            }
        }
        direction?.let { sendEvent(UiEvent.Navigate(it)) }
    }

    private fun onTransactionLoadError(onRetryAction: () -> Unit) {
        showOneDialog(
            params = OneDialogParams(
                message = MessageParams(
                    text = UiText.StringResource(R.string.simple_technical_issues_message)
                ),
                primaryAction = DialogAction(
                    text = UiText.StringResource(R.string.try_again_label),
                    onClick = onRetryAction,
                    actionType = DialogAction.ActionType.WARNING
                )
            )
        )
    }

    private fun onInstallmentBottomSheetAction(action: InstallmentBottomSheetEvent) {
        when (action) {
            is InstallmentBottomSheetEvent.OnSelectInstallment -> {
                _uiState.update { it.copy(selectedInstallment = action.installment) }
            }

            is InstallmentBottomSheetEvent.OnContinueClick -> {
                val selectedTransaction = _uiState.value.selectedTransaction
                val selectedInstallment = _uiState.value.selectedInstallment

                if (selectedTransaction != null && selectedInstallment != null) {
                    sendEvent(
                        UiEvent.Navigate(
                            MyMovementsFragmentDirections.actionMyMovementsFragmentToInstallmentPaymentGraph(
                                installments = selectedTransaction.installmentTerms.orEmpty()
                                    .toTypedArray(),
                                selectedInstallment = selectedInstallment,
                                storeName = selectedTransaction.description.orEmpty(),
                                totalAmount = selectedTransaction.amount.orZero().toFloat(),
                                transaction = selectedTransaction,
                            )
                        )
                    )
                }
                _uiState.update { it.copy(showInstallmentsBottomSheet = false) }
            }

            is InstallmentBottomSheetEvent.OnDismiss -> {
                _uiState.update { state ->
                    state.copy(
                        showInstallmentsBottomSheet = false,
                        selectedInstallment = null,
                    )
                }
            }
        }
    }

    private fun onInstallmentTutorialBottomSheetVisibility(visibility: Boolean) {
        _uiState.update { state -> state.copy(isInstallmentsTutorialShown = visibility) }
    }

    private fun onInstallTutorialCompleted() {
        _uiState.update { state ->
            state.copy(
                isInstallmentsTutorialShown = true,
                showInstallmentsBottomSheet = true,
                selectedInstallment = null,
            )
        }
    }

    fun onEvent(event: MyMovementsEvent) {
        when (event) {
            MyMovementsEvent.OnBackClick -> sendEvent(
                UiEvent.Navigate(MyMovementsFragmentDirections.actionHome())
            )

            MyMovementsEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
            is MyMovementsEvent.OnStart -> onStart()
            is MyMovementsEvent.OnCardChange -> onCardChange(event.card)
            is MyMovementsEvent.OnSearchQueryToggle -> onSearchQueryToggle(event.value)
            is MyMovementsEvent.OnCalendarQueryToggle -> onCalendarQueryToggle(event.value)
            is MyMovementsEvent.OnSearchQueryChange -> onSearchQueryChange(event.query)
            is MyMovementsEvent.OnDateRangeChange -> onDateRangeChange(event.start, event.end)
            MyMovementsEvent.OnDateRangeClick -> onDateRangeClick()
            is MyMovementsEvent.OnDateRangeDialogDismiss -> onDateRangeDialogDismiss()
            is MyMovementsEvent.OnPerformSearchQuery -> onPerformSearchQuery()
            is MyMovementsEvent.OnStatusFilterClick -> onStatusFilterClick(event.status)
            is MyMovementsEvent.OnTransactionItemAction -> onTransactionAction(
                event.action,
                event.transaction
            )

            is MyMovementsEvent.OnTransactionsLoadError -> onTransactionLoadError(event.onRetryAction)

            is MyMovementsEvent.OnInstallmentBottomSheetAction -> onInstallmentBottomSheetAction(
                event.action,
            )

            is MyMovementsEvent.OnDismissInstallmentsTutorial ->
                onInstallmentTutorialBottomSheetVisibility(true)

            is MyMovementsEvent.OnTutorialCompleted -> onInstallTutorialCompleted()
        }
    }
}
