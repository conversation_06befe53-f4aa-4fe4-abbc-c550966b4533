package sv.com.tarjetaone.presentation.view.common.termsandconditions

import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.entities.response.AuthorizationV2UI
import sv.com.tarjetaone.domain.usecases.onboarding.fatca.GetAuthorizationUseCase

abstract class TermsAndConditionsBaseViewModel(
    private val getAuthorizationUseCase: GetAuthorizationUseCase,
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(TermsAndConditionsUiState())
    val uiState = _uiState.asStateFlow()

    protected open fun onStart() {
        requestTermsAndConditions()
    }

    private fun requestTermsAndConditions() {
        viewModelScope.launch {
            sendEvent(SideEffect.Loading(true))
            getAuthorizationUseCase(
                authorizationCode = AppConstants.TERMS_CONDITIONS
            ).executeUseCase(
                onSuccessAction = { response ->
                    sendEvent(SideEffect.Loading(false))
                    _uiState.update { it.copy(title = response.data?.authorization?.authorizationName.orEmpty()) }
                    getSectionedTerms(response.data?.authorization)
                }
            )
        }
    }

    private fun getSectionedTerms(termsResponse: AuthorizationV2UI?) {
        termsResponse?.authorizationDescription?.let { terms ->
            _uiState.update { state ->
                state.copy(
                    fullTermsAndConditions = terms,
                    partialTermsAndConditions = if (terms.length > INITIAL_TERM_AND_CONDITION_LENGTH) {
                        terms.substring(ZERO_VALUE, INITIAL_TERM_AND_CONDITION_LENGTH)
                    } else {
                        terms
                    }
                )
            }
        }
    }

    protected open fun showFullTerms() {
        _uiState.update { it.copy(showFullTerms = true) }
    }

    fun onEvent(event: TermsAndConditionsUiEvent) {
        when (event) {
            is TermsAndConditionsUiEvent.OnStart -> onStart()
            is TermsAndConditionsUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            is TermsAndConditionsUiEvent.OnShowFullTerms -> showFullTerms()
        }
    }

    companion object {
        const val INITIAL_TERM_AND_CONDITION_LENGTH = 600
    }
}
