package sv.com.tarjetaone.presentation.view.common.addressdelivery.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.response.AddressDeliveryUI
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.ToggleCheckBoxButton

@Composable
fun DeliveryAddressesList(
    addresses: List<AddressDeliveryUI>,
    selectedItem: AddressDeliveryUI,
    onSelectChange: (AddressDeliveryUI) -> Unit,
) {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen16),
        modifier = Modifier.fillMaxWidth()
            .wrapContentHeight()
            .padding(
                horizontal = MaterialTheme.customDimens.dimen35,
                vertical = MaterialTheme.customDimens.dimen8
            ),
        contentPadding = PaddingValues(vertical = MaterialTheme.customDimens.dimen4)
    ) {
        items(items = addresses) { item ->
            ToggleCheckBoxButton(
                isEnable = item.isValid ?: false,
                selected = item == selectedItem,
                title = item.addressTypeName.orEmpty(),
                subtitle = item.address.orEmpty(),
                modifier = Modifier.fillParentMaxWidth()
                    .heightIn(min = MaterialTheme.customDimens.dimen100),
                titleTextStyle = MaterialTheme.typography.headlineSmall,
                subTitleTextStyle = MaterialTheme.typography.labelMedium,
                onSelectChange = { onSelectChange(item) }
            )
            if (item.isValid == false) {
                OutOfRangeMessageCard()
            }
        }
    }
}

@Composable
fun OutOfRangeMessageCard() {
    Row(
        modifier = Modifier.fillMaxWidth()
            .wrapContentHeight()
            .padding(vertical = MaterialTheme.customDimens.dimen3),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            modifier = Modifier.wrapContentSize()
                .padding(end = MaterialTheme.customDimens.dimen5),
            imageVector = ImageVector.vectorResource(R.drawable.ic_info_2),
            contentDescription = null
        )
        Text(
            color = MaterialTheme.customColors.secondarySolid,
            modifier = Modifier.wrapContentSize()
                .padding(start = MaterialTheme.customDimens.dimen5),
            text = stringResource(id = R.string.residential_not_in_range_label),
            style = MaterialTheme.typography.labelMedium
        )
    }
}
