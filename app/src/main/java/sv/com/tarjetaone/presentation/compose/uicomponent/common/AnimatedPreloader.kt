package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.annotation.RawRes
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import com.airbnb.lottie.LottieCompositionFactory
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.animateLottieCompositionAsState
import com.airbnb.lottie.compose.rememberLottieComposition
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE

/***
 * Composable function to load lottie animations with default configurations
 * @param modifier composable Modifiers to apply in the animation
 * @param animation raw resource to load animation
 * @param contentScale value to set how the animation should be scaled
 * @param iterations value to set number of times that animation should be played
 * @param animationProgress lambda function to get the progress of the animation
 */
@Composable
fun AnimatedPreloader(
    modifier: Modifier = Modifier,
    @RawRes animation: Int,
    isPlaying: Boolean = true,
    animationSpeed: Float = ONE_FLOAT_VALUE,
    contentScale: ContentScale = ContentScale.Fit,
    iterations: Int = LottieConstants.IterateForever,
    animationProgress: (Float) -> Unit = {}
) {
    val context = LocalContext.current
    val preloaderLottieComposition by rememberLottieComposition(
        LottieCompositionSpec.RawRes(animation)
    )

    val preloaderProgress by animateLottieCompositionAsState(
        composition = preloaderLottieComposition,
        speed = animationSpeed,
        iterations = iterations,
        isPlaying = isPlaying
    )

    DisposableEffect(Unit) {
        onDispose {
            LottieCompositionFactory.clearCache(context)
        }
    }

    LaunchedEffect (preloaderProgress) {
        animationProgress(preloaderProgress)
    }

    LottieAnimation(
        composition = preloaderLottieComposition,
        progress = { preloaderProgress },
        modifier = modifier,
        contentScale = contentScale
    )
}
