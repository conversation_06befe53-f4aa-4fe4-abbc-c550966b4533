package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_activation.almost_there

import android.content.Intent
import android.net.Uri
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.core.remoteconfig.RemoteConfigManager
import sv.com.tarjetaone.core.remoteconfig.RemoteProperty
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.request.CardActivationRequestUI
import sv.com.tarjetaone.domain.usecases.mycards.cardactivation.ActivateCardUseCase
import sv.com.tarjetaone.domain.utils.onApiError
import sv.com.tarjetaone.domain.utils.onNetworkError
import sv.com.tarjetaone.domain.utils.onSuccess
import javax.inject.Inject

@HiltViewModel
class AlmostThereActivateCardViewModel @Inject constructor(
    private val activateCardUseCase: ActivateCardUseCase,
    private val sharedPrefs: SecureSharedPreferencesRepository,
    private val remoteConfigManager: RemoteConfigManager,
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(AlmostThereActivateCardUiState())
    val uiState = _uiState.asStateFlow()
    private val args = AlmostThereActivateCardFragmentArgs.fromSavedStateHandle(savedStateHandle)

    private fun activateCard() {
        viewModelScope.launch {
            val request = CardActivationRequestUI(
                args.cardId,
                sharedPrefs.getCustomerId(),
                args.biometryId,
                args.lastCardDigits
            )
            activateCardUseCase(request).onSuccess {
                if (it.statusResponse?.responseStatus?.code == AppConstants.SUCCESS_RESPONSE_CODE) {
                    sendEvent(
                        UiEvent.Navigate(
                            AlmostThereActivateCardFragmentDirections
                                .actionAlmostThereActivateCardToSuccessActivateCard()
                        )
                    )
                } else {
                    _uiState.update { oldState ->
                        oldState.copy(showContactDialog = true)
                    }
                }
            }.onApiError { _, _, _, _ ->
                showUpsErrorMessage(isDismissible = false) {
                    goHome()
                }
            }.onNetworkError {
                showUpsErrorMessage(isDismissible = false) {
                    goHome()
                }
            }
        }
    }

    private fun goHome() {
        sendEvent(
            UiEvent.Navigate(AlmostThereActivateCardFragmentDirections.actionHome())
        )
    }

    private fun onTryAgain() {
        sendEvent(UiEvent.NavigateBack)
    }

    private fun onContactSupport() {
        val contactNumber = remoteConfigManager
            .getProperty(
                RemoteProperty.CustomerSupportInfoProperty
            )?.contactPhone

        sendEvent(
            UiEvent.StartIntent(
                intent = Intent(
                    Intent.ACTION_DIAL,
                    Uri.parse("${AppConstants.INTENT_DIAL_PREFIX}${contactNumber}")
                )
            )
        )
    }

    fun onEvent(event: AlmostThereActivateCardEvent) {
        when (event) {
            is AlmostThereActivateCardEvent.OnContactSupport -> onContactSupport()
            is AlmostThereActivateCardEvent.OnTryAgain -> onTryAgain()
            AlmostThereActivateCardEvent.OnStart -> activateCard()
        }
    }
}
