package sv.com.tarjetaone.presentation.compose.uicomponent.personalization

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.core.utils.extensions.isOdd
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.view.utils.NamesCard

@Composable
fun NameSelector(
    modifier: Modifier = Modifier,
    name: NamesCard,
    nameIndex: Int,
    onNameSelected: (Int) -> Unit = {}
) {
    Box(
        modifier = modifier
            .background(
                color = if (name.selected)
                    MaterialTheme.colorScheme.primary
                else MaterialTheme.customColors.secondarySoft,
                shape = RoundedCornerShape(MaterialTheme.customDimens.dimen8)
            )
            .height(MaterialTheme.customDimens.dimen40)
            .clip(RoundedCornerShape(MaterialTheme.customDimens.dimen8))
            .clickable { onNameSelected(nameIndex) }
    ) {
        Row(
            modifier = Modifier.fillMaxHeight(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.customDimens.dimen10),
                text = name.name,
                textAlign = TextAlign.Center,
                maxLines = ONE_VALUE,
                overflow = TextOverflow.Ellipsis,
                color = if (name.selected) MaterialTheme.colorScheme.onPrimary
                else MaterialTheme.colorScheme.secondary,
                style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.SemiBold)
            )
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun NameSelectorComponent(
    modifier: Modifier = Modifier,
    names: List<NamesCard> = listOf(),
    onNameSelected: (Int) -> Unit = {}
) {
    FlowRow(
        modifier = modifier
            .clip(RoundedCornerShape(MaterialTheme.customDimens.dimen8))
            .padding(all = MaterialTheme.customDimens.dimen2),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen2),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen2),
        maxItemsInEachRow = TWO_VALUE
    ) {
        names.forEachIndexed { index, item ->
            NameSelector(
                modifier = Modifier.weight(ONE_FLOAT_VALUE),
                name = item,
                nameIndex = index,
                onNameSelected = onNameSelected
            )
        }
        if (names.size.isOdd()) {
            Box(modifier = Modifier.weight(ONE_FLOAT_VALUE))
        }
    }
}

@Preview
@Composable
fun NameSelectorPreview() {
    val names = listOf(
        NamesCard("Jose", true),
        NamesCard("Antonio", false),
        NamesCard("Perez", false),
        NamesCard("Hernandez", true),
        NamesCard("Otorrinolaringologo", true)

    )
    OneAppTheme {
        NameSelectorComponent(names = names)
    }
}
