package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.applicantinfo

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI
import sv.com.tarjetaone.domain.entities.response.CatalogType.FAMILY_REFERENCES_TYPES
import sv.com.tarjetaone.domain.usecases.catalog.GetCatalogUseCase
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.helpers.hasInvalidPhoneNumber
import sv.com.tarjetaone.presentation.helpers.removeDash
import javax.inject.Inject

@HiltViewModel
class AdditionalCardApplicantInfoViewModel @Inject constructor(
    private val getCatalogUseCase: GetCatalogUseCase,
    private val sharedPrefsRepo: SecureSharedPreferencesRepository,
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(AdditionalCardApplicantInfoUiState())
    val uiState = _uiState.asStateFlow()

    private fun onStart() {
        if (_uiState.value.hasLoadedData) return
        sendEvent(SideEffect.Loading(true))
        viewModelScope.launch {
            getCatalogUseCase(FAMILY_REFERENCES_TYPES).executeUseCase { response ->
                val catalogItemCollection = response.dataCatalog?.catalogItemsCollection.orEmpty()
                _uiState.update { state ->
                    state.copy(relationships = catalogItemCollection)
                }
                sendEvent(SideEffect.Loading(false))
            }
        }
    }

    private fun onItemSelected(item: CatalogItemsCollectionUI) {
        _uiState.update { state -> state.copy(selectedItem = item) }
    }

    private fun saveAdditionalData() {
        sharedPrefsRepo.userAdditionalCardData?.let {
            sharedPrefsRepo.userAdditionalCardData = it.copy(
                customer = it.customer.copy(
                    phoneNumber = _uiState.value.phone.removeDash(),
                    referenceTypeId = _uiState.value.selectedItem?.id?.toIntOrNull().orZero()
                )
            )
        }
    }

    private fun onContinueClick() {
        saveAdditionalData()
        sendEvent(
            UiEvent.Navigate(
                AdditionalCardApplicantInfoFragmentDirections
                    .actionReadabilityAdditionalDataToAdditionalCardPhoneNotifications()
            )
        )
    }

    private fun onPhoneChange(phone: String) {
        val isPhoneValid = phone.hasInvalidPhoneNumber().not()
        _uiState.update { state ->
            state.copy(
                phone = phone,
                isPhoneValid = isPhoneValid,
                phoneError = isPhoneValid.takeIf { !it }
                    ?.let { UiText.StringResource(R.string.invalid_phone_number) }
            )
        }
    }

    fun onEvent(event: AdditionalCardApplicantInfoUiEvent) {
        when (event) {
            AdditionalCardApplicantInfoUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            AdditionalCardApplicantInfoUiEvent.OnContinueClick -> onContinueClick()
            AdditionalCardApplicantInfoUiEvent.OnStart -> onStart()
            AdditionalCardApplicantInfoUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            is AdditionalCardApplicantInfoUiEvent.OnDropdownItemSelected -> onItemSelected(
                event.item
            )

            is AdditionalCardApplicantInfoUiEvent.OnPhoneChange -> onPhoneChange(event.phone)
        }
    }
}