package sv.com.tarjetaone.presentation.view.common.otpvalidation

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.SnackbarResult
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.DASH_STRING
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.FIVE_VALUE_PERCENT
import sv.com.tarjetaone.common.utils.AppConstants.OTP_CODE_LENGTH
import sv.com.tarjetaone.common.utils.extensions.formatPhoneNumber
import sv.com.tarjetaone.domain.entities.response.ContactType
import sv.com.tarjetaone.domain.entities.response.OtpMethod
import sv.com.tarjetaone.presentation.compose.modifier.noRippleClickable
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.HyperLinkTextButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.InfoLabel
import sv.com.tarjetaone.presentation.compose.uicomponent.common.OneAppSnackBar
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.DialogConfirmation
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.input.SegmentedDigitsTextField
import sv.com.tarjetaone.presentation.compose.util.InfoStatus
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.compose.util.TextButtonVariant
import sv.com.tarjetaone.presentation.compose.util.remember.rememberSnackBarHostState
import sv.com.tarjetaone.presentation.compose.util.statusColor
import sv.com.tarjetaone.presentation.helpers.UiText

@Composable
fun OtpValidationScreen(viewModel: OtpValidationBaseViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    OtpValidationScreen(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@Composable
private fun OtpValidationScreen(
    uiState: OtpValidationUiState,
    onEvent: (OtpValidationUiEvent) -> Unit
) {
    ScreenWithTopAppBar(
        onLeftButtonClick = { onEvent(OtpValidationUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(OtpValidationUiEvent.OnTwilioClick) },
        contentPadding = PaddingValues(MaterialTheme.customDimens.dimen24),
        isProgressbarVisible = uiState.showProgressBar,
        progress = FIVE_VALUE_PERCENT,
        title = stringResource(
            id = if (uiState.contactType == ContactType.Phone) {
                R.string.phone_number
            } else {
                R.string.email_address
            }
        )
    ) {
        OtpValidationContent(
            contactType = uiState.contactType,
            contact = uiState.contact,
            sharedKey = uiState.sharedKey,
            uiState = uiState,
            onEvent = onEvent,
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = MaterialTheme.customDimens.dimen24)
        )
    }
}

@Composable
private fun OtpValidationContent(
    modifier: Modifier = Modifier,
    contactType: ContactType,
    contact: String,
    sharedKey: String,
    uiState: OtpValidationUiState,
    onEvent: (OtpValidationUiEvent) -> Unit
) {
    val focusManager = LocalFocusManager.current
    val snackBarHostState = rememberSnackBarHostState()
    var isOtpFieldFocused by remember { mutableStateOf(false) }
    LaunchedEffect(Unit) {
        onEvent(OtpValidationUiEvent.OnStart(contactType, contact, sharedKey))
    }
    LaunchedEffect(uiState.code) {
        if (uiState.code.length == OTP_CODE_LENGTH) {
            onEvent(OtpValidationUiEvent.OnValidateOtp(uiState.code))
        }
    }
    Column(
        modifier = modifier
            .fillMaxSize()
            .noRippleClickable {
                if (isOtpFieldFocused) focusManager.clearFocus()
            },
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        OtpDialogSection(isDialogVisible = uiState.showOtpMethodDialog, onEvent = onEvent)
        Image(
            imageVector = ImageVector.vectorResource(
                id = if (uiState.contactType == ContactType.Phone) {
                    R.drawable.ic_cellphone
                } else {
                    R.drawable.ic_envelope
                }
            ),
            contentDescription = null
        )
        Spacer16()
        OtpInfoSection(
            contactType = contactType,
            contact = contact
        )
        Spacer8()
        SegmentedDigitsTextField(
            onValueChange = { onEvent(OtpValidationUiEvent.OnCodeChange(it)) },
            infoStatus = uiState.otpStatus,
            modifier = Modifier.onFocusChanged { isOtpFieldFocused = it.isFocused },
        )
        Spacer8()
        OtpTooltipSection(
            otpStatus = uiState.otpStatus,
            otpLifeTime = uiState.otpLifeTime
        )
        Spacer8()
        HyperLinkTextButton(
            text = stringResource(id = R.string.send_again_code_label),
            onClick = { onEvent(OtpValidationUiEvent.OnResendOtpClick) },
            textButtonVariant = TextButtonVariant.SMALL_VARIANT,
            enabled = uiState.otpStatus != InfoStatus.Success,
            textStyle = MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.colorScheme.primary,
                textDecoration = TextDecoration.Underline
            )
        )
        uiState.otpMessage?.let {
            Spacer16()
            InfoLabel(
                text = it.asString(),
                textColor = MaterialTheme.colorScheme.error,
                iconResId = R.drawable.ic_exclamation_outline,
                modifier = Modifier.align(Alignment.Start)
            )
        }
        Spacer1f()
        OtpSnackBarSection(
            isSnackBarVisible = uiState.isSnackBarVisible,
            snackBarHostState = snackBarHostState,
            onCancelClick = { onEvent(OtpValidationUiEvent.OnHideSnackBar) }
        )
    }
}

@Composable
private fun OtpSnackBarSection(
    isSnackBarVisible: Boolean,
    snackBarHostState: SnackbarHostState,
    onCancelClick: () -> Unit
) {
    LaunchedEffect(isSnackBarVisible) {
        if (isSnackBarVisible) {
            val result = snackBarHostState.showSnackbar(message = EMPTY_STRING)
            if (result == SnackbarResult.Dismissed) {
                onCancelClick()
            }
        }
    }
    SnackbarHost(
        modifier = Modifier.fillMaxWidth(),
        hostState = snackBarHostState
    ) {
        OneAppSnackBar(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = MaterialTheme.customDimens.dimen16),
            icon = R.drawable.ic_check_circle_white,
            message = stringResource(id = R.string.code_resend_message),
            containerColor = MaterialTheme.customColors.successContainer,
            onCancelClick = { snackBarHostState.currentSnackbarData?.dismiss() }
        )
    }
}

@Composable
private fun OtpDialogSection(
    isDialogVisible: Boolean,
    onEvent: (OtpValidationUiEvent) -> Unit
) {
    if (isDialogVisible) {
        DialogConfirmation(
            messageStyle = MaterialTheme.typography.labelMedium,
            message = UiText.StringResource(R.string.otp_channel_string),
            primaryButtonText = UiText.StringResource(R.string.otp_whatsapp),
            secondaryButtonText = UiText.StringResource(R.string.otp_sms),
            onPositiveClick = {
                onEvent(OtpValidationUiEvent.OnSelectOtpMethod(OtpMethod.WHATSAPP))
            },
            onNegativeClick = {
                onEvent(OtpValidationUiEvent.OnSelectOtpMethod(OtpMethod.SMS))
            },
            onDismissRequest = {
                onEvent(OtpValidationUiEvent.OnDismissSelectOtpMethod)
            }
        )
    }
}

@Composable
private fun OtpInfoSection(
    contactType: ContactType,
    contact: String
) {
    Text(
        text = buildAnnotatedString {
            append(
                stringResource(
                    id = if (contactType == ContactType.Phone) {
                        R.string.start_experience_code_label2
                    } else {
                        R.string.email_disclaimer
                    }
                )
            )
            withStyle(style = SpanStyle(fontWeight = FontWeight.SemiBold)) {
                val formattedContact = if (contactType == ContactType.Phone) {
                    contact.formatPhoneNumber(DASH_STRING)
                } else {
                    contact
                }
                append(" $formattedContact")
            }
        },
        style = MaterialTheme.typography.bodySmall,
        color = MaterialTheme.customColors.gray700,
        textAlign = TextAlign.Center
    )
}

@Composable
private fun OtpTooltipSection(
    otpStatus: InfoStatus,
    otpLifeTime: Int
) {
    InfoLabel(
        text = when (otpStatus) {
            InfoStatus.None -> stringResource(id = R.string.otp_life_timer, otpLifeTime)
            InfoStatus.Success -> stringResource(id = R.string.success_otp)
            InfoStatus.Error -> stringResource(id = R.string.incorrect_otp)
            InfoStatus.Warning -> stringResource(id = R.string.expired_otp)
        },
        iconResId = when (otpStatus) {
            InfoStatus.None -> R.drawable.ic_info_2
            InfoStatus.Success -> R.drawable.ic_check_circle_green
            InfoStatus.Error -> R.drawable.ic_x_circle_red
            InfoStatus.Warning -> R.drawable.ic_exclamation_circle_yellow
        },
        textColor = if (otpStatus == InfoStatus.None) {
            MaterialTheme.customColors.disclaimer
        } else {
            otpStatus.statusColor()
        }
    )
}

@Preview(showBackground = true)
@Composable
private fun OtpValidationContentPreview() {
    OneAppTheme {
        OtpValidationContent(
            contactType = ContactType.Phone,
            contact = "",
            sharedKey = "",
            uiState = OtpValidationUiState(),
            onEvent = {}
        )
    }
}

@Preview
@Composable
private fun OtpValidationScreenPreview() {
    OneAppTheme {
        OtpValidationScreen(
            uiState = OtpValidationUiState(
                otpMessage = UiText.DynamicString("Error message"),
                contact = "12345678",
            ),
            onEvent = {}
        )
    }
}
