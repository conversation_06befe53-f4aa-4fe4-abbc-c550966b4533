package sv.com.tarjetaone.presentation.view.help

import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import dagger.hilt.android.AndroidEntryPoint
import sv.com.tarjetaone.core.utils.BaseActivity
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme

@AndroidEntryPoint
class SupportActivity : BaseActivity() {

    private val viewModel: SupportViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            OneAppTheme {
                SupportScreen(viewModel)
            }
        }
        collectUiEvents()
    }

    private fun collectUiEvents() {
        viewModel.uiEvents.collectEventsWithLifecycle {
            when (it) {
                is UiEvent.NavigateBack -> finish()
                else -> Unit
            }
        }
    }
}
