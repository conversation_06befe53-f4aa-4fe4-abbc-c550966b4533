package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.history

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.ButtonSize
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.SingleDatePickerDialog
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.FieldDecorationType
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component.EmptyTransactionsItem
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.history.component.PaymentTransactionItem

@Composable
fun PaymentHistoryScreen(viewModel: PaymentHistoryViewModel) {
    val uiState by viewModel.uiState.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.onEvent(PaymentHistoryUiEvent.OnStart)
    }

    PaymentHistoryContent(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@Composable
private fun PaymentHistoryContent(
    uiState: PaymentHistoryUiState = PaymentHistoryUiState(),
    onEvent: (PaymentHistoryUiEvent) -> Unit = {}
) {
    ScreenWithTopAppBar(
        title = stringResource(id = R.string.service_payment_title),
        contentPadding = PaddingValues(
            top = MaterialTheme.customDimens.dimen32,
            bottom = MaterialTheme.customDimens.dimen8,
            start = MaterialTheme.customDimens.dimen32,
            end = MaterialTheme.customDimens.dimen32,
        ),
        onLeftButtonClick = { onEvent(PaymentHistoryUiEvent.OnBack) },
        onRightButtonClick = { onEvent(PaymentHistoryUiEvent.OnTwilioClick) }
    ) {
        PaymentHistoryMain(
            uiState = uiState,
            onEvent = onEvent
        )
    }
}

@Composable
private fun PaymentHistoryMain(
    modifier: Modifier = Modifier,
    uiState: PaymentHistoryUiState,
    onEvent: (PaymentHistoryUiEvent) -> Unit
) {
    Column(modifier = modifier.fillMaxSize()) {
        if (uiState.openDialog) {
            SingleDatePickerDialog(
                initialDate = uiState.getDateInMillis(),
                confirmButtonText = stringResource(id = R.string.accept_label),
                cancelButtonText = stringResource(id = R.string.exit_label),
                onDismissRequest = { onEvent(PaymentHistoryUiEvent.OnCloseDialog) },
                onConfirmButtonClick = {
                    val datePickerEvent = when (uiState.dateInputTypeSelected) {
                        PaymentHistoryDateType.START_DATE -> {
                            PaymentHistoryUiEvent.OnStartDateSelected(it)
                        }

                        PaymentHistoryDateType.END_DATE -> {
                            PaymentHistoryUiEvent.OnEndDateSelected(it)
                        }

                        else -> null
                    }
                    datePickerEvent?.let { event -> onEvent(event) }
                },
                onCancelButtonClick = { onEvent(PaymentHistoryUiEvent.OnCloseDialog) }
            )
        }
        PaymentHistoryHeader(
            modifier = Modifier.align(Alignment.CenterHorizontally),
            uiState = uiState
        )
        Spacer24()
        PaymentHistoryPeriod(uiState = uiState, onEvent = onEvent)
        Spacer8()
        OneButton(
            modifier = Modifier.align(Alignment.CenterHorizontally),
            text = stringResource(id = R.string.payment_history_search_button),
            size = ButtonSize.MEDIUM,
            leadingIcon = {
                Icon(
                    modifier = Modifier.size(MaterialTheme.customDimens.dimen12),
                    imageVector = ImageVector.vectorResource(id = R.drawable.ic_search_white),
                    contentDescription = null,
                )
            },
            onClick = { onEvent(PaymentHistoryUiEvent.OnSearchPaymentsClick) }
        )
        PaymentHistoryList(uiState = uiState)
    }
}

@Composable
private fun PaymentHistoryPeriod(
    modifier: Modifier = Modifier,
    uiState: PaymentHistoryUiState,
    onEvent: (PaymentHistoryUiEvent) -> Unit
) {
    val labelStyle = MaterialTheme.typography.bodySmall.copy(
        fontWeight = FontWeight.SemiBold,
        color = MaterialTheme.customColors.gray5
    )
    val calendarIcon: @Composable () -> Unit = {
        Icon(
            imageVector = ImageVector.vectorResource(id = R.drawable.ic_calendar_month),
            contentDescription = null,
        )
    }

    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.customDimens.dimen24),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        SimpleElevatedTextField(
            modifier = Modifier.weight(1f),
            label = stringResource(id = R.string.payment_history_from_date_label),
            labelStyle = labelStyle,
            leadingIcon = calendarIcon,
            readOnly = true,
            value = uiState.startDate,
            onValueChange = { /* No need implementation */ },
            decorationType = FieldDecorationType.OUTLINED,
            onClick = {
                onEvent(
                    PaymentHistoryUiEvent.OnDateInputClick(PaymentHistoryDateType.START_DATE)
                )
            }
        )
        Spacer8()
        SimpleElevatedTextField(
            modifier = Modifier.weight(1f),
            label = stringResource(id = R.string.payment_history_to_date_label),
            labelStyle = labelStyle,
            leadingIcon = calendarIcon,
            readOnly = true,
            value = uiState.endDate,
            onValueChange = { /* No need implementation */ },
            decorationType = FieldDecorationType.OUTLINED,
            onClick = {
                onEvent(
                    PaymentHistoryUiEvent.OnDateInputClick(PaymentHistoryDateType.END_DATE)
                )
            }
        )
    }
}

@Composable
private fun PaymentHistoryHeader(modifier: Modifier = Modifier, uiState: PaymentHistoryUiState) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Text(
            text = stringResource(id = R.string.payment_history_title),
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            style = MaterialTheme.typography.headlineSmall
        )
        Spacer8()
        Text(
            text = uiState.serviceName,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurface,
            style = MaterialTheme.typography.bodyLarge.copy(
                fontWeight = FontWeight.SemiBold
            )
        )
    }
}

@Composable
private fun PaymentHistoryList(
    modifier: Modifier = Modifier,
    uiState: PaymentHistoryUiState,
) {
    Box(modifier = modifier.fillMaxSize()) {
        if (uiState.transactionList.isEmpty()) {
            PaymentHistoryEmpty(
                modifier = Modifier.align(Alignment.TopCenter),
                uiState = uiState
            )
        } else {
            LazyColumn(
                modifier = modifier,
                contentPadding = PaddingValues(
                    horizontal = MaterialTheme.customDimens.dimen24,
                    vertical = MaterialTheme.customDimens.dimen16
                ),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen16)
            ) {
                items(uiState.transactionList) {
                    PaymentTransactionItem(transaction = it)
                }
            }
        }
    }
}

@Composable
private fun PaymentHistoryEmpty(
    modifier: Modifier = Modifier,
    uiState: PaymentHistoryUiState
) {
    EmptyTransactionsItem(
        modifier = modifier.padding(
            top = MaterialTheme.customDimens.dimen74,
            start = MaterialTheme.customDimens.dimen48,
            end = MaterialTheme.customDimens.dimen48
        ),
        image = if (uiState.customDateIsSelected) {
            R.drawable.ic_empty_movements
        } else {
            R.drawable.ic_empty_movement
        },
        imageSize = MaterialTheme.customDimens.dimen128,
        subtitle = stringResource(
            id = if (uiState.customDateIsSelected) {
                R.string.my_movements_no_result
            } else {
                R.string.payment_history_empty_text
            }
        )
    )
}

@Preview
@Composable
private fun PaymentHistoryScreenPreview() {
    OneAppTheme {
        PaymentHistoryContent(
            uiState = PaymentHistoryUiState(
                serviceName = "ANDA",
                transactionList = RecurringTransactionDummyItems.dummyTransactionList
            )
        )
    }
}

@Preview
@Composable
private fun PaymentHistoryScreenEmptyPreview() {
    OneAppTheme {
        PaymentHistoryContent(
            uiState = PaymentHistoryUiState(
                serviceName = "ANDA",
                transactionList = emptyList()
            )
        )
    }
}
