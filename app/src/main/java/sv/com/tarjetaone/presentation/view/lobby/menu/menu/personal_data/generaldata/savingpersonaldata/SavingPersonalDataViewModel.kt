package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.generaldata.savingpersonaldata

import android.graphics.Bitmap
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.common.utils.AppConstants.BITMAP_COMPRESS_QUALITY_80
import sv.com.tarjetaone.common.utils.AppConstants.DUI
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.UserUtils
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.extensions.CalendarFormat
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.core.utils.extensions.removeLineBreak
import sv.com.tarjetaone.core.utils.extensions.toBase64
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.request.AddressesDataUpdateUI
import sv.com.tarjetaone.domain.entities.request.CustomerDataUpdateRequestUI
import sv.com.tarjetaone.domain.entities.request.CustomerDataUpdateUI
import sv.com.tarjetaone.domain.entities.request.IdentityDocumentsUI
import sv.com.tarjetaone.domain.usecases.personalData.validate_customer_data.UpdateCustomerDataUseCase
import sv.com.tarjetaone.domain.utils.onApiError
import sv.com.tarjetaone.domain.utils.onNetworkError
import sv.com.tarjetaone.domain.utils.onSuccess
import javax.inject.Inject

@HiltViewModel
class SavingPersonalDataViewModel @Inject constructor(
    private val updateCustomerDataUseCase: UpdateCustomerDataUseCase,
    private val sharedPreferencesRepository: SecureSharedPreferencesRepository,
    private val imgUtils: ImageUtils,
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {
    private val args = SavingPersonalDataFragmentArgs.fromSavedStateHandle(savedStateHandle)

    fun updateCustomerData() {
        viewModelScope.launch {

            val documentBackBitmap =
                sharedPreferencesRepository.getDocBackFileName()?.let { imgUtils.getTempImage(it) }
            val documentBackBase64 = documentBackBitmap
                ?.toBase64(Bitmap.CompressFormat.JPEG, BITMAP_COMPRESS_QUALITY_80)
                ?.removeLineBreak()

            val documentFrontBitmap =
                sharedPreferencesRepository.getDocFrontFileName()?.let { imgUtils.getTempImage(it) }
            val documentFrontBase64 = documentFrontBitmap
                ?.toBase64(Bitmap.CompressFormat.JPEG, BITMAP_COMPRESS_QUALITY_80)
                ?.removeLineBreak()

            val request = CustomerDataUpdateRequestUI(
                customerId = sharedPreferencesRepository.getCustomerId(),
                matchingSidesScore = sharedPreferencesRepository.getOcr()?.matchingSidesScore?.toDouble(),
                duiFront = documentFrontBase64,
                duiBack = documentBackBase64,
                biometryProcessId = args.biometryProcessId,
                customer = getCustomerRequest()
            )
            updateCustomerDataUseCase(request).onSuccess {
                val response = it.statusResponse?.responseStatus
                if (response?.code != AppConstants.SUCCESS_RESPONSE_CODE) {
                    navigateToResultScreen(false)
                    return@onSuccess
                }

                navigateToResultScreen(true)
            }.onApiError { _, _, _, _ ->
                navigateToResultScreen(false)
            }.onNetworkError {
                showUpsErrorMessage()
            }
        }
    }

    private fun getCustomerRequest(): CustomerDataUpdateUI {
        val data = sharedPreferencesRepository.getValidatedOcr()

        return CustomerDataUpdateUI(
            addresses = UserUtils.getUserLocations().map {
                AddressesDataUpdateUI(
                    municipio = it.municipio,
                    department = it.department,
                    houseNumber = it.houseNumber,
                    street = it.street,
                    residential = it.residential
                )
            },
            birthDate = data?.birthDate?.getFormattedDateFromTo(
                CalendarFormat.DAY_MONTH_YEAR,
                CalendarFormat.YEAR_MONTH_DAY
            ).orEmpty(),
            name = data?.name.orEmpty(),
            lastName = data?.lastName.orEmpty(),
            genderCode = data?.genderCode?.first()?.uppercase(),
            municipality = data?.municipality,
            deparment = data?.department,
            identityDocuments = listOf(
                IdentityDocumentsUI(
                    document = data?.identityDocuments?.document,
                    expirationDate = data?.identityDocuments?.expirationDate?.getFormattedDateFromTo(
                        CalendarFormat.DAY_MONTH_YEAR,
                        CalendarFormat.YEAR_MONTH_DAY
                    ).orEmpty(),
                    idTypeCode = DUI,
                    issuedDate = data?.identityDocuments?.issuedDate?.getFormattedDateFromTo(
                        CalendarFormat.DAY_MONTH_YEAR,
                        CalendarFormat.YEAR_MONTH_DAY
                    ).orEmpty()
                )
            ),
            marriedName = data?.marriedName.orEmpty(),
            knownBy = data?.knownBy.orEmpty()
        )
    }

    /**
     * Depending on the saving data result, show the right UI screen representing a success / failure message
     * for the user.
     */
    private fun navigateToResultScreen(isSuccessResult: Boolean) {
        sendEvent(
            UiEvent.Navigate(
                SavingPersonalDataFragmentDirections.actionSavingPersonalDataFragmentToSuccessUpdateDataFragment(
                    isSuccessResult = isSuccessResult
                )
            )
        )
    }
}
