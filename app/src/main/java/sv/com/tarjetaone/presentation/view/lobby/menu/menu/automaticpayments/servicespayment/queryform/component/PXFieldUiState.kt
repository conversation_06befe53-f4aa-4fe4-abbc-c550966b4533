package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.queryform.component

import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.ONE_HUNDRED_VALUE
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldType
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldValueType
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldValueUI
import sv.com.tarjetaone.presentation.helpers.UiText

data class PXFieldUiState(
    val pxFieldName: String,
    val pxField: PXFieldUI,
    val typedValue: String = EMPTY_STRING,
    val selectedValue: Pair<String, PXFieldUI>? = null,
    val hasError: Boolean = false,
    val errorMessage: UiText? = null,
) {
    val isValidField = !pxField.required || typedValue.isNotBlank() || selectedValue != null

    fun toPXFieldUI(): PXFieldUI {
        return if (pxField.type == PXFieldType.SelectBox) {
            pxField.copy(
                value = pxField.value.asValueList()?.copy(
                    value = selectedValue?.let {
                        mapOf(it.first to it.second.copy(selected = true))
                    }.orEmpty()
                ) ?: pxField.value
            )
        } else {
            pxField.copy(
                value = pxField.value.asValueString()?.copy(
                    value = parseTypedValue()
                ) ?: pxField.value
            )
        }
    }

    private fun parseTypedValue(): String = when (pxField.value.asValueString()?.type) {
        PXFieldValueType.Decimal,
        PXFieldValueType.Double,
        PXFieldValueType.Money -> {
            // For any decimal type the value is divided by 100 to account for the textField's
            // visual transformation
            (typedValue.toDoubleOrNull().orZero() / ONE_HUNDRED_VALUE).toString()
        }
        else -> typedValue
    }
}

object PXFieldDummyUiStates {
    private val accountNumber = PXFieldUI(
        index = 0,
        tooltip = "Número de cuenta tooltip",
        name = "Número de cuenta name",
        label = "Número de cuenta label",
        required = true,
        selected = false,
        reference = false,
        type = PXFieldType.Input,
        mask = null,
        value = PXFieldValueUI.FieldValueStringUI(
            type = PXFieldValueType.Text,
            mask = null,
            editable = true,
            value = ""
        )
    )

    private val documentType = PXFieldUI(
        index = 0,
        tooltip = "Documento de consulta tooltip",
        name = "Documento de consulta name",
        label = "Documento de consulta label",
        required = true,
        selected = false,
        reference = false,
        type = PXFieldType.SelectBox,
        mask = null,
        value = PXFieldValueUI.FieldValueListUI(
            type = PXFieldValueType.Text,
            mask = null,
            editable = true,
            value = mapOf(
                "Id" to PXFieldUI(
                    index = 0,
                    tooltip = "Id",
                    name = "Id",
                    label = "Id",
                    required = true,
                    selected = false,
                    reference = false,
                    type = PXFieldType.Output,
                    mask = null,
                    value = PXFieldValueUI.FieldValueStringUI(
                        type = PXFieldValueType.Text,
                        mask = null,
                        editable = true,
                        value = "Id"
                    )
                )
            )
        )
    )

    val accountNumberField = PXFieldUiState(
        pxFieldName = "accountNumber",
        pxField = accountNumber
    )

    val documentTypeField = PXFieldUiState(
        pxFieldName = "documentType",
        pxField = documentType
    )
}
