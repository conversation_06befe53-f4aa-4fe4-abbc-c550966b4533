package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.cashback.detail

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.paging.LoadState
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.collectAsLazyPagingItems
import androidx.paging.compose.items
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.DAY_OF_MONTH
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.YEAR_MONTH_DAY_WITH_DASH
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.core.utils.extensions.configCurrencyWithFractions
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.response.CashbackTUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleLoadingIndicator
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.PaymentPenaltyEmptyState
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.component.CashbackTransactionItem
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component.EmptyTransactionsItem

@Composable
fun MyCashbackScreen(viewModel: MyCashbackViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    SideEffectHandler(events = viewModel.sideEffects) {
        MyCashbackContent(
            uiState = uiState,
            onEvent = viewModel::onEvent
        )
    }
}

@Composable
private fun MyCashbackContent(
    uiState: MyCashbackUiState,
    onEvent: (MyCashbackUiEvent) -> Unit
) {
    LaunchedEffect(Unit) {
        onEvent(MyCashbackUiEvent.OnStart)
    }
    ScreenWithTopAppBar(
        contentPadding = PaddingValues(
            top = MaterialTheme.customDimens.dimen32,
            start = MaterialTheme.customDimens.dimen32,
            end = MaterialTheme.customDimens.dimen32
        ),
        title = stringResource(id = R.string.my_cashback).takeIf { !uiState.hasPaymentPenalty },
        backgroundColor = MaterialTheme.colorScheme.surface,
        onLeftButtonClick = { onEvent(MyCashbackUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(MyCashbackUiEvent.OnSupportClick) }
    ) {
        if (uiState.hasPaymentPenalty) {
            PaymentPenaltyEmptyState { onEvent(MyCashbackUiEvent.OnBackClick) }
        } else {
            MyCashbackContentBody(
                uiState = uiState,
                onEvent = onEvent
            )
        }
    }

    if (uiState.showCashbackBottomSheet) {
        MyCashbackBottomSheetContent(
            onDismissRequest = {
                onEvent(
                    MyCashbackUiEvent.OnHowToUseClick(
                        false
                    )
                )
            },
            onButtonAction = { onEvent(MyCashbackUiEvent.OnEBankingClick) },
        )
    }
}

@Composable
private fun MyCashbackContentBody(
    uiState: MyCashbackUiState,
    onEvent: (MyCashbackUiEvent) -> Unit
) {
    val cashbackItems = uiState.cashbackTransactions?.collectAsLazyPagingItems()
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.fillMaxSize()
    ) {
        Card(
            elevation = CardDefaults.cardElevation(
                defaultElevation = MaterialTheme.customDimens.dimen2
            ),
            modifier = Modifier
                .background(color = MaterialTheme.colorScheme.onPrimary)
                .fillMaxWidth()
        ) {
            Column(
                modifier = Modifier
                    .background(color = MaterialTheme.colorScheme.onPrimary)
                    .fillMaxWidth()
                    .padding(
                        vertical = MaterialTheme.customDimens.dimen20,
                        horizontal = MaterialTheme.customDimens.dimen16
                    )
            ) {
                Text(
                    text = stringResource(id = R.string.accumulated_cashback_to_date),
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = MaterialTheme.customColors.lightBackground
                    )
                )
                Text(
                    text = uiState.pointsData?.cashback?.cashback.orZero()
                        .configCurrencyWithFractions(),
                    style = MaterialTheme.typography.displayLarge.copy(
                        color = MaterialTheme.customColors.secondarySolid,
                        fontWeight = FontWeight.Normal
                    )
                )
                Text(
                    text = stringResource(id = R.string.current_period),
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = MaterialTheme.colorScheme.onBackground
                    )
                )
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = ImageVector.vectorResource(id = R.drawable.ic_calendar),
                        contentDescription = null,
                        tint = Color.Unspecified
                    )
                    Spacer8()
                    Text(
                        text = stringResource(
                            id = R.string.my_movements_current_period,
                            uiState.pointsData?.valueDate
                                ?.getFormattedDateFromTo(
                                    YEAR_MONTH_DAY_WITH_DASH,
                                    DAY_OF_MONTH
                                ).orEmpty()
                        ),
                        style = MaterialTheme.typography.bodySmall.copy(
                            color = MaterialTheme.colorScheme.secondary
                        )
                    )
                }
            }
        }
        Spacer16()
        when (cashbackItems?.loadState?.refresh) {
            is LoadState.Loading -> SimpleLoadingIndicator(modifier = Modifier.fillMaxSize())
            is LoadState.Error -> {
                onEvent(MyCashbackUiEvent.OnTransactionsLoadError { cashbackItems.retry() })
            }

            else -> CashbackTransactionsList(
                modifier = Modifier.weight(ONE_FLOAT_VALUE),
                cashbackItems = cashbackItems,
                onAppendError = { onEvent(MyCashbackUiEvent.OnTransactionsLoadError(it)) }
            )
        }
        Card(
            elevation = CardDefaults.cardElevation(
                defaultElevation = MaterialTheme.customDimens.dimen4
            ),
            shape = RoundedCornerShape(
                topStart = MaterialTheme.customDimens.dimen16,
                topEnd = MaterialTheme.customDimens.dimen16
            ),
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier
                    .background(color = MaterialTheme.colorScheme.onPrimary)
                    .padding(MaterialTheme.customDimens.dimen16)
            ) {
                OneButton(
                    text = stringResource(id = R.string.cashback_detail_how_to_use),
                    onClick = { onEvent(MyCashbackUiEvent.OnHowToUseClick(true)) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = MaterialTheme.customDimens.dimen16),
                    trailingIcon = {
                        Icon(
                            imageVector = ImageVector.vectorResource(
                                id = R.drawable.ic_chevron_right
                            ),
                            contentDescription = null,
                            modifier = Modifier.padding(
                                start = MaterialTheme.customDimens.dimen8
                            )
                        )
                    }
                )
            }
        }
    }
}

@Composable
private fun CashbackTransactionsList(
    modifier: Modifier = Modifier,
    cashbackItems: LazyPagingItems<CashbackTUI>?,
    onAppendError: (() -> Unit) -> Unit
) {
    if (cashbackItems == null || cashbackItems.itemCount == ZERO_VALUE) {
        EmptyTransactionsItem(
            modifier = modifier.padding(
                vertical = MaterialTheme.customDimens.dimen16,
                horizontal = MaterialTheme.customDimens.dimen16
            ),
            image = R.drawable.ic_empty_cashback,
            title = stringResource(id = R.string.cashback_detail_empty_title),
            subtitle = stringResource(id = R.string.cashback_detail_empty_subtitle)
        )
    } else {
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen4),
            contentPadding = PaddingValues(
                vertical = MaterialTheme.customDimens.dimen16,
                horizontal = MaterialTheme.customDimens.dimen16
            ),
            modifier = modifier.fillMaxSize()
        ) {
            item {
                Text(
                    text = stringResource(id = R.string.cashback_detail_items_header),
                    style = MaterialTheme.typography.labelLarge.copy(
                        fontSize = MaterialTheme.customDimensSp.sp15,
                        color = MaterialTheme.colorScheme.secondary
                    )
                )
                Spacer8()
            }
            items(items = cashbackItems) { cashbackTransaction ->
                cashbackTransaction?.let {
                    CashbackTransactionItem(
                        transaction = it,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
            when (cashbackItems.loadState.append) {
                is LoadState.Loading -> item {
                    SimpleLoadingIndicator(modifier = Modifier.fillMaxWidth())
                }

                is LoadState.Error -> onAppendError { cashbackItems.retry() }
                else -> Unit
            }
        }
    }
}

@Preview
@Composable
private fun MyCashbackContentPreview() {
    OneAppTheme {
        MyCashbackContent(
            uiState = MyCashbackUiState(),
            onEvent = { }
        )
    }
}
