package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_activation.almost_there

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.common.HorizontalProgressIndicator
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.DialogConfirmation
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.OneBackHandler
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.helpers.UiText


@Composable
fun AlmostThereActivateCardScreen(
    viewModel: AlmostThereActivateCardViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    AlmostThereActivateCardContent(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@Composable
fun AlmostThereActivateCardContent(
    uiState: AlmostThereActivateCardUiState,
    onEvent: (AlmostThereActivateCardEvent) -> Unit = {}
) {
    LaunchedEffect(Unit) {
        onEvent(AlmostThereActivateCardEvent.OnStart)
    }
    OneBackHandler()
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.inverseSurface)
            .padding(horizontal = MaterialTheme.customDimens.dimen48),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (uiState.showContactDialog) {
            DialogConfirmation(
                title = UiText.StringResource(R.string.ups_card_activation),
                message = UiText.StringResource(R.string.would_you_like_assistance),
                primaryButtonText = UiText.StringResource(R.string.facephi_error_dialog_by_phone),
                secondaryButtonText = UiText.StringResource(R.string.try_again_label),
                onPositiveClick = {
                    onEvent(AlmostThereActivateCardEvent.OnContactSupport)
                },
                onNegativeClick = { onEvent(AlmostThereActivateCardEvent.OnTryAgain) },
                positiveButtonVariant = ButtonVariant.TERTIARY_VARIANT,
                negativeButtonVariant = ButtonVariant.PRIMARY_VARIANT
            )
        }
        Icon(
            imageVector = ImageVector.vectorResource(id = R.drawable.ic_file_person_white),
            contentDescription = null,
            modifier = Modifier.size(
                width = MaterialTheme.customDimens.dimen88,
                height = MaterialTheme.customDimens.dimen92
            ),
            tint = MaterialTheme.colorScheme.inverseOnSurface
        )
        Spacer24()
        Text(
            text = stringResource(id = R.string.we_are_checking_your_data_label),
            style = MaterialTheme.typography.displayMedium,
            color = MaterialTheme.colorScheme.inverseOnSurface,
            textAlign = TextAlign.Center
        )
        Spacer16()
        HorizontalProgressIndicator(
            modifier = Modifier
                .width(MaterialTheme.customDimens.dimen168)
                .height(MaterialTheme.customDimens.dimen5)
        )
    }
}

@Composable
@Preview(
    showSystemUi = true
)
fun AlmostThereScreenPreview() {
    OneAppTheme {
        AlmostThereActivateCardContent(
            uiState = AlmostThereActivateCardUiState()
        )
    }
}
