package sv.com.tarjetaone.presentation.view.common.finish_personalization

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import sv.com.tarjetaone.core.utils.BaseViewModel

abstract class FinishPersonalizationBaseViewModel : BaseViewModel() {

    protected val _uiState = MutableStateFlow(FinishPersonalizationState())
    val uiState = _uiState.asStateFlow()

    fun onFinishPersonalizationEvent(event: FinishPersonalizationEvent) {
        when (event) {
            is FinishPersonalizationEvent.OnSaveCustomization -> {
                onSaveCustomization()
            }

            is FinishPersonalizationEvent.OnCustomizeAgain -> {
                onCustomizeAgain()
            }

            is FinishPersonalizationEvent.OnStart -> onStart()
        }
    }

    /**
     * Action to perform when the screen is started
     * */
    protected abstract fun onStart()

    /**
     * Action to perform when the user wants to save the customization
     * */
    protected abstract fun onSaveCustomization()

    /**
     * Action to perform when the user wants to customize again
     * */
    protected abstract fun onCustomizeAgain()
}
