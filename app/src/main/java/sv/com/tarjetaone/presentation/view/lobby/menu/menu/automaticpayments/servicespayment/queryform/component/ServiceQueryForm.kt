package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.queryform.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFormType
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedDropdown
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.helpers.capitalize
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.queryform.ServiceQueryFormUiEvent
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.queryform.ServiceQueryFormUiState

@Composable
fun ServiceQueryForm(
    uiState: ServiceQueryFormUiState,
    onEvent: (ServiceQueryFormUiEvent) -> Unit,
) {
    if (uiState.formType == PXFormType.SimpleForm) {
        ServiceSimpleForm(
            serviceName = uiState.serviceName,
            serviceCategory = uiState.categoryName,
            fields = uiState.fields,
            onVariableFieldChange = { index, typedValue, selectedValue ->
                onEvent(ServiceQueryFormUiEvent.OnPXFieldChange(index, typedValue, selectedValue))
            }
        )
    } else {
        ServiceSelectOneForm(
            serviceName = uiState.serviceName,
            serviceCategory = uiState.categoryName,
            selectedField = uiState.selectedQueryOption,
            fields = uiState.queryOptions,
            onOptionChange = { onEvent(ServiceQueryFormUiEvent.OnQueryOptionChange(it)) },
            reference = uiState.reference,
            onReferenceChange = { onEvent(ServiceQueryFormUiEvent.OnReferenceChange(it)) }
        )
    }
}

@Composable
private fun ServiceSimpleForm(
    serviceName: String,
    serviceCategory: String,
    fields: List<PXFieldUiState>,
    onVariableFieldChange: (Int, String, Pair<String, PXFieldUI>?) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .clip(MaterialTheme.shapes.large)
            .background(MaterialTheme.customColors.defaultSurface)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.customDimens.dimen16)
        ) {
            Text(
                text = serviceName,
                style = MaterialTheme.typography.bodyLarge.copy(fontWeight = FontWeight.SemiBold),
                color = MaterialTheme.colorScheme.secondary
            )
            Text(
                text = serviceCategory,
                style = MaterialTheme.typography.labelMedium,
                color = MaterialTheme.customColors.gray600
            )
            Spacer24()
            fields.forEachIndexed { index, fieldState ->
                PXQueryField(
                    fieldState = fieldState,
                    onTypedValueChange = { onVariableFieldChange(index, it, fieldState.selectedValue) },
                    onSelectedValueChange = { onVariableFieldChange(index, fieldState.typedValue, it) },
                    isLastField = index == fields.lastIndex
                )
                if (index != fields.lastIndex) Spacer16()
            }
        }
    }
}

@Composable
private fun ServiceSelectOneForm(
    serviceName: String,
    serviceCategory: String,
    selectedField: Pair<String, PXFieldUI>?,
    fields: List<Pair<String, PXFieldUI>>,
    onOptionChange: (Pair<String, PXFieldUI>) -> Unit,
    reference: String,
    onReferenceChange: (String) -> Unit
) {
    Box(
        modifier = Modifier
            .clip(MaterialTheme.shapes.large)
            .background(MaterialTheme.customColors.defaultSurface)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.customDimens.dimen16)
        ) {
            Text(
                text = serviceName,
                style = MaterialTheme.typography.bodyLarge.copy(fontWeight = FontWeight.SemiBold),
                color = MaterialTheme.colorScheme.secondary
            )
            Text(
                text = serviceCategory,
                style = MaterialTheme.typography.labelMedium,
                color = MaterialTheme.customColors.gray600
            )
            Spacer24()
            SimpleElevatedDropdown(
                items = fields,
                itemLabel = { it.second.label?.lowercase()?.capitalize().orEmpty() },
                value = selectedField,
                onValueChange = onOptionChange,
                label = stringResource(R.string.query_form_reference_type),
            )
            AnimatedVisibility(selectedField != null) {
                Column {
                    Spacer16()
                    SimpleElevatedTextField(
                        value = reference,
                        onValueChange = onReferenceChange,
                        label = selectedField?.second?.label?.lowercase()?.capitalize(),
                        placeholder = selectedField?.second?.label?.lowercase()?.capitalize()
                    )
                }
            }
        }
    }
}
