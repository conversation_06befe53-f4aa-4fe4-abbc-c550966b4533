package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.paymentdetail

import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.detail.PaymentAffiliationDetailUI

data class PaymentAffiliationDetailUiState(
    val isCapturingVoucherView: Boolean = false,
    val isCardPaymentMethod: Boolean = false,
    val paymentAffiliationDetailUI: PaymentAffiliationDetailUI = PaymentAffiliationDetailUI(
        contractNumber = "",
        contractManagementName = R.string.payment_affiliation_detail_create_automatic_payment,
        customerName = "",
        contractProductType = "",
        serviceName = "",
        identifier = "",
        contractMaxAmount = 0.0,
        selectedPaymentMethod = ""
    )
)
