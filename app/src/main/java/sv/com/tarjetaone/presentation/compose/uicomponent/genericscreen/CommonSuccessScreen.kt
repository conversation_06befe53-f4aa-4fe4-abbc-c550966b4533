package sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.SetStatusBarAppearance
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.util.LoadAsyncImage
import sv.com.tarjetaone.presentation.compose.util.Spacer1f

@Composable
fun CommonSuccessScreen(
    title: String? = null,
    description: AnnotatedString,
    descriptionStyle: TextStyle = MaterialTheme.typography.bodySmall,
    buttonText: String,
    onButtonClicked: () -> Unit
) {
    SetStatusBarAppearance(lightAppearance = false)
    Column(
        modifier = Modifier
            .background(MaterialTheme.colorScheme.inverseSurface)
            .fillMaxSize()
            .safeDrawingPadding()
            .padding(horizontal = MaterialTheme.customDimens.dimen16),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer1f()
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            LoadAsyncImage(
                modifier = Modifier
                    .width(MaterialTheme.customDimens.dimen184),
                url = R.raw.check_green
            )
            title?.let {
                Text(
                    text = stringResource(id = R.string.general_cool),
                    style = MaterialTheme.typography.displayLarge,
                    color = MaterialTheme.colorScheme.inverseOnSurface,
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .width(MaterialTheme.customDimens.dimen243)
                        .offset(y = -MaterialTheme.customDimens.dimen32)
                )
            }
            Text(
                text = description,
                style = descriptionStyle,
                color = MaterialTheme.colorScheme.inverseOnSurface,
                textAlign = TextAlign.Center,
                modifier = Modifier.width(MaterialTheme.customDimens.dimen292)
            )
        }
        Spacer1f()
        OneButton(
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.customDimens.dimen24),
            text = buttonText,
            onClick = onButtonClicked
        )
    }
}

@Preview
@Composable
fun CommonSuccessScreenPreview() {
    OneAppTheme {
        CommonSuccessScreen(
            title = stringResource(id = R.string.general_cool),
            description = buildAnnotatedString {
                append(stringResource(id = R.string.success_sign_contract))
                withStyle(style = SpanStyle(fontWeight = FontWeight.Bold)) {
                    append(" \"${stringResource(id = R.string.my_documents_label)}\"")
                }
            },
            buttonText = stringResource(id = R.string.continue_button_label)
        ) { }
    }
}
