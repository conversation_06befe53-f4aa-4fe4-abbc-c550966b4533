package sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.uicomponent.button.SolidLargeButton

@Suppress("kotlin:S107")
@Composable
fun OnboardingScreenContainer(
    isLeftButtonVisible: Boolean = true,
    isRightButtonVisible: Boolean = true,
    onLeftButtonClick: () -> Unit,
    onRightButtonClick: () -> Unit,
    currentStep: Int,
    title: String? = null,
    subtitle: String? = null,
    actions: (@Composable () -> Unit)? = null,
    content: @Composable () -> Unit
) {
    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            SimpleTopAppBar(
                isLeftButtonVisible = isLeftButtonVisible,
                isRightButtonVisible = isRightButtonVisible,
                onLeftButtonClick = onLeftButtonClick,
                onRightButtonClick = onRightButtonClick
            )
        },
        bottomBar = {
            actions?.invoke()
        },
        content = { padding ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding)
                    .verticalScroll(rememberScrollState()),
            ) {
                OnboardingHeader(
                    currentStep = currentStep,
                    title = title,
                    subtitle = subtitle,
                    icons = listOf(
                        R.drawable.ic_file_person,
                        R.drawable.ic_briefcase,
                        R.drawable.ic_references,
                        R.drawable.ic_file_person,
                        R.drawable.ic_card_un,
                    )
                )
                content()
            }
        }
    )
}

private const val PREVIEW_MAX_RANGE = 50

@Preview
@Composable
private fun OnboardingScreenContainerPreview() {
    OneAppTheme {
        OnboardingScreenContainer(
            isLeftButtonVisible = true,
            isRightButtonVisible = true,
            onLeftButtonClick = {},
            onRightButtonClick = {},
            currentStep = 1,
            title = "Title",
            actions = {
                SolidLargeButton(text = "Action") { }
            }
        ) {
            Column(
                modifier = Modifier.fillMaxSize()
            ) {
                (ONE_VALUE..PREVIEW_MAX_RANGE).toList().forEach {
                    Text("Content $it")
                }
            }
        }
    }
}
