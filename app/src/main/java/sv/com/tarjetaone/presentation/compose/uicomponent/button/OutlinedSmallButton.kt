package sv.com.tarjetaone.presentation.compose.uicomponent.button

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.poppinsFamily
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.getContainerColor

@Composable
fun OutlinedSmallButton(
    modifier: Modifier = Modifier,
    text: String,
    enabled: Boolean = true,
    buttonVariant: ButtonVariant = ButtonVariant.PRIMARY_VARIANT,
    textStyle: TextStyle = TextStyle(
        fontWeight = FontWeight.SemiBold,
        fontFamily = poppinsFamily,
        fontSize = 14.sp
    ),
    onClick: () -> Unit,
) {
    OutlinedButton(
        modifier = modifier.size(
            height = 40.dp,
            width = 140.dp
        ),
        colors = ButtonDefaults.outlinedButtonColors(
            contentColor = buttonVariant.getContainerColor(),
        ),
        border = BorderStroke(
            1.dp, buttonVariant.getContainerColor()
        ),
        enabled = enabled,
        shape = RoundedCornerShape(6.dp),
        onClick = onClick
    ) {
        Text(
            text = text,
            modifier = Modifier.align(Alignment.CenterVertically),
            style = textStyle
        )
    }
}

@Preview
@Composable
fun OutlinedSmallButtonPreview() {
    OneAppTheme {
        Surface {
            OutlinedSmallButton(
                modifier = Modifier.padding(32.dp),
                text = "Button",
                onClick = {}
            )
        }
    }
}

@Preview
@Composable
fun OutlinedSmallButtonSecondVariantPreview() {
    OneAppTheme {
        Surface {
            OutlinedSmallButton(
                modifier = Modifier.padding(32.dp),
                text = "Button",
                buttonVariant = ButtonVariant.SECONDARY_VARIANT,
                onClick = {}
            )
        }
    }
}