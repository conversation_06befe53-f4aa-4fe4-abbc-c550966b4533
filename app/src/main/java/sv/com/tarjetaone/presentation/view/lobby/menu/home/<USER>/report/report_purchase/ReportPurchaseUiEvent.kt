package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.report.report_purchase

sealed class ReportPurchaseUiEvent {
    data object OnContinueClick : ReportPurchaseUiEvent()
    data object OnBackClick : ReportPurchaseUiEvent()
    data object OnNotReportClick : ReportPurchaseUiEvent()
    data object OnSupportClick : ReportPurchaseUiEvent()
    data object OnStart : ReportPurchaseUiEvent()
    data class OnReportUnrecognizedPurchaseClick(val id: String) : ReportPurchaseUiEvent()
}
