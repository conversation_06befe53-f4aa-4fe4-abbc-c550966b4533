package sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.claims_catalog

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.menu.MenuList
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.util.ClaimCatalogMenuItem

@Composable
fun ClaimsCatalogScreen(
    claimItems: List<ClaimCatalogMenuItem>,
    onEvent: (ClaimCatalogUiEvent) -> Unit
) {
    ScreenWithTopAppBar(
        title = stringResource(R.string.claims_attention),
        onLeftButtonClick = { onEvent(ClaimCatalogUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(ClaimCatalogUiEvent.OnSupportClick) },
    ) {
        MenuList(
            items = claimItems,
            itemIcon = { it.icon },
            itemText = { it.text },
            onItemClick = { onEvent(ClaimCatalogUiEvent.OnMenuItemClick(it)) },
            modifier = Modifier.fillMaxSize()
        )
    }
}

@Preview
@Composable
private fun ClaimsCatalogScreenPreview() {
    OneAppTheme {
        ClaimsCatalogScreen(
            claimItems = listOf(
                ClaimCatalogMenuItem(
                    id = 1,
                    text = "Entrega de tarjeta",
                    icon = R.drawable.ic_truck_claim
                ),
                ClaimCatalogMenuItem(
                    id = 2,
                    text = "Funcionamento de mi tarjeta",
                    icon = R.drawable.ic_shield_check_claim
                )
            ),
            onEvent = {}
        )
    }
}
