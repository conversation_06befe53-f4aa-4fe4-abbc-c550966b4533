package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.invoiceselection.infoproducts.PXPaymentMethodUI
import sv.com.tarjetaone.domain.utils.automaticpayments.servicespayment.invoiceselection.ProductTypeCode
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.card.HorizontalIconWithText
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedDropdown
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.helpers.UiText

@Composable
fun PaymentProductsContainer(
    modifier: Modifier = Modifier,
    userProductsTypes: List<ProductTypeCode>,
    productsByTypeFiltered: List<PXPaymentMethodUI> = emptyList(),
    selectedProductType: ProductTypeCode? = null,
    selectedProduct: PXPaymentMethodUI? = null,
    onSelectProductType: (ProductTypeCode) -> Unit,
    onSelectProduct: (PXPaymentMethodUI) -> Unit,
) {
    Column(
        modifier = modifier.fillMaxWidth(),
    ) {
        Text(
            text = stringResource(id = R.string.payment_products_payment_method),
            style = MaterialTheme.typography.bodySmall.copy(
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.customColors.textBodyLight
            ),
        )
        Spacer8()
        SimpleElevatedDropdown(
            items = userProductsTypes,
            itemLabel = { it.getLabel()?.asString().orEmpty() },
            value = selectedProductType,
            onValueChange = {
                onSelectProductType(it)
            },
            modifier = Modifier.fillMaxWidth()
        )
        if (selectedProductType == null) return
        Spacer16()
        when (selectedProductType) {
            ProductTypeCode.CreditCards -> {
                HorizontalIconWithText(
                    modifier = Modifier.align(Alignment.CenterHorizontally),
                    leadingIcon = R.drawable.ic_info_outline,
                    tint = MaterialTheme.colorScheme.onSurfaceVariant,
                    iconTextSpacing = MaterialTheme.customDimens.dimen8,
                    text = UiText.StringResource(res = R.string.payment_products_disclaimer),
                    textColor = MaterialTheme.colorScheme.onSurfaceVariant,
                    style = MaterialTheme.typography.labelMedium.copy(
                        lineHeight = MaterialTheme.customDimensSp.sp14,
                    )
                )
            }
            else -> {
                Text(
                    text = stringResource(id = R.string.payment_products_account_number),
                    style = MaterialTheme.typography.bodySmall.copy(
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.customColors.textBodyLight
                    ),
                )
                Spacer8()
                SimpleElevatedDropdown(
                    modifier = Modifier.fillMaxWidth(),
                    items = productsByTypeFiltered,
                    itemLabel = { "${it.productType} - ${it.productCode.orEmpty()}" },
                    value = selectedProduct,
                    onValueChange = { onSelectProduct(it) },
                )
            }
        }
    }
}

@Preview
@Composable
fun PaymentProductsContainerPreview() {
    PaymentProductsContainer(
        userProductsTypes = listOf(
            ProductTypeCode.CreditCards,
            ProductTypeCode.BankAccounts,
        ),
        productsByTypeFiltered = listOf(
            PXPaymentMethodUI(
                productCode = "TC",
                productType = "Tarjeta de credito",
                productTypeCode = ProductTypeCode.CreditCards,
                clientCode = null,
                contractTypeID = null,
                productExpirationDate = null,
                productState = "Activa",
            ),
            PXPaymentMethodUI(
                productCode = "CT",
                productType = "Cuenta de ahorro",
                productTypeCode = ProductTypeCode.BankAccounts,
                clientCode = null,
                contractTypeID = null,
                productExpirationDate = null,
                productState = "Activa",
            ),
        ),
        selectedProductType = ProductTypeCode.CreditCards,
        selectedProduct = PXPaymentMethodUI(
            productCode = "*********",
            productType = "Tarjeta de credito",
            productTypeCode = ProductTypeCode.CreditCards,
            clientCode = null,
            contractTypeID = null,
            productExpirationDate = null,
            productState = "Activa",
        ),
        onSelectProductType = { },
        onSelectProduct = { },
    )
}
