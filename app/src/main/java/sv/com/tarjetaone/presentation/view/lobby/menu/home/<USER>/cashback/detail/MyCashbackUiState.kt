package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.cashback.detail

import androidx.paging.PagingData
import kotlinx.coroutines.flow.Flow
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.domain.entities.response.CashbackTUI
import sv.com.tarjetaone.domain.entities.response.DataAvailablePointsUI

data class MyCashbackUiState(
    val cashbackTransactions: Flow<PagingData<CashbackTUI>>? = null,
    val pointsData: DataAvailablePointsUI? = null,
    val showCashbackBottomSheet: Boolean = false,
    val eBankingUrl: String = EMPTY_STRING,
    val hasPaymentPenalty: Boolean = false,
)
