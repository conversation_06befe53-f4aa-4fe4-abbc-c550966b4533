package sv.com.tarjetaone.presentation.compose.modifier

import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import sv.com.tarjetaone.common.utils.AppConstants.VALUE_0F

fun Modifier.horizontalLineBehind(
    color: Color,
    strokeWidth: Dp,
    yPosition: Dp
): Modifier = drawBehind {
    val yPx = yPosition.toPx()
    val strokeWidthPx = strokeWidth.toPx()
    drawLine(
        color = color,
        start = Offset(VALUE_0F, yPx),
        end = Offset(size.width, yPx),
        strokeWidth = strokeWidthPx
    )
}