package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.cardblock

import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI
import sv.com.tarjetaone.domain.entities.response.CustomerCCardUI

sealed class CardBlockUiEvent {
    data object OnStart : CardBlockUiEvent()
    data object OnBackClick : CardBlockUiEvent()
    data object OnTwilioClick : CardBlockUiEvent()
    data class OnCardChange(val card: CustomerCCardUI) : CardBlockUiEvent()
    data class OnSelectedBlockReason(val cardBlockReason: CatalogItemsCollectionUI) :
        CardBlockUiEvent()
}