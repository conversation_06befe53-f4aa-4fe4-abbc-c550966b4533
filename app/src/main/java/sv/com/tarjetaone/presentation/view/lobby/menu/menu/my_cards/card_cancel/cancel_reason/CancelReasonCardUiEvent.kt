package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_cancel.cancel_reason

import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI

sealed class CancelReasonCardUiEvent {
    data object OnStart : CancelReasonCardUiEvent()
    data class OnSelectReason(val reason: CatalogItemsCollectionUI) : CancelReasonCardUiEvent()
    data class OnCommentsChange(val comments: String) : CancelReasonCardUiEvent()
    object OnContinueClick : CancelReasonCardUiEvent()
    object OnBackClick : CancelReasonCardUiEvent()
    object OnTwilioClick : CancelReasonCardUiEvent()
}
