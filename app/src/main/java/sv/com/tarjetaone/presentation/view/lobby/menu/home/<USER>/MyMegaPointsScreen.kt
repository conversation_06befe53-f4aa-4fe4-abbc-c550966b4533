package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.platform.SoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.paging.LoadState
import androidx.paging.PagingData
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.collectAsLazyPagingItems
import androidx.paging.compose.items
import kotlinx.coroutines.flow.flowOf
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.response.PointsTUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedDropdown
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleLoadingIndicator
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.PaymentPenaltyEmptyState
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.compose.util.FieldDecorationType
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.component.MegaPointsDetailCard
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.component.MegaPointsTransactionItem
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.component.MyMegaPointsFilters
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component.EmptyTransactionsItem

@Composable
fun MyMegaPointsScreen(viewModel: MyMegaPointsViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    SideEffectHandler(viewModel.sideEffects) {
        MyMegaPointsContainer(
            uiState = uiState,
            onEvent = viewModel::onEvent
        )
    }
}

@Composable
private fun MyMegaPointsContainer(
    uiState: MyMegaPointsUiState = MyMegaPointsUiState(),
    onEvent: (event: MyMegaPointsUiEvent) -> Unit = {}
) {
    val transactionItems = uiState.pointTransactionsPaging?.collectAsLazyPagingItems()
    val keyboardController = LocalSoftwareKeyboardController.current
    var isDetailExpanded by remember { mutableStateOf(true) }

    LaunchedEffect(Unit) {
        onEvent(MyMegaPointsUiEvent.OnStart)
    }
    ScreenWithTopAppBar(
        contentPadding = PaddingValues(
            top = MaterialTheme.customDimens.dimen32,
            start = MaterialTheme.customDimens.dimen32,
            end = MaterialTheme.customDimens.dimen32,
            bottom = MaterialTheme.customDimens.dimen4
        ),
        title = stringResource(id = R.string.my_megapoints).takeIf { !uiState.hasPaymentPenalty },
        onLeftButtonClick = { onEvent(MyMegaPointsUiEvent.OnBackPressed) },
        onRightButtonClick = { onEvent(MyMegaPointsUiEvent.OnSupportClick) }
    ) {
        if (uiState.hasPaymentPenalty) {
            PaymentPenaltyEmptyState { onEvent(MyMegaPointsUiEvent.OnBackPressed) }
        } else {
            MyMegaPointsContent(
                uiState = uiState,
                onEvent = onEvent,
                isDetailExpanded = isDetailExpanded,
                keyboardController = keyboardController,
                transactionItems = transactionItems,
                onClickDetailCard = { isDetailExpanded = !isDetailExpanded }
            )
        }
    }
}

@Composable
private fun MyMegaPointsContent(
    uiState: MyMegaPointsUiState,
    onEvent: (event: MyMegaPointsUiEvent) -> Unit,
    isDetailExpanded: Boolean,
    keyboardController: SoftwareKeyboardController?,
    transactionItems: LazyPagingItems<PointsTUI>?,
    onClickDetailCard: () -> Unit = {},
) {
    LazyColumn(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen8),
        modifier = Modifier.padding(horizontal = MaterialTheme.customDimens.dimen32)
    ) {
        item {
            SimpleElevatedDropdown(
                items = uiState.pointStatementList,
                itemLabel = { it.cutOffDateName },
                value = uiState.pointStatementSelected,
                onValueChange = { onEvent(MyMegaPointsUiEvent.OnPointStatementSelected(it)) },
                decorationType = FieldDecorationType.OUTLINED,
                modifier = Modifier.padding(horizontal = MaterialTheme.customDimens.dimen8)
            )
        }
        item {
            MegaPointsDetailCard(
                isExpanded = isDetailExpanded,
                onClick = onClickDetailCard,
                modifier = Modifier.padding(horizontal = MaterialTheme.customDimens.dimen8),
                pointStatement = uiState.pointStatementSelected
            )
            Spacer16()
        }
        item {
            MyMegaPointsFilters(
                uiState = uiState,
                keyboardController = keyboardController,
                onEvent = onEvent
            )
            Spacer16()
        }
        when (transactionItems?.loadState?.refresh) {
            is LoadState.Loading -> item { SimpleLoadingIndicator(modifier = Modifier.fillMaxSize()) }
            is LoadState.Error -> {
                onEvent(MyMegaPointsUiEvent.OnTransactionLoadError { transactionItems.retry() })
            }

            else -> {
                if (transactionItems == null || transactionItems.itemCount == 0) {
                    item {
                        EmptyPointsTransaction(uiState = uiState)
                        Spacer16()
                    }
                } else {
                    items(transactionItems) { transaction ->
                        transaction?.let {
                            MegaPointsTransactionItem(
                                pointTransactionData = it,
                                pointsStatus = uiState.megaPointsStatusFilter
                            )
                        }
                    }
                }
            }
        }
        when (transactionItems?.loadState?.append) {
            is LoadState.Loading -> item {
                SimpleLoadingIndicator(modifier = Modifier.fillMaxWidth())
            }

            is LoadState.Error -> {
                onEvent(MyMegaPointsUiEvent.OnTransactionLoadError {
                    transactionItems.retry()
                })
            }

            else -> Unit
        }
    }
}

@Composable
private fun EmptyPointsTransaction(
    uiState: MyMegaPointsUiState
) {
    EmptyTransactionsItem(
        image = if (uiState.megaPointsSearchMode is MegaPointsTransactionsMode.ByText) {
            R.drawable.ic_empty_movements
        } else {
            R.drawable.ic_movement_default
        },
        subtitle = stringResource(
            id = if (uiState.megaPointsSearchMode is MegaPointsTransactionsMode.ByText) {
                R.string.my_movements_no_result
            } else {
                R.string.my_movements_no_movements
            }
        )
    )
}


@Preview
@Composable
private fun MyMegaPointsScreenPreview() {

    val paymentAmount = 8.50
    val pointsAmount = 550
    val pointsDate = "2023-02-17"
    val pointsDescription = "Pago Burger King"

    OneAppTheme {
        MyMegaPointsContainer(
            uiState = MyMegaPointsUiState(
                pointTransactionsPaging = flowOf(
                    PagingData.from(
                        listOf(
                            PointsTUI(
                                type = "",
                                pointsAmount = pointsAmount,
                                valueDate = pointsDate,
                                description = pointsDescription,
                                paymentAmount = paymentAmount
                            ),
                            PointsTUI(
                                type = "",
                                pointsAmount = pointsAmount,
                                valueDate = pointsDate,
                                description = pointsDescription,
                                paymentAmount = paymentAmount
                            ),
                            PointsTUI(
                                type = "",
                                pointsAmount = pointsAmount,
                                valueDate = pointsDate,
                                description = pointsDescription,
                                paymentAmount = paymentAmount
                            )
                        )
                    )
                )
            )
        )
    }
}
