package sv.com.tarjetaone.presentation.view.common.locationcapture.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.HyperLinkTextButton
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedDropdown
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.compose.util.TextButtonVariant
import sv.com.tarjetaone.presentation.view.common.locationcapture.LocationCaptureUiEvent
import sv.com.tarjetaone.presentation.view.common.locationcapture.LocationCaptureUiState
import sv.com.tarjetaone.presentation.view.common.locationcapture.helper.AddressAliasSuggestion

@Composable
fun AddressInfoForm(
    modifier: Modifier = Modifier,
    uiState: LocationCaptureUiState,
    onEvent: (LocationCaptureUiEvent) -> Unit
) {
    val focusManager = LocalFocusManager.current
    Column(
        modifier
            .fillMaxWidth()
            .verticalScroll(rememberScrollState())
            .padding(horizontal = MaterialTheme.customDimens.dimen24),
    ) {
        Text(
            textAlign = TextAlign.Center,
            text = stringResource(id = R.string.address_location),
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.secondary,
            modifier = Modifier.fillMaxWidth()
        )
        Spacer16()
        SimpleElevatedDropdown(
            items = uiState.addressAliasSuggestions,
            itemLabel = { it.suggestionToString() },
            value = uiState.addressAliasSelected,
            label = stringResource(id = R.string.custom_address_label),
            placeholder = stringResource(id = R.string.select),
            onValueChange = {
                onEvent(LocationCaptureUiEvent.OnAddressAliasSuggestionChange(it))
            }
        )
        AnimatedVisibility(
            visible = uiState.addressAliasSelected == AddressAliasSuggestion.Other,
        ) {
            Column {
                Spacer16()
                SimpleElevatedTextField(
                    value = uiState.addressAlias.orEmpty(),
                    placeholder = stringResource(id = R.string.address_suggestion_placeholder),
                    onValueChange = { onEvent(LocationCaptureUiEvent.OnAddressAliasChange(it)) },
                    label = stringResource(R.string.custom_address_input_label),
                    error = uiState.getInvalidAliasMessage()?.asString(),
                    hasError = (!uiState.validAlias || !uiState.validAliasLength) && uiState.addressAlias?.isNotEmpty() == true,
                )
            }
        }
        Spacer16()
        SimpleElevatedDropdown(
            items = uiState.states,
            itemLabel = { it.name.orEmpty() },
            value = uiState.selectedState,
            label = stringResource(id = R.string.state_label),
            placeholder = stringResource(id = R.string.select),
            onValueChange = {
                onEvent(LocationCaptureUiEvent.OnStateChange(it))
            }
        )
        Spacer16()
        SimpleElevatedDropdown(
            items = uiState.municipalities,
            itemLabel = { it.name.orEmpty() },
            value = uiState.selectedMunicipality,
            label = stringResource(id = R.string.city_label),
            placeholder = stringResource(id = R.string.select),
            onValueChange = { onEvent(LocationCaptureUiEvent.OnMunicipalityChange(it)) }
        )
        Spacer16()
        SimpleElevatedTextField(
            placeholder = stringResource(id = R.string.residential_label),
            value = uiState.neighborhood,
            onValueChange = { onEvent(LocationCaptureUiEvent.OnNeighborhoodChange(it)) },
            label = stringResource(R.string.residential_or_colony),
            error = stringResource(id = R.string.invalid_characters),
            hasError = !uiState.validNeighborhood && uiState.neighborhood.isNotEmpty(),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next),
            keyboardActions = KeyboardActions(
                onNext = { focusManager.moveFocus(FocusDirection.Down) }
            )
        )
        Spacer16()
        SimpleElevatedTextField(
            value = uiState.street,
            placeholder = stringResource(id = R.string.ej_calle4),
            onValueChange = { onEvent(LocationCaptureUiEvent.OnStreetChange(it)) },
            label = stringResource(R.string.calle_avenida),
            error = stringResource(id = R.string.invalid_characters),
            hasError = !uiState.validStreet && uiState.street.isNotEmpty(),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next),
            keyboardActions = KeyboardActions(
                onNext = { focusManager.moveFocus(FocusDirection.Down) }
            )
        )
        Spacer16()
        SimpleElevatedTextField(
            value = uiState.homeNumber,
            placeholder = stringResource(id = R.string.ej_casa_4),
            onValueChange = { onEvent(LocationCaptureUiEvent.OnHomeNumberChange(it)) },
            label = stringResource(R.string.home_appartment),
            error = stringResource(id = R.string.invalid_characters),
            hasError = !uiState.validHomeNumber && uiState.homeNumber.isNotEmpty(),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next),
            keyboardActions = KeyboardActions(
                onNext = { focusManager.moveFocus(FocusDirection.Down) }
            )
        )
        Spacer16()
        SimpleElevatedTextField(
            modifier = Modifier.height(MaterialTheme.customDimens.dimen140),
            value = uiState.additionalInfo,
            onValueChange = { onEvent(LocationCaptureUiEvent.OnAdditionalInfoChange(it)) },
            label = stringResource(R.string.additional_info),
            error = stringResource(id = R.string.invalid_characters),
            hasError = !uiState.validAdditionalInfo && uiState.additionalInfo.isNotEmpty(),
            singleLine = false,
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
            keyboardActions = KeyboardActions(
                onDone = { focusManager.clearFocus() }
            )
        )
        Spacer16()
        Spacer1f()
        OneButton(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(id = R.string.save_address),
            enabled = uiState.validAddressForm,
            onClick = { onEvent(LocationCaptureUiEvent.OnContinueClick) }
        )
        Spacer8()
        HyperLinkTextButton(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(id = R.string.search_my_address_in_the_map),
            onClick = { onEvent(LocationCaptureUiEvent.OnSearchMyAddressClick) },
            textButtonVariant = TextButtonVariant.SMALL_VARIANT
        )
        Spacer16()
    }
}

@Preview(showBackground = true)
@Composable
private fun AddressInfoFormPreview() {
    OneAppTheme {
        AddressInfoForm(
            uiState = LocationCaptureUiState(),
            onEvent = {}
        )
    }
}
