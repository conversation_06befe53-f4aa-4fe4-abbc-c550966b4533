package sv.com.tarjetaone.presentation.view.common.reference_form

import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI

sealed class ReferenceFormEvent {
    data object OnStart: ReferenceFormEvent()
    data object OnFetchReferences : ReferenceFormEvent()
    data class OnNameChanged(val name: String) : ReferenceFormEvent()
    data class OnPhoneNumberChanged(val phoneNumber: String) : ReferenceFormEvent()
    data class OnValidatePhoneNumber(val phoneNumber: String) : ReferenceFormEvent()
    data class OnReferenceValueChanged(val item: CatalogItemsCollectionUI) : ReferenceFormEvent()
    data object OnContinueButtonClicked : ReferenceFormEvent()
    data object OnBackClick : ReferenceFormEvent()
    data object OnSupportClick : ReferenceFormEvent()
}
