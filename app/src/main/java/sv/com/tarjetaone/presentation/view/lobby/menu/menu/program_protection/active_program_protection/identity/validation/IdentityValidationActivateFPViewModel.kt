package sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.active_program_protection.identity.validation

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.SUCCESS_RESPONSE_CODE
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.request.FraudProtectionPutRequestUI
import sv.com.tarjetaone.domain.entities.response.ContractsMode
import sv.com.tarjetaone.domain.usecases.auth.FaceAuthenticationUseCase
import sv.com.tarjetaone.domain.usecases.fraudProtection.PutFraudProtectionUseCase
import sv.com.tarjetaone.presentation.view.common.identity_validation.IdentityValidationBaseViewModel
import javax.inject.Inject

@HiltViewModel
class IdentityValidationActivateFPViewModel @Inject constructor(
    private val putFraudProtectionUseCase: PutFraudProtectionUseCase,
    faceAuthenticationUseCase: FaceAuthenticationUseCase,
    sharedPrefsRepo: SecureSharedPreferencesRepository,
    savedStateHandle: SavedStateHandle
) : IdentityValidationBaseViewModel(
    faceAuthenticationUseCase = faceAuthenticationUseCase,
    sharedPrefs = sharedPrefsRepo
) {
    private val args =
        IdentityValidationActivateFPFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override val onBlockedAction = {
        sendEvent(UiEvent.NavigateBackTo(R.id.navigation_home))
    }

    private fun onBackNavigation() =
        sendEvent(UiEvent.NavigateBackTo(destinationId = R.id.prepareForPictureActivateFPFragment))

    override fun operation(biometryId: Int) {
        viewModelScope.launch {
            val request = FraudProtectionPutRequestUI(
                cCardId = sharedPrefs.mainCard?.creditCardId.orZero(),
                fProtectionId = args.fraud?.fraudProtection?.fraudProtectionId.orZero(),
                biometryProcessId = biometryId,
                mode = ContractsMode.ACTIVATION.mode
            )
            putFraudProtectionUseCase(request).executeUseCase(
                onApiErrorAction = { _, _, _, _ ->
                    showUpsErrorMessage(isDismissible = false) {
                        onBackNavigation()
                    }
                },
                onSuccessAction = { response ->
                    if (response.statusResponse?.responseStatus?.code == SUCCESS_RESPONSE_CODE) {
                        sendEvent(
                            UiEvent.Navigate(
                                IdentityValidationActivateFPFragmentDirections
                                    .actionIdentityValidationActivateFPFragmentToDetailProgramProtectionFragment(
                                        fraudResponse = response.dataResponse
                                    )
                            )
                        )
                    } else {
                        showUpsErrorMessage(isDismissible = false) {
                            onBackNavigation()
                        }
                    }
                }
            )
        }
    }

    override fun onFailedIdentityValidation(resultCode: String?) {
        onBackNavigation()
    }
}
