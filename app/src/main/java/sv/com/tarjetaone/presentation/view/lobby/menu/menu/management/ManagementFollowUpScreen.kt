package sv.com.tarjetaone.presentation.view.lobby.menu.menu.management

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.core.graphics.toColorInt
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.paging.LoadState
import androidx.paging.PagingData
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.collectAsLazyPagingItems
import androidx.paging.compose.items
import kotlinx.coroutines.flow.flowOf
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.domain.entities.response.LogManagementItemsCollectionUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.HyperLinkTextButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedDropdown
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleLoadingIndicator
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.management.component.ManagementCardScreen
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.settings.activity_log.ActivityLogDateRange
import sv.com.tarjetaone.presentation.view.utils.DAY_OF_WEEK_START_MONTH_FORMAT
import sv.com.tarjetaone.presentation.view.utils.parseDateByString

@Composable
fun ManagementFollowUpScreen(viewModel: ManagementFollowUpViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val managementItems = uiState.managementPagingData?.collectAsLazyPagingItems()
    SideEffectHandler(viewModel.sideEffects) {
        RequestFollowUpScreenContent(uiState, managementItems, viewModel::onEvent)
    }
}

@Composable
private fun RequestFollowUpScreenContent(
    uiState: ManagementFollowUpUiState,
    managementItems: LazyPagingItems<LogManagementItemsCollectionUI>?,
    onEvent: (ManagementFollowUpUiEvent) -> Unit
) {
    LaunchedEffect(Unit) {
        onEvent(ManagementFollowUpUiEvent.OnStart)
    }
    ScreenWithTopAppBar(
        contentPadding = PaddingValues(
            top = MaterialTheme.customDimens.dimen32,
            start = MaterialTheme.customDimens.dimen32,
            end = MaterialTheme.customDimens.dimen32
        ),
        title = stringResource(id = R.string.request_title),
        onLeftButtonClick = { onEvent(ManagementFollowUpUiEvent.OnBackPressed) },
        onRightButtonClick = { onEvent(ManagementFollowUpUiEvent.OnSupportClicked) }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight()
                .background(MaterialTheme.colorScheme.surface),
            verticalArrangement = Arrangement.Top
        ) {
            Spacer8()
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.customDimens.dimen6),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Spacer16()
                SimpleElevatedDropdown(
                    modifier = Modifier.weight(ONE_FLOAT_VALUE),
                    items = ActivityLogDateRange.entries,
                    itemLabel = { stringResource(id = it.label) },
                    value = uiState.dateRange,
                    onValueChange = { onEvent(ManagementFollowUpUiEvent.OnDateChange(it)) }
                )
                Spacer16()
                SimpleElevatedDropdown(
                    modifier = Modifier.weight(ONE_FLOAT_VALUE),
                    items = uiState.requestStatusList,
                    itemLabel = { it.name.orEmpty() },
                    value = uiState.selectedRequestStatus,
                    onValueChange = { onEvent(ManagementFollowUpUiEvent.OnFilterChange(it)) }
                )
                Spacer16()
            }
            Spacer24()
            when (managementItems?.loadState?.refresh) {
                is LoadState.Loading -> if (uiState.selectedRequestStatus.name?.isNotEmpty() == true) {
                    SimpleLoadingIndicator(modifier = Modifier.fillMaxSize())
                }

                is LoadState.Error -> {
                    onEvent(ManagementFollowUpUiEvent.OnManagementLoadError(callback = managementItems::retry))
                }

                else -> {
                    if (managementItems?.itemCount == ZERO_VALUE) {
                        HyperLinkTextButton(
                            modifier = Modifier
                                .padding(horizontal = MaterialTheme.customDimens.dimen25)
                                .fillMaxWidth(),
                            text = stringResource(id = R.string.request_new_request),
                            onClick = { onEvent(ManagementFollowUpUiEvent.OnNewManagementClicked) }
                        )
                    } else {
                        ManagementsList(
                            modifier = Modifier.weight(ONE_FLOAT_VALUE),
                            managementLogItems = managementItems,
                            onAppendError = {
                                onEvent(ManagementFollowUpUiEvent.OnManagementLoadError(callback = it))
                            },
                            onItemClickEvent = {
                                onEvent(ManagementFollowUpUiEvent.OnManagementClick(it))
                            },
                            onNewManagementClickEvent = {
                                onEvent(ManagementFollowUpUiEvent.OnNewManagementClicked)
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun ManagementsList(
    modifier: Modifier = Modifier,
    managementLogItems: LazyPagingItems<LogManagementItemsCollectionUI>?,
    onAppendError: (() -> Unit) -> Unit,
    onItemClickEvent: (LogManagementItemsCollectionUI) -> Unit,
    onNewManagementClickEvent: () -> Unit
) {
    LazyColumn(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen20),
        contentPadding = PaddingValues(MaterialTheme.customDimens.dimen5)
    ) {
        managementLogItems?.let { items ->
            items(items = items) { managementItem ->
                managementItem?.let {
                    ManagementCardScreen(
                        title = it.manTypeNameApp,
                        date = parseDateByString(
                            inputDate = it.manDate,
                            outputFormatString = DAY_OF_WEEK_START_MONTH_FORMAT
                        ),
                        state = it.mRStatusNameApp,
                        managementNumber = it.mainType,
                        stateColor = Color(it.MRSColor.toColorInt())
                    ) {
                        onItemClickEvent(it)
                    }
                }
                Spacer8()
                if (items.itemSnapshotList.last() == managementItem) {
                    HyperLinkTextButton(
                        modifier = Modifier
                            .padding(horizontal = MaterialTheme.customDimens.dimen25)
                            .fillMaxWidth(),
                        text = stringResource(id = R.string.request_new_request),
                        onClick = { onNewManagementClickEvent() }
                    )
                }
            }
            when (items.loadState.append) {
                is LoadState.Loading -> Unit

                is LoadState.Error -> onAppendError {
                    items.retry()
                }

                else -> Unit
            }
        }
    }
}

@Composable
@Preview
private fun ManagementFollowUpContentPreview() {
    val uiState = ManagementFollowUpUiState()
    val managementItems =
        flowOf(
            PagingData.from(
                listOf(
                    LogManagementItemsCollectionUI(
                        123,
                        "G12345",
                        "Retiro de credito",
                        "Favorable",
                        "#DEAD00",
                        "20 mayo 2024",
                        "1234123"
                    ),
                    LogManagementItemsCollectionUI(
                        202,
                        "R98765",
                        "Ingreso de Reclamo",
                        "En Proceso",
                        "#F1C40F",
                        "28 junio 2024",
                        "6543210"
                    )
                )
            )
        ).collectAsLazyPagingItems()
    OneAppTheme {
        RequestFollowUpScreenContent(
            uiState = uiState,
            managementItems,
            {}
        )
    }
}

@Composable
@Preview
private fun ManagementsListPreview() {
    ManagementsList(
        Modifier, flowOf(
            PagingData.from(
                listOf(
                    LogManagementItemsCollectionUI(
                        101,
                        "A13579",
                        "Actualización de Datos",
                        "Completado",
                        "#2ECC71",
                        "10 mayo 2024",
                        "1357924"
                    ),
                    LogManagementItemsCollectionUI(
                        456,
                        "S78901",
                        "Reemplazo de Tarjeta",
                        "Pendiente",
                        "#3498DB",
                        "15 junio 2024",
                        "5678901"
                    )
                )
            )
        ).collectAsLazyPagingItems(), {}, {}, {})
}
