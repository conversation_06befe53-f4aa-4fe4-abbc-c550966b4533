package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.report.unrecognized

import sv.com.tarjetaone.domain.entities.response.UnrecognizedTransactionUI

sealed class UnrecognizedMovementUiEvent {
    data object OnContinueClick : UnrecognizedMovementUiEvent()

    data object OnBackClick : UnrecognizedMovementUiEvent()

    class OnTransactionClick(val transaction: UnrecognizedTransactionUI) :
        UnrecognizedMovementUiEvent()

    data object OnSupportClick : UnrecognizedMovementUiEvent()

    data object OnLoadingError : UnrecognizedMovementUiEvent()
    data object OnStart : UnrecognizedMovementUiEvent()
}