package sv.com.tarjetaone.presentation.view.lobby.menu.menu.account_deletion.delete_account_detail

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.presentation.helpers.DeleteAccountDetailType
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.common.management.ManagementAttributesUiModel
import sv.com.tarjetaone.presentation.view.common.management.ManagementDetailBaseViewModel
import sv.com.tarjetaone.presentation.view.utils.DAY_OF_WEEK_MONTH_TIME_FORMAT
import sv.com.tarjetaone.presentation.view.utils.YEAR_MONTH_DAY_TIME_FORMAT
import javax.inject.Inject

@HiltViewModel
class DeleteAccountDetailViewModel @Inject constructor(
    imageUtils: ImageUtils,
    savedStateHandle: SavedStateHandle,
) : ManagementDetailBaseViewModel(imageUtils) {
    private val args = DeleteAccountDetailFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onStart() {
        args.deleteAccountDetail?.let { deleteAccountDetail ->
            _uiState.update {
                it.copy(
                    managementNumber = deleteAccountDetail.manRequestId.toString(),
                    managementStatus = deleteAccountDetail.mrStatusNameApp.orEmpty(),
                    managementStatusColor = deleteAccountDetail.mrStatusTextColor.orEmpty(),
                    managementName = deleteAccountDetail.manTypeNameApp.orEmpty(),
                    customerName = deleteAccountDetail.clientName.orEmpty(),
                    requestStartDate = deleteAccountDetail.processDate?.getFormattedDateFromTo(
                        YEAR_MONTH_DAY_TIME_FORMAT,
                        DAY_OF_WEEK_MONTH_TIME_FORMAT
                    ).orEmpty(),
                    requestEndDate = deleteAccountDetail.closeDate?.getFormattedDateFromTo(
                        YEAR_MONTH_DAY_TIME_FORMAT,
                        DAY_OF_WEEK_MONTH_TIME_FORMAT
                    ).orEmpty(),
                    resolutionDays = deleteAccountDetail.availableDay,
                    creditCardType = deleteAccountDetail.typeCardText.orEmpty(),
                    creditCardNumber = deleteAccountDetail.cardNumMasked.orEmpty(),
                    managementAttributes = if (args.deleteAccountDetailType == DeleteAccountDetailType.VERIFYING_PROCESS_STATUS) {
                        transformAttributesToUiTextList(deleteAccountDetail.manAttributes) + listOf(
                            ManagementAttributesUiModel(
                                managementAttributeDisplayName = UiText.StringResource(
                                    R.string.delete_account_detail_label
                                ),
                                managementAttributeValue = UiText.StringResource(
                                    R.string.delete_account_detail_description
                                )
                            )
                        )
                    } else transformAttributesToUiTextList(deleteAccountDetail.manAttributes),
                    primaryButtonText = if (args.deleteAccountDetailType == DeleteAccountDetailType.ACCOUNT_DELETED) {
                        R.string.accept_label
                    } else {
                        R.string.logout
                    }
                )
            }
        }
    }

    override fun onPrimaryButtonClick() {
        if (args.deleteAccountDetailType == DeleteAccountDetailType.ACCOUNT_DELETED) {
            baseSharedPrefs.apply {
                removeUserCredentials()
                removeUserProfile()
                removeUser()
                putUserHasLogin(false)
                putDuiValidation(false)
            }
        }
        sendEvent(UiEvent.Logout)
    }
}