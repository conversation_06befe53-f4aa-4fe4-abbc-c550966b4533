package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.queryform

import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING

data class ServiceAffiliationQueryFormUiState(
    val serviceName: String = EMPTY_STRING,
    val categoryName: String = EMPTY_STRING,
    val inputLabel: String? = null,
    val referenceNumber: String? = null,
    val queryFormError: String? = null,
) {
    val isValid: Boolean
        get() = referenceNumber.isNullOrEmpty().not() && queryFormError.isNullOrEmpty()
}
