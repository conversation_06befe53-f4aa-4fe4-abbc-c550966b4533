package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.references.referenceform

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.view.common.reference_form.ReferenceFormEvent
import sv.com.tarjetaone.presentation.view.common.reference_form.ReferenceFormUiState
import sv.com.tarjetaone.presentation.view.common.reference_form.components.ReferenceFormContent

@Composable
fun ReferenceFormScreen(
    viewModel: ReferencesFormViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    SideEffectHandler(viewModel.sideEffects) {
        ReferenceFormScreenContent(
            uiState = uiState,
            onEvent = viewModel::onEvent
        )
    }
}

@Composable
private fun ReferenceFormScreenContent(
    uiState: ReferenceFormUiState,
    onEvent: (ReferenceFormEvent) -> Unit
) {
    LaunchedEffect(Unit) {
        onEvent(ReferenceFormEvent.OnFetchReferences)
    }
    LaunchedEffect(Unit) {
        onEvent(ReferenceFormEvent.OnStart)
    }
    ScreenWithTopAppBar(
        isLeftButtonVisible = false,
        onLeftButtonClick = { /* Without implementation */ },
        onRightButtonClick = { onEvent(ReferenceFormEvent.OnSupportClick) },
        title = stringResource(id = R.string.references)
    ) {
        ReferenceFormContent(
            uiState = uiState,
            onEvent = onEvent
        )
    }
}

@Preview(
    showBackground = true,
    showSystemUi = true
)
@Composable
private fun ReferenceFormScreenPreview() {
    OneAppTheme {
        ReferenceFormScreenContent(
            uiState = ReferenceFormUiState(),
            onEvent = { }
        )
    }
}
