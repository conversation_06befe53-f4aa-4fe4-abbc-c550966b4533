package sv.com.tarjetaone.presentation.view.lobby.menu.dataedit

sealed class PersonalDataEditUiEvent {
    data object OnStart : PersonalDataEditUiEvent()
    data object OnBackClick : PersonalDataEditUiEvent()
    data object OnTwilioClick : PersonalDataEditUiEvent()
    data class OnNameChange(val value: String) : PersonalDataEditUiEvent()
    data class OnLastNameChange(val value: String) : PersonalDataEditUiEvent()
    data class OnDuiChange(val value: String) : PersonalDataEditUiEvent()
    data object OnSaveChanges : PersonalDataEditUiEvent()
    data object OnCancelChanges : PersonalDataEditUiEvent()
}