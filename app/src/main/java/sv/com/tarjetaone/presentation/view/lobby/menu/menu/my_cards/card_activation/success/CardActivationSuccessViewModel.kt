package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_activation.success

import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.analytics.amplitude.AmplitudeEvents.ON_ACTIVATE_CARD_EVENT
import sv.com.tarjetaone.analytics.amplitude.AmplitudeManager
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import javax.inject.Inject

@HiltViewModel
class CardActivationSuccessViewModel @Inject constructor(
    private val amplitudeManager: AmplitudeManager
) : BaseViewModel() {

    private fun onContinueClicked() {
        amplitudeManager.track(ON_ACTIVATE_CARD_EVENT)
        sendEvent(
            UiEvent.Navigate(
                CardActivationSuccessFragmentDirections
                    .actionSuccessActivateCardToNavigationHome()
            )
        )
    }

    fun onEvent(event: CardActivationSuccessUiEvent) {
        when (event) {
            CardActivationSuccessUiEvent.OnContinueClick -> onContinueClicked()
        }
    }
}
