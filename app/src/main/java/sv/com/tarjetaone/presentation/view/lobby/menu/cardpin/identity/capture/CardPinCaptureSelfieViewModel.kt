package sv.com.tarjetaone.presentation.view.lobby.menu.cardpin.identity.capture

import android.graphics.Bitmap
import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.presentation.view.common.selfiecapture.SelfieCaptureBaseViewModel
import javax.inject.Inject

@HiltViewModel
class CardPinCaptureSelfieViewModel @Inject constructor(
    facephiResultHandler: <PERSON>phiResultHandler,
    savedStateHandle: SavedStateHandle
) : SelfieCaptureBaseViewModel(facephiResultHandler) {
    private val args = CardPinCaptureSelfieFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onSuccessfulSelfieCapture(selfie: Bitmap) {
        sendEvent(
            UiEvent.Navigate(
                CardPinCaptureSelfieFragmentDirections
                    .actionCardPinCaptureSelfieFragmentToCardPinPreviewSelfieFragment(pin = args.pin)
            )
        )
    }
}
