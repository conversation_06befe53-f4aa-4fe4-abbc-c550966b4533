package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.references.identity.validation

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.usecases.auth.FaceAuthenticationUseCase
import sv.com.tarjetaone.presentation.view.common.identity_validation.IdentityValidationBaseViewModel
import javax.inject.Inject

@HiltViewModel
class IdentityValidationReferencesViewModel @Inject constructor(
    faceAuthenticationUseCase: FaceAuthenticationUseCase,
    sharedPrefsRepo: SecureSharedPreferencesRepository,
    savedStateHandle: SavedStateHandle
) : IdentityValidationBaseViewModel(
    faceAuthenticationUseCase = faceAuthenticationUseCase,
    sharedPrefs = sharedPrefsRepo
) {
    private val args =
        IdentityValidationReferencesFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override val onBlockedAction = {
        sendEvent(UiEvent.NavigateBackTo(R.id.navigation_home))
    }

    override fun operation(biometryId: Int) {
        sendEvent(
            UiEvent.Navigate(
                IdentityValidationReferencesFragmentDirections
                    .actionReferencesIdentityValidationFragmentToReferencesFormFragment(
                        biometryId = biometryId,
                        referenceData = args.referenceData,
                        referenceType = args.referenceType
                    )
            )
        )
    }

    override fun onFailedIdentityValidation(resultCode: String?) = onBackAction()

    override fun onBackAction() =
        sendEvent(UiEvent.NavigateBackTo(R.id.prepareForPictureReferencesFragment))
}
