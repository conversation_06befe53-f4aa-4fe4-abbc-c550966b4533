package sv.com.tarjetaone.presentation.compose.uicomponent

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CardElevation
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.modifier.conditional
import sv.com.tarjetaone.presentation.compose.theme.LocalCustomColors
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.common.RadioButtonComponent
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SelectableCardMainContainer

@Suppress("kotlin:S107")
@Composable
fun ToggleCheckBoxButton(
    isEnable: Boolean = false,
    title: String,
    subtitle: String,
    modifier: Modifier,
    selected: Boolean = false,
    elevation: CardElevation = CardDefaults.cardElevation(
        defaultElevation = MaterialTheme.customDimens.dimen2,
    ),
    titleTextStyle: TextStyle = MaterialTheme.typography.headlineSmall,
    subTitleTextStyle: TextStyle = MaterialTheme.typography.labelMedium,
    selectedContainerColor: Color = LocalCustomColors.current.successContainer,
    unselectedContainerColor: Color = MaterialTheme.colorScheme.background,
    selectedContentColor: Color = MaterialTheme.colorScheme.onPrimary,
    unselectedContentColor: Color = MaterialTheme.colorScheme.onSurface,
    isDisabledBackgroundColor: Color = LocalCustomColors.current.gray300,
    isDisabledContentColor: Color = MaterialTheme.colorScheme.secondaryContainer,
    onSelectChange: () -> Unit
) {

    SelectableCardMainContainer(
        modifier = modifier,
        selected = selected,
        elevation = elevation,
        selectedContainerColor = if (isEnable) selectedContainerColor else isDisabledBackgroundColor,
        unselectedContainerColor = if (isEnable) unselectedContainerColor else isDisabledBackgroundColor,
        selectedContentColor = if (isEnable) selectedContentColor else isDisabledContentColor,
        unselectedContentColor = if (isEnable) unselectedContentColor else isDisabledContentColor,
        shape = MaterialTheme.shapes.large,
    ) {
        Column(
            modifier = modifier.clickable { if (isEnable) onSelectChange() },
            verticalArrangement = Arrangement.Center
        ) {
            Row(modifier = Modifier.padding(end = MaterialTheme.customDimens.dimen16)) {
                RadioButtonComponent(
                    modifier = Modifier.align(Alignment.CenterVertically),
                    selected = selected,
                    onClick = { if (isEnable) onSelectChange() }
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(vertical = MaterialTheme.customDimens.dimen16),
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = title,
                        style = titleTextStyle,
                        modifier = Modifier.fillMaxWidth()
                    )
                    SubtitleWithMoreToggle(
                        isEnable = isEnable,
                        subtitle = subtitle,
                        selected = selected,
                        subTitleTextStyle = subTitleTextStyle,
                        isDisabledContentColor = isDisabledContentColor,
                        selectedContentColor = selectedContentColor,
                    )
                }
            }
        }
    }
}

@Composable
private fun SubtitleWithMoreToggle(
    isEnable: Boolean,
    subtitle: String,
    selected: Boolean,
    subTitleTextStyle: TextStyle,
    isDisabledContentColor: Color,
    selectedContentColor: Color,
) {

    var isExpanded by remember { mutableStateOf(false) }
    var showSeeMore by remember { mutableStateOf(false) }

    Column {
        if (subtitle.isNotBlank()) {
            Text(
                text = subtitle,
                style = subTitleTextStyle,
                maxLines = if (isExpanded) Int.MAX_VALUE else SUBTITLE_MAX_LINES,
                overflow = TextOverflow.Ellipsis,
                onTextLayout = { if (!showSeeMore) showSeeMore = it.hasVisualOverflow },
                modifier = Modifier
                    .fillMaxWidth()
                    .animateContentSize()
            )

            if (showSeeMore) {
                SeeMoreButton(
                    modifier = Modifier
                        .align(Alignment.End)
                        .padding(end = MaterialTheme.customDimens.dimen8)
                        .conditional(
                            condition = isEnable,
                            onTrue = { Modifier.clickable { isExpanded = !isExpanded } },
                            onFalse = { Modifier }
                        ),
                    isEnable = isEnable,
                    isExpanded = isExpanded,
                    selected = selected,
                    isDisabledContentColor = isDisabledContentColor,
                    selectedContentColor = selectedContentColor
                )
            }
        }
    }
}

@Composable
private fun SeeMoreButton(
    modifier: Modifier,
    isEnable: Boolean,
    isExpanded: Boolean,
    selected: Boolean,
    isDisabledContentColor: Color,
    selectedContentColor: Color
) {
    Text(
        text = stringResource(if (isExpanded) R.string.show_less else R.string.show_more),
        style = MaterialTheme.typography.bodySmall,
        color = when {
            !isEnable -> isDisabledContentColor
            selected -> selectedContentColor
            else -> MaterialTheme.colorScheme.primary
        },
        textDecoration = TextDecoration.Underline,
        modifier = modifier
    )
}

private const val SUBTITLE_MAX_LINES = 3

@Composable
@Preview
fun ToggleCheckBoxButtonPreview() {
    OneAppTheme {
        ToggleCheckBoxButton(
            isEnable = true,
            title = "Enviar a mi casa",
            subtitle = "Santa Maria 2670, Providencia, Región Metropolitana de Santiago, Chile Carrera 11 #95-37 Bogota Londres 247, Del Carmen, Ciudad de Mexico",
            modifier = Modifier
                .fillMaxWidth()
                .height(150.dp)
        ) {}
    }
}
