package sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.Spacer8

@Composable
fun OnboardingHeader(
    modifier: Modifier = Modifier,
    contentPadding: PaddingValues = PaddingValues(
        start = MaterialTheme.customDimens.dimen24,
        end = MaterialTheme.customDimens.dimen24,
        bottom = MaterialTheme.customDimens.dimen12,
    ),
    currentStep: Int,
    icons: List<Int>,
    textColor: Color = MaterialTheme.colorScheme.onSurface,
    title: String? = null,
    subtitle: String? = null
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
            .fillMaxWidth()
            .padding(contentPadding)
    ) {
        OnboardingProgressBar(
            currentStep = currentStep,
            icons = icons,
            modifier = Modifier.fillMaxWidth()
        )
        title?.let {
            Spacer8()
            Text(
                text = it,
                style = MaterialTheme.typography.titleMedium,
                color = textColor
            )
        }
        subtitle?.let {
            Spacer8()
            Text(
                textAlign = TextAlign.Center,
                text = it,
                style = MaterialTheme.typography.bodyMedium,
                color = textColor,
                maxLines = TWO_VALUE
            )
        }
    }
}

@Composable
@Preview(showBackground = true)
fun OnboardingTopProgressPreview() {
    val icons = listOf(
        R.drawable.ic_direction,
        R.drawable.ic_bio_metric,
        R.drawable.ic_savings,
        R.drawable.ic_archive,
        R.drawable.ic_aditional_card_dialog,
    )
    OneAppTheme {
        OnboardingHeader(
            currentStep = 1,
            icons = icons,
            title = "This is a title",
            subtitle = "Lorem ipsum dolor sit amet, consectetur adipiscing elit"
        )
    }
}
