package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.employment_info

sealed class EmploymentInfoOverviewUiEvent {
    data object OnStart : EmploymentInfoOverviewUiEvent()
    data object OnBackClick : EmploymentInfoOverviewUiEvent()
    data object OnSupportClick : EmploymentInfoOverviewUiEvent()
    data class OnModifyEmploymentInfo(
        val jobId: Int,
        val contactId: Int,
        val contactTypeCode: String
    ) : EmploymentInfoOverviewUiEvent()
}
