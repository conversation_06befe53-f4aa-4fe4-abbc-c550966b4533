package sv.com.tarjetaone.presentation.view.common.survey.row

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.entities.request.SurveyAnswerUI
import sv.com.tarjetaone.domain.entities.request.SurveyType
import sv.com.tarjetaone.domain.entities.response.OptionsSurveyUI
import sv.com.tarjetaone.domain.usecases.survey.GetSurveyByNameUseCase
import sv.com.tarjetaone.domain.usecases.survey.SaveSurveyUseCase
import sv.com.tarjetaone.presentation.view.common.survey.base.BaseSurveyViewModel

abstract class BaseSurveyRowViewModel(
    getSurveyByNameUseCase: GetSurveyByNameUseCase,
    saveSurveyUseCase: SaveSurveyUseCase,
) : BaseSurveyViewModel(getSurveyByNameUseCase, saveSurveyUseCase) {
    private var surveyType: SurveyType? = null
    private val _uiState = MutableStateFlow(SurveyRowTypeUiState())
    val uiState: StateFlow<SurveyRowTypeUiState> = _uiState.asStateFlow()

    abstract fun onSurveySent()

    private fun onStart(surveyType: SurveyType) {
        getSurveyByType(
            surveyType = surveyType,
            onSurveyFetched = { response ->
                _uiState.update { state ->
                    state.copy(
                        surveyOptions = response.data.questions.flatMap { it.options },
                        description = response.data.description.orEmpty()
                    )
                }
            }
        )
    }

    private fun onSendSurvey() {
        _uiState.value.selectedOption?.let { option ->
            sendSurveyByType(
                surveyType = surveyType,
                surveyAnswers = listOf(
                    SurveyAnswerUI(
                        optionId = option.id,
                        other = with(_uiState.value) {
                            additionalComments.takeIf { it.isNotEmpty() } ?: selectedOption?.value
                        },
                        surveyType = surveyType
                    )
                ),
                onSurveySent = {
                    onSurveySent()
                }
            )
        }
    }

    private fun onOptionSelected(item: OptionsSurveyUI) {
        if (item.id != _uiState.value.selectedOption?.id) {
            _uiState.update {
                it.copy(selectedOption = item, additionalComments = EMPTY_STRING)
            }
        }
    }

    private fun onAdditionalCommentsChange(text: String) {
        _uiState.update {
            it.copy(
                additionalComments = text,
                selectedOption = it.selectedOption?.copy(value = text)
            )
        }
    }

    fun onEvent(event: SurveyRowTypeUiEvent) {
        when (event) {
            is SurveyRowTypeUiEvent.OnStart -> onStart(event.surveyType)
            SurveyRowTypeUiEvent.OnSendSurvey -> onSendSurvey()
            SurveyRowTypeUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            is SurveyRowTypeUiEvent.OnAdditionalCommentsChange -> onAdditionalCommentsChange(
                event.text
            )

            is SurveyRowTypeUiEvent.OnOptionSelected -> onOptionSelected(event.option)
            SurveyRowTypeUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
        }
    }
}