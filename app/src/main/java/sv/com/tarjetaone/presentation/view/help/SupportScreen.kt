package sv.com.tarjetaone.presentation.view.help

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer4
import sv.com.tarjetaone.presentation.compose.util.Spacer64

@Composable
fun SupportScreen(viewModel: SupportViewModel) {
    SideEffectHandler(viewModel.sideEffects) {
        SupportContent(onEvent = viewModel::onEvent)
    }
}

@Composable
private fun SupportContent(onEvent: (SupportUiEvent) -> Unit = {}) {
    Column(
        modifier = Modifier
            .background(MaterialTheme.customColors.successLightContainer)
            .fillMaxSize()
            .safeDrawingPadding()
            .padding(MaterialTheme.customDimens.dimen16)
    ) {
        IconButton(
            modifier = Modifier.align(Alignment.End),
            colors = IconButtonDefaults.iconButtonColors(
                contentColor = MaterialTheme.colorScheme.onPrimary
            ),
            onClick = { onEvent(SupportUiEvent.OnCloseClick) }
        ) {
            Icon(
                imageVector = ImageVector.vectorResource(R.drawable.ic_close),
                contentDescription = null,
                modifier = Modifier.size(MaterialTheme.customDimens.dimen30)
            )
        }
        Spacer16()
        Text(
            text = stringResource(id = R.string.if_you_need_help_label),
            style = MaterialTheme.typography.titleMedium.copy(
                textAlign = TextAlign.Center
            ),
            color = MaterialTheme.colorScheme.onPrimary,
            modifier = Modifier.align(Alignment.CenterHorizontally)
        )
        Spacer64()
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(min = MaterialTheme.customDimens.dimen36)
                .padding(horizontal = MaterialTheme.customDimens.dimen16)
                .clickable(onClick = { onEvent(SupportUiEvent.OnCallClick) })
        ) {
            Icon(
                imageVector = ImageVector.vectorResource(id = R.drawable.ic_telephone_support),
                tint = MaterialTheme.colorScheme.onPrimary,
                contentDescription = null
            )
            Spacer24()
            Text(
                text = stringResource(id = R.string.phone_call_label),
                color = MaterialTheme.colorScheme.onPrimary,
                style = MaterialTheme.typography.titleMedium
            )
            Spacer4()
            Icon(
                imageVector = ImageVector.vectorResource(id = R.drawable.ic_chevron_right),
                tint = MaterialTheme.colorScheme.onPrimary,
                contentDescription = null
            )
        }
    }
}

@Preview
@Composable
private fun SupportScreenPreview() {
    SupportContent()
}