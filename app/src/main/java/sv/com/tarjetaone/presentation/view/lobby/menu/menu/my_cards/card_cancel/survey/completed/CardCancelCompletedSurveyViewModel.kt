package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_cancel.survey.completed

import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import javax.inject.Inject

@HiltViewModel
class CardCancelCompletedSurveyViewModel @Inject constructor() : BaseViewModel() {
    private fun onReturnHome() {
        sendEvent(
            UiEvent.Navigate(
                CardCancelCompletedSurveyFragmentDirections
                    .actionFinishCancelCardSurveyFragmentToNavigationHome()
            )
        )
    }

    fun onEvent(event: CardCancelCompletedSurveyUiEvent) {
        when (event) {
            CardCancelCompletedSurveyUiEvent.OnReturnHome -> onReturnHome()
            CardCancelCompletedSurveyUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
        }
    }
}