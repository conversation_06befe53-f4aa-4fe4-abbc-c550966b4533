package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.SheetState
import androidx.compose.material3.Text
import androidx.compose.material3.rememberStandardBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.core.utils.extensions.toBoolean
import sv.com.tarjetaone.domain.entities.response.InstallmentTermsUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.ToggleCheckBoxButton
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.card.HorizontalIconWithText
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.helpers.UiText

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun InstallmentSelectionBottomSheet(
    bottomSheetState: SheetState,
    installments: List<InstallmentTermsUI>,
    selectedInstallment: InstallmentTermsUI? = null,
    showLastAmountDisclaimer: Boolean,
    onDismissRequest: () -> Unit,
    onContinueClick: () -> Unit,
    onSelectInstallment: (InstallmentTermsUI) -> Unit,
) {
    CommonInstallmentBottomSheet(
        onDismissRequest = onDismissRequest,
        bottomSheetState = bottomSheetState,
    ) {
        InstallmentSelectionContent(
            installments = installments,
            selectedInstallment = selectedInstallment,
            showLastAmountDisclaimer = showLastAmountDisclaimer,
            onContinueClick = onContinueClick,
            onSelectInstallment = onSelectInstallment,
        )
    }
}

@Composable
private fun InstallmentSelectionContent(
    modifier: Modifier = Modifier,
    installments: List<InstallmentTermsUI>,
    selectedInstallment: InstallmentTermsUI? = null,
    showLastAmountDisclaimer: Boolean,
    onContinueClick: () -> Unit,
    onSelectInstallment: (InstallmentTermsUI) -> Unit,
) {
    LazyColumn(
        modifier = modifier.fillMaxWidth(),
        contentPadding = PaddingValues(
            horizontal = MaterialTheme.customDimens.dimen16,
        )
    ) {
        item {
            InstallmentSelectionTitle()
            Spacer24()
        }

        items(installments) { installment ->
            InstallmentOption(
                modifier = Modifier.heightIn(min = MaterialTheme.customDimens.dimen66),
                installment = installment,
                isSelected = installment == selectedInstallment,
                onSelectInstallment = onSelectInstallment,
            )
            Spacer8()
        }

        item {
            InstalmentSelectionBottom(
                showLastAmountDisclaimer = showLastAmountDisclaimer,
                isButtonEnabled = selectedInstallment != null,
                onContinueClick = onContinueClick,
            )
            Spacer16()
        }
    }
}

@Composable
private fun InstallmentSelectionTitle() {
    Text(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.customDimens.dimen24),
        text = stringResource(id = R.string.select_installment_term),
        style = MaterialTheme.typography.bodyMedium.copy(
            color = MaterialTheme.colorScheme.secondary,
        ),
        textAlign = TextAlign.Center,
    )
}

@Composable
private fun InstallmentOption(
    modifier: Modifier = Modifier,
    installment: InstallmentTermsUI,
    isSelected: Boolean,
    onSelectInstallment: (InstallmentTermsUI) -> Unit,
) {
    ToggleCheckBoxButton(
        modifier = modifier,
        isEnable = true,
        elevation = CardDefaults.cardElevation(
            defaultElevation = MaterialTheme.customDimens.dimen1,
        ),
        selected = isSelected,
        title = installment.description.orEmpty(),
        subtitle = EMPTY_STRING,
        onSelectChange = { onSelectInstallment(installment) },
    )
}

@Composable
private fun InstalmentSelectionBottom(
    showLastAmountDisclaimer: Boolean,
    isButtonEnabled: Boolean,
    onContinueClick: () -> Unit,
) {
    if (showLastAmountDisclaimer) {
        Column(modifier = Modifier.fillMaxWidth()) {
            HorizontalIconWithText(
                modifier = Modifier.align(Alignment.CenterHorizontally),
                leadingIcon = R.drawable.ic_info_outline,
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                iconTextSpacing = MaterialTheme.customDimens.dimen8,
                text = UiText.StringResource(res = R.string.variable_last_installment),
                textColor = MaterialTheme.colorScheme.onSurfaceVariant,
                style = MaterialTheme.typography.labelMedium.copy(
                    lineHeight = MaterialTheme.customDimensSp.sp14,
                )
            )
            Spacer24()
        }
    }
    OneButton(
        modifier = Modifier.fillMaxWidth(),
        enabled = isButtonEnabled,
        text = stringResource(id = R.string.continue_button_label),
        onClick = onContinueClick
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Preview
@Composable
private fun InstallmentBottomSheetPreview() {
    val installments = listOf(
        InstallmentTermsUI(
            installmentNumbers = 3,
            amount = 100.0,
            isLastInstallmentAdjusted = 1,
            description = "3 meses | $500",
        ),
        InstallmentTermsUI(
            installmentNumbers = 6,
            amount = 90.0,
            isLastInstallmentAdjusted = 1,
            description = "6 meses | $250",
        )
    )
    OneAppTheme {
        InstallmentSelectionBottomSheet(
            bottomSheetState = rememberStandardBottomSheetState(),
            installments = installments,
            selectedInstallment = installments.firstOrNull(),
            showLastAmountDisclaimer = installments.any { it.isLastInstallmentAdjusted.toBoolean() },
            onDismissRequest = { },
            onContinueClick = { },
            onSelectInstallment = { },
        )
    }
}
