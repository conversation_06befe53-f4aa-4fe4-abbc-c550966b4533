package sv.com.tarjetaone.presentation.view.common.survey.row

import sv.com.tarjetaone.domain.entities.request.SurveyType
import sv.com.tarjetaone.domain.entities.response.OptionsSurveyUI

sealed class SurveyRowTypeUiEvent {
    data class OnStart(val surveyType: SurveyType) : SurveyRowTypeUiEvent()
    data object OnBackClick : SurveyRowTypeUiEvent()
    data object OnSendSurvey : SurveyRowTypeUiEvent()
    data class OnAdditionalCommentsChange(val text: String) : SurveyRowTypeUiEvent()
    data class OnOptionSelected(val option: OptionsSurveyUI) : SurveyRowTypeUiEvent()
    data object OnTwilioClick : SurveyRowTypeUiEvent()
}