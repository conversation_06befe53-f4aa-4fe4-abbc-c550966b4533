package sv.com.tarjetaone.presentation.view.lobby.menu.home.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.SheetState
import androidx.compose.material3.SheetValue
import androidx.compose.material3.Text
import androidx.compose.material3.rememberStandardBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SheetDragHandle
import sv.com.tarjetaone.presentation.compose.util.Spacer12
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer40
import sv.com.tarjetaone.presentation.compose.util.Spacer8

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChargeOffInfoBottomSheet(
    sheetState: SheetState,
    onDismissRequest: () -> Unit,
    onUnderstoodClick: () -> Unit
) {
    ModalBottomSheet(
        onDismissRequest = { onDismissRequest() },
        sheetState = sheetState,
        dragHandle = { SheetDragHandle() },
        containerColor = MaterialTheme.colorScheme.background
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.customDimens.dimen16),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer8()
            IconButton(
                modifier = Modifier
                    .size(MaterialTheme.customDimens.dimen24)
                    .align(Alignment.End),
                onClick = { onDismissRequest() }
            ) {
                Icon(
                    modifier = Modifier
                        .align(Alignment.CenterHorizontally)
                        .size(MaterialTheme.customDimens.dimen24),
                    imageVector = ImageVector.vectorResource(id = R.drawable.ic_close),
                    tint = MaterialTheme.colorScheme.secondary,
                    contentDescription = null
                )
            }
            Spacer24()
            Image(
                imageVector = ImageVector.vectorResource(id = R.drawable.ic_one_card_circle),
                contentDescription = null,
            )
            Spacer8()
            Text(
                text = buildAnnotatedString {
                    append(stringResource(id = R.string.charge_off_empty_state_contract_cancelled_label))
                    withStyle(
                        style = SpanStyle(
                            color = MaterialTheme.colorScheme.primary,
                        )
                    ) {
                        append(stringResource(id = R.string.charge_off_empty_state_overdue_payments))
                    }
                },
                style = MaterialTheme.typography.displaySmall.copy(
                    color = MaterialTheme.colorScheme.secondary,
                    textAlign = TextAlign.Center,
                )
            )
            Spacer24()
            Text(
                text = buildAnnotatedString {
                    append(stringResource(id = R.string.charge_off_reminder_label))
                    withStyle(
                        style = SpanStyle(
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.secondary,
                        )
                    ) {
                        append(stringResource(id = R.string.charge_off_pending_balance_label))
                    }
                },
                style = MaterialTheme.typography.bodySmall.copy(
                    color = MaterialTheme.colorScheme.onBackground,
                    textAlign = TextAlign.Center,
                )
            )
            Text(
                text = stringResource(id = R.string.charge_off_payment_instructions),
                style = MaterialTheme.typography.bodySmall.copy(
                    color = MaterialTheme.colorScheme.onBackground,
                    textAlign = TextAlign.Center,
                )
            )
            Spacer40()
            Text(
                text = stringResource(id = R.string.more_info_contact),
                style = MaterialTheme.typography.labelMedium.copy(
                    color = MaterialTheme.colorScheme.onBackground,
                    textAlign = TextAlign.Center,
                )
            )
            Spacer12()
            OneButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = MaterialTheme.customDimens.dimen12),
                text = stringResource(id = R.string.understood),
                onClick = { onUnderstoodClick() },
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Preview
@Composable
private fun ChargeOffInfoBottomSheetPreview() {
    OneAppTheme {
        ChargeOffInfoBottomSheet(
            sheetState = rememberStandardBottomSheetState(
                initialValue = SheetValue.Expanded,
            ),
            onDismissRequest = {},
            onUnderstoodClick = {}
        )
    }
}
