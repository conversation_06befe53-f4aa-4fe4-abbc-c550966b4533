package sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.common.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.ButtonSize
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.util.Spacer16

@Composable
fun ProgramProtectionEmptyState(
    modifier: Modifier = Modifier,
    onActionButtonClick: () -> Unit,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
    ) {
        Image(
            modifier = Modifier.size(MaterialTheme.customDimens.dimen98),
            imageVector = ImageVector.vectorResource(id = R.drawable.ic_circle_one_icon),
            contentDescription = null,
        )
        Spacer16()
        Text(
            text = stringResource(id = R.string.unable_to_load_content),
            style = MaterialTheme.typography.bodyLarge.copy(
                color = MaterialTheme.customColors.gray700,
                fontWeight = FontWeight.SemiBold,
                textAlign = TextAlign.Center,
            )
        )
        Spacer16()
        Text(
            text = stringResource(id = R.string.content_not_loaded_as_expected),
            style = MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.customColors.gray700,
                textAlign = TextAlign.Center,
            )
        )
        Spacer16()
        OneButton(
            text = stringResource(id = R.string.ok_message),
            onClick = onActionButtonClick,
            size = ButtonSize.MEDIUM,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun ProgramProtectionEmptyStatePreview() {
    OneAppTheme {
        ProgramProtectionEmptyState(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = MaterialTheme.customDimens.dimen16),
            onActionButtonClick = { }
        )
    }
}
