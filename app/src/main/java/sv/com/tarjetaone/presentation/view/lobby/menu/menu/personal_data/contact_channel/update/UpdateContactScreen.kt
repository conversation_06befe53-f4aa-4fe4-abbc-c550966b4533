package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.contact_channel.update

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.platform.SoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.response.ContactType
import sv.com.tarjetaone.domain.entities.response.OtpMethod
import sv.com.tarjetaone.presentation.compose.modifier.conditional
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.PhoneTextField
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.DialogConfirmation
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.helpers.UiText

@Composable
fun UpdateContactScreen(viewModel: UpdateContactViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    UpdateContactContent(uiState = uiState, onEvent = viewModel::onEvent)
}

@Composable
fun UpdateContactContent(
    uiState: UpdateContactUiState,
    onEvent: (UpdateContactUiEvent) -> Unit
) {
    LaunchedEffect(Unit) {
        onEvent(UpdateContactUiEvent.OnStart)
    }

    val title = if (uiState.contactType == ContactType.Email) {
        R.string.email_address
    } else {
        R.string.phone_number
    }

    ScreenWithTopAppBar(
        onLeftButtonClick = { },
        onRightButtonClick = { onEvent(UpdateContactUiEvent.OnTwilioClick) },
        isLeftButtonVisible = false,
        title = stringResource(title)
    ) {
        val keyboardController = LocalSoftwareKeyboardController.current
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = MaterialTheme.customDimens.dimen50)
        ) {
            if (uiState.showOtpMethodDialog) {
                DialogConfirmation(
                    message = UiText.StringResource(R.string.otp_channel_string),
                    primaryButtonText = UiText.StringResource(R.string.otp_whatsapp),
                    secondaryButtonText = UiText.StringResource(R.string.otp_sms),
                    onPositiveClick = {
                        onEvent(UpdateContactUiEvent.OnSelectOtpMethod(OtpMethod.WHATSAPP))
                    },
                    onNegativeClick = {
                        onEvent(UpdateContactUiEvent.OnSelectOtpMethod(OtpMethod.SMS))
                    },
                    onDismissRequest = { onEvent(UpdateContactUiEvent.OnDismissSelectOtpMethod) }
                )
            }
            Spacer32()
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .conditional(
                        uiState.contactType == ContactType.Email,
                        onTrue = { height(MaterialTheme.customDimens.dimen120) },
                        onFalse = { height(MaterialTheme.customDimens.dimen72) }
                    ),
                contentAlignment = Alignment.BottomStart
            ) {
                uiState.errorMessage?.let {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = MaterialTheme.customDimens.dimen8),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_exclamation_outline),
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.error
                        )
                        Spacer8()
                        Text(
                            text = it.asString(),
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }
            ContactTypeTextField(
                uiState = uiState,
                onEvent = onEvent,
                keyboardController = keyboardController
            )
            Spacer24()
            OneButton(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(id = R.string.continue_button_label),
                onClick = {
                    keyboardController?.hide()
                    onEvent(UpdateContactUiEvent.OnContinueClick)
                },
                enabled = uiState.shouldEnableContinueButton()
            )
        }
    }
}

@Composable
private fun ContactTypeTextField(
    uiState: UpdateContactUiState,
    onEvent: (UpdateContactUiEvent) -> Unit,
    keyboardController: SoftwareKeyboardController? = null
) {
    if (uiState.contactType == ContactType.Email) {
        SimpleElevatedTextField(
            value = uiState.contact,
            onValueChange = { onEvent(UpdateContactUiEvent.OnEmailChange(it)) },
            label = stringResource(id = R.string.type_your_email_address),
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Email,
                imeAction = ImeAction.Done
            ),
            keyboardActions = KeyboardActions(
                onDone = { keyboardController?.hide() }
            ),
            textFieldAlignment = Alignment.Center,
            modifier = Modifier.fillMaxWidth()
        )
    } else {
        PhoneTextField(
            value = uiState.contact,
            onValueChange = { onEvent(UpdateContactUiEvent.OnPhoneChange(it)) },
            label = stringResource(id = R.string.type_phone_number_label),
            hasError = uiState.hasError(),
            error = uiState.contactError?.asString(),
            modifier = Modifier.fillMaxWidth(),
            trailingIcon = when {
                uiState.isValidatingContact -> {
                    {
                        CircularProgressIndicator(
                            modifier = Modifier.size(MaterialTheme.customDimens.dimen12),
                            strokeWidth = MaterialTheme.customDimens.dimen2
                        )
                    }
                }

                else -> null
            }
        )
    }
}

@Preview
@Composable
private fun UpdateContactScreenPreview() {
    OneAppTheme {
        UpdateContactContent(
            uiState = UpdateContactUiState(
                contactType = ContactType.Email,
                errorMessage = UiText.DynamicString("This is an error message")
            ),
            onEvent = {}
        )
    }
}
