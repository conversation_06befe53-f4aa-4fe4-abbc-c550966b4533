package sv.com.tarjetaone.presentation.compose.uicomponent.button

import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import sv.com.tarjetaone.presentation.compose.theme.LocalCustomColors
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.poppinsFamily
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.getContainerColor
import sv.com.tarjetaone.presentation.compose.util.getContentColor

@Composable
fun SolidRoundedSmallButton(
    modifier: Modifier = Modifier,
    text: String,
    enabled: Boolean = true,
    buttonVariant: ButtonVariant = ButtonVariant.PRIMARY_VARIANT,
    textStyle: TextStyle = TextStyle(
        fontWeight = FontWeight.SemiBold,
        fontFamily = poppinsFamily,
        fontSize = 14.sp
    ),
    onClick: () -> Unit,
) {
    val customColors = LocalCustomColors.current
    Button(
        modifier = modifier.size(width = 140.dp, height = 40.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = buttonVariant.getContainerColor(),
            contentColor = buttonVariant.getContentColor(),
            disabledContainerColor = customColors.gray300,
            disabledContentColor = customColors.gray600,
        ),
        enabled = enabled,
        shape = RoundedCornerShape(6.dp),
        onClick = onClick
    ) {
        Text(
            text = text,
            modifier = Modifier.align(Alignment.CenterVertically),
            style = textStyle
        )
    }
}

@Preview
@Composable
private fun SolidRoundedButtonSmallPreview() {
    OneAppTheme {
        Surface {
            SolidRoundedSmallButton(
                modifier = Modifier.padding(32.dp),
                text = "Button",
                onClick = {}
            )
        }
    }
}

@Preview
@Composable
private fun SolidRoundedButtonSmallSecondaryPreview() {
    OneAppTheme {
        Surface {
            SolidRoundedSmallButton(
                modifier = Modifier.padding(32.dp),
                text = "Button",
                buttonVariant = ButtonVariant.SECONDARY_VARIANT,
                onClick = {}
            )
        }
    }
}