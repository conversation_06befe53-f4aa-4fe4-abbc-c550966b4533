package sv.com.tarjetaone.presentation.compose.uicomponent.card

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Card
import androidx.compose.material3.CardColors
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CardElevation
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.LocalCustomColors
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.helpers.UiText

@Suppress("LongParameterList")
@Composable
fun SimpleCardComponent(
    modifier: Modifier = Modifier,
    border: BorderStroke? = null,
    shape: Shape = MaterialTheme.shapes.large,
    cardColors: CardColors = CardDefaults.cardColors(
        containerColor = MaterialTheme.colorScheme.background
    ),
    elevation: CardElevation = CardDefaults.cardElevation(
        defaultElevation = MaterialTheme.customDimens.dimen8
    ),
    contentPadding: PaddingValues = PaddingValues(MaterialTheme.customDimens.dimen24),
    content: @Composable () -> Unit,
) {
    Card(
        modifier = modifier,
        elevation = elevation,
        border = border,
        shape = shape,
        colors = cardColors
    ) {
        Column(
            modifier = Modifier.padding(contentPadding)
        ) {
            content()
        }
    }
}

@Preview
@Composable
fun SimpleCardComponentPreview() {
    OneAppTheme {
        SimpleCardComponent {
            Column(
                verticalArrangement = Arrangement.spacedBy(
                    MaterialTheme.customDimens.dimen32,
                    Alignment.CenterVertically
                )
            ) {
                HorizontalIconWithText(
                    leadingIcon = R.drawable.ic_check_circle_white,
                    text = UiText.DynamicString("This is the first card component"),
                    tint = LocalCustomColors.current.successContainer
                )
                HorizontalIconWithText(
                    leadingIcon = R.drawable.ic_check_circle_white,
                    text = UiText.DynamicString("This is the second card component"),
                    tint = LocalCustomColors.current.successContainer
                )
                HorizontalIconWithText(
                    leadingIcon = R.drawable.ic_check_circle_white,
                    text = UiText.DynamicString("This is the thrid card component"),
                    tint = LocalCustomColors.current.successContainer
                )
            }
        }
    }
}
