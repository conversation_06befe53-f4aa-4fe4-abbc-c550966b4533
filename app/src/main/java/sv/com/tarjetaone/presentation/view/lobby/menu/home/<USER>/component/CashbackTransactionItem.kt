package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.component

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.extensions.configCurrencyWithFractions
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.response.CashbackTUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.Spacer16

@Composable
fun CashbackTransactionItem(
    modifier: Modifier = Modifier,
    transaction: CashbackTUI
) {
    ElevatedCard(
        modifier = modifier,
        colors = CardDefaults.elevatedCardColors(
            containerColor = MaterialTheme.customColors.defaultSurface,
            contentColor = MaterialTheme.colorScheme.secondary
        )
    ) {
        Row(
            modifier = Modifier.padding(MaterialTheme.customDimens.dimen16)
        ) {
            Text(
                text = transaction.getDateFormatted(),
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.SemiBold
                ),
                modifier = Modifier.weight(1f)
            )
            Spacer16()
            Text(
                text = stringResource(
                    id = R.string.positive_transaction,
                    transaction.cashback.orZero().configCurrencyWithFractions()
                ),
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.customColors.successContainer
                )
            )
        }
    }
}

@Preview
@Composable
private fun CashbackTransactionItemPreview() {
    OneAppTheme {
        CashbackTransactionItem(
            transaction = CashbackTUI(
                type = "D",
                cashback = 2.25,
                valueDate = "08/03/2023",
                description = "Cashback",
                paymentAmount = 2.25
            )
        )
    }
}
