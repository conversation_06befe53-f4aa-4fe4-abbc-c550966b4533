package sv.com.tarjetaone.presentation.view.common.selfiepreview

import com.facephi.fphiwidgetcore.WidgetResult

sealed class SelfiePreviewUiEvent {
    data object OnBack: SelfiePreviewUiEvent()
    data object OnTwilioChat: SelfiePreviewUiEvent()
    data object OnStart: SelfiePreviewUiEvent()
    data object OnContinueClick: SelfiePreviewUiEvent()
    data object OnTakeNewSelfieClick: SelfiePreviewUiEvent()
    data class OnCaptureSelfie(
        val result: WidgetResult
    ): SelfiePreviewUiEvent()
}
