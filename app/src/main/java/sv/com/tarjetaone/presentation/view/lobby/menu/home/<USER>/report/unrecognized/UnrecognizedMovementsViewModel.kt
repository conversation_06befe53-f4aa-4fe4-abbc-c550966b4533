package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.report.unrecognized

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_FLOAT_VALUE
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.CommonDialogWithIconParams
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.entities.response.UnrecognizedTransactionUI
import sv.com.tarjetaone.domain.usecases.purchase.ReportPurchaseListUseCase
import sv.com.tarjetaone.presentation.helpers.UiText
import javax.inject.Inject

@HiltViewModel
class UnrecognizedMovementsViewModel @Inject constructor(
    private val getUnrecognizedPurchaseReportUseCase: ReportPurchaseListUseCase,
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {
    private val args = UnrecognizedMovementFragmentArgs.fromSavedStateHandle(savedStateHandle)
    private val _uiState = MutableStateFlow(UnrecognizedMovementsUIState())
    val uiState = _uiState.asStateFlow()
    val selectedItems: SnapshotStateList<UnrecognizedTransactionUI> = mutableStateListOf()

    private fun onStart() {
        setUiState()
        getUnrecognizedPurchases()
    }

    private fun setUiState() {
        _uiState.update { state ->
            state.copy(
                cardStatusId = args.cardStatusId,
                maskedCard = args.address.panMasked.orEmpty(),
                selectedUnrecognizedTransactionUI = UnrecognizedTransactionUI(
                    referenceNumber = args.address.referenceNumber.orEmpty(),
                    authorizationNumber = args.address.authorizationCode.orEmpty(),
                    valueDate = args.address.valueDate.orEmpty(),
                    amount = args.address.amount?.toFloat() ?: ZERO_FLOAT_VALUE,
                    description = args.address.description.orEmpty(),
                    isFavorite = null
                ),
                cCardId = args.cardId,
                referenceNumber = args.address.referenceNumber.orEmpty(),
                authorizationCode = args.address.authorizationCode.orEmpty(),
                valueDate = args.address.valueDate.orEmpty(),
                amount = args.address.amount?.toFloat() ?: ZERO_FLOAT_VALUE,
                description = args.address.description.orEmpty()
            )
        }
    }

    private fun getUnrecognizedPurchases() {
        _uiState.update { state ->
            state.copy(
                unrecognizedTransactionUIPaging = getUnrecognizedPurchaseReportUseCase(
                    cCardId = state.cCardId,
                    referenceNumber = state.referenceNumber,
                    authorizationCode = state.authorizationCode,
                    valueDate = state.valueDate,
                    amount = state.amount,
                    description = state.description,
                    pageNumber = state.pageNumber,
                    pageSize = state.pageSize
                )
            )
        }
    }

    fun onContinueClick() {
        showWarningDialog()
    }

    private fun showWarningDialog() {
        sendEvent(
            UiEvent.ShowCommonDialog(
                CommonDialogWithIconParams(
                    icon = R.drawable.ic_red_warning,
                    title = UiText.StringResource(R.string.block_card_title_dialog),
                    message = UiText.StringResource(R.string.block_card_subtitle_dialog),
                    buttonText = UiText.StringResource(R.string.block_card_accept_dialog),
                    secondaryActionVisible = true,
                    secondaryActionText = UiText.StringResource(R.string.block_card_reject_dialog),
                    onButtonClick = {
                        navigateToPrepareIdentity()
                    }
                )
            )
        )
    }

    private fun navigateToPrepareIdentity() {
        val transactions = _uiState.value.selectedUnrecognizedTransactionUI?.let {
            selectedItems + it
        } ?: return
        sendEvent(
            UiEvent.Navigate(
                UnrecognizedMovementFragmentDirections.actionReportAndBlockFragmentToReportPrepareIdentityFragment(
                    cardStatusId = _uiState.value.cardStatusId,
                    cardId = _uiState.value.cCardId,
                    reportableTransactions = transactions.toTypedArray()
                )
            )
        )
    }

    private fun onTransactionClick(transaction: UnrecognizedTransactionUI) {
        transaction.isFavorite?.let {
            transaction.isFavorite = !it
        }
        if (transaction.isFavorite == true) {
            selectedItems.add(transaction)
        } else {
            selectedItems.removeAll { it == transaction }
        }
    }

    fun onTwilioClick() {
        sendEvent(UiEvent.TwilioClick)
    }

    private fun showLoadingError() {
        onDefaultNetworkError()
    }

    fun onEvent(event: UnrecognizedMovementUiEvent) {
        when (event) {
            UnrecognizedMovementUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            UnrecognizedMovementUiEvent.OnContinueClick -> onContinueClick()
            UnrecognizedMovementUiEvent.OnSupportClick -> onTwilioClick()
            UnrecognizedMovementUiEvent.OnLoadingError -> showLoadingError()
            is UnrecognizedMovementUiEvent.OnTransactionClick -> {
                onTransactionClick(event.transaction)
            }

            UnrecognizedMovementUiEvent.OnStart -> onStart()
        }
    }
}
