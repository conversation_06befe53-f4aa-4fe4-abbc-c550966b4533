package sv.com.tarjetaone.presentation.view.common.contracts

import android.net.Uri
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.domain.entities.response.DataContractUI
import sv.com.tarjetaone.presentation.helpers.UiText

data class ContractListUiState(
    val contracts: List<DataContractUI> = emptyList(),
    val contractIdSelected: Int = AppConstants.ZERO_VALUE,
    val areDocumentsRead: Boolean = false,
    val isBackEnabled: Boolean = false,
    val primaryButtonText: UiText = UiText.StringResource(R.string.sign_contracts),
    val documentUri: Uri? = null,
    val documentName: String = EMPTY_STRING,
    val isContractsDataLoaded: Boolean = false
) {
    val areDocumentChecked = areDocumentsRead && contracts.all { it.checked }
}
