package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.menu.MenuList
import sv.com.tarjetaone.presentation.compose.util.StaticMenuItem

@Composable
fun PersonalDataMenuScreen(viewModel: PersonalDataMenuViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    PersonalDataMenuScreen(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@Composable
fun PersonalDataMenuScreen(
    uiState: PersonalDataMenuUiState,
    onEvent: (PersonalDataMenuUiEvent) -> Unit
) {
    ScreenWithTopAppBar(
        title = stringResource(R.string.my_personal_data),
        onLeftButtonClick = { onEvent(PersonalDataMenuUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(PersonalDataMenuUiEvent.OnSupportClick) },
    ) {
        MenuList(
            items = uiState.items,
            itemIcon = { it.icon },
            itemText = { stringResource(id = it.label) },
            displayItemArrow = { it.displayArrow },
            onItemClick = { onEvent(PersonalDataMenuUiEvent.OnItemClick(it)) },
            modifier = Modifier.fillMaxSize()
        )
    }
}

@Preview
@Composable
private fun PersonalDataMenuScreenPreview() {
    OneAppTheme {
        PersonalDataMenuScreen(
            uiState = PersonalDataMenuUiState(
                items = listOf(
                    StaticMenuItem(
                        icon = R.drawable.ic_people_circle,
                        label = R.string.general_data,
                    ),
                    StaticMenuItem(
                        icon = R.drawable.ic_house_new,
                        label = R.string.address_location,
                    ),
                ),
            ),
            onEvent = {}
        )
    }
}
