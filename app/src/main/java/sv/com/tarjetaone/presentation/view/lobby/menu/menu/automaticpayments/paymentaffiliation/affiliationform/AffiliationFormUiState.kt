package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform

import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.ONE_HUNDRED_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.request.automaticpayments.paymentaffiliation.contract.AffiliationContractActionType
import sv.com.tarjetaone.domain.entities.request.automaticpayments.paymentaffiliation.contract.AffiliationContractRequestUI
import sv.com.tarjetaone.domain.entities.request.automaticpayments.paymentaffiliation.contract.ContractIdentifierUI
import sv.com.tarjetaone.domain.entities.request.automaticpayments.paymentaffiliation.contract.ContractParameterUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.affiliationform.NotificationChannelUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.contractslist.RecurringChargeContractUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.invoiceselection.infoproducts.PXPaymentMethodUI
import sv.com.tarjetaone.domain.utils.automaticpayments.servicespayment.invoiceselection.ProductTypeCode
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform.PaymentAffiliationConstants.COMPANY_CODE
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform.PaymentAffiliationConstants.CONTRACT_ACCEPTED_DATE_SUGGESTED
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform.PaymentAffiliationConstants.CONTRACT_ACCEPTED_SOURCE_SUGGESTED
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform.PaymentAffiliationConstants.CONTRACT_ACCEPTED_SYSTEM_VALUE
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform.PaymentAffiliationConstants.CONTRACT_BANK_CODE
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform.PaymentAffiliationConstants.CONTRACT_FREQUENCY_CODE
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform.PaymentAffiliationConstants.IDENTIFIER_LINE
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform.PaymentAffiliationConstants.PARAMETER_LINE
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform.PaymentAffiliationConstants.PARAMETER_TYPE
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform.PaymentAffiliationConstants.SERVICE_TYPE_CODE
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.invoiceselection.util.InvoicePXFieldUiState

data class AffiliationFormUiState(
    val serviceName: String = EMPTY_STRING,
    val serviceCategoryName: String? = null,
    val formFields: List<InvoicePXFieldUiState>? = null,
    val maxChargeAmount: String = EMPTY_STRING,
    val isMaxAmountEnabled: Boolean? = null,
    val notificationChannels: List<NotificationChannelUI> = emptyList(),
    val selectedNotificationChannel: NotificationChannelUI? = null,
    val userProducts: List<PXPaymentMethodUI> = emptyList(),
    val userProductsTypes: List<ProductTypeCode> = emptyList(),
    val userProductsFiltered: List<PXPaymentMethodUI> = emptyList(),
    val selectedProduct: PXPaymentMethodUI? = null,
    val selectedProductType: ProductTypeCode? = null,
    val acceptedTermsAndConditions: Boolean = false,
    val showTermsAndConditions: Boolean = true,
    val catalogServiceCode: String = EMPTY_STRING,
    val totalPayment: Int = ZERO_VALUE,
    val identifierType: String = EMPTY_STRING,
    val referenceNumber: String = EMPTY_STRING,
) {
    val isValidForm: Boolean
        get() = (isMaxAmountEnabled == false || maxChargeAmount.isNotEmpty()) &&
                selectedNotificationChannel != null &&
                selectedProduct != null &&
                (!showTermsAndConditions || acceptedTermsAndConditions)

    private fun getContractProduct() = if (selectedProductType == ProductTypeCode.CreditCards)
        selectedProduct?.contractTypeID.orEmpty()
    else selectedProduct?.productCode.orEmpty()

    fun getSelectedPaymentMethod(): String? {
        return if (selectedProduct != null && selectedProductType != null) {
            "${selectedProduct.productType.orEmpty()} - ${selectedProduct.productCode.orEmpty()}"
        } else null
    }

    fun getMaxAmount(): Double = maxChargeAmount
        .toDoubleOrNull()
        ?.div(ONE_HUNDRED_VALUE)
        ?.takeIf { isMaxAmountEnabled == true }
        .orZero()

    fun toCreateAffiliationContractRequestUI(dui: String) = AffiliationContractRequestUI(
        action = AffiliationContractActionType.Create,
        companyCode = COMPANY_CODE,
        serviceTypeCode = SERVICE_TYPE_CODE,
        serviceCode = catalogServiceCode,
        clientCode = dui,
        clientIdentificationType = AppConstants.DUI,
        clientId = dui,
        contractFrequency = CONTRACT_FREQUENCY_CODE,
        contractCurrency = AppConstants.USD,
        contractBank = CONTRACT_BANK_CODE,
        contractInitialDate = EMPTY_STRING,
        contractProductType = selectedProductType?.value.orEmpty(),
        contractProduct = getContractProduct(),
        productCurrency = AppConstants.USD,
        applicationAmount = totalPayment,
        maxAmount = getMaxAmount(),
        acceptSystemValue = CONTRACT_ACCEPTED_SYSTEM_VALUE,
        acceptSuggestedDate = CONTRACT_ACCEPTED_DATE_SUGGESTED,
        acceptSpecificSource = CONTRACT_ACCEPTED_SOURCE_SUGGESTED,
        commentary = EMPTY_STRING,
        contractAlias = EMPTY_STRING,
        identifiers = listOf(
            ContractIdentifierUI(
                identifierLine = IDENTIFIER_LINE,
                identifierType = identifierType,
                identifierValue = referenceNumber,
            )
        ),
        parameters = listOf(
            ContractParameterUI(
                parameterLine = PARAMETER_LINE,
                parameterType = PARAMETER_TYPE,
                parameterValue = selectedNotificationChannel?.code.orEmpty(),
            )
        )
    )

    fun toUpdateAffiliationContractRequestUI(contract: RecurringChargeContractUI) =
        AffiliationContractRequestUI(
            action = AffiliationContractActionType.Update,
            contractNumber = contract.contractNumber,
            contractFrequency = contract.contractFrequency,
            contractCurrency = contract.contractCurrency,
            contractBank = contract.contractBank,
            contractProductType = selectedProductType?.value.orEmpty(),
            contractProduct = getContractProduct(),
            productCurrency = contract.productCurrency,
            applicationAmount = contract.applicationAmount.toIntOrNull().orZero(),
            maxAmount = getMaxAmount(),
            acceptSystemValue = CONTRACT_ACCEPTED_SYSTEM_VALUE, // Always use this value
            commentary = contract.commentary,
            contractAlias = contract.alias,
            contractStatus = contract.status?.value.orEmpty(),
            identifiers = null,
            parameters = listOf(
                ContractParameterUI(
                    parameterLine = PARAMETER_LINE, // Always use this value
                    parameterType = PARAMETER_TYPE, // Always use this value
                    parameterValue = selectedNotificationChannel?.code.orEmpty(),
                )
            )
        )
}
