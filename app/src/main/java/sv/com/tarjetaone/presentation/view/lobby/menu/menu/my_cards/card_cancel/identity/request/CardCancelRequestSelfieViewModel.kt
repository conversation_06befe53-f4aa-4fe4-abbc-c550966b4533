package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_cancel.identity.request

import android.graphics.Bitmap
import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.presentation.view.common.selfiecapture.SelfieCaptureBaseViewModel
import javax.inject.Inject


@HiltViewModel
class CardCancelRequestSelfieViewModel @Inject constructor(
    facephiResultHandler: FacephiResultHandler,
    savedStateHandle: SavedStateHandle
) : SelfieCaptureBaseViewModel(facephiResultHandler) {
    private val args = CardCancelRequestSelfieFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onSuccessfulSelfieCapture(selfie: Bitmap) {
        sendEvent(
            UiEvent.Navigate(
                CardCancelRequestSelfieFragmentDirections
                    .actionCardCancelPreparePictureFragmentToCardCancelPreviewSelfieFragment(
                        card = args.card,
                        reason = args.reason,
                        comments = args.comments
                    )
            )
        )
    }
}