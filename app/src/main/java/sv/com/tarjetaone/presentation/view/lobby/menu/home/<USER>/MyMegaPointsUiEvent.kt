package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints

import sv.com.tarjetaone.domain.entities.response.PointStatementUI

sealed class MyMegaPointsUiEvent {
    data object OnStart : MyMegaPointsUiEvent()
    data object OnBackPressed : MyMegaPointsUiEvent()
    data object OnSupportClick : MyMegaPointsUiEvent()
    data class OnPointStatementSelected(val pointStatement: PointStatementUI) :
        MyMegaPointsUiEvent()

    data class OnFilterStatusChange(val status: MegaPointsStatus) : MyMegaPointsUiEvent()
    data class OnSearchQueryChange(val query: String) : MyMegaPointsUiEvent()
    data object OnPerformSearchQuery : MyMegaPointsUiEvent()
    data class OnTransactionLoadError(val onRetry: () -> Unit) : MyMegaPointsUiEvent()
}
