package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.paymentdetail

import android.graphics.Bitmap

sealed class PxPaymentServiceDetailUiEvent {
    data object OnStart : PxPaymentServiceDetailUiEvent()
    data object OnTwilioClick : PxPaymentServiceDetailUiEvent()
    data object OnContinueClick : PxPaymentServiceDetailUiEvent()
    data class OnStoragePermissionGranted(val bitmap: Bitmap?) : PxPaymentServiceDetailUiEvent()
    data class OnStoragePermissionDenied(val showRationale: Boolean) : PxPaymentServiceDetailUiEvent()
    data class OnShareVoucherClick(
        val requestPermissionCallback: () -> Unit
    ) : PxPaymentServiceDetailUiEvent()
    data object OnHideComponentBeforeCapture : PxPaymentServiceDetailUiEvent()
}
