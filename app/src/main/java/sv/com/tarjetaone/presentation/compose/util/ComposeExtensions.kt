package sv.com.tarjetaone.presentation.compose.util

import android.Manifest
import android.content.Context
import android.graphics.Bitmap
import android.view.View
import androidx.compose.foundation.background
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.platform.LocalContext
import androidx.core.graphics.toColorInt
import androidx.core.view.drawToBitmap
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.PermissionState
import com.google.accompanist.permissions.rememberPermissionState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import sv.com.tarjetaone.common.utils.AppConstants.HUNDRED_MILLIS
import sv.com.tarjetaone.core.utils.delayThenExecute
import sv.com.tarjetaone.core.utils.shouldRequestStoragePermission
import sv.com.tarjetaone.core.utils.shouldShowRationale
import sv.com.tarjetaone.presentation.compose.theme.LocalCustomColors
import sv.com.tarjetaone.presentation.compose.theme.customColors

/**
 * Will determine the container color of a button (background) depending on its variant. E.g. Primary, Secondary, etc.
 */
@Composable
fun ButtonVariant.getContainerColor(): Color {
    val customColors = LocalCustomColors.current
    return when (this) {
        ButtonVariant.PRIMARY_VARIANT -> MaterialTheme.colorScheme.primary
        ButtonVariant.SECONDARY_VARIANT -> customColors.secondaryButton
        ButtonVariant.TERTIARY_VARIANT -> Color.Transparent
        else -> MaterialTheme.colorScheme.secondary
    }
}

/**
 * Will determine the content color of a button (text color) depending on its variant. E.g. Primary, Secondary, etc.
 */
@Composable
fun ButtonVariant.getContentColor(): Color {
    return when (this) {
        ButtonVariant.PRIMARY_VARIANT -> MaterialTheme.colorScheme.onPrimary
        ButtonVariant.SECONDARY_VARIANT -> MaterialTheme.colorScheme.secondary
        ButtonVariant.TERTIARY_VARIANT -> MaterialTheme.colorScheme.primary
        else -> MaterialTheme.colorScheme.onPrimary
    }
}

@Composable
fun ButtonVariant.getButtonColors(): ButtonColors {
    return when (this) {
        ButtonVariant.TERTIARY_VARIANT -> ButtonDefaults.outlinedButtonColors(
            containerColor = getContainerColor(),
            contentColor = getContentColor()
        )
        else -> ButtonDefaults.buttonColors(
            containerColor = getContainerColor(),
            contentColor = getContentColor(),
            disabledContainerColor = LocalCustomColors.current.gray300,
            disabledContentColor = LocalCustomColors.current.gray600,
        )
    }
}

/**
 * Listen for Any lifecycle event (onResume(), onPause(), onStart(), etc.) within the app inside a composable screen.
 * The below function works for now, but consider updating `androidx.lifecycle:lifecycle-runtime-compose version to 2.7.0`
 * and compileSdkVersion to 34 to use `LifecycleEventEffect` instead of the below function.
 */
@Composable
fun rememberLifecycleEvent(lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current): Lifecycle.Event {
    var state by remember { mutableStateOf(Lifecycle.Event.ON_ANY) }

    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            state = event
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
    return state
}

/**
 * Convert a hexadecimal color to a Color object
 * @param defaultColor The default color to return if the string is empty or the color parsing fails
 */
fun String.toColor(defaultColor: Color = Color.White): Color = try {
    if (this.isEmpty()) {
        defaultColor
    } else {
        Color(toColorInt())
    }
} catch (_: Exception) {
    defaultColor
}

/**
 * Set the background of a composable from a list of colors
 */
fun Modifier.setBackgroundFromList(
    colors: List<Color>,
    shape: Shape = RectangleShape
): Modifier = composed {
    if (colors.size > 1) {
        this.background(
            brush = Brush.linearGradient(
                colors = colors,
                start = Offset(0f, Float.POSITIVE_INFINITY),
                end = Offset(Float.POSITIVE_INFINITY, 0f)
            ),
            shape = shape
        )
    } else {
        this.background(
            color = colors.firstOrNull() ?: MaterialTheme.customColors.defaultSurface,
            shape = shape
        )
    }
}

/**
 * Reusable function to handle permission state and for sharing a voucher event.
 */
@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun <T> handleVoucherSharingPermission(
    voucherView: View?,
    onHideComponentBeforeCapture: () -> T,
    onPermissionGranted: (Bitmap?) -> T,
    onPermissionDenied: (showRationale: Boolean) -> T,
    onEvent: (T) -> Unit
): PermissionState {
    val context = LocalContext.current
    val storagePermission = Manifest.permission.WRITE_EXTERNAL_STORAGE

    val permissionState = rememberPermissionState(storagePermission) { isGranted ->
        if (isGranted) {
            onEvent(onHideComponentBeforeCapture())

            CoroutineScope(Dispatchers.Main).delayThenExecute(HUNDRED_MILLIS) {
                onEvent(onPermissionGranted(voucherView?.drawToBitmap()))
            }
        } else {
            onEvent(
                onPermissionDenied(
                    context.shouldShowRationale(storagePermission)
                )
            )
        }
    }

    return permissionState
}

/**
 * Reusable function to handle the callback for sharing a voucher from a composable view.
 */
@OptIn(ExperimentalPermissionsApi::class)
fun <T> handleShareVoucherCallback(
    context: Context,
    voucherView: View?,
    storagePermissionState: PermissionState,
    onEvent: (T) -> Unit,
    hideBeforeCaptureEvent: T,
    onGrantedEvent: (Bitmap?) -> T,
    onShareVoucherClickEvent: ((() -> Unit) -> T)
): () -> Unit {
    return {
        onEvent(hideBeforeCaptureEvent)
        CoroutineScope(Dispatchers.Main).delayThenExecute(HUNDRED_MILLIS) {
            onEvent(
                onShareVoucherClickEvent {
                    if (context.shouldRequestStoragePermission()) {
                        storagePermissionState.launchPermissionRequest()
                    } else {
                        onEvent(onGrantedEvent(voucherView?.drawToBitmap()))
                    }
                }
            )
        }
    }
}
