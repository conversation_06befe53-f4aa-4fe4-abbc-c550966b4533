package sv.com.tarjetaone.presentation.compose.theme

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.ui.graphics.Color

/**
 * Holds the custom colors not available in the material3 colorScheme for the app to be provided
 * by composition local to avoid accessing them directly from the Color.kt file
 *
 * Extend with more colors when needed
 */
data class CustomColors(
    val successContainer: Color = md_theme_light_successContainer,
    val successLightContainer: Color = md_theme_light_successLightContainer,
    val onSuccess: Color = md_theme_light_onSuccess,
    val alertContainer: Color = md_theme_light_alertContainer,
    val alertDark: Color = md_theme_light_alertDark,
    val alertVariant: Color = md_theme_light_alertVariant,
    val onAlert: Color = md_theme_light_onAlert,
    val infoContainer: Color = md_theme_light_infoContainer,
    val onInfo: Color = md_theme_light_onInfo,
    val onHeadingTextDark: Color = md_theme_light_onHeadingTextDark,
    val secondarySolid: Color = md_theme_light_secondary,
    val secondaryButton: Color = md_theme_light_secondary_soft,
    val secondaryLight: Color = md_theme_light_secondary_light,
    val secondaryDark: Color = md_theme_light_secondary_dark,
    val secondarySoft: Color = md_theme_light_secondary_soft,
    val secondaryContainer: Color = md_theme_light_onSecondaryContainer,
    val disabledPlaceholder: Color = md_theme_light_disabled_gray_placeholder,
    val disclaimer: Color = md_theme_light_disclaimer,
    val baseText: Color = md_theme_light_base_text,
    val textBodyLight: Color = md_theme_text_body_light,
    val tertiaryDark: Color = md_theme_light_tertiary_dark,
    val tertiaryLight: Color = md_theme_light_tertiary,
    val textSurfaceVariant: Color = md_theme_light_gray_text_surface_variant,
    val errorText: Color = md_theme_light_surfaceTint,
    val progressColor: Color = md_theme_light_primary,
    val progressBackgroundColor: Color = md_theme_white_background_progress,
    val defaultSurface: Color = Color.White,
    val onDefaultSurface: Color = Color.Black,
    val headingDark: Color = md_theme_light_heading_dark,
    val backgroundError: Color = md_theme_light_selected,
    val lightError: Color = md_theme_light_error,
    val lightBackground: Color = md_theme_light_onBackground,
    val lightProgress: Color = md_theme_light_progress,
    val softPrimary: Color = md_theme_soft_primary,

    // Gradients
    val primaryGradient: List<Color> = listOf(
        md_theme_light_primary_gradient_1,
        md_theme_light_primary_gradient_2,
        md_theme_light_primary_gradient_3,
        md_theme_light_primary_gradient_4
    ),

    val primaryGradientReversed: List<Color> = listOf(
        md_theme_light_primary_gradient_3,
        md_theme_light_primary_gradient_2,
        md_theme_light_primary_gradient_1,
        md_theme_light_primary_gradient_2,
        md_theme_light_primary_gradient_3,
        md_theme_light_primary_gradient_4
    ),

    // Not in design system
    val cardRed: Color = md_theme_card_red,
    val polygonRed: Color = md_theme_polygon_red,
    val divider: Color = md_theme_divider,
    val lightBlueBackground: Color = md_theme_light_blue_background,
    val softVioletGray: Color = md_theme_light_soft_violet_gray,
    val arcticVeil: Color = md_theme_light_arctic_veil,

    // Grays
    val gray5: Color = md_theme_gray_5,
    val gray50: Color = md_theme_gray_50,
    val gray100: Color = md_theme_gray_100,
    val gray200: Color = md_theme_gray_200,
    val gray300: Color = md_theme_gray_300,
    val gray400: Color = md_theme_gray_400,
    val gray500: Color = md_theme_gray_500,
    val gray600: Color = md_theme_gray_600,
    val gray700: Color = md_theme_gray_700,
    val gray800: Color = md_theme_gray_800,
    val gray900: Color = md_theme_gray_900,
    val disabledGray: Color = md_theme_light_disabled_gray,
    val bodyLightGray: Color = md_theme_light_body_light_gray,
    val cardLightBackground: Color = md_theme_light_cardBackground,
    val steelGray: Color = md_theme_steel_gray,
    val lightGray: Color = md_theme_light_gray,

    // Skeleton colors
    val skeletonStartColor: Color = md_skeleton_start_color,
    val skeletonMiddleColor: Color = md_skeleton_middle_color,
    val skeletonEndColor: Color = md_skeleton_end_color,

    // Banner colors
    val bannerText: Color = md_theme_banner_text,
    val trackingBanner: Color = md_theme_tracking_banner,
    val alertBanner: Color = md_theme_alert_banner,
    val alertBannerSoft: Color = md_theme_alert_banner_soft
)

val LocalCustomColors = compositionLocalOf { CustomColors() }

val MaterialTheme.customColors: CustomColors
    @Composable
    @ReadOnlyComposable
    get() = LocalCustomColors.current
