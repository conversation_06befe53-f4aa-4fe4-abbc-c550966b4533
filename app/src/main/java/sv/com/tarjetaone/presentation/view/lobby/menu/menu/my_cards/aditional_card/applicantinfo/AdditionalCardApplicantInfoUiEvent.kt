package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.applicantinfo

import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI

sealed class AdditionalCardApplicantInfoUiEvent {
    data object OnStart : AdditionalCardApplicantInfoUiEvent()
    data object OnBackClick : AdditionalCardApplicantInfoUiEvent()
    data object OnTwilioClick : AdditionalCardApplicantInfoUiEvent()
    data object OnContinueClick : AdditionalCardApplicantInfoUiEvent()
    data class OnDropdownItemSelected(val item: CatalogItemsCollectionUI) : AdditionalCardApplicantInfoUiEvent()
    data class OnPhoneChange(val phone: String) : AdditionalCardApplicantInfoUiEvent()
}