package sv.com.tarjetaone.presentation.view.lobby.menu.menu.management

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_STRING
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI
import sv.com.tarjetaone.domain.entities.response.CatalogType.GC_MANAGEMENT_STATUS
import sv.com.tarjetaone.domain.entities.response.LogManagementItemsCollectionUI
import sv.com.tarjetaone.domain.entities.response.ManagementUI
import sv.com.tarjetaone.domain.usecases.catalog.GetCatalogUseCase
import sv.com.tarjetaone.domain.usecases.managments.GetLogManagementsUseCase
import sv.com.tarjetaone.domain.usecases.managments.details.GetLogManagementsDetailUseCase
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.DialogAction
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.MessageParams
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.OneDialogParams
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.settings.activity_log.ActivityLogDateRange
import sv.com.tarjetaone.presentation.view.utils.getRequestDateRange
import javax.inject.Inject

@HiltViewModel
class ManagementFollowUpViewModel @Inject constructor(
    private val getLogManagementDetailsUseCase: GetLogManagementsDetailUseCase,
    private val getLogManagementUseCase: GetLogManagementsUseCase,
    private val getCatalogUseCase: GetCatalogUseCase,
    private val sharedPrefs: SecureSharedPreferencesRepository
) : BaseViewModel() {
    private val _uiState: MutableStateFlow<ManagementFollowUpUiState> =
        MutableStateFlow(ManagementFollowUpUiState())
    val uiState = _uiState.asStateFlow()

    private fun onStart() {
        if (uiState.value.requestStatusList.isNotEmpty()) return
        cleanFilters()
        getManagementStatus()
    }

    private fun cleanFilters() {
        _uiState.update {
            it.copy(
                requestStatusList = listOf(),
                managementPagingData = null
            )
        }
    }

    private fun onDateChange(value: ActivityLogDateRange) {
        _uiState.update { it.copy(dateRange = value) }
        getLogManagement()
    }

    private fun onFilterChange(value: CatalogItemsCollectionUI) {
        _uiState.update { it.copy(selectedRequestStatus = value) }
        getLogManagement()
    }

    private fun onRequestClick(value: LogManagementItemsCollectionUI) {
        getLogManagementDetailsUseCase(value.logApplicationID)
    }

    private fun onNewRequestClick() {
        sendEvent(
            UiEvent.Navigate(
                ManagementFollowUpFragmentDirections
                    .actionFragmentManagementToClaimDynamicFormFragment()
            )
        )
    }

    private fun onBackPressed() {
        sendEvent(UiEvent.NavigateBack)
    }

    private fun onTwilioClicked() {
        sendEvent(UiEvent.TwilioClick)
    }

    private fun getLogManagementDetailsUseCase(manRequest: Int) {
        viewModelScope.launch {
            sendEvent(SideEffect.Loading(true))
            getLogManagementDetailsUseCase.invoke(manRequest).executeUseCase { details ->
                sendEvent(SideEffect.Loading(false))
                details.data?.manRequests?.apply {
                    sendEvent(
                        UiEvent.Navigate(
                            ManagementFollowUpFragmentDirections
                                .actionFragmentManagementToClaimDetailFragment(
                                    ManagementUI(
                                        manRequestId = manRequestId,
                                        clientName = clientName,
                                        manNumberApp = manNumberApp,
                                        mrStatusTextColor = mrStatusTextColor,
                                        mrStatusNameApp = mrStatusNameApp,
                                        manTypeNameApp = manTypeNameApp,
                                        closeDate = closeDate,
                                        applicationDate = applicationDate,
                                        processDate = processDate,
                                        cardNumMasked = cardNumMasked,
                                        typeCardText = typeCardText,
                                        description = description,
                                        showDescription = showDescription,
                                        availableDay = availableDay
                                    )
                                )
                        )
                    )
                }
            }
        }
    }

    private fun getLogManagement() {
        val (startDate, endDate) = getRequestDateRange(_uiState.value.dateRange)
        _uiState.update {
            it.copy(
                managementPagingData = getLogManagementUseCase(
                    startDate = startDate,
                    endDate = endDate,
                    customerId = sharedPrefs.getCustomerId(),
                    statusId = _uiState.value.selectedRequestStatus.id.toIntOrNull(),
                )
            )
        }
    }

    private fun getManagementStatus() {
        viewModelScope.launch {
            sendEvent(SideEffect.Loading(true))
            getCatalogUseCase.invoke(GC_MANAGEMENT_STATUS).executeUseCase { catalog ->
                sendEvent(SideEffect.Loading(false))
                catalog.dataCatalog?.catalogItemsCollection?.let { uiCatalog ->
                    _uiState.update {
                        it.copy(
                            requestStatusList = uiCatalog,
                            selectedRequestStatus = if (it.selectedRequestStatus.id == ZERO_STRING) {
                                uiCatalog.first()
                            } else {
                                it.selectedRequestStatus
                            }
                        )
                    }
                }
                getLogManagement()
            }
        }
    }

    private fun onManagementLoadError(callback: () -> Unit) =
        showOneDialog(
            params = OneDialogParams(
                isDismissible = false,
                message = MessageParams(
                    text = UiText.StringResource(R.string.simple_technical_issues_message)
                ),
                primaryAction = DialogAction(
                    text = UiText.StringResource(R.string.try_again_label),
                    onClick = callback,
                    actionType = DialogAction.ActionType.WARNING
                )
            )
        )

    fun onEvent(event: ManagementFollowUpUiEvent) {
        when (event) {
            is ManagementFollowUpUiEvent.OnStart -> onStart()
            is ManagementFollowUpUiEvent.OnDateChange -> onDateChange(event.value)
            is ManagementFollowUpUiEvent.OnFilterChange -> onFilterChange(event.value)
            is ManagementFollowUpUiEvent.OnManagementClick -> onRequestClick(event.value)
            is ManagementFollowUpUiEvent.OnNewManagementClicked -> onNewRequestClick()
            is ManagementFollowUpUiEvent.OnBackPressed -> onBackPressed()
            is ManagementFollowUpUiEvent.OnSupportClicked -> onTwilioClicked()
            is ManagementFollowUpUiEvent.OnManagementLoadError -> onManagementLoadError(event.callback)
        }
    }
}
