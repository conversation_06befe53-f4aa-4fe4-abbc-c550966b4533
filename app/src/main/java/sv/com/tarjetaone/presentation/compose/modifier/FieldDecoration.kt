package sv.com.tarjetaone.presentation.compose.modifier

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.FieldDecorationType

/**
 * Add a border or shadow decoration to a field.
 * @param decorationType The type of decoration to apply.
 * @param hasError If true, the decoration will always be a border with the error color.
 */
fun Modifier.fieldDecoration(
    decorationType: FieldDecorationType,
    hasError: Boolean = false,
    enabled: Boolean = true
): Modifier {
    return composed {
        if (hasError) {
            this.then(
                Modifier
                    .padding(MaterialTheme.customDimens.dimen1)
                    .border(
                        width = MaterialTheme.customDimens.dimen1,
                        color = MaterialTheme.colorScheme.error,
                        shape = MaterialTheme.shapes.small
                    )
            )
        } else if (enabled.not()) {
            this.then(Modifier.clip(MaterialTheme.shapes.small))
        } else {
            when (decorationType) {
                FieldDecorationType.ELEVATED -> {
                    this.then(
                        Modifier.shadow(
                            elevation = MaterialTheme.customDimens.dimen1,
                            shape = MaterialTheme.shapes.small
                        )
                    )
                }
                FieldDecorationType.OUTLINED -> {
                    this.then(
                        Modifier
                            .padding(MaterialTheme.customDimens.dimen1)
                            .border(
                                width = MaterialTheme.customDimens.dimen1,
                                color = MaterialTheme.colorScheme.secondary,
                                shape = MaterialTheme.shapes.small
                            )
                    )
                }
            }
        }
    }
}
