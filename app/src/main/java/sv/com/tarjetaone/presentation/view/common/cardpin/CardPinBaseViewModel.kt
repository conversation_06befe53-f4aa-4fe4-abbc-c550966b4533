package sv.com.tarjetaone.presentation.view.common.cardpin

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.entities.response.CreditCardStatus

abstract class CardPinBaseViewModel : BaseViewModel() {
    protected val _uiState = MutableStateFlow(CardPinUiState())
    val uiState: StateFlow<CardPinUiState> = _uiState.asStateFlow()

    abstract fun onContinueClick()

    protected open fun onStart() {
        _uiState.update {
            it.copy(
                canModifyPin = baseSharedPrefs.mainCard?.ccStatus == CreditCardStatus.Active.name
            )
        }
    }

    protected open fun onPinChange(pin: String) {
        _uiState.update { it.copy(pin = pin) }
    }

    fun onUiEvent(event: CardPinUiEvent) {
        when (event) {
            CardPinUiEvent.OnStart -> onStart()
            CardPinUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            CardPinUiEvent.OnContinueClick -> onContinueClick()
            CardPinUiEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
            is CardPinUiEvent.OnPinChange -> onPinChange(event.pin)
        }
    }
}