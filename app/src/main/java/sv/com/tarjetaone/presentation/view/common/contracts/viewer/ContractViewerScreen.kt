package sv.com.tarjetaone.presentation.view.common.contracts.viewer

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.PdfViewer

@Composable
fun ContractViewerScreen(
    viewModel: BaseContractsViewerViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LaunchedEffect(Unit) {
        viewModel.onEvent(ContractViewerUiEvent.OnStart)
    }

    ContractViewerScreenContent(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@Composable
fun ContractViewerScreenContent(
    uiState: ContractViewerUiState,
    onEvent: (ContractViewerUiEvent) -> Unit
) {
    ScreenWithTopAppBar(
        title = uiState.fileName,
        isProgressbarVisible = uiState.progress != null,
        progress = uiState.progress.orZero(),
        onLeftButtonClick = { onEvent(ContractViewerUiEvent.OnBackAction) },
        onRightButtonClick = { onEvent(ContractViewerUiEvent.OnTwilioClick) }
    ) {
        uiState.fileUri?.let { uri ->
            PdfViewer(
                uri = uri,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(MaterialTheme.customDimens.dimen16)
                    .clip(MaterialTheme.shapes.medium)
                    .background(MaterialTheme.customColors.defaultSurface)
            )
        }
    }
}
