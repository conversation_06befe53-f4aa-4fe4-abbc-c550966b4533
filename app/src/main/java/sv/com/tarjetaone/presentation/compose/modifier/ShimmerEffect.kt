package sv.com.tarjetaone.presentation.compose.modifier

import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.unit.IntSize
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.util.ShimmerSize
import sv.com.tarjetaone.presentation.compose.util.ShimmerStyle

/**
 * Modifier to apply a shimmer effect to a Composable.
 */
@Composable
fun Modifier.shimmerEffect(
    shape: Shape = MaterialTheme.shapes.small,
    shimmerSize: ShimmerSize = ShimmerSize.MEDIUM,
    shimmerStyle: ShimmerStyle = ShimmerStyle.LIGHT_BACKGROUND,
    animationTime: Int = SHIMMER_ANIMATION_DURATION,
): Modifier = composed {

    val transition = rememberInfiniteTransition(label = SHIMMER_LABEL)
    var size by remember { mutableStateOf(IntSize.Zero) }

    // Calculate the shimmer width based on the size of the element.
    val shimmerSizeValue = when (shimmerSize) {
        ShimmerSize.SMALL -> size.width.div(EIGHT_VALUE_PERCENT)
        ShimmerSize.MEDIUM -> size.width.div(FOUR_VALUE_PERCENT)
        ShimmerSize.LARGE -> size.width.div(TWO_VALUE_PERCENT)
    }

    // Animate the shimmer effect from left to right.
    val startOffsetX by transition.animateFloat(
        initialValue = -shimmerSizeValue,
        targetValue = size.width.toFloat() + shimmerSizeValue,
        animationSpec = infiniteRepeatable(
            animation = tween(
                durationMillis = animationTime,
                easing = FastOutSlowInEasing
            )
        ),
        label = SHIMMER_ANIMATION_LABEL
    )

    // Define the colors of the shimmer effect based on the background style.
    val shimmerBrushColors = when (shimmerStyle) {
        ShimmerStyle.LIGHT_BACKGROUND -> listOf(
            Color.Transparent,
            MaterialTheme.customColors.skeletonMiddleColor,
            Color.Transparent
        )

        ShimmerStyle.DARK_BACKGROUND -> listOf(
            Color.Transparent, // transparent to light wave effect
            MaterialTheme.customColors.skeletonMiddleColor.copy(alpha = 0.5f),
            Color.Transparent
        )

        ShimmerStyle.NONE -> emptyList()
    }

    val shimmerBrush = Brush.linearGradient(
        colors = shimmerBrushColors,
        start = Offset(startOffsetX, SHIMMER_ANIMATION_START_OFFSET),
        end = Offset(startOffsetX + shimmerSizeValue, SHIMMER_ANIMATION_END_OFFSET)
    )

    this
        .clip(shape)
        .drawWithContent {
            drawContent() // draw given background first (existing backgroundColor)

            // draw the shimmer on top of the background
            if (shimmerBrushColors.isNotEmpty()) {
                drawRect(brush = shimmerBrush)
            }
        }
        .onGloballyPositioned { size = it.size }
}

const val SHIMMER_ANIMATION_DURATION = 1500
const val SHIMMER_LABEL = "Shimmer"
const val SHIMMER_ANIMATION_LABEL = "ShimmerAnimation"
const val EIGHT_VALUE_PERCENT = 8f
const val FOUR_VALUE_PERCENT = 4f
const val TWO_VALUE_PERCENT = 2f
const val SHIMMER_ANIMATION_START_OFFSET = 0f
const val SHIMMER_ANIMATION_END_OFFSET = 0f
