package sv.com.tarjetaone.presentation.view.lobby.menu.home.account_status

import sv.com.tarjetaone.domain.entities.response.DataAccountStateUI

sealed class AccountStatusUiEvent {
    data object OnStart: AccountStatusUiEvent()
    data object OnBackClick : AccountStatusUiEvent()
    data object OnSupportClick : AccountStatusUiEvent()
    data class OnCutOffDateChange(val data: DataAccountStateUI) : AccountStatusUiEvent()
    data object OnDownloadClick : AccountStatusUiEvent()
}
