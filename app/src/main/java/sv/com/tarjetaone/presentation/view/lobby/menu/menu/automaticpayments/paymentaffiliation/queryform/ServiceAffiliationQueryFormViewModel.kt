package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.queryform

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.entities.request.automaticpayments.paymentaffiliation.PXGetBalanceRequestUI
import sv.com.tarjetaone.domain.usecases.automaticpayments.paymentaffiliation.GetPxBalanceUseCase

@HiltViewModel
class ServiceAffiliationQueryFormViewModel @Inject constructor(
    private val getPxBalanceUseCase: GetPxBalanceUseCase,
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {

    private val _uiState: MutableStateFlow<ServiceAffiliationQueryFormUiState> =
        MutableStateFlow(ServiceAffiliationQueryFormUiState())
    val uiState = _uiState.asStateFlow()

    private val args = ServiceAffiliationQueryFormFragmentArgs.fromSavedStateHandle(savedStateHandle)

    private fun onStart() {
        with(args.service) {
            _uiState.update {
                it.copy(
                    serviceName = serviceName,
                    categoryName = categoriesName,
                    inputLabel = serviceLabel
                )
            }
        }
    }

    private fun onChangeReferenceNumber(referenceNumber: String) {
        _uiState.update {
            it.copy(
                referenceNumber = referenceNumber,
                queryFormError = null // Clean error when user changes reference number
            )
        }
    }

    private fun onContinueClick() {
        fetchPxBalance()
    }

    private fun fetchPxBalance() {
        sendEvent(UiEvent.Loading(true))
        viewModelScope.launch {
            val request = PXGetBalanceRequestUI(
                catalogServiceCode = args.service.catalogServiceCode,
                identifierCode = args.service.fieldAutomatic.orEmpty(),
                referenceNumber = uiState.value.referenceNumber.orEmpty()
            )
            getPxBalanceUseCase(request).executeUseCase(
                onApiErrorAction = { _, error, _, _ ->
                    sendEvent(UiEvent.Loading(false))
                    if (error?.code in NON_FRIENDLY_ERROR_CODES) {
                        showUpsErrorMessage()
                    } else {
                        _uiState.update { it.copy(queryFormError = error?.result) }
                    }
                },
                onSuccessAction = {
                    sendEvent(UiEvent.Loading(false))
                    sendEvent(
                        UiEvent.Navigate(
                            ServiceAffiliationQueryFormFragmentDirections
                                .navigateToAffiliateServiceFragment(
                                    serviceName = args.service.serviceName,
                                    serviceCategoryName = args.service.categoriesName,
                                    balance = it,
                                    catalogServiceCode = args.service.catalogServiceCode,
                                    identifierCode = args.service.fieldAutomatic.orEmpty(),
                                    referenceNumber = uiState.value.referenceNumber.orEmpty()
                                )
                        )
                    )
                }
            )
        }
    }

    fun onEvent(event: ServiceAffiliationQueryFormUiEvent) {
        when (event) {
            ServiceAffiliationQueryFormUiEvent.OnStart -> onStart()
            ServiceAffiliationQueryFormUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            ServiceAffiliationQueryFormUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            is ServiceAffiliationQueryFormUiEvent.OnChangeReferenceNumber -> onChangeReferenceNumber(event.referenceNumber)
            ServiceAffiliationQueryFormUiEvent.OnContinueClick -> onContinueClick()
        }
    }

    companion object {
        private val NON_FRIENDLY_ERROR_CODES = listOf("500")
    }
}
