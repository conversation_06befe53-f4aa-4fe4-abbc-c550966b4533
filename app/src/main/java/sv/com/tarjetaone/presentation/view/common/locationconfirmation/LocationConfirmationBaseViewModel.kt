package sv.com.tarjetaone.presentation.view.common.locationconfirmation

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent

abstract class LocationConfirmationBaseViewModel : BaseViewModel() {

    protected val _uiState: MutableStateFlow<LocationConfirmationUiState> =
        MutableStateFlow(LocationConfirmationUiState())
    val uiState = _uiState.asStateFlow()

    protected open fun onChangeAddressClick(isModifyingAddress: Boolean) {
        sendEvent(UiEvent.NavigateBack)
    }

    /**
     * Called when the screen starts.
     */
    protected open fun onStart() {
        // Default implementation, child classes can override.
    }

    protected abstract fun onContinueClick()

    fun onEvent(event: LocationConfirmationUiEvent) {
        when (event) {
            LocationConfirmationUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            LocationConfirmationUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            is LocationConfirmationUiEvent.OnChangeAddressClick -> onChangeAddressClick(event.isModifyingAddress)
            LocationConfirmationUiEvent.OnContinueClick -> onContinueClick()
            LocationConfirmationUiEvent.OnStart -> onStart()
        }
    }
}
