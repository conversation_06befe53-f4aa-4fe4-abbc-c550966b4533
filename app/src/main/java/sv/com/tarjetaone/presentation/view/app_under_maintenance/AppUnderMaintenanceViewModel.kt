package sv.com.tarjetaone.presentation.view.app_under_maintenance

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.interfaces.TextFromResourcesUtil
import sv.com.tarjetaone.core.di.IoDispatcher
import sv.com.tarjetaone.core.di.MainDispatcher
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.ScreenHome
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.usecases.maintenance.CheckServerStateUseCase
import sv.com.tarjetaone.domain.utils.onApiError
import sv.com.tarjetaone.domain.utils.onNetworkError
import sv.com.tarjetaone.domain.utils.onSuccess

@HiltViewModel
class AppUnderMaintenanceViewModel @Inject constructor(
    private val checkServerStateUseCase: CheckServerStateUseCase,
    private val sharedPreferences: SecureSharedPreferencesRepository,
    private val textFromResourcesUtil: TextFromResourcesUtil,
    @IoDispatcher val ioDispatcher: CoroutineDispatcher,
    @MainDispatcher val mainDispatcher: CoroutineDispatcher
) : BaseViewModel() {

    private val _uiState: MutableStateFlow<AppUnderMaintenanceUiState> =
        MutableStateFlow(AppUnderMaintenanceUiState())
    val uiState: StateFlow<AppUnderMaintenanceUiState> = _uiState.asStateFlow()

    private val _navigateTo: MutableStateFlow<ScreenHome?> = MutableStateFlow(null)
    val navigateTo: StateFlow<ScreenHome?> = _navigateTo.asStateFlow()

    private fun onCheckServerState() {
        _uiState.update { it.copy(showLoading = true) }
        viewModelScope.launch(ioDispatcher) {
            checkServerStateUseCase().onSuccess { response ->
                delay(LOADING_DELAY)
                if (response == null) {
                    checkLastUserAppState()
                } else {
                    hideLoading()
                }

            }.onApiError { _, _, _, _ ->
                hideLoading()
            }.onNetworkError {
                hideLoading()
            }
        }
    }

    private suspend fun hideLoading() {
        withContext(mainDispatcher) {
            _uiState.update { it.copy(showLoading = false) }
        }
    }

    private fun checkLastUserAppState() {
        sharedPreferences.userIsLoggedIn = false // Reset session value

        if (sharedPreferences.getUserHasLogin() || sharedPreferences.duiValidated()) {
            _navigateTo.value = ScreenHome.LoginScreen(
                textFromResourcesUtil.getString(R.string.welcome_user_label)
            )
            return
        }

        _navigateTo.value = ScreenHome.WelcomeScreen
    }


    fun onScreenUiEvent(event: AppUnderMaintenanceUiEvent) {
        when (event) {
            AppUnderMaintenanceUiEvent.OnRetryClick -> onCheckServerState()
        }
    }

    companion object {
        const val LOADING_DELAY = 2000L
    }
}
