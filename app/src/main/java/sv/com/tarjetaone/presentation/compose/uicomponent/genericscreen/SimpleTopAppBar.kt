package sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen

import androidx.annotation.DrawableRes
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.common.topbar.component.BackActionButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.topbar.component.CancelActionButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.topbar.component.SupportActionButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.topbar.component.TopBarAction

@Suppress("kotlin:S107")
@Composable
fun SimpleTopAppBar(
    modifier: Modifier = Modifier,
    @DrawableRes headerIcon: Int = R.drawable.app_one_logo,
    contentPadding: PaddingValues = PaddingValues(
        horizontal = MaterialTheme.customDimens.dimen24,
        vertical = MaterialTheme.customDimens.dimen12,
    ),
    isLeftButtonVisible: Boolean = true,
    isRightButtonVisible: Boolean = true,
    leftActionButton: TopBarAction = TopBarAction.BACK,
    rightActionButton: TopBarAction = TopBarAction.SUPPORT,
    onLeftButtonClick: () -> Unit,
    onRightButtonClick: () -> Unit
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(contentPadding)
    ) {
        if (isLeftButtonVisible) {
            when (leftActionButton) {
                TopBarAction.BACK -> {
                    BackActionButton(
                        modifier = Modifier.align(Alignment.CenterStart),
                        onClick = onLeftButtonClick
                    )
                }

                else -> Unit
            }
        }
        Icon(
            modifier = Modifier
                .align(Alignment.Center)
                .height(MaterialTheme.customDimens.dimen18),
            imageVector = ImageVector.vectorResource(headerIcon),
            contentDescription = null
        )
        if (isRightButtonVisible) {
            when (rightActionButton) {
                TopBarAction.SUPPORT -> {
                    SupportActionButton(
                        modifier = Modifier.align(Alignment.CenterEnd),
                        onClick = onRightButtonClick
                    )
                }

                TopBarAction.CANCEL -> {
                    CancelActionButton(
                        modifier = Modifier.align(Alignment.CenterEnd),
                        onClick = onRightButtonClick
                    )
                }

                else -> Unit
            }
        }
    }
}

@Composable
@Preview(showBackground = true)
fun OnboardingTopBarPreview() {
    OneAppTheme {
        SimpleTopAppBar(
            onLeftButtonClick = {},
            onRightButtonClick = {}
        )
    }
}
