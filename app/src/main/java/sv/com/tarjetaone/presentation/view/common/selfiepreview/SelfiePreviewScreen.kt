package sv.com.tarjetaone.presentation.view.common.selfiepreview

import android.graphics.Bitmap
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.MaterialTheme.shapes
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.core.utils.extensions.withNotNull
import sv.com.tarjetaone.core.utils.facephi.CaptureType
import sv.com.tarjetaone.core.utils.facephi.rememberFacephiWidgetLauncher
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer16

/**
 * Generic screen for selfie preview that implements the [rememberFacephiWidgetLauncher] to take a new selfie
 */

@Composable
fun SelfiePreviewScreen(viewModel: SelfiePreviewViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    SelfiePreviewContent(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@Composable
private fun SelfiePreviewContent(
    uiState: SelfiePreviewUiState = SelfiePreviewUiState(),
    onEvent: (SelfiePreviewUiEvent) -> Unit = {}
) {
    LaunchedEffect(Unit) {
        onEvent(SelfiePreviewUiEvent.OnStart)
    }
    ScreenWithTopAppBar(
        isRightButtonVisible = uiState.isRightButtonVisible,
        isProgressbarVisible = uiState.progressValue != null,
        title = stringResource(id = R.string.identity_validation),
        progress = uiState.progressValue.orZero(),
        onLeftButtonClick = { onEvent(SelfiePreviewUiEvent.OnBack) },
        onRightButtonClick = { onEvent(SelfiePreviewUiEvent.OnTwilioChat) }
    ) {
        Column(
            modifier = Modifier
                .padding(horizontal = MaterialTheme.customDimens.dimen32)
                .fillMaxSize()
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            SelfiePreviewPhoto(selfie = uiState.selfie)
            SelfiePreviewActionButtons(onEvent = onEvent)
        }
    }
}

@Composable
private fun SelfiePreviewPhoto(
    modifier: Modifier = Modifier,
    selfie: Bitmap? = null
) {
    Column(
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        selfie.withNotNull {
            Image(
                bitmap = it.asImageBitmap(),
                contentScale = ContentScale.Crop,
                contentDescription = null,
                modifier = Modifier
                    .size(
                        width = MaterialTheme.customDimens.dimen260,
                        height = MaterialTheme.customDimens.dimen325
                    )
                    .clip(shapes.large),
            )
        }
        Spacer16()
        Text(
            text = stringResource(id = R.string.do_you_like_it),
            style = MaterialTheme.typography.headlineSmall,
        )
    }
}

@Composable
private fun SelfiePreviewActionButtons(
    modifier: Modifier = Modifier,
    onEvent: (SelfiePreviewUiEvent) -> Unit = {}
) {
    val context = LocalContext.current
    val facephiLauncher = rememberFacephiWidgetLauncher(CaptureType.Selfie) {
        onEvent(SelfiePreviewUiEvent.OnCaptureSelfie(it))
    }

    Column(
        modifier = modifier.padding(vertical = MaterialTheme.customDimens.dimen32)
    ) {
        OneButton(
            text = stringResource(id = R.string.yes_continue_button_label),
            onClick = { onEvent(SelfiePreviewUiEvent.OnContinueClick) },
            modifier = Modifier.fillMaxWidth(),
        )
        Spacer16()
        OneButton(
            text = stringResource(id = R.string.take_a_new_selfie),
            buttonVariant = ButtonVariant.SECONDARY_VARIANT,
            onClick = {
                onEvent(SelfiePreviewUiEvent.OnTakeNewSelfieClick)
                facephiLauncher.initWidget(context)
            },
            modifier = Modifier.fillMaxWidth(),
        )
    }
}

@Preview(
    showBackground = true,
    showSystemUi = true
)
@Composable
private fun SelfiePreviewScreenPreview() {
    OneAppTheme {
        SelfiePreviewContent()
    }
}
