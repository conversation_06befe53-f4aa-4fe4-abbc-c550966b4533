package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component.tutorial

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.CustomDotsIndicator
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SheetDragHandle
import sv.com.tarjetaone.presentation.compose.util.Spacer12
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer20
import sv.com.tarjetaone.presentation.compose.util.annotatedStringResource

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun InstallmentsTutorialBottomSheetContent(
    onDismiss: () -> Unit,
    onCompleted: () -> Unit
) {
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    ModalBottomSheet(
        modifier = Modifier
            .background(Color.Transparent)
            .safeDrawingPadding(),
        onDismissRequest = { onDismiss() },
        containerColor = MaterialTheme.colorScheme.surface,
        dragHandle = {
            SheetDragHandle(
                color = MaterialTheme.customColors.gray500
            )
        },
        sheetState = sheetState
    ) {
        CardTransactionDetailsContent(onCompleted)
    }
}

@Composable
fun CardTransactionDetailsContent(onCompleted: () -> Unit) {
    val coroutineScope = rememberCoroutineScope()
    val pagerState = rememberPagerState(
        pageCount = { TWO_VALUE }
    )
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .background(MaterialTheme.customColors.textSurfaceVariant)
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
    ) {
        HorizontalPager(state = pagerState) { page ->
            Column(modifier = Modifier.fillMaxWidth()) {
                Image(
                    painter = painterResource(
                        id = validatePageValue(
                            page = page,
                            firstPageValue = R.drawable.ic_transaction_tutorial_one,
                            lastPageValue = R.drawable.ic_transaction_tutorial_two
                        )
                    ),
                    contentDescription = null,
                    contentScale = ContentScale.FillWidth,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = MaterialTheme.customDimens.dimen16)
                        .clip(
                            RoundedCornerShape(MaterialTheme.customDimens.dimen16)
                        )
                )
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(MaterialTheme.customColors.textSurfaceVariant)
                        .padding(
                            horizontal = MaterialTheme.customDimens.dimen16
                        )
                ) {
                    Spacer16()
                    Text(
                        text = stringResource(
                            validatePageValue(
                                page = page,
                                firstPageValue = R.string.quota_payment_tutorial_first_title,
                                lastPageValue = R.string.quota_payment_tutorial_second_title
                            )
                        ),
                        style = MaterialTheme.typography.displaySmall.copy(
                            fontWeight = FontWeight.SemiBold,
                            color = MaterialTheme.colorScheme.secondary
                        )
                    )
                    Spacer16()
                    Text(
                        text = stringResource(
                            validatePageValue(
                                page = page,
                                firstPageValue = R.string.quota_payment_tutorial_first_subtitle,
                                lastPageValue = R.string.quota_payment_tutorial_second_subtitle
                            )
                        ),
                        style = MaterialTheme.typography.bodyMedium.copy(
                            color = MaterialTheme.colorScheme.secondary
                        )
                    )
                    Spacer16()
                    CardTransactionTutorialItem(
                        page = page,
                        pageOneText = R.string.quota_payment_tutorial_first_detail_one,
                        pageTwoText = R.string.quota_payment_tutorial_second_detail_one,
                        pageTwoIcon = R.drawable.ic_one_number
                    )
                    Spacer16()
                    Row(verticalAlignment = validatePageAlignment(page)) {
                        Icon(
                            imageVector = ImageVector.vectorResource(
                                id = validatePageValue(
                                    page = page,
                                    firstPageValue = R.drawable.ic_patch_check_fill,
                                    lastPageValue = R.drawable.ic_two_number
                                )
                            ),
                            contentDescription = null,
                            tint = Color.Unspecified
                        )
                        Spacer12()
                        if (page == ZERO_VALUE) {
                            Text(
                                text = annotatedStringResource(
                                    R.string.quota_payment_tutorial_first_detail_two
                                ),
                                style = MaterialTheme.typography.bodySmall.copy(
                                    color = MaterialTheme.colorScheme.secondary
                                )
                            )
                        } else {
                            Text(
                                text = stringResource(
                                    R.string.quota_payment_tutorial_second_detail_two
                                ),
                                style = MaterialTheme.typography.bodySmall.copy(
                                    color = MaterialTheme.colorScheme.secondary
                                )
                            )
                        }
                    }
                    Spacer16()
                    CardTransactionTutorialItem(
                        page = page,
                        pageOneText = R.string.quota_payment_tutorial_first_detail_three,
                        pageTwoText = R.string.quota_payment_tutorial_second_detail_three,
                        pageTwoIcon = R.drawable.ic_three_number
                    )
                }
            }
        }
        Spacer16()
        Spacer1f()
        CustomDotsIndicator(
            modifier = Modifier.align(Alignment.CenterHorizontally),
            totalDots = TWO_VALUE,
            activeIndex = pagerState.currentPage,
            activeColor = MaterialTheme.colorScheme.primary,
            inactiveColor = MaterialTheme.customColors.gray200,
            dotSize = MaterialTheme.customDimens.dimen8
        )
        Spacer16()
        OneButton(
            modifier = Modifier
                .padding(horizontal = MaterialTheme.customDimens.dimen16)
                .fillMaxWidth(),
            text = stringResource(
                validatePageValue(
                    page = pagerState.currentPage,
                    firstPageValue = R.string.continue_button_label,
                    lastPageValue = R.string.quota_payment_tutorial_create_quota
                )
            ),
            onClick = {
                if (pagerState.currentPage == ZERO_VALUE) {
                    val currentPage = pagerState.currentPage + ONE_VALUE
                    coroutineScope.launch {
                        pagerState.animateScrollToPage(page = currentPage)
                    }
                } else {
                    onCompleted()
                }
            }
        )
        Spacer20()
    }
}

@Composable
fun CardTransactionTutorialItem(
    page: Int,
    pageOneText: Int,
    pageTwoText: Int,
    pageTwoIcon: Int
) {
    Row(verticalAlignment = validatePageAlignment(page)) {
        Icon(
            imageVector = ImageVector.vectorResource(
                id = validatePageValue(
                    page,
                    R.drawable.ic_patch_check_fill,
                    pageTwoIcon
                )
            ),
            contentDescription = null,
            tint = Color.Unspecified
        )
        Spacer12()
        Text(
            text = stringResource(id = validatePageValue(page, pageOneText, pageTwoText)),
            style = MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.colorScheme.secondary
            )
        )
    }
}

@Preview
@Composable
fun CardTransactionDetailsPreview() {
    OneAppTheme {
        CardTransactionDetailsContent(
            onCompleted = {}
        )
    }
}

/**
 * @param page value of the page where the ViewPager is
 * @param firstPageValue value to return when the pager is on the first page
 * @param lastPageValue value to return when the pager is on the lastPage
 * */
private fun validatePageValue(page: Int, firstPageValue: Int, lastPageValue: Int): Int =
    if (page == ZERO_VALUE) {
        firstPageValue
    } else {
        lastPageValue
    }

/**
 * @param page value of the page where the ViewPager is
 * */
private fun validatePageAlignment(page: Int): Alignment.Vertical = if (page == ZERO_VALUE) {
    Alignment.CenterVertically
} else {
    Alignment.Top
}
