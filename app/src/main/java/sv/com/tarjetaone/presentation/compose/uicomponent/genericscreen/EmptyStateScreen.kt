package sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.card.EmptyStateCard
import sv.com.tarjetaone.presentation.compose.uicomponent.card.EmptyStateCardVariant
import sv.com.tarjetaone.presentation.compose.util.CurveLineBackgroundComponent
import sv.com.tarjetaone.presentation.compose.util.Spacer12
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer32

@Composable
fun EmptyStateScreen(
    modifier: Modifier = Modifier,
    title: String? = null,
    titleStyle: TextStyle = MaterialTheme.typography.displaySmall.copy(
        color = MaterialTheme.colorScheme.secondary,
    ),
    variant: EmptyStateCardVariant = EmptyStateCardVariant.PRIMARY,
    contentDescription: @Composable (() -> Unit) = { },
    primaryActionText: String,
    onPrimaryAction: () -> Unit,
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        title?.let {
            Text(
                modifier = Modifier.padding(horizontal = MaterialTheme.customDimens.dimen24),
                text = it,
                style = titleStyle,
                textAlign = TextAlign.Center
            )
            Spacer12()
        }
        Spacer1f()
        Spacer32()
        Column(
            verticalArrangement = Arrangement.Center,
        ) {
            CurveLineBackgroundComponent(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(MaterialTheme.customDimens.dimen160),
                height = MaterialTheme.customDimens.dimen160
            ) {
                EmptyStateCard(
                    modifier = Modifier.size(
                        width = MaterialTheme.customDimens.dimen175,
                        height = MaterialTheme.customDimens.dimen260,
                    ),
                    variant = variant,
                )
            }
            Spacer32()
            contentDescription()
        }
        Spacer1f()
        Spacer32()
        OneButton(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    start = MaterialTheme.customDimens.dimen32,
                    end = MaterialTheme.customDimens.dimen32,
                    bottom = MaterialTheme.customDimens.dimen32
                ),
            text = primaryActionText,
            onClick = { onPrimaryAction() }
        )
    }
}

@Composable
fun PostActivationDescription() {
    Column {
        Text(
            textAlign = TextAlign.Center,
            text = buildAnnotatedString {
                append(stringResource(id = R.string.empty_state_post_activation_subtitle_one))
                withStyle(
                    style = SpanStyle(
                        fontWeight = FontWeight.SemiBold
                    )
                ) {
                    append(stringResource(id = R.string.empty_state_post_activation_activated))
                }
                append(stringResource(id = R.string.empty_state_post_activation_subtitle_two))
            },
            style = MaterialTheme.typography.labelMedium.copy(
                fontSize = MaterialTheme.customDimensSp.sp16,
                lineHeight = MaterialTheme.customDimensSp.sp20
            ),
            modifier = Modifier.fillMaxWidth()
        )
        Spacer16()
        Text(
            text = stringResource(
                id = R.string.empty_state_post_activation_options
            ),
            style = MaterialTheme.typography.labelMedium.copy(
                fontSize = MaterialTheme.customDimensSp.sp16,
                lineHeight = MaterialTheme.customDimensSp.sp20
            ),
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    start = MaterialTheme.customDimens.dimen32,
                    end = MaterialTheme.customDimens.dimen16
                )
        )
    }
}

@Composable
fun ActionNotAllowedDescription() {
    Column {
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.customDimens.dimen24),
            text = stringResource(id = R.string.empty_state_card_action_not_allowed_description),
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.labelMedium.copy(
                fontSize = MaterialTheme.customDimensSp.sp16,
                lineHeight = MaterialTheme.customDimensSp.sp20
            ),
        )
    }
}

@Composable
fun ContactHelpDescription() {
    Column {
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.customDimens.dimen24),
            text = stringResource(id = R.string.empty_state_contact_help_description),
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.labelMedium.copy(
                fontSize = MaterialTheme.customDimensSp.sp16,
                lineHeight = MaterialTheme.customDimensSp.sp20
            ),
        )
    }
}

@Preview
@Composable
private fun PrimaryEmptyStateScreenPreview() {
    OneAppTheme {
        EmptyStateScreen(
            modifier = Modifier.background(MaterialTheme.colorScheme.surface),
            variant = EmptyStateCardVariant.PRIMARY,
            primaryActionText = stringResource(id = R.string.go_back),
            onPrimaryAction = { }
        )
    }
}

@Preview
@Composable
private fun SecondaryEmptyStateScreenPreview() {
    OneAppTheme {
        EmptyStateScreen(
            modifier = Modifier.background(MaterialTheme.colorScheme.surface),
            variant = EmptyStateCardVariant.SECONDARY,
            primaryActionText = stringResource(id = R.string.go_back),
            onPrimaryAction = { }
        )
    }
}
