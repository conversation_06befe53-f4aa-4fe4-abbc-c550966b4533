package sv.com.tarjetaone.presentation.view.common.addressdelivery

import android.provider.Settings.ACTION_LOCATION_SOURCE_SETTINGS
import androidx.lifecycle.viewModelScope
import com.google.android.gms.maps.model.LatLng
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE_DOUBLE
import sv.com.tarjetaone.core.interfaces.LocationUtil
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.core.utils.openAppSettings
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.response.AddressDeliveryUI
import sv.com.tarjetaone.domain.usecases.requestcard.locationtoreceive.GetDeliveryAddressUseCase
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.DialogAction
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.MessageParams
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.OneDialogParams
import sv.com.tarjetaone.presentation.helpers.UiText

abstract class AddressDeliveryBaseViewModel(
    private val getDeliveryAddressUseCase: GetDeliveryAddressUseCase,
    private val sharedPrefs: SecureSharedPreferencesRepository,
    private val locationUtil: LocationUtil
) : BaseViewModel() {

    protected val _uiState = MutableStateFlow(AddressDeliveryUiState())
    val uiState = _uiState.asStateFlow()

    abstract fun onContinueClick()

    abstract fun onGpsLocationCaptured(latLng: LatLng)

    protected open fun onBackClick() = Unit

    protected open fun onStart() {
        if (_uiState.value.hasLoadedData) return
        getAddressesToDeliver()
    }

    private fun getAddressesToDeliver() {
        sendEvent(SideEffect.Loading(true))
        viewModelScope.launch {
            getDeliveryAddressUseCase(sharedPrefs.getCustomerId().orZero())
                .executeUseCase { response ->
                    fetchAddresses(response.addresses)
                    sendEvent(SideEffect.Loading(false))
                }
        }
    }

    private fun fetchAddresses(fetchAddresses: List<AddressDeliveryUI>) {
        _uiState.update { it.copy(addresses = fetchAddresses) }
    }

    private fun getCurrentUserLocation(): Pair<Double, Double> =
        locationUtil.getLastLocation() ?: Pair(ZERO_VALUE_DOUBLE, ZERO_VALUE_DOUBLE)

    private fun onLocationPermissionGranted() {
        if (locationUtil.isLocationProviderEnabled()) {
            val (latitude, longitude) = getCurrentUserLocation()
            onGpsLocationCaptured(LatLng(latitude, longitude))
        } else {
            showOneDialog(
                OneDialogParams(
                    title = UiText.StringResource(R.string.location_access),
                    message = MessageParams(UiText.StringResource(R.string.activate_gps_to_continue)),
                    primaryAction = DialogAction(
                        text = UiText.StringResource(R.string.settings),
                        onClick = {
                            sendEvent(
                                SideEffect.StartIntent(
                                    openAppSettings(
                                        action = ACTION_LOCATION_SOURCE_SETTINGS,
                                        uri = null
                                    )
                                )
                            )
                        }
                    )
                )
            )
        }
    }

    private fun onLocationPermissionDenied(showRationale: Boolean) {
        showOneDialog(
            OneDialogParams(
                title = UiText.StringResource(R.string.location_access),
                message = MessageParams(UiText.StringResource(R.string.location_dialog_description)),
                primaryAction = DialogAction(
                    text = UiText.StringResource(
                        if (showRationale) {
                            R.string.accept_label
                        } else {
                            R.string.settings
                        }
                    ),
                    onClick = {
                        if (!showRationale) sendEvent(SideEffect.StartIntent(openAppSettings()))
                    }
                )
            )
        )
    }

    private fun onSelectAddress(address: AddressDeliveryUI) {
        _uiState.update { it.copy(selectedAddress = address) }
    }

    private fun onDismissSecurityAlert() {
        _uiState.update { it.copy(alertVisibility = false) }
    }

    fun onEvent(event: AddressDeliveryUiEvent) {
        when (event) {
            is AddressDeliveryUiEvent.OnStart -> onStart()
            AddressDeliveryUiEvent.OnBackClick -> onBackClick()
            is AddressDeliveryUiEvent.OnContinueClick -> onContinueClick()
            is AddressDeliveryUiEvent.OnSelectAddress -> onSelectAddress(event.address)
            is AddressDeliveryUiEvent.OnLocationPermissionGranted -> onLocationPermissionGranted()
            is AddressDeliveryUiEvent.OnLocationPermissionDenied -> onLocationPermissionDenied(event.showRationale)
            is AddressDeliveryUiEvent.OnDismissSecurityAlert -> onDismissSecurityAlert()
            is AddressDeliveryUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
        }
    }
}