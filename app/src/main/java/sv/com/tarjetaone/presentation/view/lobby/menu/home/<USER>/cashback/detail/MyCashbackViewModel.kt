package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.cashback.detail

import android.content.Intent
import androidx.core.net.toUri
import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.core.remoteconfig.RemoteConfigManager
import sv.com.tarjetaone.core.remoteconfig.RemoteProperty
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.usecases.transactions.GetCashbackTransactionsUseCase
import javax.inject.Inject

@HiltViewModel
class MyCashbackViewModel @Inject constructor(
    private val cashbackTransactionsUseCase: GetCashbackTransactionsUseCase,
    private val sharedPrefs: SecureSharedPreferencesRepository,
    remoteConfigManager: RemoteConfigManager,
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {

    private val _uiState = with(
        remoteConfigManager.getProperty(RemoteProperty.CustomerSupportInfoProperty)
    ) {
        MutableStateFlow(MyCashbackUiState(eBankingUrl = this?.eBankingURL.orEmpty()))
    }
    val uiState = _uiState.asStateFlow()

    private val args = MyCashbackFragmentArgs.fromSavedStateHandle(savedStateHandle)

    private val hasPaymentPenalty: Boolean
        get() = sharedPrefs.mainCard?.hasPaymentPenalty ?: false

    private fun loadCashbackData() {
        if (hasPaymentPenalty) {
            _uiState.update { it.copy(hasPaymentPenalty = hasPaymentPenalty) }
            return
        }

        _uiState.update {
            it.copy(
                pointsData = args.pointsData,
                cashbackTransactions = cashbackTransactionsUseCase(
                    cardId = sharedPrefs.mainCard?.creditCardId.orZero(),
                    mode = CURRENT_PERIOD_ACCUMULATED_MODE,
                    queryText = null
                )
            )
        }
    }

    private fun onEBankingClick() {
        sendEvent(
            SideEffect.StartIntent(
                Intent(
                    Intent.ACTION_VIEW,
                    _uiState.value.eBankingUrl.toUri()
                )
            )
        )
    }

    fun onEvent(event: MyCashbackUiEvent) {
        when (event) {
            MyCashbackUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            MyCashbackUiEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
            MyCashbackUiEvent.OnStart -> loadCashbackData()
            is MyCashbackUiEvent.OnTransactionsLoadError -> showUpsErrorMessage(
                onButtonClick = event.onRetryAction
            )

            MyCashbackUiEvent.OnEBankingClick -> onEBankingClick()

            is MyCashbackUiEvent.OnHowToUseClick -> {
                _uiState.update { it.copy(showCashbackBottomSheet = event.visibility) }
            }
        }
    }

    companion object {
        // Only supported mode
        private const val CURRENT_PERIOD_ACCUMULATED_MODE = "QUERY_CURRENT_PERIOD_ACCUMULATED"
    }
}
