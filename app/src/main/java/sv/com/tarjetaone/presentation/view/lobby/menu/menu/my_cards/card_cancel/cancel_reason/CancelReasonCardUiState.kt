package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_cancel.cancel_reason

import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI

data class CancelReasonCardUiState(
    val reasonOptions: List<CatalogItemsCollectionUI> = emptyList(),
    val selectedReason: CatalogItemsCollectionUI? = null,
    val isOtherSelected: Boolean = false,
    val otherComments: String = "",
)
