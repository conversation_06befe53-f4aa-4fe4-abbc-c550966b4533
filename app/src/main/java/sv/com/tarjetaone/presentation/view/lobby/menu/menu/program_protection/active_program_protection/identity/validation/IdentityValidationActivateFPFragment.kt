package sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.active_program_protection.identity.validation

import android.os.Bundle
import android.view.View
import androidx.fragment.app.viewModels
import dagger.hilt.android.AndroidEntryPoint
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.base.BaseFragment
import sv.com.tarjetaone.databinding.FragmentComposeBinding
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.loading.IdentityValidationLoadingScreen

@AndroidEntryPoint
class IdentityValidationActivateFPFragment : BaseFragment<FragmentComposeBinding>(
    R.layout.fragment_compose
) {
    override val viewModel: IdentityValidationActivateFPViewModel by viewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.composeView.setContentScreen {
            IdentityValidationLoadingScreen(viewModel = viewModel)
        }
    }
}
