package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_cancel.confirmation

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.BLANK_SPACE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.card.OneAppSmallCardAlert
import sv.com.tarjetaone.presentation.compose.uicomponent.card.SimpleCardComponent
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer12
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f

@Composable
fun CardCancelConfirmationScreen(viewModel: CardCancelConfirmationViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    CardCancelConfirmationScreenContent(uiState = uiState, onEvent = viewModel::onEvent)
}

@Composable
fun CardCancelConfirmationScreenContent(
    uiState: CardCancelConfirmationUiState,
    onEvent: (CardCancelConfirmationUiEvent) -> Unit = {}
) {
    LaunchedEffect(Unit) {
        onEvent(CardCancelConfirmationUiEvent.OnStart)
    }
    ScreenWithTopAppBar(
        title = stringResource(R.string.cancel_main_card_title),
        onLeftButtonClick = {
            onEvent(CardCancelConfirmationUiEvent.OnBackClick)
        },
        onRightButtonClick = {
            onEvent(CardCancelConfirmationUiEvent.OnTwilioClick)
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = MaterialTheme.customDimens.dimen24),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            SimpleCardComponent(
                modifier = Modifier.fillMaxWidth(),
                contentPadding = PaddingValues(MaterialTheme.customDimens.dimen16)
            ) {
                Row(
                    modifier = Modifier
                        .wrapContentSize(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(
                        MaterialTheme.customDimens.dimen16,
                        Alignment.Start
                    )
                ) {
                    OneAppSmallCardAlert()
                    Column {
                        Text(
                            text = stringResource(
                                R.string.cancel_card_confirmation_mask,
                                uiState.cardMask
                            ),
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = buildAnnotatedString {
                                append(stringResource(R.string.cancel_main_card_expiration_date))
                                append(BLANK_SPACE)
                                withStyle(
                                    SpanStyle(
                                        color = MaterialTheme.customColors.onHeadingTextDark,
                                        fontWeight = FontWeight.SemiBold
                                    )
                                ) {
                                    append(uiState.expirationDate)
                                }
                            },
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
            Spacer1f()
            Text(
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                text = if (uiState.isMainCard) {
                    stringResource(R.string.cancel_main_card_disclaimer)
                } else {
                    stringResource(R.string.cancel_additional_card_disclaimer)
                },
                style = MaterialTheme.typography.titleMedium
            )
            Spacer16()
            OneButton(
                text = stringResource(R.string.cancel_main_card_not_now_button),
                buttonVariant = ButtonVariant.SECONDARY_VARIANT,
                modifier = Modifier.fillMaxWidth(),
                onClick = {
                    onEvent(CardCancelConfirmationUiEvent.OnNotKnowClick)
                }
            )
            Spacer12()
            OneButton(
                modifier = Modifier.fillMaxWidth(),
                text = if (uiState.isMainCard) {
                    stringResource(R.string.cancel_main_card_cancel_button)
                } else {
                    stringResource(R.string.cancel_additional_card_cancel_button)
                },
                onClick = {
                    onEvent(CardCancelConfirmationUiEvent.OnCardCancelClick)
                }
            )
            Spacer16()
        }
    }
}

@Preview
@Composable
fun CardCancelConfirmationContentScreenPreview() {
    OneAppTheme {
        val uiState = CardCancelConfirmationUiState(expirationDate = "12/24", cardMask = "1234")
        CardCancelConfirmationScreenContent(uiState = uiState)
    }
}