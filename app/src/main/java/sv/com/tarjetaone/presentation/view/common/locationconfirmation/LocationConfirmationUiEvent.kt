package sv.com.tarjetaone.presentation.view.common.locationconfirmation

sealed class LocationConfirmationUiEvent {
    data object OnStart : LocationConfirmationUiEvent()
    data object OnBackClick : LocationConfirmationUiEvent()
    data object OnTwilioClick : LocationConfirmationUiEvent()
    data object OnContinueClick : LocationConfirmationUiEvent()
    data class OnChangeAddressClick(val isModifyingAddress: Boolean) : LocationConfirmationUiEvent()
}
