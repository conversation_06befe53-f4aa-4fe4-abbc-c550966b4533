package sv.com.tarjetaone.presentation.view.lobby.menu.home.component

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.button.SolidLargeButton
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.view.lobby.menu.home.BiometricSheetType

@Composable
fun HomeBottomSheet(
    modifier: Modifier = Modifier,
    type: BiometricSheetType,
    onButtonClick: (BiometricSheetType) -> Unit,
    onSecondaryClick: () -> Unit
) {
    when (type) {
        BiometricSheetType.ACTIVATE -> {
            HomeBiometricSheet(
                icon = R.drawable.ic_security,
                title = stringResource(id = R.string.activate_biometry_title),
                message = stringResource(id = R.string.activate_biometry_message),
                textButton = stringResource(id = R.string.activate),
                onButtonClick = { onButtonClick(type) },
                onSecondaryButtonClick = onSecondaryClick,
                modifier = modifier
            )
        }
        BiometricSheetType.FAILED -> {
            HomeBiometricSheet(
                icon = R.drawable.exclamation_octagon,
                title = stringResource(id = R.string.failed_biometry_title),
                message = stringResource(id = R.string.failed_biometry_message),
                textButton = stringResource(id = R.string.retry),
                onButtonClick = { onButtonClick(type) },
                onSecondaryButtonClick = onSecondaryClick,
                modifier = modifier
            )
        }
        BiometricSheetType.SUCCESS -> {
            HomeBiometricSheet(
                icon = R.drawable.ic_success,
                title = stringResource(id = R.string.success_biometry_title),
                message = stringResource(id = R.string.success_biometry_message),
                textButton = stringResource(id = R.string.continue_button_label),
                showSecondaryButton = false,
                onButtonClick = { onButtonClick(type) },
                onSecondaryButtonClick = onSecondaryClick,
                modifier = modifier
            )
        }
    }
}

@Suppress("kotlin:S107")
@Composable
private fun HomeBiometricSheet(
    modifier: Modifier = Modifier,
    @DrawableRes icon: Int,
    title: String,
    message: String,
    textButton: String,
    showSecondaryButton: Boolean = true,
    onButtonClick: () -> Unit,
    onSecondaryButtonClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier.padding(MaterialTheme.customDimens.dimen16)
    ) {
        Spacer16()
        Image(
            imageVector = ImageVector.vectorResource(id = icon),
            contentDescription = null
        )
        Spacer16()
        Text(
            text = title,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.secondary,
            style = MaterialTheme.typography.bodyLarge.copy(
                lineHeight = MaterialTheme.customDimensSp.sp27,
                fontWeight = FontWeight.SemiBold
            ),
            modifier = Modifier.padding(horizontal = MaterialTheme.customDimens.dimen32)
        )
        Spacer8()
        Text(
            text = message,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.secondary,
            style = MaterialTheme.typography.bodyMedium
        )
        Spacer16()
        SolidLargeButton(
            text = textButton,
            onClick = onButtonClick
        )
        Spacer16()
        if (showSecondaryButton) {
            Text(
                text = stringResource(id = R.string.decline_activation),
                textDecoration = TextDecoration.Underline,
                color = MaterialTheme.colorScheme.outline,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.clickable(onClick = onSecondaryButtonClick)
            )
        }
    }
}

@Preview
@Composable
private fun HomeActivateBottomSheetPreview() {
    OneAppTheme {
        Surface(modifier = Modifier.fillMaxWidth()) {
            HomeBottomSheet(
                type = BiometricSheetType.ACTIVATE,
                onButtonClick = {},
                onSecondaryClick = {},
            )
        }
    }
}

@Preview
@Composable
private fun HomeFailedBottomSheetPreview() {
    OneAppTheme {
        Surface(modifier = Modifier.fillMaxWidth()) {
            HomeBottomSheet(
                type = BiometricSheetType.FAILED,
                onButtonClick = {},
                onSecondaryClick = {},
            )
        }
    }
}

@Preview
@Composable
private fun HomeSuccessBottomSheetPreview() {
    OneAppTheme {
        Surface(modifier = Modifier.fillMaxWidth()) {
            HomeBottomSheet(
                type = BiometricSheetType.SUCCESS,
                onButtonClick = {},
                onSecondaryClick = {},
            )
        }
    }
}
