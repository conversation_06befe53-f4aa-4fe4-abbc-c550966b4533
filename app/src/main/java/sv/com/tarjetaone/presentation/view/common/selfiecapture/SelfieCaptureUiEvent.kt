package sv.com.tarjetaone.presentation.view.common.selfiecapture

import com.facephi.fphiwidgetcore.WidgetResult

sealed class SelfieCaptureUiEvent {
    data object OnStart: SelfieCaptureUiEvent()
    data object OnBackClick : SelfieCaptureUiEvent()
    data object OnTwilioClick : SelfieCaptureUiEvent()
    data object OnShowTutorialClick : SelfieCaptureUiEvent()
    data class OnContinueClick(val requestPermission: () -> Unit) : SelfieCaptureUiEvent()
    data class OnCameraPermissionDenied(val showRationale: Boolean) : SelfieCaptureUiEvent()
    data class OnSelfieCaptured(val result: WidgetResult) : SelfieCaptureUiEvent()
    data class OnSelfieCaptureFailed(val result: WidgetResult) : SelfieCaptureUiEvent()
}
