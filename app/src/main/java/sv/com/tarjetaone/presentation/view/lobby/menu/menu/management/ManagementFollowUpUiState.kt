package sv.com.tarjetaone.presentation.view.lobby.menu.menu.management

import androidx.compose.runtime.Stable
import androidx.paging.PagingData
import kotlinx.coroutines.flow.Flow
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_STRING
import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI
import sv.com.tarjetaone.domain.entities.response.LogManagementItemsCollectionUI
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.settings.activity_log.ActivityLogDateRange

@Stable
data class ManagementFollowUpUiState(
    val managementPagingData: Flow<PagingData<LogManagementItemsCollectionUI>>? = null,
    val requestStatusList: List<CatalogItemsCollectionUI> = listOf(),
    val selectedRequestStatus: CatalogItemsCollectionUI = CatalogItemsCollectionUI(
        ZERO_STRING,
        false,
        EMPTY_STRING,
        EMPTY_STRING,
        EMPTY_STRING
    ),
    val dateRange: ActivityLogDateRange = ActivityLogDateRange.LatestWeek
)
