package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_cancel.survey.start

import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.usecases.survey.GetSurveyByNameUseCase
import sv.com.tarjetaone.domain.usecases.survey.SaveSurveyUseCase
import sv.com.tarjetaone.presentation.view.common.survey.row.BaseSurveyRowViewModel
import javax.inject.Inject

@HiltViewModel
class CardCancelSurveyViewModel @Inject constructor(
    getSurveyByNameUseCase: GetSurveyByNameUseCase,
    saveSurveyUseCase: SaveSurveyUseCase,
) : BaseSurveyRowViewModel(getSurveyByNameUseCase, saveSurveyUseCase) {
    override fun onSurveySent() {
        sendEvent(
            UiEvent.Navigate(
                CardCancelSurveyFragmentDirections
                    .actionCancelCardSurveyFragmentToFinishCancelCardSurveyFragment()
            )
        )
    }
}