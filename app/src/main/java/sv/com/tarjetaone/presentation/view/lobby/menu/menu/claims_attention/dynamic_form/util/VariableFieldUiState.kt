package sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.dynamic_form.util

import sv.com.tarjetaone.domain.entities.response.CatalogsUI
import sv.com.tarjetaone.domain.entities.response.FieldDataType
import sv.com.tarjetaone.domain.entities.response.FieldType
import sv.com.tarjetaone.domain.entities.response.VariableFieldUI
import sv.com.tarjetaone.presentation.helpers.UiText

data class VariableFieldUiState(
    val variableField: VariableFieldUI = VariableFieldUI(),
    val typedValue: String = "",
    val selectedValue: CatalogsUI? = null,
    val hasError: Boolean = false,
    val errorMessage: UiText = UiText.DynamicString("")
)

object VariableFieldDummyUiStates {
    val selectableMonth = VariableFieldUiState(
        variableField = VariableFieldUI(
            fieldType = FieldType.SelectedValue,
            manAttributeTypeNameApp = "Mes de cuadratura",
            catalogs = listOf(
                CatalogsUI(fieldTypeCatalogName = "Enero"),
                CatalogsUI(fieldTypeCatalogName = "Febrero"),
                CatalogsUI(fieldTypeCatalogName = "Marzo"),
                CatalogsUI(fieldTypeCatalogName = "Abril")
            )
        ),
        selectedValue = CatalogsUI(fieldTypeCatalogName = "Enero")
    )

    val typedEmail = VariableFieldUiState(
        variableField = VariableFieldUI(
            fieldType = FieldType.TypedValue,
            manAttributeTypeNameApp = "Correo electrónico",
            dataTypeCode = "EMAIL",
            dataType = FieldDataType.Email,
            placeholder = "<EMAIL>"
        )
    )
}
