package sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.dynamic_form

import androidx.compose.runtime.toMutableStateList
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.entities.request.ManagementDefClaimsRequestUI
import sv.com.tarjetaone.domain.entities.response.CatalogsUI
import sv.com.tarjetaone.domain.usecases.claims.GetManagementDefUseCase
import sv.com.tarjetaone.domain.usecases.claims.SendClaimUseCase
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.dynamic_form.util.VariableFieldUiState
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.dynamic_form.util.isValidInput
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.dynamic_form.util.toVariableDefUI
import javax.inject.Inject

@HiltViewModel
class ClaimDynamicFormViewModel @Inject constructor(
    private val getManDefUseCase: GetManagementDefUseCase,
    private val sendClaimUseCase: SendClaimUseCase,
    savedStateHandle: SavedStateHandle,
) : BaseViewModel() {
    private val args = ClaimDynamicFormFragmentArgs.fromSavedStateHandle(savedStateHandle)

    private val _uiState = MutableStateFlow(ClaimDynamicFormUiState())
    val uiState = _uiState.asStateFlow()

    // Stateless
    private var isPriority: Boolean = false

    private fun getManagementDef() {
        sendEvent(SideEffect.Loading(true))
        viewModelScope.launch {
            getManDefUseCase(args.claimOptionId).executeUseCase { response ->
                sendEvent(SideEffect.Loading(false))
                if (response.statusResponse?.responseStatus?.code == AppConstants.SUCCESS_RESPONSE_CODE) {
                    isPriority = response.data.isHighPriority ?: false
                    _uiState.update { state ->
                        state.copy(
                            claimOptionName = response.data.manTypeNameApp ?: args.claimOptionName.orEmpty(),
                            variableFields = response.data.variableFields.map {
                                VariableFieldUiState(
                                    variableField = it,
                                    selectedValue = it.catalogs.firstOrNull()
                                )
                            }.toMutableStateList(),
                            usageInfo = response.data.usageInformation
                        )
                    }
                } else {
                    showUpsErrorMessage(false) { sendEvent(UiEvent.NavigateBack) }
                }
            }
        }
    }

    private fun onDescriptionChange(description: String) {
        _uiState.update { it.copy(claimDescription = description) }
        validateCanSend()
    }

    private fun onVariableFieldChange(
        index: Int,
        typedValue: String,
        selectedValue: CatalogsUI?
    ) {
        _uiState.value.variableFields.apply {
            this[index] = this[index].copy(
                typedValue = typedValue,
                selectedValue = selectedValue
            )
        }
        validateCanSend()
    }

    private fun validateCanSend() {
        _uiState.update { state ->
            val validDesc = state.claimDescription.isNotBlank()
            val validFields = state.variableFields.all { it.isValidInput() }
            state.copy(canSend = validDesc && validFields)
        }
    }

    private fun onSendClick() {
        val request = with(_uiState.value) {
            ManagementDefClaimsRequestUI(
                cCardId = baseSharedPrefs.mainCard?.creditCardId,
                manTypeId = args.claimOptionId,
                description = claimDescription,
                variableFields = variableFields.map { it.toVariableDefUI() }
            )
        }
        sendEvent(SideEffect.Loading(true))
        viewModelScope.launch {
            sendClaimUseCase(request).executeUseCase { response ->
                sendEvent(SideEffect.Loading(false))
                if (response.statusResponse?.responseStatus?.code == AppConstants.SUCCESS_RESPONSE_CODE) {
                    sendEvent(
                        UiEvent.Navigate(
                            ClaimDynamicFormFragmentDirections
                                .actionClaimDynamicFormFragmentToClaimSentFragment(
                                    claimDetail = response.unrecognizedPurchaseReport?.management,
                                    isPriority = isPriority
                                )
                        )
                    )
                } else {
                    showSimpleError(response.statusResponse?.responseStatus?.message.orEmpty())
                }
            }
        }
    }

    fun onEvent(event: ClaimDynamicFormUiEvent) {
        when (event) {
            ClaimDynamicFormUiEvent.OnStart -> getManagementDef()
            ClaimDynamicFormUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            ClaimDynamicFormUiEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
            is ClaimDynamicFormUiEvent.OnDescriptionChange -> onDescriptionChange(event.description)
            is ClaimDynamicFormUiEvent.OnVariableFieldChange -> onVariableFieldChange(
                event.index,
                event.typedValue,
                event.selectedValue
            )
            ClaimDynamicFormUiEvent.OnSendClick -> onSendClick()
            ClaimDynamicFormUiEvent.OnCancelClick -> sendEvent(UiEvent.NavigateBack)
        }
    }
}
