package sv.com.tarjetaone.presentation.view.lobby.menu.home.skeleton

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.ButtonSize
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.util.Spacer16

@Composable
fun HomeNoContentState(
    modifier: Modifier = Modifier,
    onLoadAgain: () -> Unit,
) {
    Column(
        modifier = modifier
            .padding(horizontal = MaterialTheme.customDimens.dimen16),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Image(
            modifier = Modifier.size(MaterialTheme.customDimens.dimen98),
            imageVector = ImageVector.vectorResource(R.drawable.ic_circle_one_icon),
            contentDescription = null,
        )
        Spacer16()
        Text(
            text = stringResource(id = R.string.unable_load_content_title),
            style = MaterialTheme.typography.bodyLarge.copy(
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.customColors.gray700,
            ),
        )
        Text(
            text = stringResource(id = R.string.unable_load_content_description),
            style = MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.customColors.gray700,
            ),
        )
        Spacer16()
        OneButton(
            text = stringResource(id = R.string.load_again_button_label),
            onClick = onLoadAgain,
            size = ButtonSize.MEDIUM,
        )
    }
}

@Preview(
    showBackground = true,
)
@Composable
fun HomeNoContentStatePreview() {
    OneAppTheme {
        Column(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.Center
        ) {
            HomeNoContentState(
                modifier = Modifier.fillMaxWidth(),
                onLoadAgain = { },
            )
        }
    }
}
