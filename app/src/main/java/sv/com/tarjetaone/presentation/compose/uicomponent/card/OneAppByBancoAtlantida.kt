package sv.com.tarjetaone.presentation.compose.uicomponent.card

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.Spacer4

@Composable
fun OneAppByBancoAtlantida(
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.Bottom
    ) {
        Image(
            modifier = Modifier.size(
                width = MaterialTheme.customDimens.dimen30,
                height = MaterialTheme.customDimens.dimen40
            ),
            imageVector = ImageVector.vectorResource(id = R.drawable.ic_icon_one),
            contentDescription = stringResource(id = R.string.app_name),
        )
        Spacer4()
        Text(
            text = stringResource(id = R.string.by),
            style = MaterialTheme.typography.headlineSmall.copy(
                color = MaterialTheme.colorScheme.onPrimary
            )
        )
        Spacer4()
        Image(
            modifier = Modifier.size(
                width = MaterialTheme.customDimens.dimen110,
                height = MaterialTheme.customDimens.dimen25
            ),
            imageVector = ImageVector.vectorResource(id = R.drawable.ic_atlantida_logo),
            contentDescription = stringResource(id = R.string.app_name),
        )
    }
}

@Preview
@Composable
fun OneAppByBancoAtlantidaPreview() {
    OneAppByBancoAtlantida()
}

