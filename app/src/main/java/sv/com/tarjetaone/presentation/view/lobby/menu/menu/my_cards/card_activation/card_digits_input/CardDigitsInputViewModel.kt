package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_activation.card_digits_input

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.analytics.amplitude.AmplitudeEvents
import sv.com.tarjetaone.analytics.amplitude.AmplitudeManager
import sv.com.tarjetaone.common.utils.CardColor
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.data.api.models.Colors
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.usecases.general.GetCardColorUseCase
import sv.com.tarjetaone.domain.utils.onApiError
import sv.com.tarjetaone.domain.utils.onNetworkError
import sv.com.tarjetaone.domain.utils.onSuccess
import javax.inject.Inject
import kotlin.reflect.javaType
import kotlin.reflect.typeOf

@HiltViewModel
class CardDigitsInputViewModel @Inject constructor(
    private val getCardColorUseCase: GetCardColorUseCase,
    private val sharedPrefs: SecureSharedPreferencesRepository,
    private val amplitudeManager: AmplitudeManager,
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(ConfirmActivateCardUiState())
    val uiState = _uiState.asStateFlow()

    private var args = ConfirmActivateCardFragmentArgs.fromSavedStateHandle(savedStateHandle)

    private fun onDigitsChanged(digits: String) {
        _uiState.update { state ->
            state.copy(
                cardDigits = digits.filter { it.isDigit() },
                areDigitsValid = digits.length == DIGITS_LENGTH
            )
        }
    }

    //TODO: Experimental Api is used as a workaround to use typeOf<List<Colors>>().javaType
    @OptIn(ExperimentalStdlibApi::class)
    fun getCardColor() {
        args?.cardId?.let { cardId ->
            sendEvent(UiEvent.Loading(true))
            viewModelScope.launch {
                getCardColorUseCase(
                    customerId = sharedPrefs.getCustomerId().orZero(),
                    creditCardId = cardId
                ).onSuccess { response ->
                    val colors: List<Colors> = response.data?.cardColor?.let {
                        val wrappedJson = "[${response.data?.cardColor}]"
                        val type = typeOf<List<Colors>>().javaType
                        Gson().fromJson(wrappedJson, type)
                    } ?: listOf(Colors(color = DEFAULT_CARD_COLOR, percentage = 100f))
                    _uiState.update { oldState ->
                        oldState.copy(
                            cardColor = CardColor(
                                colors = colors.map { it.color },
                                textColor = response.data?.textColor ?: DEFAULT_TEXT_COLOR,
                                iconColor = response.data?.visaColor ?: DEFAULT_TEXT_COLOR
                            ),
                            nameOnCard = response.data?.nameOnCard.orEmpty(),
                        )
                    }
                    sendEvent(UiEvent.Loading(false))
                }.onApiError { _, _, _, _ ->
                    showUpsErrorMessage(isDismissible = false) {
                        sendEvent(UiEvent.NavigateBack)
                    }
                }.onNetworkError { _ ->
                    showUpsErrorMessage(isDismissible = false) {
                        sendEvent(UiEvent.NavigateBack)
                    }
                }
            }
        }
    }

    private fun onContinueButtonClicked() {
        amplitudeManager.track(AmplitudeEvents.ON_CHECK_ACTIVATION_DIGITS_EVENT)
        sendEvent(
            UiEvent.Navigate(
                ConfirmActivateCardFragmentDirections
                    .actionAlmostThereActivateCardToAlmostThereActivateCard(
                        cardId = args.cardId.orZero(),
                        biometryId = args.biometryId.orZero(),
                        lastCardDigits = _uiState.value.cardDigits
                    )
            )
        )
    }

    fun onEvent(event: ConfirmActivateCardEvent) {
        when (event) {
            is ConfirmActivateCardEvent.OnDigitsChanged -> onDigitsChanged(event.digits)
            is ConfirmActivateCardEvent.OnContinueButtonClicked -> onContinueButtonClicked()
            is ConfirmActivateCardEvent.OnTwilioIconClicked -> sendEvent(UiEvent.TwilioClick)
            ConfirmActivateCardEvent.OnStart -> getCardColor()
        }
    }

    companion object {
        const val DIGITS_LENGTH = 6
        const val DEFAULT_CARD_COLOR = "#EEEEEE"
        const val DEFAULT_TEXT_COLOR = "#4F4D4D"
    }
}
