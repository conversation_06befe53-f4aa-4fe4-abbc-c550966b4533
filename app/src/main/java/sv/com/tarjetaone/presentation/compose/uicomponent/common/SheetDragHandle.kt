package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens

@Composable
fun SheetDragHandle(
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.customColors.gray300,
) {
    Box(
        modifier = modifier
            .padding(vertical = MaterialTheme.customDimens.dimen8)
            .height(MaterialTheme.customDimens.dimen8)
            .width(MaterialTheme.customDimens.dimen64)
            .clip(CircleShape)
            .background(color)
    )
}

@Preview
@Composable
private fun SheetDragHandlePreview() {
    OneAppTheme {
        SheetDragHandle()
    }
}
