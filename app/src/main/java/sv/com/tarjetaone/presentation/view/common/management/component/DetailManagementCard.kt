package sv.com.tarjetaone.presentation.view.common.management.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.button.ShareVoucherButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.TimelineMode
import sv.com.tarjetaone.presentation.compose.uicomponent.common.TimelineNode
import sv.com.tarjetaone.presentation.compose.uicomponent.common.TimelineNodePosition
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.common.management.DetailManagementUiState
import sv.com.tarjetaone.presentation.view.common.management.ManagementAttributesUiModel
import sv.com.tarjetaone.presentation.view.utils.ManAttributeType

@Composable
fun DetailManagementCard(
    modifier: Modifier = Modifier,
    uiState: DetailManagementUiState,
    onShareVoucherButtonClick: () -> Unit
) {
    Card(
        modifier = modifier
            .fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.onPrimaryContainer
        ),
        shape = RoundedCornerShape(MaterialTheme.customDimens.dimen30)
    ) {
        Column(
            modifier = modifier
                .padding(
                    top = MaterialTheme.customDimens.dimen32,
                    start = MaterialTheme.customDimens.dimen32,
                    end = MaterialTheme.customDimens.dimen32,
                    bottom = MaterialTheme.customDimens.dimen16
                ),
            verticalArrangement = Arrangement.spacedBy(
                space = MaterialTheme.customDimens.dimen8
            )
        ) {
            DetailManagementGeneralInfoSection(
                managementName = uiState.managementName,
                customerName = uiState.customerName,
                creditCardNumber = uiState.creditCardNumber
            )
            DetailManagementTimelineSection(
                showTimeline = uiState.showTimeline,
                requestStartDate = uiState.requestStartDate,
                requestEndDate = uiState.requestEndDate
            )
            DetailManagementExtraInfoSection(
                creditCardType = uiState.creditCardType,
                managementAttributes = uiState.managementAttributes,
                description = uiState.description,
                descriptionResource = uiState.descriptionResource
            )
            uiState.resolutionDays
                .takeIf { uiState.managementStatus.equals(IN_PROCESS_STATUS, true) }
                ?.let { ManagementResolutionTime(it) }
            if (uiState.isCapturingVoucherView.not()) {
                ShareVoucherButton(
                    modifier = Modifier.align(Alignment.CenterHorizontally),
                    onClick = onShareVoucherButtonClick
                )
            }
        }
    }
}

@Composable
private fun DetailManagementGeneralInfoSection(
    managementName: String,
    customerName: String,
    creditCardNumber: String
) {
    DetailManagementCardItem(
        title = stringResource(id = R.string.name_of_request),
        description = managementName
    )
    HorizontalDivider(
        color = MaterialTheme.customColors.gray200,
        thickness = MaterialTheme.customDimens.dimen1
    )
    DetailManagementCardItem(
        title = stringResource(id = R.string.client_name),
        description = customerName
    )
    HorizontalDivider(
        color = MaterialTheme.customColors.gray200,
        thickness = MaterialTheme.customDimens.dimen1
    )
    if (creditCardNumber.isNotEmpty()) {
        DetailManagementCardItem(
            title = stringResource(id = R.string.credit_card),
            description = stringResource(id = R.string.credit_card_number_visa, creditCardNumber)
        )
        HorizontalDivider(
            color = MaterialTheme.customColors.gray200,
            thickness = MaterialTheme.customDimens.dimen1
        )
    }
}

@Composable
private fun DetailManagementTimelineSection(
    showTimeline: Boolean,
    requestStartDate: String,
    requestEndDate: String
) {
    if (showTimeline) {
        Column {
            TimelineNode(
                position = TimelineNodePosition.START,
                timelineNodeMode = if (requestEndDate.isNotEmpty()) {
                    TimelineMode.START_END_DATES
                } else {
                    TimelineMode.START_DATE_ONLY
                }
            ) { modifier ->
                DetailManagementCardItem(
                    modifier = modifier.padding(
                        bottom = if (requestEndDate.isNotEmpty()) {
                            MaterialTheme.customDimens.dimen32
                        } else {
                            MaterialTheme.customDimens.dimen8
                        }
                    ),
                    title = stringResource(id = R.string.request_date),
                    description = requestStartDate,
                )
            }
            if (requestEndDate.isNotEmpty()) {
                TimelineNode(position = TimelineNodePosition.LAST) { modifier ->
                    DetailManagementCardItem(
                        modifier = modifier.padding(bottom = MaterialTheme.customDimens.dimen8),
                        title = stringResource(id = R.string.ending_application_label),
                        description = requestEndDate
                    )
                }
            }
            HorizontalDivider(
                color = MaterialTheme.customColors.gray200,
                thickness = MaterialTheme.customDimens.dimen1
            )
        }
    }
}

@Composable
private fun DetailManagementExtraInfoSection(
    creditCardType: String,
    managementAttributes: List<ManagementAttributesUiModel>,
    description: String?,
    descriptionResource: Int?,
) {
    managementAttributes.forEach {
        DetailManagementCardItem(
            title = it.managementAttributeDisplayName.asString(),
            description = it.managementAttributeValue.asString(),
            descriptionStyle = getAttributeDescriptionTextStyle(it.managementAttributeType)
        )
        HorizontalDivider(
            color = MaterialTheme.customColors.gray200,
            thickness = MaterialTheme.customDimens.dimen1
        )
    }
    if (creditCardType.isNotEmpty()) {
        DetailManagementCardItem(
            title = stringResource(id = R.string.credit_card_type),
            description = creditCardType
        )
        HorizontalDivider(
            color = MaterialTheme.customColors.gray200,
            thickness = MaterialTheme.customDimens.dimen1
        )
    }
    description?.let {
        DetailManagementCardItem(
            title = stringResource(id = R.string.description_label),
            description = it
        )
        HorizontalDivider(
            color = MaterialTheme.customColors.gray200,
            thickness = MaterialTheme.customDimens.dimen1
        )
    }
    descriptionResource?.let {
        DetailManagementCardItem(
            title = stringResource(id = R.string.description_label),
            description = stringResource(it)
        )
        HorizontalDivider(
            color = MaterialTheme.customColors.gray200,
            thickness = MaterialTheme.customDimens.dimen1
        )
    }
}

/**
 * Returns the [TextStyle] to be used for the **description** of a given [ManAttributeType].
 *
 * This allows customizing the typography of the description based on the attribute type,
 * enabling specific visual emphasis for certain attributes.
 *
 * @param manAttributeType The type of management attribute for which to retrieve the description style.
 * @return A [TextStyle] to be applied to the description, or `null` if the attribute type is not handled.
 */
@Composable
private fun getAttributeDescriptionTextStyle(manAttributeType: ManAttributeType?) =
    MaterialTheme.typography.titleMedium
        .copy(fontSize = MaterialTheme.customDimensSp.sp24)
        .takeIf { manAttributeType == ManAttributeType.AmountPaid }

@Preview(showBackground = true)
@Composable
fun DetailManagementCardPreview() {
    OneAppTheme {
        DetailManagementCard(
            uiState = DetailManagementUiState(
                managementName = "Gestión de desactivación de protección",
                managementStatus = IN_PROCESS_STATUS,
                customerName = "Juan Pérez",
                creditCardNumber = "**** **** **** 1234",
                creditCardType = "Crédito Visa Titular",
                requestStartDate = "Lunes 21 de mayo, 11:52 PM",
                requestEndDate = "Lunes 21 de mayo, 11:52 PM",
                description = "Ya no necesito el programa de proteccion",
                resolutionDays = 7,
                secondaryButtonText = R.string.go_to_account,
                managementAttributes = listOf(
                    ManagementAttributesUiModel(
                        managementAttributeDisplayName = UiText.DynamicString("Cuota Mensual"),
                        managementAttributeValue = UiText.DynamicString("$1.99 + IVA")
                    )
                )
            ),
            onShareVoucherButtonClick = {}
        )
    }
}

private const val IN_PROCESS_STATUS = "En proceso"
