package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.contact_channel.successupdate

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.response.ContactType
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.OneBackHandler
import sv.com.tarjetaone.presentation.view.common.confirmation_gradient.SuccessGradientScreen

@Composable
fun SuccessUpdateContactScreen(viewModel: SuccessUpdateContactViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    OneBackHandler()
    SuccessGradientScreen(
        onButtonClick = { viewModel.onEvent(SuccessUpdateContactUiEvent.OnDoneClick) },
        message = stringResource(
            if (uiState.contactType == ContactType.Email) {
                R.string.email_updated_successfully
            } else {
                R.string.phone_updated_successfully
            }
        ),
        buttonText = stringResource(id = R.string.done_button)
    )
}
