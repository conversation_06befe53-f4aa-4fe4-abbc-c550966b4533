package sv.com.tarjetaone.presentation.view.common.locationcapture.helper

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import sv.com.tarjetaone.R

sealed class AddressAliasSuggestion(val value: String) {
    data object Home : AddressAliasSuggestion(HOME)
    data object Work : AddressAliasSuggestion(WORK)
    data object Other : AddressAliasSuggestion(OTHER)

    @Composable
    fun suggestionToString(): String = when (this) {
        Home -> stringResource(R.string.address_suggestion_home)
        Work -> stringResource(R.string.address_suggestion_work)
        else -> stringResource(R.string.address_suggestion_other)
    }

    companion object {
        /**
         * Returns an [AddressAliasSuggestion] based on the provided name.
         * If the name matches a predefined suggestion, it returns the corresponding object.
         * Otherwise, it returns [Other].
         */
        fun fromName(name: String): AddressAliasSuggestion = when (name) {
            HOME -> Home
            WORK -> Work
            else -> Other
        }
    }
}

private const val HOME = "Casa"
private const val WORK = "Trabajo"
private const val OTHER = "Otro"
