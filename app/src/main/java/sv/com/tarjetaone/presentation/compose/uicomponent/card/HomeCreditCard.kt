package sv.com.tarjetaone.presentation.compose.uicomponent.card

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.SIX_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.TWELVE_VALUE
import sv.com.tarjetaone.common.utils.CardColor
import sv.com.tarjetaone.domain.entities.response.CreditCardStatus
import sv.com.tarjetaone.domain.entities.response.ReplacementStatus
import sv.com.tarjetaone.presentation.compose.modifier.blur
import sv.com.tarjetaone.presentation.compose.modifier.conditional
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.button.HyperLinkTextButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.ResizableText
import sv.com.tarjetaone.presentation.compose.uicomponent.personalization.CardPersonalizationDummyData
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.compose.util.setBackgroundFromList
import sv.com.tarjetaone.presentation.compose.util.toColor
import sv.com.tarjetaone.presentation.view.utils.PreviewUtils.AVAILABLE_CREDIT
import sv.com.tarjetaone.presentation.view.utils.PreviewUtils.CARD_NUM_MASKED
import sv.com.tarjetaone.presentation.view.utils.PreviewUtils.HOLDER_NAME
import sv.com.tarjetaone.presentation.view.utils.PreviewUtils.USED_CREDIT

/**
 * Composable to display relevant info of a credit card in the home screen.
 */
@Suppress("kotlin:S107")
@Composable
fun HomeCreditCard(
    modifier: Modifier = Modifier,
    availableCredit: String,
    usedCredit: String?,
    cardNumMasked: String?,
    holderName: String? = null,
    cardColors: CardColor,
    isMainCard: Boolean,
    status: CreditCardStatus? = null,
    replacementStatus: ReplacementStatus? = null,
    isCardBlurEnabled: Boolean = true,
    onRequestCardClick: () -> Unit = {}
) {
    Box(
        modifier = modifier
            .size(MaterialTheme.customDimens.dimen230, MaterialTheme.customDimens.dimen285)
            .setBackgroundFromList(
                colors = if (status == CreditCardStatus.Blocked || status == CreditCardStatus.Close) {
                    listOf(MaterialTheme.customColors.gray500)
                } else {
                    cardColors.colors.map { it.toColor() }
                },
                shape = MaterialTheme.shapes.large
            )
    ) {
        when (status) {
            CreditCardStatus.Blocked,
            CreditCardStatus.Close -> {
                BlockedCardContent(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(MaterialTheme.customDimens.dimen18)
                        .align(Alignment.Center),
                    onRequestCardClick = onRequestCardClick,
                    status = status,
                    iconColor = cardColors.iconColor.toColor(),
                    replacementStatus = replacementStatus
                )
            }

            else -> {
                CardContent(
                    isHiddenIconVisible = isCardBlurEnabled,
                    availableCredit = availableCredit,
                    usedCredit = usedCredit,
                    cardNumMasked = cardNumMasked,
                    holderName = holderName,
                    cardColors = cardColors,
                    isMainCard = isMainCard,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(MaterialTheme.customDimens.dimen18)
                )
            }
        }
    }
}

@Composable
private fun CardContent(
    modifier: Modifier = Modifier,
    availableCredit: String,
    usedCredit: String?,
    cardNumMasked: String?,
    holderName: String?,
    cardColors: CardColor,
    isMainCard: Boolean,
    isHiddenIconVisible: Boolean
) {
    var isCreditBlurred by remember { mutableStateOf(false) }
    val textColor = cardColors.textColor.toColor()
    val iconColor = cardColors.iconColor.toColor()

    Column(modifier = modifier) {
        if (isHiddenIconVisible) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .align(Alignment.End)
                    .clickable { isCreditBlurred = !isCreditBlurred }
            ) {

                val (cardDataText, cardDataIcon) = getCardDataResources(isCreditBlurred)

                Text(
                    text = stringResource(cardDataText),
                    style = MaterialTheme.typography.labelMedium.copy(
                        lineHeight = MaterialTheme.customDimensSp.sp14
                    ),
                    color = textColor
                )
                Spacer8()
                Icon(
                    imageVector = ImageVector.vectorResource(id = cardDataIcon),
                    contentDescription = null,
                    tint = textColor,
                    modifier = Modifier.size(MaterialTheme.customDimens.dimen24)
                )
            }
        }
        Spacer24()
        Column {
            Text(
                text = stringResource(id = R.string.available_credit),
                style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.SemiBold),
                color = textColor
            )
            ResizableText(
                text = availableCredit,
                color = textColor,
                style = MaterialTheme.typography.displayLarge.copy(
                    fontSize = MaterialTheme.customDimensSp.sp40
                ),
                modifier = Modifier
                    .conditional(
                        condition = isCreditBlurred,
                        onTrue = { blur(TWELVE_VALUE) }
                    )
            )
        }
        if (isMainCard) {
            Column {
                Text(
                    text = stringResource(id = R.string.used),
                    style = MaterialTheme.typography.labelMedium.copy(fontWeight = FontWeight.SemiBold),
                    color = textColor
                )
                Text(
                    text = usedCredit ?: stringResource(id = R.string.hint_zero_dollars),
                    style = MaterialTheme.typography.headlineSmall,
                    color = textColor,
                    modifier = Modifier
                        .conditional(
                            condition = isCreditBlurred,
                            onTrue = { blur(SIX_VALUE) }
                        )
                )
            }
            Spacer1f()
        } else {
            Text(
                text = cardNumMasked.orEmpty(),
                style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.SemiBold),
                color = textColor
            )
            Spacer1f()
            ResizableText(
                text = holderName.orEmpty(),
                style = MaterialTheme.typography.headlineSmall,
                color = textColor,
                modifier = Modifier
                    .conditional(
                        condition = isCreditBlurred,
                        onTrue = { blur(SIX_VALUE) }
                    )
            )
            Spacer16()
        }
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Icon(
                imageVector = ImageVector.vectorResource(id = R.drawable.ic_atlantida_logo),
                contentDescription = null,
                tint = iconColor
            )
            Icon(
                imageVector = ImageVector.vectorResource(id = R.drawable.ic_visa_icon_2),
                contentDescription = null,
                tint = iconColor
            )
        }
    }
}

@Composable
private fun BlockedCardContent(
    modifier: Modifier = Modifier,
    status: CreditCardStatus?,
    replacementStatus: ReplacementStatus?,
    iconColor: Color?,
    onRequestCardClick: () -> Unit
) {
    Box(modifier = modifier) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.align(Alignment.Center)
        ) {
            Image(
                imageVector = ImageVector.vectorResource(id = replacementStatus.getCardIcon()),
                contentDescription = null
            )
            Spacer8()
            Text(
                text = stringResource(
                    id = if (status == CreditCardStatus.Blocked) {
                        replacementStatus.getCardTitle()
                    } else {
                        R.string.account_in_cancel_process_message
                    }
                ),
                style = MaterialTheme.typography.headlineSmall,
                color = Color.White,
                textAlign = TextAlign.Center
            )
            if (status == CreditCardStatus.Blocked) {
                HyperLinkTextButton(
                    text = stringResource(id = replacementStatus.getCardActionLabel()),
                    onClick = onRequestCardClick,
                    textStyle = MaterialTheme.typography.bodySmall.copy(
                        textDecoration = TextDecoration.Underline,
                        textAlign = TextAlign.Center,
                        color = Color.White
                    )
                )
            }
        }
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
        ) {
            Icon(
                imageVector = ImageVector.vectorResource(id = R.drawable.ic_atlantida_logo),
                contentDescription = null,
                tint = iconColor ?: Color.White
            )
            Icon(
                imageVector = ImageVector.vectorResource(id = R.drawable.ic_visa_icon_2),
                contentDescription = null,
                tint = iconColor ?: Color.White
            )
        }
    }
}

fun ReplacementStatus?.getCardIcon(): Int = when (this) {
    ReplacementStatus.InProgress -> R.drawable.file_arrow_down_fill
    ReplacementStatus.Delivered -> R.drawable.file_check_fill
    null -> R.drawable.ic_lock_fill
}

fun ReplacementStatus?.getCardTitle(): Int = when (this) {
    ReplacementStatus.InProgress -> R.string.card_replacement_in_progress_title
    ReplacementStatus.Delivered -> R.string.card_replacement_delivered_title
    null -> R.string.card_blocked
}

fun ReplacementStatus?.getCardActionLabel(): Int = when (this) {
    ReplacementStatus.InProgress -> R.string.card_replacement_in_progress_action_label
    ReplacementStatus.Delivered -> R.string.card_replacement_delivered_action_label
    null -> R.string.request_your_card
}

private fun getCardDataResources(isCreditBlurred: Boolean): Pair<Int, Int> {
    val cardDataText = if (isCreditBlurred) R.string.show_card_data else R.string.hide_card_data
    val cardDataIcon =
        if (isCreditBlurred) R.drawable.ic_hide_card_details else R.drawable.ic_visibility
    return Pair(cardDataText, cardDataIcon)
}

@Preview
@Composable
private fun HomeCreditCardPreview() {
    OneAppTheme {
        HomeCreditCard(
            availableCredit = AVAILABLE_CREDIT,
            usedCredit = USED_CREDIT,
            cardNumMasked = CARD_NUM_MASKED,
            holderName = HOLDER_NAME,
            isMainCard = true,
            cardColors = CardPersonalizationDummyData.colors.first()
        )
    }
}

@Preview
@Composable
private fun HomeAdditionalCreditCardPreview() {
    OneAppTheme {
        HomeCreditCard(
            availableCredit = AVAILABLE_CREDIT,
            usedCredit = USED_CREDIT,
            cardNumMasked = CARD_NUM_MASKED,
            holderName = HOLDER_NAME,
            isMainCard = false,
            cardColors = CardPersonalizationDummyData.colors.first()
        )
    }
}

@Preview
@Composable
private fun HomeBlockedCardPreview() {
    OneAppTheme {
        HomeCreditCard(
            availableCredit = AVAILABLE_CREDIT,
            usedCredit = USED_CREDIT,
            cardNumMasked = CARD_NUM_MASKED,
            holderName = HOLDER_NAME,
            isMainCard = false,
            status = CreditCardStatus.Blocked,
            cardColors = CardPersonalizationDummyData.colors.first()
        )
    }
}

@Preview
@Composable
private fun HomeBlockedInProgressCardPreview() {
    OneAppTheme {
        HomeCreditCard(
            availableCredit = AVAILABLE_CREDIT,
            usedCredit = USED_CREDIT,
            cardNumMasked = CARD_NUM_MASKED,
            holderName = HOLDER_NAME,
            isMainCard = false,
            status = CreditCardStatus.Blocked,
            replacementStatus = ReplacementStatus.InProgress,
            cardColors = CardPersonalizationDummyData.colors.first()
        )
    }
}

@Preview
@Composable
private fun HomeBlockedDeliveredCardPreview() {
    OneAppTheme {
        HomeCreditCard(
            availableCredit = AVAILABLE_CREDIT,
            usedCredit = USED_CREDIT,
            cardNumMasked = CARD_NUM_MASKED,
            holderName = HOLDER_NAME,
            isMainCard = false,
            status = CreditCardStatus.Blocked,
            replacementStatus = ReplacementStatus.Delivered,
            cardColors = CardPersonalizationDummyData.colors.first()
        )
    }
}
