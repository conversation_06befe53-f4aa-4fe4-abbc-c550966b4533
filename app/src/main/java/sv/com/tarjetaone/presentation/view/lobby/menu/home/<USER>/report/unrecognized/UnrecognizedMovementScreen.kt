package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.report.unrecognized

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.paging.LoadState
import androidx.paging.compose.collectAsLazyPagingItems
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.domain.entities.response.UnrecognizedTransactionUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleLoadingIndicator
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar

@Composable
fun UnrecognizedMovementScreen(
    viewModel: UnrecognizedMovementsViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    UnrecognizedMovementContent(
        uiState = uiState,
        onUiEvent = viewModel::onEvent,
        selectedList = viewModel.selectedItems
    )
}

@Composable
fun UnrecognizedMovementContent(
    uiState: UnrecognizedMovementsUIState,
    onUiEvent: (UnrecognizedMovementUiEvent) -> Unit,
    selectedList: SnapshotStateList<UnrecognizedTransactionUI>
) {
    val transactionItems = uiState.unrecognizedTransactionUIPaging?.collectAsLazyPagingItems()
    LaunchedEffect(Unit) {
        onUiEvent(UnrecognizedMovementUiEvent.OnStart)
    }
    ScreenWithTopAppBar(
        title = stringResource(id = R.string.unrecognized_purchase),
        onLeftButtonClick = { onUiEvent(UnrecognizedMovementUiEvent.OnBackClick) },
        onRightButtonClick = { onUiEvent(UnrecognizedMovementUiEvent.OnSupportClick) },
    ) {
        Column(modifier = Modifier.fillMaxSize()) {
            Text(
                textAlign = TextAlign.Center,
                text = stringResource(id = R.string.unrecognized_purchase_reported),
                style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.SemiBold),
                modifier = Modifier.fillMaxWidth()
            )

            uiState.selectedUnrecognizedTransactionUI?.let {
                CardSwitchableTransactionItem(
                    maskedCard = uiState.maskedCard,
                    transaction = it,
                    isSelected = null,
                    onTransactionAction = { _ -> },
                    modifier = Modifier
                        .padding(
                            horizontal = MaterialTheme.customDimens.dimen16,
                            vertical = MaterialTheme.customDimens.dimen5
                        )
                        .fillMaxWidth()
                )
            }
            Text(
                textAlign = TextAlign.Center,
                text = stringResource(id = R.string.unrecognized_purchase_multiple),
                style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.SemiBold),
                modifier = Modifier
                    .padding(vertical = MaterialTheme.customDimens.dimen16)
                    .fillMaxWidth()
            )

            when (transactionItems?.loadState?.refresh) {
                is LoadState.Loading -> SimpleLoadingIndicator(modifier = Modifier.fillMaxSize())
                is LoadState.Error -> {
                    onUiEvent(UnrecognizedMovementUiEvent.OnLoadingError)
                }

                else -> {
                    CardUnrecognizedMovementListContent(
                        modifier = Modifier.weight(ONE_FLOAT_VALUE),
                        maskedCard = uiState.maskedCard,
                        transactionItems = transactionItems,
                        selectedItems = selectedList,
                        onTransactionAction = { transaction ->
                            onUiEvent(UnrecognizedMovementUiEvent.OnTransactionClick(transaction))
                        },
                        onAppendError = { }
                    )
                }
            }

            OneButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        horizontal = MaterialTheme.customDimens.dimen24,
                        vertical = MaterialTheme.customDimens.dimen16
                    ),
                text = stringResource(id = R.string.continue_button_label),
                onClick = {
                    onUiEvent(UnrecognizedMovementUiEvent.OnContinueClick)
                },
            )
        }
    }
}

@Composable
@Preview
fun UnrecognizedMovementPreview() {
    OneAppTheme {
        UnrecognizedMovementContent(
            uiState = UnrecognizedMovementsUIState(
                selectedUnrecognizedTransactionUI = UnrecognizedTransactionUI(
                    referenceNumber = "123123123",
                    authorizationNumber = "123123123",
                    valueDate = "12",
                    amount = 12F,
                    description = "descripcion"
                )
            ),
            onUiEvent = {},
            selectedList = remember { mutableStateListOf() }
        )
    }
}
