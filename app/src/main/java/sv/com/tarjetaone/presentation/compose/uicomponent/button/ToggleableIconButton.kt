package sv.com.tarjetaone.presentation.compose.uicomponent.button

import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme

@Composable
fun ToggleableIconButton(
    modifier: Modifier = Modifier,
    @DrawableRes icon: Int,
    iconDescription: String?,
    checked: <PERSON>olean,
    onCheckedChange: (Boolean) -> Unit
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
            .sizeIn(minWidth = 46.dp, minHeight = 46.dp, maxWidth = 50.dp, maxHeight = 50.dp)
            .shadow(
                elevation = 2.dp,
                shape = MaterialTheme.shapes.small
            )
            .clip(MaterialTheme.shapes.small)
            .background(if (checked) MaterialTheme.colorScheme.primary else Color.White)
            .clickable { onCheckedChange(!checked) }
    ) {
        Icon(
            painter = painterResource(icon),
            contentDescription = iconDescription,
            tint = if (checked) Color.White else MaterialTheme.colorScheme.primary,
            modifier = Modifier
                .fillMaxSize()
                .padding(10.dp)
        )
    }
}

@Composable
@Preview
private fun ToggleableIconButtonPreview() {
    OneAppTheme {
        Column {
            ToggleableIconButton(
                icon = R.drawable.ic_search_white,
                iconDescription = null,
                checked = false,
                onCheckedChange = {},
                modifier = Modifier.size(46.dp)
            )
            ToggleableIconButton(
                icon = R.drawable.ic_search_white,
                iconDescription = null,
                checked = true,
                onCheckedChange = {},
                modifier = Modifier.size(46.dp)
            )
        }
    }
}
