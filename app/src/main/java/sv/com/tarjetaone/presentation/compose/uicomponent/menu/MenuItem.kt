package sv.com.tarjetaone.presentation.compose.uicomponent.menu

import androidx.annotation.DrawableRes
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow.Companion.Ellipsis
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.HALF_COLOR_TRANSPARENCY
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens

@Composable
fun MenuItem(
    modifier: Modifier = Modifier,
    @DrawableRes icon: Int,
    text: String,
    textColor: Color = Color.Unspecified,
    onClick: () -> Unit,
    displayArrow: Boolean = true
) {
    Column(
        modifier = modifier
            .clickable(onClick = onClick)
            .padding(horizontal = MaterialTheme.customDimens.dimen30)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .height(MaterialTheme.customDimens.dimen72)
        ) {
            Icon(
                painter = painterResource(id = icon),
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier
                    .padding(end = MaterialTheme.customDimens.dimen12)
            )
            Text(
                text = text,
                style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                color = textColor,
                maxLines = TWO_VALUE,
                overflow = Ellipsis,
                modifier = Modifier
                    .weight(ONE_FLOAT_VALUE)
                    .padding(horizontal = MaterialTheme.customDimens.dimen12)
            )
            if (displayArrow) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_arrow_right_stroke),
                    contentDescription = null,
                    modifier = Modifier.padding(start = MaterialTheme.customDimens.dimen12)
                )
            }
        }
        HorizontalDivider(
            thickness = 1.dp,
            color = MaterialTheme.customColors.tertiaryDark.copy(alpha = HALF_COLOR_TRANSPARENCY)
        )
    }
}

@Preview
@Composable
private fun MenuItemPreview() {
    OneAppTheme {
        Surface {
            MenuItem(
                icon = R.drawable.ic_folder_menu,
                text = "Menu option",
                onClick = {},
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}
