package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.employment_info

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.employment_info.component.EmploymentInfoItem

@Composable
fun EmploymentInfoOverviewScreen(
    viewModel: EmploymentInfoOverviewViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    EmploymentInfoOverviewContent(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@Composable
private fun EmploymentInfoOverviewContent(
    uiState: EmploymentInfoOverviewUiState,
    onEvent: (EmploymentInfoOverviewUiEvent) -> Unit,
) {
    LaunchedEffect(Unit) {
        onEvent(EmploymentInfoOverviewUiEvent.OnStart)
    }
    ScreenWithTopAppBar(
        title = stringResource(id = R.string.employment_info),
        onLeftButtonClick = { onEvent(EmploymentInfoOverviewUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(EmploymentInfoOverviewUiEvent.OnSupportClick) },
    ) {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(
                horizontal = MaterialTheme.customDimens.dimen48
            ),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen16)
        ) {
            items(uiState.employmentInfoList) { data ->
                EmploymentInfoItem(
                    employmentInfo = data,
                    onModifyPhoneNumberClick = { jobId, contactId, contactTypeCode ->
                        onEvent(
                            EmploymentInfoOverviewUiEvent.OnModifyEmploymentInfo(
                                jobId = jobId,
                                contactId = contactId,
                                contactTypeCode = contactTypeCode
                            )
                        )
                    }
                )
            }
            item {
                Spacer8()
                EmploymentInfoDisclaimer()
            }
        }
    }
}

@Composable
private fun EmploymentInfoDisclaimer(
    modifier: Modifier = Modifier
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
        modifier = modifier.padding(start = MaterialTheme.customDimens.dimen8)
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_info_2),
            contentDescription = null
        )
        Spacer8()
        Text(
            text = stringResource(id = R.string.employment_info_disclaimer_message),
            style = MaterialTheme.typography.labelMedium.copy(lineHeight = MaterialTheme.customDimensSp.sp14),
            color = MaterialTheme.customColors.disclaimer
        )
    }
}

@Preview
@Composable
private fun EmploymentInfoOverviewScreenPreview() {
    OneAppTheme {
        EmploymentInfoOverviewContent(
            uiState = EmploymentInfoOverviewUiState(
                employmentInfoList = remember {
                    mutableStateListOf(
                        EmploymentInfo(
                            incomeType = IncomeType.SALARIED.type,
                            fixedIncome = "$800",
                            variableIncome = "$800",
                            companyName = "TarjetaOne",
                            sinceDate = "Junio 2023",
                            hiringType = "Indefinido",
                            occupation = "Developer",
                            address = "Col. Escalón, San Salvador, San Salvador, Calle 4, Casa #4",
                            phoneNumber = "2245 4526"
                        ),
                        EmploymentInfo(
                            incomeType = "Arrendador",
                            fixedIncome = "$800",
                            variableIncome = "$800",
                            companyName = "TarjetaOne",
                            sinceDate = "Junio 2023",
                            hiringType = "Indefinido",
                            occupation = "Developer",
                            address = "Col. Escalón, San Salvador, San Salvador, Calle 4, Casa #4",
                            phoneNumber = "2245 4526"
                        )
                    )
                }
            ),
            onEvent = {}
        )
    }
}
