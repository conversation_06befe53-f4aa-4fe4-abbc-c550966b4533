package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component

import sv.com.tarjetaone.common.utils.AppConstants.BLANK_SPACE
import sv.com.tarjetaone.common.utils.AppConstants.DAY_MONTH_YEAR_WITH_SPACES
import sv.com.tarjetaone.common.utils.AppConstants.MONTH_DAY_YEAR_WITH_SLASH
import sv.com.tarjetaone.core.utils.extensions.configCurrencyWithFractions
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.core.utils.extensions.substringAfter
import sv.com.tarjetaone.domain.entities.response.PointsCTUI
import sv.com.tarjetaone.domain.entities.response.TransactionsUI
import sv.com.tarjetaone.presentation.helpers.BenefitType

fun TransactionsUI.getLastCardDigits(): String {
    return panMasked?.substringAfter(panMasked?.length.orZero() / 2)?.trim().orEmpty()
}

fun TransactionsUI.getCardNumberHidden(): String {
    return panMasked?.replaceBefore(BLANK_SPACE, CARD_NUMBER_REPLACEMENT).orEmpty()
}

fun TransactionsUI.getDateFormatted(format: String = DAY_MONTH_YEAR_WITH_SPACES): String {
    return valueDate?.getFormattedDateFromTo(MONTH_DAY_YEAR_WITH_SLASH, format).orEmpty()
}

fun TransactionsUI.getAmountFormatted(): String {
    return amount?.configCurrencyWithFractions().orEmpty()
}

fun TransactionsUI.displayNotEnoughPoints(userPoints: PointsCTUI): Boolean {
    // Validating if user has enough points is not necessary as BE does that already
    return pointsPaymentMessage == true && userPoints.isCashBack().not() && isDebitType()
}

fun TransactionsUI.displayPointsNeeded(userPoints: PointsCTUI): Boolean {
    return userPoints.isCashBack().not() && isDebitType()
}

fun TransactionsUI.isCreditType(): Boolean = type == TRANSACTION_TYPE_CREDIT
fun TransactionsUI.isDebitType(): Boolean = type == TRANSACTION_TYPE_DEBIT

fun PointsCTUI.isCashBack(): Boolean {
    return type == BenefitType.CASHBACK_BENEFIT.type
}

private const val TRANSACTION_TYPE_DEBIT = "D"
private const val TRANSACTION_TYPE_CREDIT = "C"
private const val CARD_NUMBER_REPLACEMENT = "****"
