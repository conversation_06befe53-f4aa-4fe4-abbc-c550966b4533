package sv.com.tarjetaone.presentation.view.common.locationcapture

import android.os.Build
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.BottomSheetScaffold
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.SheetValue
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.rememberBottomSheetScaffoldState
import androidx.compose.material3.rememberStandardBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.onPlaced
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.model.CameraPosition
import com.google.android.gms.maps.model.LatLngBounds
import com.google.maps.android.compose.CameraMoveStartedReason
import com.google.maps.android.compose.GoogleMap
import com.google.maps.android.compose.MapUiSettings
import com.google.maps.android.compose.Marker
import com.google.maps.android.compose.Polygon
import com.google.maps.android.compose.rememberCameraPositionState
import com.google.maps.android.compose.rememberMarkerState
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.onEach
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.DEFAULT_CAMERA_ZOOM
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.SAMSUNG_MANUFACTURER
import sv.com.tarjetaone.common.utils.AppConstants.SEARCH_QUERY_DEBOUNCE_TIMER
import sv.com.tarjetaone.common.utils.AppConstants.SEVENTY_FIVE_VALUE_PERCENT
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.core.utils.vectorToBitmap
import sv.com.tarjetaone.domain.entities.response.SuggestionsItemUi
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.ButtonSize
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.OneAppSnackBar
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SheetDragHandle
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.view.common.locationcapture.component.AddressInfoForm
import sv.com.tarjetaone.presentation.view.common.locationcapture.component.MapSearchDropdown

@Composable
fun LocationCaptureScreen(
    viewModel: LocationCaptureBaseViewModel,
    showExcludedZones: Boolean = true
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    LaunchedEffect(Unit) {
        viewModel.onEvent(
            LocationCaptureUiEvent.OnStart(showExcludedZones = showExcludedZones)
        )
    }
    SideEffectHandler(viewModel.sideEffects) {
        LocationCaptureScreen(
            uiState = uiState,
            onEvent = viewModel::onEvent
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LocationCaptureScreen(
    uiState: LocationCaptureUiState,
    onEvent: (LocationCaptureUiEvent) -> Unit
) {
    val scaffoldState = rememberBottomSheetScaffoldState(
        bottomSheetState = rememberStandardBottomSheetState(initialValue = SheetValue.Expanded)
    )
    var sheetHeight by remember { mutableIntStateOf(ZERO_VALUE) }
    var dragHandleHeight by remember { mutableIntStateOf(ZERO_VALUE) }
    BottomSheetScaffold(
        sheetPeekHeight = MaterialTheme.customDimens.dimenZero,
        sheetSwipeEnabled = false,
        sheetContainerColor = MaterialTheme.colorScheme.surface,
        scaffoldState = scaffoldState,
        sheetDragHandle = {
            SheetDragHandle(modifier = Modifier.onPlaced { dragHandleHeight = it.size.height })
        },
        sheetContent = {
            LocationCaptureSheetSection(
                uiState = uiState,
                onEvent = onEvent,
                onGloballyPositionedChange = { height ->
                    sheetHeight = (height / TWO_VALUE).run {
                        if (Build.MANUFACTURER.equals(SAMSUNG_MANUFACTURER, true)) {
                            // Samsung devices ignore the drag handle height
                            this + dragHandleHeight
                        } else this
                    }
                }
            )
        },
    ) {
        var mapBounds: LatLngBounds? by remember { mutableStateOf(null) }
        val cameraPositionState = rememberCameraPositionState {
            position = CameraPosition.fromLatLngZoom(position.target, DEFAULT_CAMERA_ZOOM)
        }

        val markerState = rememberMarkerState(position = uiState.location)
        LaunchedEffect(uiState.location) {
            markerState.position = uiState.location
            cameraPositionState.animate(
                update = CameraUpdateFactory.newCameraPosition(
                    CameraPosition.fromLatLngZoom(
                        uiState.location,
                        cameraPositionState.position.zoom
                    )
                )
            )
            mapBounds = cameraPositionState.projection?.visibleRegion?.latLngBounds
        }
        LaunchedEffect(cameraPositionState.cameraMoveStartedReason, cameraPositionState.isMoving) {
            if (cameraPositionState.cameraMoveStartedReason == CameraMoveStartedReason.GESTURE) {
                mapBounds = cameraPositionState.projection?.visibleRegion?.latLngBounds
            }
        }
        val visibleExcludedZones by remember(uiState.excludedZones, mapBounds) {
            derivedStateOf { uiState.filterVisibleExcludedZones(mapBounds) }
        }

        Box(modifier = Modifier.fillMaxSize()) {
            GoogleMap(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(uiState.getGoogleMapMaxHeightWeight())
                    .background(MaterialTheme.customColors.gray500),
                uiSettings = MapUiSettings(
                    compassEnabled = false,
                    mapToolbarEnabled = false,
                    myLocationButtonEnabled = false,
                    rotationGesturesEnabled = false,
                    zoomControlsEnabled = false,
                    tiltGesturesEnabled = false
                ),
                cameraPositionState = cameraPositionState,
                onMapClick = { onEvent(LocationCaptureUiEvent.OnMapClick(it)) },
                onPOIClick = { onEvent(LocationCaptureUiEvent.OnMapClick(it.latLng)) },
                contentPadding = uiState.getMapContentPadding(sheetHeight.dp),
                onMapLoaded = {
                    // Update map bounds only when the camera is stationary and projection is available
                    cameraPositionState.takeIf { !it.isMoving }
                        ?.projection
                        ?.visibleRegion
                        ?.latLngBounds
                        ?.let { mapBounds = it }
                }
            ) {
                Marker(
                    state = markerState,
                    draggable = false,
                    icon = LocalContext.current.vectorToBitmap(R.drawable.ic_location)
                )
                if (uiState.showExcludedZones) {
                    visibleExcludedZones.forEach { excludedZone ->
                        Polygon(
                            points = excludedZone,
                            fillColor = MaterialTheme.customColors.polygonRed,
                            strokeColor = MaterialTheme.customColors.polygonRed.copy(alpha = ONE_FLOAT_VALUE),
                            strokeWidth = ONE_FLOAT_VALUE,
                            geodesic = true
                        )
                    }
                }
            }

            if (!uiState.isAddingAddressInfo) {
                ExtraAddressInfoContent(
                    uiState = uiState,
                    onEvent = onEvent,
                    sheetHeight = sheetHeight + dragHandleHeight
                )
            }
        }
    }
}

@Composable
private fun ExtraAddressInfoContent(
    uiState: LocationCaptureUiState,
    onEvent: (LocationCaptureUiEvent) -> Unit,
    sheetHeight: Int
) {
    Column(
        modifier = Modifier.statusBarsPadding()
    ) {
        BackButton(
            modifier = Modifier.padding(MaterialTheme.customDimens.dimen16),
            onClick = { onEvent(LocationCaptureUiEvent.OnBackClick) }
        )
        SearchSection(
            searchQuery = uiState.searchQuery,
            onSearchQueryChange = {
                onEvent(LocationCaptureUiEvent.OnSearchQueryChange(it))
            },
            onPerformSearch = { onEvent(LocationCaptureUiEvent.OnPerformSearch(it)) },
            validLocation = uiState.validLocation,
            address = uiState.displayAddress,
            suggestions = uiState.searchSuggestions,
            onSuggestionClick = { onEvent(LocationCaptureUiEvent.OnSuggestionClick(it)) },
            snackBarVisibility = uiState.showSearchSnackBar,
            onCancelClick = { onEvent(LocationCaptureUiEvent.OnSnackBarVisibility(false)) },
            showSnackBarError = uiState.showSnackBarError,
            modifier = Modifier
                .fillMaxSize()
                .padding(
                    start = MaterialTheme.customDimens.dimen30,
                    end = MaterialTheme.customDimens.dimen30,
                    top = MaterialTheme.customDimens.dimen12,
                    bottom = sheetHeight.dp // To avoid the search section being covered by the bottom sheet
                )
        )
    }
}

@Composable
private fun BackButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
            .size(MaterialTheme.customDimens.dimen36)
            .clip(CircleShape)
            .background(MaterialTheme.colorScheme.secondary)
            .clickable(onClick = onClick)
    ) {
        Icon(
            imageVector = ImageVector.vectorResource(id = R.drawable.ic_back_icon),
            contentDescription = null,
            tint = MaterialTheme.colorScheme.onSecondary,
            modifier = Modifier.size(MaterialTheme.customDimens.dimen16)
        )
    }
}

@OptIn(FlowPreview::class)
@Suppress("kotlin:S107")
@Composable
private fun SearchSection(
    modifier: Modifier,
    searchQuery: String,
    onSearchQueryChange: (String) -> Unit,
    onPerformSearch: (String) -> Unit,
    suggestions: List<SuggestionsItemUi>,
    onSuggestionClick: (SuggestionsItemUi) -> Unit,
    validLocation: Boolean,
    snackBarVisibility: Boolean,
    onCancelClick: () -> Unit,
    showSnackBarError: Boolean,
    address: String,
) {
    val snackBarHostState = remember { SnackbarHostState() }
    LaunchedEffect(address, validLocation) {
        if (!validLocation && address.isNotBlank()) {
            snackBarHostState.showSnackbar(EMPTY_STRING, duration = SnackbarDuration.Indefinite)
        }
    }
    LaunchedEffect(searchQuery) {
        snapshotFlow { searchQuery }
            .debounce(SEARCH_QUERY_DEBOUNCE_TIMER)
            .filter { it.isNotBlank() }
            .onEach { onPerformSearch(it) }
            .collect()
    }
    Column(
        modifier = modifier
    ) {
        MapSearchDropdown(
            modifier = Modifier.fillMaxWidth(),
            items = suggestions,
            onSuggestionClick = onSuggestionClick,
            query = searchQuery,
            onQueryChange = onSearchQueryChange
        )
        Spacer16()
        AnimatedVisibility(snackBarVisibility || showSnackBarError) {
            val snackBarParameters = if (showSnackBarError) {
                LocationCaptureSnackBarParameters(
                    message = R.string.location_capture_snackbar_not_found,
                    containerColor = MaterialTheme.colorScheme.error,
                    icon = R.drawable.ic_exclamation_circle_white
                )
            } else {
                LocationCaptureSnackBarParameters(
                    message = R.string.location_pin_map_instruction,
                    containerColor = MaterialTheme.customColors.secondarySolid,
                    icon = R.drawable.ic_check_circle_white
                )
            }
            OneAppSnackBar(
                message = stringResource(snackBarParameters.message),
                messageStyle = MaterialTheme.typography.labelSmall,
                containerColor = snackBarParameters.containerColor,
                icon = snackBarParameters.icon,
                isDismissible = !showSnackBarError,
                modifier = Modifier.fillMaxWidth(),
                onCancelClick = { onCancelClick() }
            )
        }
        Spacer1f()
        SnackbarHost(
            hostState = snackBarHostState,
            modifier = Modifier.fillMaxWidth()
        ) {
            OneAppSnackBar(
                message = stringResource(id = R.string.zone_not_allow),
                containerColor = MaterialTheme.colorScheme.error,
                onCancelClick = { snackBarHostState.currentSnackbarData?.dismiss() },
                icon = R.drawable.ic_icon_close_circle,
                modifier = Modifier.fillMaxWidth()
            )
        }
        Spacer16()
    }
}

@Composable
private fun AddressSelectionSection(
    modifier: Modifier = Modifier,
    address: String,
    validAddress: Boolean,
    onSaveClick: () -> Unit
) {
    Column(
        modifier = modifier.padding(MaterialTheme.customDimens.dimen16),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(id = R.string.selected_address),
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.customColors.onDefaultSurface
        )
        Spacer8()
        Row(
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = address,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.customColors.onDefaultSurface,
                modifier = Modifier.weight(ONE_FLOAT_VALUE)
            )
            Spacer8()
            OneButton(
                text = stringResource(id = R.string.save_address),
                onClick = onSaveClick,
                enabled = validAddress,
                size = ButtonSize.MEDIUM
            )
        }
    }
}

@Composable
private fun LocationCaptureSheetSection(
    uiState: LocationCaptureUiState,
    onEvent: (LocationCaptureUiEvent) -> Unit,
    onGloballyPositionedChange: (Int) -> Unit = { },
) {
    AnimatedContent(
        targetState = uiState.isAddingAddressInfo,
        label = "bottom_sheet_content",
        transitionSpec = {
            (slideInVertically(initialOffsetY = { it }) + fadeIn()) togetherWith
                    (slideOutVertically(targetOffsetY = { it }) + fadeOut())
        }
    ) { showForm ->
        if (showForm) {
            AddressInfoForm(
                uiState = uiState,
                onEvent = onEvent,
                modifier = Modifier
                    .fillMaxHeight(SEVENTY_FIVE_VALUE_PERCENT)
                    .navigationBarsPadding()
            )
        } else {
            AddressSelectionSection(
                modifier = Modifier
                    .fillMaxWidth()
                    .onGloballyPositioned { onGloballyPositionedChange(it.size.height) }
                    .animateContentSize()
                    .navigationBarsPadding(),
                address = uiState.displayAddress,
                validAddress = uiState.validLocation,
                onSaveClick = { onEvent(LocationCaptureUiEvent.OnSaveClick) }
            )
        }
    }
}

@Preview
@Composable
private fun LocationCaptureScreenPreview() {
    OneAppTheme {
        LocationCaptureScreen(
            uiState = LocationCaptureUiState(
                displayAddress = "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
                isAddingAddressInfo = false
            ),
            onEvent = {}
        )
    }
}
