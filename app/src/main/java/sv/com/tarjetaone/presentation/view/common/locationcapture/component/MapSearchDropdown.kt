package sv.com.tarjetaone.presentation.view.common.locationcapture.component

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.toSize
import androidx.compose.ui.window.PopupProperties
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.response.SuggestionsItemUi
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.util.Spacer8

@Composable
fun MapSearchDropdown(
    modifier: Modifier = Modifier,
    items: List<SuggestionsItemUi>,
    onSuggestionClick: (SuggestionsItemUi) -> Unit,
    query: String,
    onQueryChange: (String) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }
    var dropDownSize by remember { mutableStateOf(Size.Zero) }
    val focusManager = LocalFocusManager.current

    BoxWithConstraints(
        modifier = modifier
    ) {
        MapSearchTextField(
            value = query,
            onValueChange = {
                onQueryChange(it)
                expanded = it.isNotBlank()
            },
            placeholder = stringResource(id = R.string.address_input_hint),
            modifier = Modifier
                .width(constraints.maxWidth.dp)
                .onGloballyPositioned { coords ->
                    dropDownSize = coords.size.toSize()
                }
        )
        DropdownMenu(
            expanded = expanded && items.isNotEmpty(),
            properties = PopupProperties(focusable = false),
            onDismissRequest = { expanded = false },
            containerColor = MaterialTheme.customColors.defaultSurface,
            shape = MaterialTheme.shapes.medium,
            modifier = Modifier.width(with(LocalDensity.current) { dropDownSize.width.toDp() })
        ) {
            items.forEach {
                DropdownMenuItem(
                    text = {
                        Text(
                            text = it.address,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.customColors.bodyLightGray,
                            fontWeight = FontWeight.Medium,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    },
                    onClick = {
                        expanded = false
                        onSuggestionClick(it)
                        focusManager.clearFocus()
                    },
                    modifier = Modifier
                        .padding(horizontal = MaterialTheme.customDimens.dimen8)
                        .clip(MaterialTheme.shapes.medium)
                )
            }
        }
    }
}

@Composable
private fun MapSearchTextField(
    modifier: Modifier = Modifier,
    value: String,
    onValueChange: (String) -> Unit,
    placeholder: String? = null,
    textStyle: TextStyle = MaterialTheme.typography.bodyLarge.copy(
        lineHeight = MaterialTheme.customDimensSp.sp22
    )
) {
    BasicTextField(
        modifier = modifier
            .heightIn(min = MaterialTheme.customDimens.dimen46)
            .padding(MaterialTheme.customDimens.dimen1)
            .border(
                width = MaterialTheme.customDimens.dimen1,
                color = MaterialTheme.customColors.lightBackground,
                shape = MaterialTheme.shapes.small
            )
            .background(Color.White, MaterialTheme.shapes.small),
        value = value,
        onValueChange = onValueChange,
        singleLine = true,
        cursorBrush = SolidColor(MaterialTheme.colorScheme.secondary),
        textStyle = textStyle.copy(color = MaterialTheme.colorScheme.secondary),
        decorationBox = { innerTextField ->
            Row(
                modifier = Modifier.padding(
                    horizontal = MaterialTheme.customDimens.dimen16,
                    vertical = MaterialTheme.customDimens.dimen8
                ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_search_white),
                    contentDescription = null,
                    tint = MaterialTheme.customColors.lightBackground,
                    modifier = Modifier.size(MaterialTheme.customDimens.dimen16)
                )
                Spacer8()
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .wrapContentHeight()
                ) {
                    if (value.isEmpty() && placeholder != null) {
                        Text(
                            text = placeholder,
                            style = textStyle.copy(color = MaterialTheme.customColors.disabledPlaceholder)
                        )
                    }
                    innerTextField()
                }
            }
        }
    )
}

@Preview
@Composable
private fun SimpleElevatedTextFieldPreview() {
    OneAppTheme {
        MapSearchDropdown(
            items = emptyList(),
            onSuggestionClick = {},
            query = "",
            onQueryChange = {},
            modifier = Modifier.fillMaxWidth()
        )
    }
}
