package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.address_location.home_address_list

import androidx.compose.runtime.toMutableStateList
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.AppConstants.HOME_ADDRESS_TYPE
import sv.com.tarjetaone.common.utils.AppConstants.SUCCESS_RESPONSE_CODE
import sv.com.tarjetaone.common.utils.extensions.capitalizeAllWords
import sv.com.tarjetaone.core.interfaces.LocationUtil
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.request.UserLocationUI
import sv.com.tarjetaone.domain.entities.response.HomeAddressesUI
import sv.com.tarjetaone.domain.usecases.personalData.address.DeleteCustomerAddressUseCase
import sv.com.tarjetaone.domain.usecases.personalData.address.GetCustomerAddressesUseCase
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.OneDialogParams
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.address_location.UserHomeAddressAction
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.openAppSettings
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.DialogAction
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.MessageParams
import javax.inject.Inject

@HiltViewModel
class AddresLocationViewModel @Inject constructor(
    private val getCustomerAddressesUseCase: GetCustomerAddressesUseCase,
    private val deleteCustomerAddressUseCase: DeleteCustomerAddressUseCase,
    private val sharedPrefs: SecureSharedPreferencesRepository,
    private val locationUtil: LocationUtil
) : BaseViewModel() {

    private val _uiState: MutableStateFlow<UserAddressesUiState> =
        MutableStateFlow(UserAddressesUiState())
    val uiState: StateFlow<UserAddressesUiState> = _uiState.asStateFlow()

    private var homeAddressesList: List<HomeAddressesUI> = emptyList()

    private fun onAddAddress() {
        locationUtil.getLastLocation()?.let {
            sendEvent(
                UiEvent.Navigate(
                    AddressLocationFragmentDirections.actionAddresLocationToNewAddressLocationCaptureFragment(
                        userLocation = UserLocationUI(
                            addressType = HOME_ADDRESS_TYPE,
                            latitude = it.first.toString(),
                            longitude = it.second.toString()
                        ),
                        action = UserHomeAddressAction.SAVE
                    )
                )
            )
        }
    }

    private fun onEditAddress(id: Int) {
        homeAddressesList.find { id == it.addressId.orZero() }?.let {
            sendEvent(
                UiEvent.Navigate(
                    AddressLocationFragmentDirections
                        .actionAddresLocationToNewAddressLocationCaptureFragment(
                            userLocation = UserLocationUI(
                                addressId = it.addressId,
                                addressType = HOME_ADDRESS_TYPE,
                                isMainAddress = it.isMainAddress,
                                alias = it.alias,
                                latitude = it.latitude,
                                longitude = it.longitude,
                            ),
                            action = UserHomeAddressAction.UPDATE
                        )
                )
            )
        }
    }

    private fun showOrHideDialog(show: Boolean) {
        _uiState.update { it.copy(showDeleteConfirmationDialog = show) }
    }

    private fun showOrHideSnackBar(show: Boolean) {
        _uiState.update { it.copy(showDeleteConfirmationSnackBar = show) }
    }

    private fun getUserAddresses() {
        sendEvent(SideEffect.Loading(true))
        viewModelScope.launch {
            getCustomerAddressesUseCase(
                sharedPrefs.getCustomerId().orZero()
            ).executeUseCase {
                val responseStatus = it.statusResponse?.responseStatus
                if (responseStatus?.code != SUCCESS_RESPONSE_CODE) {
                    showSimpleError(responseStatus?.message.orEmpty())
                    return@executeUseCase
                }

                homeAddressesList =
                    it.data.addresses // Store addresses to find object later based on id
                loadCustomerAddresses(it.data.maxActiveRows, homeAddressesList)
            }
        }
    }

    private fun loadCustomerAddresses(maxActiveAddress: Int, homeAddresses: List<HomeAddressesUI>) {
        val addresses = homeAddresses.map { address ->
            with(address) {
                UserAddress(
                    addressId = addressId.orZero(),
                    addressName = UiText.DynamicString(alias.orEmpty()),
                    fullAddress = UiText.DynamicString(
                        "$municipalityName, $departmentName, $fullAddres".capitalizeAllWords()
                    ),
                    latitude = latitude.orEmpty(),
                    longitude = longitude.orEmpty()
                )
            }
        }.reversed().toMutableStateList()

        _uiState.update {
            it.copy(
                addressList = addresses,
                maxAddressItems = maxActiveAddress
            )
        }

        sendEvent(SideEffect.Loading(false))
    }

    private fun deleteCustomerAddress(addressId: Int) {
        _uiState.update { it.copy(showDeleteConfirmationDialog = false) }
        sendEvent(SideEffect.Loading(true))
        viewModelScope.launch {
            deleteCustomerAddressUseCase(
                customerId = sharedPrefs.getCustomerId().orZero(), addressId = addressId
            ).executeUseCase { response ->
                sendEvent(SideEffect.Loading(false))

                val result = response.statusResponse?.responseStatus
                if (result?.code != SUCCESS_RESPONSE_CODE) {
                    showSimpleError(result?.message.orEmpty())
                    return@executeUseCase
                }

                // Remove address from list
                _uiState.value.addressList.apply {
                    removeAt(indexOfFirst { it.addressId == addressId })
                }

                showOrHideSnackBar(true)
            }
        }
    }

    private fun onLocationPermissionDenied(showRationale: Boolean) {
        showOneDialog(
            params = OneDialogParams(
                icon = R.drawable.ic_match_error,
                title = UiText.StringResource(R.string.location_access),
                message = MessageParams(
                    text = UiText.StringResource(R.string.location_dialog_description)
                ),
                primaryAction = DialogAction(
                    text = UiText.StringResource(
                        if (showRationale) R.string.accept_label else R.string.settings
                    ),
                    onClick = {
                        if (!showRationale) {
                            sendEvent(SideEffect.StartIntent(openAppSettings()))
                        }
                    }
                )
            )
        )
    }

    fun onEvent(event: UserAddressesUiEvent) {
        when (event) {
            is UserAddressesUiEvent.OnAddAddress -> onAddAddress()
            is UserAddressesUiEvent.OnEditAction -> onEditAddress(event.id)
            is UserAddressesUiEvent.OnDeleteAction -> {
                _uiState.update { it.copy(addressIdSelected = event.id) }
                showOrHideDialog(true)
            }
            is UserAddressesUiEvent.OnDialogDeleteButtonClick -> {
                deleteCustomerAddress(_uiState.value.addressIdSelected)
            }

            UserAddressesUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            UserAddressesUiEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
            UserAddressesUiEvent.OnDialogCancelClick -> showOrHideDialog(false)
            UserAddressesUiEvent.OnResetSnackbar -> showOrHideSnackBar(false)
            UserAddressesUiEvent.OnStart -> getUserAddresses()
            is UserAddressesUiEvent.OnLocationPermissionDenied -> onLocationPermissionDenied(
                event.showRationale
            )
        }
    }
}
