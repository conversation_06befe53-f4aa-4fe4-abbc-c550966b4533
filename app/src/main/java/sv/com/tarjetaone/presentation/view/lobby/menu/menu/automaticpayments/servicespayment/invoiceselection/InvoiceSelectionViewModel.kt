package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.invoiceselection

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.CommonDialogWithIconParams
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.invoiceselection.BalanceClientAutomaticResponseUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.invoiceselection.infoproducts.PXPaymentMethodUI
import sv.com.tarjetaone.domain.usecases.automaticpayments.servicespayment.invoiceselection.infoproducts.InfoProductsUseCase
import sv.com.tarjetaone.domain.utils.automaticpayments.servicespayment.invoiceselection.InfoProductListType
import sv.com.tarjetaone.domain.utils.automaticpayments.servicespayment.invoiceselection.ProductTypeCode
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.invoiceselection.util.toInvoiceList
import javax.inject.Inject

@HiltViewModel
class InvoiceSelectionViewModel @Inject constructor(
    private val infoProductsUseCase: InfoProductsUseCase,
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {

    private val args = InvoiceSelectionFragmentArgs.fromSavedStateHandle(savedStateHandle)

    // Stateless
    private var balanceClientResponse: BalanceClientAutomaticResponseUI = args.balanceClient

    private val _uiState = MutableStateFlow(InvoiceSelectionUiState())
    val uiState = _uiState.asStateFlow()

    private fun getInfoProducts() {
        if (_uiState.value.userProducts.isNotEmpty()) return // avoid fetching user's products again
        viewModelScope.launch {
            sendEvent(UiEvent.Loading(true))
            infoProductsUseCase(
                documentType = AppConstants.DUI,
                documentNumber = baseSharedPrefs.dui(),
                infoProductListType = InfoProductListType.ALL
            ).executeUseCase(
                onApiErrorAction = { _, _, _, _ ->
                    sendEvent(UiEvent.Loading(false))
                    showEmptyPaymentMethodsMessage()
                },
                onSuccessAction = { response ->
                    sendEvent(UiEvent.Loading(false))
                    val invalidPaymentMethods = response.none {
                        it.productTypeCode == ProductTypeCode.CreditCards || it.productTypeCode == ProductTypeCode.BankAccounts
                    }
                    if (response.isEmpty() || invalidPaymentMethods) {
                        showEmptyPaymentMethodsMessage()
                        return@executeUseCase
                    }
                    _uiState.update { state ->
                        state.copy(
                            userProducts = response,
                            userProductsTypes = response
                                .filter { it.productTypeCode != ProductTypeCode.Unknown }
                                .distinctBy { it.productTypeCode }
                                .map { it.productTypeCode }
                        )
                    }
                }
            )
        }
    }

    private fun showEmptyPaymentMethodsMessage() {
        sendEvent(
            UiEvent.ShowCommonDialog(
                CommonDialogWithIconParams(
                    isDismissible = false,
                    title = UiText.StringResource(R.string.payment_method_error_title),
                    message = UiText.StringResource(R.string.invalid_payment_method_message),
                    icon = R.drawable.ic_error_red_light,
                    buttonText = UiText.StringResource(R.string.back_to_my_account),
                    buttonColor = R.color.error_red,
                    onButtonClick = {
                        sendEvent(
                            UiEvent.NavigateBackTo(
                                R.id.servicePaymentHomeFragment,
                                inclusive = false
                            )
                        )
                    }
                )
            )
        )
    }

    private fun onStart() {
        if (_uiState.value.invoices.isNotEmpty()) return
        _uiState.update { state ->
            val invoices = balanceClientResponse.service.dataSets.data.toInvoiceList()
            state.copy(
                serviceName = balanceClientResponse.service.name,
                serviceCategoryName = args.serviceCategoryName,
                invoices = invoices,
                // If there is only one invoice, select it by default
                selectedInvoice = if (invoices.size == ONE_VALUE) ZERO_VALUE else null
            )
        }
        getInfoProducts()
    }

    private fun onFieldValueChanged(
        formKey: String,
        fieldIndex: Int,
        value: String
    ) {
        _uiState.value.invoices.firstOrNull { invoice ->
            invoice.formKey == formKey
        }?.updateValue(fieldIndex, value)
    }

    private fun onSelectInvoice(invoiceIndex: Int) {
        _uiState.update { state ->
            state.copy(selectedInvoice = invoiceIndex)
        }
    }

    private fun onSelectProductType(product: ProductTypeCode) {
        _uiState.update { state ->
            state.copy(
                selectedProductType = product,
                userProductsFiltered = state.userProducts.filterProductsByCode(product),
                selectedProduct = state.userProducts
                    .filterProductsByCode(product)
                    .firstOrNull()
            )
        }
    }

    private fun onSelectProduct(product: PXPaymentMethodUI) {
        _uiState.update { state ->
            state.copy(selectedProduct = product)
        }
    }

    private fun onContinueClick() {
        val selectedInvoiceIndex = _uiState.value.selectedInvoice ?: return
        val pxPaymentInformation = _uiState.value.toPxPaymentInformation(
            selectedInvoiceIndex = selectedInvoiceIndex,
            balanceClient = balanceClientResponse,
            isFromScanning = args.isFromScanning
        )
        sendEvent(
            UiEvent.Navigate(
                InvoiceSelectionFragmentDirections
                    .navigateToPxPaymentServicePrepareForPictureFragment(pxPaymentInformation)
            )
        )
    }

    fun onEvent(event: InvoiceSelectionUiEvent) {
        when (event) {
            is InvoiceSelectionUiEvent.OnStart -> onStart()
            is InvoiceSelectionUiEvent.OnFieldValueChanged -> onFieldValueChanged(
                event.formKey, event.fieldIndex, event.value
            )

            is InvoiceSelectionUiEvent.OnSelectInvoice -> onSelectInvoice(event.invoiceIndex)
            is InvoiceSelectionUiEvent.OnSelectProductType -> onSelectProductType(event.productType)
            is InvoiceSelectionUiEvent.OnSelectProduct -> onSelectProduct(event.product)
            is InvoiceSelectionUiEvent.OnContinueClick -> onContinueClick()
            is InvoiceSelectionUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            is InvoiceSelectionUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
        }
    }
}
