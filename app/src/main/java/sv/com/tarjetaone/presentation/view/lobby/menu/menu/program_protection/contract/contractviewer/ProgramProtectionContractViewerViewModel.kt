package sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.contract.contractviewer

import androidx.lifecycle.SavedStateHandle
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.presentation.view.common.contracts.viewer.BaseContractsViewerViewModel
import javax.inject.Inject

class ProgramProtectionContractViewerViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle
) : BaseContractsViewerViewModel() {

    private val args = ProgramProtectionContractsViewerFragmentArgs
        .fromSavedStateHandle(savedStateHandle)

    override fun onStart() {
        _uiState.update {
            it.copy(
                fileUri = args.fileUri,
                fileName = args.fileName
            )
        }
    }
}