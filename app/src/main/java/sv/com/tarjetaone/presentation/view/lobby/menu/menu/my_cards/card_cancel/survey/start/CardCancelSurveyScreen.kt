package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_cancel.survey.start

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.request.SurveyType.CANCEL_CARD_SURVEY
import sv.com.tarjetaone.domain.entities.response.OptionsSurveyUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.view.common.survey.row.SurveyRowTypeScreen
import sv.com.tarjetaone.presentation.view.common.survey.row.SurveyRowTypeUiEvent
import sv.com.tarjetaone.presentation.view.common.survey.row.SurveyRowTypeUiState

@Composable
fun CardCancelSurveyScreen(viewModel: CardCancelSurveyViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    SideEffectHandler(viewModel.sideEffects) {
        CardCancelSurveyScreenContent(
            uiState = uiState,
            onEvent = viewModel::onEvent
        )
    }
}

@Composable
fun CardCancelSurveyScreenContent(
    uiState: SurveyRowTypeUiState,
    onEvent: (SurveyRowTypeUiEvent) -> Unit = {}
) {
    LaunchedEffect(Unit) {
        onEvent(SurveyRowTypeUiEvent.OnStart(CANCEL_CARD_SURVEY))
    }
    ScreenWithTopAppBar(
        title = stringResource(R.string.survey_first_title),
        isLeftButtonVisible = false,
        onLeftButtonClick = {},
        onRightButtonClick = {
            onEvent(SurveyRowTypeUiEvent.OnTwilioClick)
        }
    ) {
        SurveyRowTypeScreen(
            uiState = uiState,
            onEvent = onEvent,
            actionButtonLabel = R.string.send_label
        )
    }
}

@Preview
@Composable
fun CardCancelSurveyScreenPreview() {
    OneAppTheme {
        val optionClosed = "OPTION_CLOSED"
        val selectedId = 6
        val uiState =
            SurveyRowTypeUiState(
                additionalComments = "",
                selectedOption = OptionsSurveyUI(
                    id = selectedId,
                    value = "Otro, ¿Cúal?",
                    type = "OPTION_OPEN"
                ),
                description = stringResource(R.string.survey_first_motivation_message),
                surveyOptions =
                    listOf(
                        OptionsSurveyUI(
                            id = 0,
                            value = "Ingreso de la información de registro",
                            type = optionClosed
                        ),
                        OptionsSurveyUI(
                            id = 1,
                            value = "Personalización de tarjeta",
                            type = optionClosed
                        ),
                        OptionsSurveyUI(
                            id = 2,
                            value = "Características de la oferta recibida",
                            type = optionClosed
                        ),
                        OptionsSurveyUI(
                            id = 3,
                            value = "Entrega de tarjeta",
                            type = optionClosed
                        ),
                        OptionsSurveyUI(
                            id = 4,
                            value = "Firma de contrato",
                            type = optionClosed
                        ),
                        OptionsSurveyUI(
                            id = 5,
                            value = "Beneficios asociados",
                            type = optionClosed
                        ),
                        OptionsSurveyUI(
                            id = 6,
                            value = "Otro, ¿Cúal?",
                            type = optionClosed
                        ),
                    )

            )
        CardCancelSurveyScreenContent(uiState = uiState)
    }
}
