package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints

import androidx.compose.runtime.Stable
import androidx.paging.PagingData
import kotlinx.coroutines.flow.Flow
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.domain.entities.response.PointStatementUI
import sv.com.tarjetaone.domain.entities.response.PointsTUI

@Stable
data class MyMegaPointsUiState(
    val pointStatementList: List<PointStatementUI> = emptyList(),
    val pointStatementSelected: PointStatementUI? = null,
    val pointTransactionsPaging: Flow<PagingData<PointsTUI>>? = null,
    val megaPointsStatusFilter: MegaPointsStatus = MegaPointsStatus.Accumulated,
    val megaPointsSearchMode: MegaPointsTransactionsMode? = null,
    val searchQuery: String = EMPTY_STRING,
    val hasPaymentPenalty: Boolean = false,
)

sealed class MegaPointsStatus {
    data object Accumulated : MegaPointsStatus()
    data object Redeemed : MegaPointsStatus()
}

sealed class MegaPointsTransactionsMode(val accumulated: String, val redeemed: String) {

    data object Current : MegaPointsTransactionsMode(
        accumulated = "QUERY_CURRENT_PERIOD_ACCUMULATED",
        redeemed = "QUERY_CURRENT_PERIOD_REDEEMED"
    )

    data object ByPeriod : MegaPointsTransactionsMode(
        accumulated = "QUERY_PERIOD_ACCUMULATED",
        redeemed = "QUERY_PERIOD_REDEEMED"
    )

    data object ByText : MegaPointsTransactionsMode(
        accumulated = "QUERY_BY_TEXT_ACCUMULATED",
        redeemed = "QUERY_BY_TEXT_REDEEMED"
    )
}
