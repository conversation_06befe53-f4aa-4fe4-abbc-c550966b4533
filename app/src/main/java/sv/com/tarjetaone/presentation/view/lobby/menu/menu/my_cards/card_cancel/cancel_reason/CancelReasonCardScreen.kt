package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_cancel.cancel_reason

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.SolidLargeButton
import sv.com.tarjetaone.presentation.compose.uicomponent.card.BaesSelectableCard
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.Spacer32

@Composable
fun CancelReasonCardScreen(
    viewModel: CancelReasonCardViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    CancelReasonCardContent(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@Composable
fun CancelReasonCardContent(
    uiState: CancelReasonCardUiState,
    onEvent: (CancelReasonCardUiEvent) -> Unit
) {
    LaunchedEffect(Unit) {
        onEvent(CancelReasonCardUiEvent.OnStart)
    }
    ScreenWithTopAppBar(
        onLeftButtonClick = { onEvent(CancelReasonCardUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(CancelReasonCardUiEvent.OnTwilioClick) },
        title = stringResource(id = R.string.cancel_reason_title)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = MaterialTheme.customDimens.dimen50)
        ) {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen16),
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                contentPadding = PaddingValues(bottom = MaterialTheme.customDimens.dimen32)
            ) {
                items(items = uiState.reasonOptions) { option ->
                    val isSelected = option == uiState.selectedReason
                    BaesSelectableCard(
                        isSelected = isSelected,
                        onClick = { onEvent(CancelReasonCardUiEvent.OnSelectReason(option)) }
                    ) {
                        Text(
                            text = option.name.orEmpty(),
                            style = MaterialTheme.typography.bodySmall,
                            fontWeight = FontWeight.SemiBold,
                            color = if (isSelected) MaterialTheme.customColors.onSuccess
                            else MaterialTheme.customColors.onDefaultSurface
                        )
                    }
                }
                item {
                    AnimatedVisibility(visible = uiState.isOtherSelected) {
                        SimpleElevatedTextField(
                            value = uiState.otherComments,
                            onValueChange = {
                                onEvent(
                                    CancelReasonCardUiEvent.OnCommentsChange(
                                        it.take(MAX_COMMENTS_LENGTH)
                                    )
                                )
                            },
                            placeholder = stringResource(id = R.string.cancel_reason_others_hint),
                            hint = stringResource(
                                id = R.string.second_onboarding_limit_180_characters
                            ),
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            }
            SolidLargeButton(
                text = stringResource(id = R.string.continue_button_label),
                onClick = { onEvent(CancelReasonCardUiEvent.OnContinueClick) },
                enabled = uiState.selectedReason != null &&
                        (!uiState.isOtherSelected || uiState.otherComments.isNotBlank()),
                modifier = Modifier.fillMaxWidth()
            )
            Spacer32()
        }
    }
}

private const val MAX_COMMENTS_LENGTH = 180

@Preview
@Composable
private fun CancelReasonCardScreenPreview() {
    OneAppTheme {
        CancelReasonCardContent(
            uiState = CancelReasonCardUiState(
                reasonOptions = listOf(
                    CatalogItemsCollectionUI(
                        id = "1",
                        name = "No uso mi tarjeta",
                        isActive = true,
                        parentId = "1",
                        behaviourInfo = "1"
                    ),
                    CatalogItemsCollectionUI(
                        id = "1",
                        name = "Otro",
                        isActive = true,
                        parentId = "1",
                        behaviourInfo = "True"
                    )
                ),
                selectedReason = CatalogItemsCollectionUI(
                    id = "1",
                    name = "Otro",
                    isActive = true,
                    parentId = "1",
                    behaviourInfo = "True"
                ),
                isOtherSelected = true,
            ),
            onEvent = {}
        )
    }
}
