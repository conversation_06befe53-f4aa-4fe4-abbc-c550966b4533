package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.queryform

import android.annotation.SuppressLint
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFormType
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.SolidLargeButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.OneAppSnackBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.queryform.component.PXFieldDummyUiStates
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.queryform.component.ServiceQueryForm

@Composable
fun ServiceQueryFormScreen(viewModel: ServiceQueryFormViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    ServiceQueryFormScreen(
        uiState = uiState,
        onEvent = viewModel::onEvent,
    )
}

@Composable
fun ServiceQueryFormScreen(
    uiState: ServiceQueryFormUiState,
    onEvent: (ServiceQueryFormUiEvent) -> Unit,
) {
    val snackBarHostState = remember { SnackbarHostState() }
    LaunchedEffect(Unit) {
        onEvent(ServiceQueryFormUiEvent.OnStart)
    }
    LaunchedEffect(uiState.queryFormError) {
        if (uiState.queryFormError != null) {
            snackBarHostState.showSnackbar(
                message = uiState.queryFormError,
                duration = SnackbarDuration.Indefinite
            )
        } else {
            snackBarHostState.currentSnackbarData?.dismiss()
        }
    }
    ScreenWithTopAppBar(
        onLeftButtonClick = { onEvent(ServiceQueryFormUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(ServiceQueryFormUiEvent.OnTwilioClick) },
        title = stringResource(R.string.service_payment_title),
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = MaterialTheme.customDimens.dimen24)
        ) {
            Box(modifier = Modifier.weight(1f)) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .verticalScroll(rememberScrollState())
                ) {
                    Spacer1f()
                    ServiceQueryForm(
                        uiState = uiState,
                        onEvent = onEvent
                    )
                    Spacer1f()
                }
                SnackbarHost(
                    hostState = snackBarHostState,
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.BottomCenter)
                ) {
                    OneAppSnackBar(
                        message = snackBarHostState.currentSnackbarData?.visuals?.message.orEmpty(),
                        containerColor = MaterialTheme.colorScheme.error,
                        onCancelClick = { snackBarHostState.currentSnackbarData?.dismiss() },
                        icon = R.drawable.ic_icon_close_circle,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
            SolidLargeButton(
                text = stringResource(R.string.continue_button_label),
                onClick = { onEvent(ServiceQueryFormUiEvent.OnContinueClick) },
                enabled = uiState.validForm && uiState.queryFormError == null,
                modifier = Modifier.padding(vertical = MaterialTheme.customDimens.dimen16)
            )
        }
    }
}

@SuppressLint("UnrememberedMutableState")
@Preview
@Composable
private fun ServiceQueryFormScreenPreview() {
    OneAppTheme {
        ServiceQueryFormScreen(
            uiState = ServiceQueryFormUiState(
                serviceName = "Service name",
                categoryName = "Category name",
                formType = PXFormType.SimpleForm,
                fields = mutableStateListOf(
                    PXFieldDummyUiStates.accountNumberField,
                    PXFieldDummyUiStates.documentTypeField
                )
            ),
            onEvent = {}
        )
    }
}
