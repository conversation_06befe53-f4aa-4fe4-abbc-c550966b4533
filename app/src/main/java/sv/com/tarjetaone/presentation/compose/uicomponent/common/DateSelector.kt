package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer8

@Suppress("kotlin:S107")
@Composable
fun DateSelector(
    modifier: Modifier = Modifier,
    months: List<Pair<Int, String>> = emptyList(),
    selectedStartMonth: Pair<Int, String>? = null,
    years: List<Int> = emptyList(),
    selectedStartYear: Int? = null,
    isDateInvalid: Boolean = false,
    onStartMonthChange: (Pair<Int, String>) -> Unit = {},
    onStartYearChange: (Int) -> Unit = {}
) {
    Column {
        Row(
            modifier = modifier.fillMaxWidth()
        ) {
            SimpleElevatedDropdown(
                items = months,
                itemLabel = { it.second },
                value = selectedStartMonth,
                onValueChange = {
                    onStartMonthChange(it)
                },
                hasError = isDateInvalid,
                label = stringResource(id = R.string.month),
                placeholder = stringResource(id = R.string.label_june),
                modifier = Modifier.weight(1f)
            )
            Spacer16()
            SimpleElevatedDropdown(
                items = years,
                itemLabel = { it.toString() },
                value = selectedStartYear,
                onValueChange = {
                    onStartYearChange(it)
                },
                hasError = isDateInvalid,
                label = stringResource(id = R.string.year),
                placeholder = stringResource(id = R.string.label_2009),
                modifier = Modifier.weight(1f)
            )
        }
        Spacer8()
        if (isDateInvalid) {
            Text(
                text = stringResource(id = R.string.invalid_date_error_message),
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.labelMedium
            )
        }
    }
}

@Preview
@Composable
fun DateSelectorPreview() {
    OneAppTheme {
        DateSelector()
    }
}

@Preview
@Composable
fun DateSelectorWithErrorPreview() {
    OneAppTheme {
        DateSelector(isDateInvalid = true)
    }
}
