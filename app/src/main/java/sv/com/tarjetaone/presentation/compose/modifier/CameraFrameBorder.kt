package sv.com.tarjetaone.presentation.compose.modifier

import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp

fun Modifier.cameraFrameBorder(
    cornerSize: Dp,
    strokeWidth: Dp,
    color: Color
): Modifier = drawWithContent {
    val cornerSizePx = cornerSize.toPx()
    val strokeWidthPx = strokeWidth.toPx()
    drawContent()
    // Top Left
    drawLine(
        color = color,
        start = Offset(0f, cornerSizePx),
        end = Offset(0f, 0f),
        strokeWidth = strokeWidthPx
    )
    drawLine(
        color = color,
        start = Offset(0f, 0f),
        end = Offset(cornerSizePx, 0f),
        strokeWidth = strokeWidthPx
    )

    // Top right
    drawLine(
        color = color,
        start = Offset(size.width, 0f),
        end = Offset(size.width - cornerSizePx, 0f),
        strokeWidth = strokeWidthPx
    )
    drawLine(
        color = color,
        start = Offset(size.width, 0f),
        end = Offset(size.width, cornerSizePx),
        strokeWidth = strokeWidthPx
    )

    // Bottom left
    drawLine(
        color = color,
        start = Offset(0f, size.height),
        end = Offset(0f, size.height - cornerSizePx),
        strokeWidth = strokeWidthPx
    )
    drawLine(
        color = color,
        start = Offset(0f, size.height),
        end = Offset(cornerSizePx, size.height),
        strokeWidth = strokeWidthPx
    )

    // Bottom right
    drawLine(
        color = color,
        start = Offset(size.width, size.height),
        end = Offset(size.width - cornerSizePx, size.height),
        strokeWidth = strokeWidthPx
    )
    drawLine(
        color = color,
        start = Offset(size.width, size.height),
        end = Offset(size.width, size.height - cornerSizePx),
        strokeWidth = strokeWidthPx
    )
}
