package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.paymentdetail

import android.graphics.Bitmap
import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.interfaces.TextFromResourcesUtil
import sv.com.tarjetaone.common.utils.AppConstants.DASH_STRING
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.CommonDialogWithIconParams
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.files.ContentType
import sv.com.tarjetaone.core.utils.openAppSettings
import sv.com.tarjetaone.domain.utils.automaticpayments.servicespayment.invoiceselection.ProductTypeCode
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.utils.getTimestamp

@HiltViewModel
class PxPaymentServiceDetailViewModel @Inject constructor(
    private val imageUtils: ImageUtils,
    private val textFromResourcesUtil: TextFromResourcesUtil,
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {

    // Stateless
    private val args = PxPaymentServiceDetailFragmentArgs.fromSavedStateHandle(savedStateHandle)

    // Stateful
    private val _uiState = MutableStateFlow(PxPaymentServiceDetailUiState())
    val uiState = _uiState.asStateFlow()

    private fun onStart() {
        _uiState.update {
            it.copy(
                pxPaymentServiceDetail = args.pxPaymentServiceDetail,
                isCardPaymentMethod = args.paymentMethodType == ProductTypeCode.CreditCards.value,
                selectedPaymentMethod = args.selectedPaymentMethod,
                isFromScanning = args.isFromScanning
            )
        }
    }

    /**
     * Return a callback to the UI, to launch `requestPermissionLauncher` with the required permission.
     */
    private fun onShareVoucherClick(requestPermissionCallback: () -> Unit) {
        requestPermissionCallback()
    }

    private fun onHideComponentBeforeCapture() {
        _uiState.update { it.copy(isCapturingVoucherView = true) }
    }

    private fun onContinueClick() {
        baseSharedPrefs.removeCollectorsNpeCode()
        sendEvent(UiEvent.Navigate(PxPaymentServiceDetailFragmentDirections.actionHome()))
    }

    /**
     * Share Voucher after saving image in the device's storage.
     */
    private fun onShareVoucher(bitmap: Bitmap?) {
        bitmap?.let { bitmapImage ->
            imageUtils.saveImage(fileCapturedName(), bitmapImage) { uri ->
                sendEvent(UiEvent.ShareContent(uri, ContentType.ANY_IMAGE))
                _uiState.update { it.copy(isCapturingVoucherView = false) }
            }
        }
    }

    private fun fileCapturedName(): String {
        return "${textFromResourcesUtil.getString(R.string.app_name)} $DASH_STRING ${getTimestamp()}"
    }

    private fun onStoragePermissionGranted(bitmap: Bitmap?) {
        onShareVoucher(bitmap)
    }

    fun onPermissionDenied(showRationale: Boolean) {
        sendEvent(
            UiEvent.ShowCommonDialog(
                CommonDialogWithIconParams(
                    title = UiText.StringResource(R.string.multimedia_access),
                    message = UiText.StringResource(R.string.permission_external_storage),
                    buttonText = UiText.StringResource(if (showRationale) R.string.accept_label else R.string.settings),
                    onButtonClick = {
                        if (!showRationale) sendEvent(UiEvent.StartIntent(openAppSettings()))
                    }
                )
            )
        )
    }

    fun onEvent(event: PxPaymentServiceDetailUiEvent) {
        when (event) {
            PxPaymentServiceDetailUiEvent.OnStart -> onStart()
            PxPaymentServiceDetailUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            PxPaymentServiceDetailUiEvent.OnContinueClick -> onContinueClick()
            is PxPaymentServiceDetailUiEvent.OnHideComponentBeforeCapture -> onHideComponentBeforeCapture()
            is PxPaymentServiceDetailUiEvent.OnShareVoucherClick -> onShareVoucherClick(event.requestPermissionCallback)
            is PxPaymentServiceDetailUiEvent.OnStoragePermissionDenied -> onPermissionDenied(event.showRationale)
            is PxPaymentServiceDetailUiEvent.OnStoragePermissionGranted -> {
                onStoragePermissionGranted(event.bitmap)
            }
        }
    }
}
