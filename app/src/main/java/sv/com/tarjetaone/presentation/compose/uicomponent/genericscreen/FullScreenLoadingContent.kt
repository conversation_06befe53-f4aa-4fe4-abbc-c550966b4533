package sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.SetStatusBarAppearance
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.common.OutlinedProgressBar
import sv.com.tarjetaone.presentation.compose.util.LoadAsyncImage
import sv.com.tarjetaone.presentation.compose.util.Spacer16

/**
 * Base loading screen content to ruse main UI components.
 *
 * @param modifier to apply customizations on the main composable column.
 * @param title screen main title.
 * @param subtitle the small text shown below the title.
 * @param icon the main screen icon shown above the title.
 * @param progressBarTime some screens need to show a progressBarTimer.
 */
@Composable
fun FullScreenLoadingContent(
    modifier: Modifier = Modifier,
    title: String,
    subtitle: String? = null,
    icon: Int = R.raw.loading_bars,
    progressBarTime: Int? = null,
    titleTypography: TextStyle = MaterialTheme.typography.displayLarge
) {
    SetStatusBarAppearance(lightAppearance = false)
    OneBackHandler()
    Column(
        modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.inverseSurface),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        LoadAsyncImage(
            modifier = Modifier
                .width(MaterialTheme.customDimens.dimen92)
                .height(MaterialTheme.customDimens.dimen92),
            url = icon,
            placeholderPreview = icon
        )
        Spacer16()
        Text(
            modifier = Modifier
                .padding(horizontal = MaterialTheme.customDimens.dimen45)
                .fillMaxWidth(),
            text = title,
            style = titleTypography,
            color = MaterialTheme.colorScheme.inverseOnSurface,
            textAlign = TextAlign.Center
        )
        subtitle?.let {
            Text(
                text = it,
                color = MaterialTheme.colorScheme.inverseOnSurface,
                style = MaterialTheme.typography.bodySmall,
                textAlign = TextAlign.Center
            )
        }
        progressBarTime?.let {
            Spacer16()
            OutlinedProgressBar(
                modifier = Modifier.width(MaterialTheme.customDimens.dimen252),
                progressBarTime = it
            )
        }
    }
}

@Preview
@Composable
fun LoadingScreenContentPreview() {
    OneAppTheme {
        FullScreenLoadingContent(
            title = stringResource(id = R.string.validating_your_identity_label),
            subtitle = stringResource(id = R.string.this_should_take_little_time),
        )
    }
}
