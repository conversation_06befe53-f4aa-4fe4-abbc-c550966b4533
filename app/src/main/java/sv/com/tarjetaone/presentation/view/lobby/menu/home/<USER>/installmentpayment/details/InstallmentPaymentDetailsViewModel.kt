package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.installmentpayment.details

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.presentation.helpers.capitalize
import sv.com.tarjetaone.presentation.view.common.management.ManagementDetailBaseViewModel
import sv.com.tarjetaone.presentation.view.utils.DAY_OF_WEEK_MONTH_TIME_FORMAT
import sv.com.tarjetaone.presentation.view.utils.YEAR_MONTH_DAY_TIME_FORMAT
import javax.inject.Inject

@HiltViewModel
class InstallmentPaymentDetailsViewModel @Inject constructor(
    imageUtils: ImageUtils,
    savedStateHandle: SavedStateHandle
) : ManagementDetailBaseViewModel(imageUtils) {
    private val args = InstallmentPaymentDetailsFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onStart() {
        args.response.management?.let { management ->
            _uiState.update {
                it.copy(
                    managementNumber = management.manRequestId.toString(),
                    managementStatus = management.mrStatusNameApp.orEmpty(),
                    managementStatusColor = management.mrStatusTextColor.orEmpty(),
                    managementName = management.manTypeNameApp.orEmpty(),
                    customerName = management.clientName.orEmpty(),
                    creditCardNumber = management.cardNumMasked.orEmpty(),
                    creditCardType = management.typeCardText.orEmpty(),
                    requestStartDate = management.processDate?.getFormattedDateFromTo(
                        YEAR_MONTH_DAY_TIME_FORMAT,
                        DAY_OF_WEEK_MONTH_TIME_FORMAT
                    )?.capitalize().orEmpty(),
                    description = management.description.takeIf { management.showDescription == true },
                    resolutionDays = management.availableDay,
                    managementAttributes = transformAttributesToUiTextList(management.manAttributes)
                )
            }
        }
    }

    override fun onPrimaryButtonClick() {
        sendEvent(
            UiEvent.Navigate(
                InstallmentPaymentDetailsFragmentDirections.actionHome()
            )
        )
    }
}