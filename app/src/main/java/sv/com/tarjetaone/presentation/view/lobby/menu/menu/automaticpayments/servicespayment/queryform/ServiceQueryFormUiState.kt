package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.queryform

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.domain.entities.request.automaticpayments.servicespayment.invoiceselection.BalanceClientRequestDataUI
import sv.com.tarjetaone.domain.entities.request.automaticpayments.servicespayment.invoiceselection.BalanceClientRequestUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldsResponseUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFormType
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.queryform.component.PXFieldUiState

data class ServiceQueryFormUiState(
    val serviceName: String = EMPTY_STRING,
    val categoryName: String = EMPTY_STRING,
    val formType: PXFormType? = null,
    val queryFormError: String? = null,
    val fields: SnapshotStateList<PXFieldUiState> = mutableStateListOf(),

    // Values to build the form when the formType is PXFormType.SelectOne
    val selectedQueryOption: Pair<String, PXFieldUI>? = null,
    val queryOptions: List<Pair<String, PXFieldUI>> = emptyList(),
    val reference: String = EMPTY_STRING,
) {
    val validForm: Boolean
        get() = if (formType == PXFormType.SimpleForm) {
            fields.all { it.isValidField }
        } else {
            selectedQueryOption != null && reference.isNotBlank()
        }

    fun toBalanceClientRequest(response: PXFieldsResponseUI): BalanceClientRequestUI {
        val fields = if (formType == PXFormType.SimpleForm) {
            fields.associate { it.pxFieldName to it.toPXFieldUI() }
        } else {
            selectedQueryOption?.let {
                val fieldValue = it.second.valueString?.copy(value = reference) ?: it.second.value
                mapOf(it.first to it.second.copy(selected = true, value = fieldValue))
            }
        }.orEmpty()
        val form = response.dataSets.data.entries.firstOrNull()?.let {
            mapOf(it.key to it.value.copy(fields = fields))
        }.orEmpty()

        return BalanceClientRequestUI(
            transactionNumber = response.transactionNumber,
            requestData = BalanceClientRequestDataUI(
                name = response.name,
                serviceCode = response.serviceCode,
                dataSets = response.dataSets.copy(
                    data = form
                )
            )
        )
    }
}
