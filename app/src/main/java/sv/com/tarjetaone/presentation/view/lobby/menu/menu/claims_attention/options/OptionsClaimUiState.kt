package sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.options

import androidx.compose.runtime.Stable
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI

@Stable
data class OptionsClaimUiState(
    val claimName: String = EMPTY_STRING,
    val claimOptions: List<CatalogItemsCollectionUI> = emptyList(),
)
