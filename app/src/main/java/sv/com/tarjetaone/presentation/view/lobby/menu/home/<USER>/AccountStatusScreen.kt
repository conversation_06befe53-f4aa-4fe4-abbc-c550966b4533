package sv.com.tarjetaone.presentation.view.lobby.menu.home.account_status

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.domain.entities.response.DataAccountStateUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.card.HorizontalIconWithText
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedDropdown
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.lobby.menu.home.account_status.component.AccountNumberCard

@Composable
fun AccountStatusScreen(
    viewModel: AccountStatusViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    SideEffectHandler(viewModel.sideEffects) {
        AccountStatusContent(
            uiState = uiState,
            onEvent = viewModel::onEvent
        )
    }
}

@Composable
private fun AccountStatusContent(
    uiState: AccountStatusUiState = AccountStatusUiState(),
    onEvent: (AccountStatusUiEvent) -> Unit
) {
    val horizontalPadding =
        Modifier.padding(horizontal = MaterialTheme.customDimens.dimen38)

    LaunchedEffect(Unit) {
        onEvent(AccountStatusUiEvent.OnStart)
    }
    ScreenWithTopAppBar(
        contentPadding = PaddingValues(
            top = MaterialTheme.customDimens.dimen32,
            start = MaterialTheme.customDimens.dimen32,
            end = MaterialTheme.customDimens.dimen32,
            bottom = ZERO_VALUE.dp
        ),
        title = stringResource(R.string.account_status),
        onLeftButtonClick = { onEvent(AccountStatusUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(AccountStatusUiEvent.OnSupportClick) }
    ) {
        if (uiState.accountStates.isEmpty()) {
            AccountStatusEmptyState(modifier = horizontalPadding)
        } else {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = horizontalPadding
            ) {
                Spacer32()
                AccountNumberCard(accountNumber = uiState.accountNumber)
                Spacer32()
                SimpleElevatedDropdown(
                    items = uiState.accountStates,
                    itemLabel = { item -> item.cutOffYearMonthName.orEmpty() },
                    value = uiState.accountStateSelected,
                    onValueChange = { onEvent(AccountStatusUiEvent.OnCutOffDateChange(it)) }
                )
                Spacer32()
                OneButton(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(id = R.string.download_button_label),
                    onClick = { onEvent(AccountStatusUiEvent.OnDownloadClick) },
                )
                Spacer8()
                HorizontalIconWithText(
                    iconTextSpacing = MaterialTheme.customDimens.dimen8,
                    text = UiText.StringResource(R.string.account_status_disclaimer),
                    leadingImageIcon = R.drawable.ic_info_2,
                    style = MaterialTheme.typography.labelMedium,
                    textColor = MaterialTheme.customColors.gray700
                )
            }
        }
    }
}

@Composable
private fun AccountStatusEmptyState(
    modifier: Modifier = Modifier
) {
    Column(
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier.fillMaxSize()
    ) {
        Image(
            imageVector = ImageVector.vectorResource(id = R.drawable.account_statement_empty),
            contentDescription = null
        )
        Text(
            text = stringResource(id = R.string.account_status_empty_state),
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Preview
@Composable
private fun AccountStatusScreenPreview() {
    OneAppTheme {
        AccountStatusContent(
            uiState = AccountStatusUiState(
                accountNumber = "**********",
                accountStates = listOf(
                    DataAccountStateUI(
                        cutOffYearMonthName = "Abril 2021",
                    ),
                    DataAccountStateUI(
                        cutOffYearMonthName = "Marzo 2021",
                    ),
                    DataAccountStateUI(
                        cutOffYearMonthName = "Febrero 2021",
                    ),
                ),
            ),
            onEvent = {}
        )
    }
}

@Preview
@Composable
private fun AccountStatusScreenEmptyPreview() {
    OneAppTheme {
        AccountStatusContent(
            uiState = AccountStatusUiState(
                accountNumber = "**********",
                accountStates = emptyList(),
            ),
            onEvent = {}
        )
    }
}