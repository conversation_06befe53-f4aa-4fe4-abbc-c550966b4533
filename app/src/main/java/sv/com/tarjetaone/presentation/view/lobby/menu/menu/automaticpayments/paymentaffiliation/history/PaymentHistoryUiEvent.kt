package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.history

sealed class PaymentHistoryUiEvent {
    data object OnStart: PaymentHistoryUiEvent()
    data object OnBack: PaymentHistoryUiEvent()
    data object OnTwilioClick: PaymentHistoryUiEvent()
    data class OnDateInputClick(val type: PaymentHistoryDateType): PaymentHistoryUiEvent()
    data object OnCloseDialog: PaymentHistoryUiEvent()
    data class OnStartDateSelected(val date: Long?): PaymentHistoryUiEvent()
    data class OnEndDateSelected(val date: Long?): PaymentHistoryUiEvent()
    data object OnSearchPaymentsClick: PaymentHistoryUiEvent()
}