package sv.com.tarjetaone.presentation.view.common.contactinput

import androidx.core.text.isDigitsOnly
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.PHONE_MAX_LENGTH
import sv.com.tarjetaone.core.interfaces.PatternsValidator
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.entities.response.ContactType
import sv.com.tarjetaone.domain.entities.response.OtpMethod
import sv.com.tarjetaone.domain.entities.response.OtpType
import sv.com.tarjetaone.presentation.helpers.hasInvalidPhoneNumber

abstract class ContactInputBaseViewModel(
    private val patternsValidator: PatternsValidator
) : BaseViewModel() {

    protected val _uiState = MutableStateFlow(ContactInputUiState())
    val uiState = _uiState.asStateFlow()

    protected open fun onBackClick(contactType: ContactType) = sendEvent(UiEvent.NavigateBack)
    protected abstract fun onSelectOtpMethod(type: OtpType, method: OtpMethod)

    protected open fun onStart(contactType: ContactType) {
        _uiState.update { it.copy(contactType = contactType) }
    }

    private fun onPhoneChange(phone: String) {
        _uiState.update {
            it.copy(
                contact = phone,
                showPhoneActions = phone.isNotBlank(),
                validationError = validatePhoneNumber(phone)
            )
        }
    }

    private fun onEmailChange(email: String) {
        _uiState.update {
            it.copy(
                contact = email,
                validationError = validateEmail(email)
            )
        }
    }

    private fun validatePhoneNumber(phone: String): ContactValidationError = when {
        phone.length >= PHONE_MAX_LENGTH && phone.hasInvalidPhoneNumber() ->
            ContactValidationError.InvalidFormat(ContactType.Phone)

        !phone.isDigitsOnly() -> ContactValidationError.InvalidCharacters
        else -> ContactValidationError.None
    }

    private fun validateEmail(email: String): ContactValidationError = when {
        email.length >= ONE_VALUE && !patternsValidator.isEmailAddressValid(email) ->
            ContactValidationError.InvalidFormat(ContactType.Email)

        else -> ContactValidationError.None
    }

    fun onEvent(event: ContactInputUiEvent) {
        when (event) {
            is ContactInputUiEvent.OnBackClick -> onBackClick(event.contactType)
            ContactInputUiEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
            is ContactInputUiEvent.OnStart -> onStart(event.contactType)
            is ContactInputUiEvent.OnPhoneChange -> onPhoneChange(event.phone)
            is ContactInputUiEvent.OnEmailChange -> onEmailChange(event.email)
            is ContactInputUiEvent.OnSelectOtpMethod -> onSelectOtpMethod(event.type, event.method)
        }
    }
}
