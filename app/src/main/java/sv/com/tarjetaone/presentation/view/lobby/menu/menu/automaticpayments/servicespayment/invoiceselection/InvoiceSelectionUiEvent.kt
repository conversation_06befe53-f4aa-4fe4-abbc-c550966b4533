package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.invoiceselection

import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.invoiceselection.infoproducts.PXPaymentMethodUI
import sv.com.tarjetaone.domain.utils.automaticpayments.servicespayment.invoiceselection.ProductTypeCode

sealed class InvoiceSelectionUiEvent {
    data object OnStart : InvoiceSelectionUiEvent()
    data class OnFieldValueChanged(
        val formKey: String,
        val fieldIndex: Int,
        val value: String
    ) : InvoiceSelectionUiEvent()
    data class OnSelectInvoice(val invoiceIndex: Int) : InvoiceSelectionUiEvent()
    data class OnSelectProductType(val productType: ProductTypeCode) : InvoiceSelectionUiEvent()
    data class OnSelectProduct(val product: PXPaymentMethodUI) : InvoiceSelectionUiEvent()
    data object OnContinueClick : InvoiceSelectionUiEvent()
    data object OnBackClick : InvoiceSelectionUiEvent()
    data object OnTwilioClick : InvoiceSelectionUiEvent()
}
