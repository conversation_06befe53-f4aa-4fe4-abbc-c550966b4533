package sv.com.tarjetaone.presentation.view.common.sign

import androidx.annotation.StringRes
import androidx.compose.runtime.Stable
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_FLOAT_VALUE

@Stable
data class SignContractUiState(
    @StringRes val actionButtonRes: Int = R.string.hire_label,
    val isProgressVisible: Boolean = false,
    val progress: Float = ZERO_FLOAT_VALUE,
    val customerName: String = EMPTY_STRING,
    val isSignSelected: Boolean = false
)