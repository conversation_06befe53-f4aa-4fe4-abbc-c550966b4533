package sv.com.tarjetaone.presentation.view.lobby.menu.menu.management

import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI
import sv.com.tarjetaone.domain.entities.response.LogManagementItemsCollectionUI
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.settings.activity_log.ActivityLogDateRange

sealed class ManagementFollowUpUiEvent {
    data object OnStart : ManagementFollowUpUiEvent()
    data class OnDateChange(val value: ActivityLogDateRange) : ManagementFollowUpUiEvent()
    data class OnFilterChange(val value: CatalogItemsCollectionUI) : ManagementFollowUpUiEvent()
    data class OnManagementClick(val value: LogManagementItemsCollectionUI) :
        ManagementFollowUpUiEvent()

    data object OnNewManagementClicked : ManagementFollowUpUiEvent()
    data object OnBackPressed : ManagementFollowUpUiEvent()
    data object OnSupportClicked : ManagementFollowUpUiEvent()
    data class OnManagementLoadError(val callback: () -> Unit) : ManagementFollowUpUiEvent()
}