package sv.com.tarjetaone.presentation.view.common.survey.row.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.LocalCustomColors
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.compose.util.Spacer

@Composable
fun SurveyOtherCommentSection(
    modifier: Modifier = Modifier,
    isSectionVisible: Boolean,
    isOtherTextVisible: Boolean = false,
    commentValue: String,
    onValueChange: (String) -> Unit
) {
    AnimatedVisibility(
        visible = isSectionVisible,
        modifier = modifier
    ) {
        Column {
            if (isOtherTextVisible) {
                Text(
                    text = stringResource(id = R.string.second_onboarding_other_label),
                    style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.SemiBold),
                    color = LocalCustomColors.current.bodyLightGray
                )
                Spacer(MaterialTheme.customDimens.dimen2)
            }
            SimpleElevatedTextField(
                textStyle = MaterialTheme.typography.labelLarge,
                value = commentValue,
                onValueChange = onValueChange,
                placeholder = stringResource(id = R.string.second_onboarding_leave_comments_placeholder),
            )
            Text(
                modifier = Modifier.padding(top = MaterialTheme.customDimens.dimen8),
                text = stringResource(id = R.string.second_onboarding_limit_180_characters),
                style = MaterialTheme.typography.labelMedium,
                color = LocalCustomColors.current.gray600
            )
        }
    }
}

@Preview
@Composable
fun SurveyOtherCommentSectionPreview() {
    OneAppTheme {
        SurveyOtherCommentSection(
            isSectionVisible = true,
            commentValue = "",
            onValueChange = {}
        )
    }
}