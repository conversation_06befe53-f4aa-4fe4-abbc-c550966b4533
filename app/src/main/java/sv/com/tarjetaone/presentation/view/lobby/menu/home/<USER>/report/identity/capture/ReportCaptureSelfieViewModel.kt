package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.report.identity.capture

import android.graphics.Bitmap
import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.presentation.view.common.selfiecapture.SelfieCaptureBaseViewModel
import javax.inject.Inject

@HiltViewModel
class ReportCaptureSelfieViewModel @Inject constructor(
    facephiResultHandler: FacephiResultHandler,
    savedStateHandle: SavedStateHandle
) : SelfieCaptureBaseViewModel(facephiResultHandler) {
    private val args = ReportCaptureSelfieFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onSuccessfulSelfieCapture(selfie: Bitmap) {
        sendEvent(
            UiEvent.Navigate(
                ReportCaptureSelfieFragmentDirections
                    .actionReportCaptureSelfieFragmentToReportPreviewSelfieFragment(
                        cardStatusId = args.cardStatusId,
                        cardId = args.cardId,
                        reportableTransactions = args.reportableTransactions
                    )
            )
        )
    }
}
