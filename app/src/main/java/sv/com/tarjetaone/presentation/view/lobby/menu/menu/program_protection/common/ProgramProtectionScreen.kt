package sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.common

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.response.FProtectionTypesUI
import sv.com.tarjetaone.domain.entities.response.FraudProtectionDataUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.PaymentPenaltyEmptyState
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.common.component.ProgramProtectionEmptyState
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.common.component.ProtectionEnrollActivateContent
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.deactivate_program_protection.actived_program_protection.DeactivateProgramProtectionInfoContent
import sv.com.tarjetaone.presentation.view.utils.PreviewUtils.FP_AMOUNT
import sv.com.tarjetaone.presentation.view.utils.PreviewUtils.MONTHLY_AMOUNT_WITH_IVA

@Composable
fun ProtectionProgramScreen(
    viewModel: ProgramProtectionViewModel,
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    SideEffectHandler(viewModel.sideEffects) {
        ProtectionProgramContent(
            uiState = uiState,
            onEvent = viewModel::onEvent
        )
    }
}

@Composable
fun ProtectionProgramContent(
    uiState: ProgramProtectionUiState,
    onEvent: (ProgramProtectionUiEvent) -> Unit = {},
) {
    ScreenWithTopAppBar(
        title = uiState.screenTitle?.asString(),
        onLeftButtonClick = { onEvent(ProgramProtectionUiEvent.OnNavigateBack) },
        onRightButtonClick = { onEvent(ProgramProtectionUiEvent.OnSupportClick) },
    ) {
        LaunchedEffect(Unit) {
            onEvent(ProgramProtectionUiEvent.OnStart)
        }
        if (!uiState.hasPaymentPenalty) {
            ProtectionProgramContentBody(
                uiState = uiState,
                onEvent = onEvent
            )
        } else {
            PaymentPenaltyEmptyState {
                onEvent(ProgramProtectionUiEvent.OnNavigateBack)
            }
        }
    }
}

@Composable
private fun ProtectionProgramContentBody(
    uiState: ProgramProtectionUiState,
    onEvent: (ProgramProtectionUiEvent) -> Unit = {},
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = MaterialTheme.customDimens.dimen24),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        when (uiState.protectionProgramState) {
            ProgramProtectionState.Enroll,
            ProgramProtectionState.Activate -> {
                ProtectionEnrollActivateContent(
                    modifier = Modifier
                        .fillMaxWidth()
                        .verticalScroll(rememberScrollState()),
                    programProtectionState = uiState.protectionProgramState,
                    amountToCharge = uiState.getAmountAsString(),
                    isActionButtonEnabled = uiState.isActionButtonEnabled,
                    onPrimaryButtonClick = { onEvent(ProgramProtectionUiEvent.OnActionButtonClick) }
                )
            }

            ProgramProtectionState.Deactivate -> {
                DeactivateProgramProtectionInfoContent(
                    modifier = Modifier.fillMaxWidth(),
                    amount = uiState.getAmountAsDouble(),
                    canDeactivate = uiState.canDeactivate,
                    onKeepFraudProtectionButtonClick = {
                        onEvent(ProgramProtectionUiEvent.OnNavigateBack)
                    },
                    onDeactivateFraudProtectionButtonClick = {
                        onEvent(ProgramProtectionUiEvent.OnActionButtonClick)
                    }
                )
            }

            ProgramProtectionState.Invalid -> {
                ProgramProtectionEmptyState(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = MaterialTheme.customDimens.dimen16),
                    onActionButtonClick = { onEvent(ProgramProtectionUiEvent.OnNavigateBack) }
                )
            }

            else -> Unit
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun ProgramProtectionEnrollPreview() {
    OneAppTheme {
        ProtectionProgramContent(
            uiState = ProgramProtectionUiState(
                protectionProgramState = ProgramProtectionState.Enroll,
                fraudProtectionDetails = FraudProtectionDataUI(
                    fProtectionTypes = listOf(
                        FProtectionTypesUI(
                            fProtectionTypeId = "1",
                            logoNameApp = MONTHLY_AMOUNT_WITH_IVA,
                            amount = FP_AMOUNT
                        )
                    )
                )
            )
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun ProgramProtectionActivatePreview() {
    OneAppTheme {
        ProtectionProgramContent(
            uiState = ProgramProtectionUiState(
                protectionProgramState = ProgramProtectionState.Activate,
                fraudProtectionDetails = FraudProtectionDataUI(
                    fProtectionTypes = listOf(
                        FProtectionTypesUI(
                            fProtectionTypeId = "2",
                            logoNameApp = MONTHLY_AMOUNT_WITH_IVA,
                            amount = FP_AMOUNT
                        )
                    )
                )
            )
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun ProgramProtectionDeactivatePreview() {
    OneAppTheme {
        ProtectionProgramContent(
            uiState = ProgramProtectionUiState(
                screenTitle = UiText.StringResource(R.string.protection_plan),
                protectionProgramState = ProgramProtectionState.Deactivate,
                fraudProtectionDetails = FraudProtectionDataUI(
                    fProtectionTypes = listOf(
                        FProtectionTypesUI(
                            fProtectionTypeId = "3",
                            logoNameApp = MONTHLY_AMOUNT_WITH_IVA,
                            amount = FP_AMOUNT
                        )
                    )
                )
            )
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun ProgramProtectionInvalidPreview() {
    OneAppTheme {
        ProtectionProgramContent(
            uiState = ProgramProtectionUiState(
                protectionProgramState = ProgramProtectionState.Invalid,
            )
        )
    }
}
