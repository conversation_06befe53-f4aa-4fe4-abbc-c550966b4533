package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.scanner

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.modifier.cameraFrameBorder
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.compose.util.barcodescanner.BarcodeScannerPreview

@Composable
fun CodeScannerScreen(viewModel: CodeScannerViewModel) {
    BarcodeScannerContent(viewModel::onEvent)
}

@Composable
fun BarcodeScannerContent(
    onUiEvent: (CodeScannerUiEvent) -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.secondary)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.customDimens.dimen24)
                .align(Alignment.Center)
        ) {
            Text(
                text = stringResource(R.string.scan_code_scanner_title),
                color = MaterialTheme.customColors.defaultSurface,
                style = MaterialTheme.typography.titleMedium,
                textAlign = TextAlign.Center
            )
            Spacer32()
            BarcodeScannerPreview(
                onBarcodeScanned = {
                    onUiEvent(CodeScannerUiEvent.OnCodeScan(it))
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(MaterialTheme.customDimens.dimen285)
                    .cameraFrameBorder(
                        cornerSize = MaterialTheme.customDimens.dimen32,
                        strokeWidth = MaterialTheme.customDimens.dimen10,
                        color = MaterialTheme.colorScheme.primary
                    )
            )
        }
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .padding(MaterialTheme.customDimens.dimen40)
                .size(MaterialTheme.customDimens.dimen40)
                .clip(CircleShape)
                .clickable(
                    onClick = {
                        onUiEvent(CodeScannerUiEvent.OnCancel)
                    }
                )
                .align(Alignment.TopEnd)
        ) {
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = null,
                tint = MaterialTheme.customColors.defaultSurface,
                modifier = Modifier.size(MaterialTheme.customDimens.dimen32)
            )
        }
    }
}

@Preview
@Composable
fun BarcodeScannerScreenPreview() {
    OneAppTheme {
        BarcodeScannerContent { }
    }
}