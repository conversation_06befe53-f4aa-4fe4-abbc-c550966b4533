package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.identity.preview

import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.presentation.view.common.selfiepreview.SelfiePreviewViewModel
import javax.inject.Inject

@HiltViewModel
class AdditionalCardSelfiePreviewViewModel @Inject constructor(
    imageUtils: ImageUtils,
    facephiResultHandler: FacephiResultHandler
) : SelfiePreviewViewModel(imageUtils, facephiResultHandler) {
    override fun onContinueClick() {
        sendEvent(
            UiEvent.Navigate(
                AdditionalCardSelfiePreviewFragmentDirections
                    .actionSelieConfirmationAdditionalCardFragmentToIdentityValidationACFragment()
            )
        )
    }
}
