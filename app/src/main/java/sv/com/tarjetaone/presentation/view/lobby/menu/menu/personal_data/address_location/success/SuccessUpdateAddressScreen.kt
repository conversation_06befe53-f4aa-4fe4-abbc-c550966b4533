package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.address_location.success

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.view.common.confirmation_gradient.SuccessGradientScreen
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.address_location.UserHomeAddressAction

@Composable
fun SuccessUpdateAddressScreen(viewModel: SuccessUpdateAddressViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    SuccessUpdateAddressScreenContent(onEvent = viewModel::onEvent, action = uiState.action)
}

@Composable
private fun SuccessUpdateAddressScreenContent(
    onEvent: (SuccessUpdateAddressUiEvent) -> Unit = {},
    action: UserHomeAddressAction
) {
    SuccessGradientScreen(
        message = stringResource(
            id = R.string.address_was_successfully,
            if (action == UserHomeAddressAction.SAVE) {
                stringResource(R.string.address_was_added)
            } else {
                stringResource(R.string.address_was_updated)
            }
        ),
        buttonText = stringResource(id = R.string.done_button),
        onButtonClick = { onEvent(SuccessUpdateAddressUiEvent.OnDoneClick) }
    )
}

@Preview
@Composable
fun SuccessUpdateAddressScreenPreview() {
    OneAppTheme {
        SuccessUpdateAddressScreenContent(action = UserHomeAddressAction.SAVE)
    }
}
