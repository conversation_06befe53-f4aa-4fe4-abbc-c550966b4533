package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.modifier.fieldDecoration
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.util.FieldDecorationType
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer4
import sv.com.tarjetaone.presentation.compose.util.Spacer8

@Suppress("kotlin:S107", "kotlin:S3776")
@Composable
fun SimpleElevatedTextField(
    modifier: Modifier = Modifier,
    value: String,
    onValueChange: (String) -> Unit,
    readOnly: Boolean = false,
    placeholder: String? = null,
    label: String? = null,
    labelAlignment: Alignment.Horizontal = Alignment.Start,
    labelIcon: Int? = null,
    labelIconColor: Color = MaterialTheme.customColors.disclaimer,
    labelIconClick: () -> Unit = {},
    labelStyle: TextStyle = MaterialTheme.typography.bodySmall.copy(
        fontWeight = FontWeight.Medium,
        color = MaterialTheme.customColors.gray700
    ),
    hint: String? = null,
    hintStyle: TextStyle = MaterialTheme.typography.bodySmall.copy(
        color = MaterialTheme.customColors.gray700
    ),
    leadingIcon: (@Composable () -> Unit)? = null,
    trailingIcon: (@Composable () -> Unit)? = null,
    textFieldAlignment: Alignment = Alignment.TopStart,
    textStyle: TextStyle = MaterialTheme.typography.bodyLarge.copy(
        lineHeight = MaterialTheme.customDimensSp.sp22,
        textAlign = if (textFieldAlignment == Alignment.Center) TextAlign.Center else TextAlign.Unspecified
    ),
    singleLine: Boolean = true,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    decorationType: FieldDecorationType = FieldDecorationType.ELEVATED,
    hasError: Boolean = false, // controls the decoration
    error: String? = null, // controls the error label
    errorStyle: TextStyle = MaterialTheme.typography.labelMedium.copy(
        color = MaterialTheme.colorScheme.error
    ),
    onClick: (() -> Unit)? = null,
    innerHorizontalPadding: Dp = MaterialTheme.customDimens.dimen16,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    enabled: Boolean = true
) {
    Column(
        modifier = modifier.height(IntrinsicSize.Min)
    ) {
        label?.let {
            Row(
                modifier = Modifier.align(labelAlignment),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = it,
                    style = labelStyle,
                )
                labelIcon?.let { icon ->
                    Spacer8()
                    Icon(
                        imageVector = ImageVector.vectorResource(id = icon),
                        tint = labelIconColor,
                        contentDescription = null,
                        modifier = Modifier.clickable(onClick = labelIconClick)
                    )
                }
            }
            Spacer4()
        }
        BasicTextField(
            modifier = Modifier
                .weight(1f)
                .heightIn(min = MaterialTheme.customDimens.dimen46)
                .fieldDecoration(
                    decorationType = decorationType,
                    hasError = hasError,
                    enabled = enabled
                )
                .background(
                    if (enabled) Color.White else MaterialTheme.customColors.gray200,
                    MaterialTheme.shapes.small,
                )
                .then( // Only apply clickable modifier if lambda is passed and readOnly is true
                    if (readOnly && onClick != null && enabled) Modifier.clickable { onClick() } else Modifier
                ),
            interactionSource = interactionSource,
            value = value,
            onValueChange = onValueChange,
            singleLine = singleLine,
            cursorBrush = SolidColor(MaterialTheme.colorScheme.secondary),
            textStyle = textStyle.copy(
                color = when {
                    hasError -> MaterialTheme.colorScheme.error
                    !enabled -> MaterialTheme.customColors.steelGray
                    else -> MaterialTheme.colorScheme.secondary
                }
            ),
            readOnly = readOnly,
            keyboardOptions = keyboardOptions,
            keyboardActions = keyboardActions,
            visualTransformation = visualTransformation,
            enabled = if (readOnly && onClick != null) false else enabled, // To make sure clickable modifier works
            decorationBox = { innerTextField ->
                Row(
                    modifier = Modifier.padding(
                        horizontal = innerHorizontalPadding,
                        vertical = MaterialTheme.customDimens.dimen8
                    ),
                    verticalAlignment = if (singleLine) Alignment.CenterVertically else Alignment.Top
                ) {
                    leadingIcon?.let {
                        it()
                        Spacer8()
                    }
                    Box(
                        contentAlignment = textFieldAlignment,
                        modifier = Modifier
                            .weight(1f)
                            .wrapContentHeight()
                    ) {
                        if (value.isEmpty() && placeholder != null) {
                            Text(
                                placeholder,
                                style = textStyle.copy(color = MaterialTheme.colorScheme.onSecondaryContainer),
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis
                            )
                        }
                        innerTextField()
                    }
                    trailingIcon?.let {
                        Spacer16()
                        it()
                    }
                }
            }
        )
        hint?.let {
            Spacer4()
            Text(text = it, style = hintStyle)
        }
        if (hasError && error.isNullOrEmpty().not()) {
            Spacer4()
            Text(text = error.orEmpty(), style = errorStyle)
        }
    }
}

@Preview
@Composable
private fun SimpleElevatedTextFieldPreview() {
    OneAppTheme {
        SimpleElevatedTextField(
            value = "Regular",
            onValueChange = { },
            modifier = Modifier.fillMaxWidth(),
            trailingIcon = {
                Icon(
                    painter = painterResource(id = R.drawable.ic_search_white),
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
            }
        )
    }
}

@Preview
@Composable
private fun SimpleOutlinedTextFieldPreview() {
    OneAppTheme {
        SimpleElevatedTextField(
            value = "Outlined",
            onValueChange = { },
            modifier = Modifier.fillMaxWidth(),
            trailingIcon = {
                Icon(
                    painter = painterResource(id = R.drawable.ic_search_white),
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
            },
            decorationType = FieldDecorationType.OUTLINED
        )
    }
}

@Preview
@Composable
private fun SimpleTextFieldWithErrorPreview() {
    OneAppTheme {
        SimpleElevatedTextField(
            value = "Outlined With Error",
            onValueChange = { },
            modifier = Modifier.fillMaxWidth(),
            trailingIcon = {
                Icon(
                    painter = painterResource(id = R.drawable.ic_search_white),
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
            },
            decorationType = FieldDecorationType.OUTLINED,
            label = "Este es un label",
            labelIcon = R.drawable.ic_question_circle_fill,
            hasError = true,
            error = "Este es un mensaje de error",
            hint = "Este es un mensaje de ayuda"
        )
    }
}

@Preview
@Composable
private fun DisabledTextFieldPreview() {
    OneAppTheme {
        SimpleElevatedTextField(
            value = "Disabled",
            onValueChange = { },
            modifier = Modifier.fillMaxWidth(),
            trailingIcon = {
                Icon(
                    painter = painterResource(id = R.drawable.ic_search_white),
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
            },
            decorationType = FieldDecorationType.OUTLINED,
            enabled = false
        )
    }
}
