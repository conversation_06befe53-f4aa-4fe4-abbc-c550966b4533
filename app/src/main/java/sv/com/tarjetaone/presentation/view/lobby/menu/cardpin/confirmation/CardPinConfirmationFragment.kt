package sv.com.tarjetaone.presentation.view.lobby.menu.cardpin.confirmation

import android.os.Bundle
import android.view.View
import androidx.fragment.app.viewModels
import dagger.hilt.android.AndroidEntryPoint
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.base.BaseFragment
import sv.com.tarjetaone.databinding.FragmentComposeBinding
import sv.com.tarjetaone.presentation.view.common.cardpin.CardPinScreen
import sv.com.tarjetaone.presentation.view.utils.requireLobbyActivity

@AndroidEntryPoint
class CardPinConfirmationFragment : BaseFragment<FragmentComposeBinding>(
    R.layout.fragment_compose
) {
    override val viewModel: CardPinConfirmationViewModel by viewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requireLobbyActivity().hideNavigationBottom()
        binding.composeView.setContentScreen {
            CardPinScreen(viewModel = viewModel)
        }
    }
}
