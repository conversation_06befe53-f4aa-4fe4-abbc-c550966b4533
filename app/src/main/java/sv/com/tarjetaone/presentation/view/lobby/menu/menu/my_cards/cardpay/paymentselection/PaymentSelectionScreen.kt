package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.cardpay.paymentselection

import androidx.compose.animation.AnimatedContent
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.card.OneAppSmallCardAlert
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.cardpay.paymentselection.component.PaymentMethodForm
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.cardpay.paymentselection.component.PaymentTypeForm

@Composable
fun PaymentSelectionScreen(
    viewModel: PaymentSelectionViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    PaymentSelectionScreen(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@Composable
fun PaymentSelectionScreen(
    uiState: PaymentSelectionUiState,
    onEvent: (PaymentSelectionUiEvent) -> Unit
) {
    LaunchedEffect(Unit) {
        onEvent(PaymentSelectionUiEvent.OnStart)
    }

    ScreenWithTopAppBar(
        title = stringResource(id = R.string.credit_card_payment_title),
        onLeftButtonClick = {
            onEvent(PaymentSelectionUiEvent.OnBackClick)
        },
        onRightButtonClick = {
            onEvent(PaymentSelectionUiEvent.OnTwilioClick)
        }
    ) {
        Column(modifier = Modifier.padding(MaterialTheme.customDimens.dimen30)) {
            CardSummarySection(cardNumber = uiState.cardNumber)
            AnimatedContent(
                targetState = uiState.isSelectingAccount,
                label = "form_content",
                modifier = Modifier.weight(ONE_FLOAT_VALUE)
            ) { isSelectingAccount ->
                if (isSelectingAccount) {
                    PaymentMethodForm(
                        paymentAmount = uiState.finalAmount,
                        paymentType = uiState.selectedPayment,
                        accountTypes = uiState.accountTypes,
                        selectedAccountType = uiState.selectedAccountType,
                        onAccountTypeChange = {
                            onEvent(PaymentSelectionUiEvent.OnAccountTypeChange(it))
                        },
                        accounts = uiState.filteredAccounts,
                        selectedAccount = uiState.selectedAccount,
                        onAccountChange = { onEvent(PaymentSelectionUiEvent.OnAccountChange(it)) },
                        paymentDesc = uiState.paymentDescription,
                        onPaymentDescChange = {
                            onEvent(PaymentSelectionUiEvent.OnPaymentDescChange(it))
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = MaterialTheme.customDimens.dimen16)
                    )
                } else {
                    PaymentTypeForm(
                        minAmount = uiState.minAmount,
                        fullAmount = uiState.fullAmount,
                        otherAmount = uiState.otherAmount,
                        onOtherAmountChange = {
                            onEvent(PaymentSelectionUiEvent.OnOtherPaymentChange(it))
                        },
                        selectedPayment = uiState.selectedPayment,
                        onPaymentTypeChange = { onEvent(PaymentSelectionUiEvent.OnSelectPayment(it)) },
                        modifier = Modifier
                            .weight(ONE_FLOAT_VALUE)
                            .fillMaxWidth()
                    )
                }
            }
            OneButton(
                text = stringResource(id = R.string.continue_button_label),
                onClick = {
                    when {
                        uiState.isSelectingAccount -> {
                            onEvent(PaymentSelectionUiEvent.OnContinuePaymentClick)
                        }

                        else -> {
                            onEvent(PaymentSelectionUiEvent.OnContinueMethodSelectionClick)
                        }
                    }
                },
                enabled = when {
                    uiState.isSelectingAccount -> uiState.hasEnoughBalance
                    // has selected a payment and is a valid amount
                    else -> uiState.isValidPayment()
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = MaterialTheme.customDimens.dimen32)
            )
        }
    }
}

@Composable
private fun CardSummarySection(cardNumber: String) {
    ElevatedCard(
        colors = CardDefaults.elevatedCardColors(
            containerColor = MaterialTheme.customColors.defaultSurface,
            contentColor = MaterialTheme.customColors.onDefaultSurface
        ),
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.customDimens.dimen50)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.customDimens.dimen16)
        ) {
            OneAppSmallCardAlert()
            Column(
                modifier = Modifier
                    .weight(ONE_FLOAT_VALUE)
                    .padding(start = MaterialTheme.customDimens.dimen16)
            ) {
                Text(
                    text = stringResource(id = R.string.card_number_colon),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.customColors.bodyLightGray
                )
                Text(
                    text = cardNumber,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}

@Preview
@Composable
private fun PaymentSelectionScreenPreview() {
    OneAppTheme {
        PaymentSelectionScreen(
            uiState = PaymentSelectionUiState(
                selectedPayment = PaymentType.OTHER,
                minAmount = 100.0,
                fullAmount = 2000.0,
                cardNumber = "1234 **** **** 5678",
                otherAmount = ""
            ),
            onEvent = {}
        )
    }
}
