package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.cardpay.paymentselection

import sv.com.tarjetaone.domain.entities.response.CardPaymentAccountsDebitDataUI

sealed class PaymentSelectionUiEvent {
    data object OnStart : PaymentSelectionUiEvent()
    data object OnBackClick : PaymentSelectionUiEvent()
    data object OnTwilioClick : PaymentSelectionUiEvent()
    data object OnContinueMethodSelectionClick : PaymentSelectionUiEvent()
    data object OnContinuePaymentClick : PaymentSelectionUiEvent()
    data class OnSelectPayment(val payment: PaymentType) : PaymentSelectionUiEvent()
    data class OnOtherPaymentChange(val other: String) : PaymentSelectionUiEvent()
    data class OnPaymentDescChange(val desc: String) : PaymentSelectionUiEvent()
    data class OnAccountTypeChange(val accountType: String) : PaymentSelectionUiEvent()
    data class OnAccountChange(val account: CardPaymentAccountsDebitDataUI) : PaymentSelectionUiEvent()
}
