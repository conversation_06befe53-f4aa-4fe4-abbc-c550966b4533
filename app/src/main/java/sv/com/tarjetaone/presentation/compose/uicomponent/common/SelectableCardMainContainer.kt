package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CardElevation
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.LocalCustomColors
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme

/**
 * Main selectable container to be reuse on several items with the same logic to tint the background container.
 *
 * @param modifier to apply specific modifications to the card container.
 * @param selected to decide the container color to apply to the card.
 * @param boxContentAlignment to align the image at any card corner if applicable.
 * @param iconContent if an icon needs to be shown at any corner of the card.
 * @param content the main card content provided by any caller screen.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Suppress("kotlin:S107")
@Composable
fun SelectableCardMainContainer(
    modifier: Modifier = Modifier,
    selected: Boolean = false,
    selectedContainerColor: Color = LocalCustomColors.current.successContainer,
    unselectedContainerColor: Color = MaterialTheme.colorScheme.background,
    selectedContentColor: Color = MaterialTheme.colorScheme.onPrimary,
    unselectedContentColor: Color = MaterialTheme.colorScheme.onSurface,
    boxContentAlignment: Alignment = Alignment.TopEnd,
    elevation: CardElevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
    border: BorderStroke? = null,
    shape: Shape = CardDefaults.shape,
    onClick: () -> Unit = {},
    iconContent: (@Composable () -> Unit)? = null,
    content: @Composable ColumnScope.() -> Unit,
) {
    var containerColor = unselectedContainerColor
    var contentColor = unselectedContentColor

    if (selected) {
        containerColor = selectedContainerColor
        contentColor = selectedContentColor
    }

    Box(
        contentAlignment = boxContentAlignment
    ) {
        Card(
            modifier = modifier,
            shape = shape,
            colors = CardDefaults.cardColors(
                containerColor = containerColor,
                contentColor = contentColor
            ),
            elevation = elevation,
            border = border,
            onClick = onClick,
            content = content
        )
        iconContent?.let { iconContent() }
    }
}

@Preview
@Composable
fun SelectableCardMainContainerPreview() {
    OneAppTheme {
        SelectableCardMainContainer(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 12.dp, end = 15.dp, start = 8.dp),
            selected = true,
            content = {
                Column(
                    Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(text = "Replace with your own container")
                }
            },
            iconContent = {
                Image(
                    painter = painterResource(id = R.drawable.ic_best_option),
                    contentDescription = null,
                    alignment = Alignment.TopEnd
                )
            }
        )
    }
}
