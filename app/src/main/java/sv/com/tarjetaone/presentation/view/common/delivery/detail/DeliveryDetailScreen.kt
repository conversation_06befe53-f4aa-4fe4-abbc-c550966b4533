package sv.com.tarjetaone.presentation.view.common.delivery.detail

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.DAY_OF_WEEK
import sv.com.tarjetaone.common.utils.AppConstants.EIGHT_VALUE_PERCENT
import sv.com.tarjetaone.common.utils.AppConstants.YEAR_MONTH_DAY_WITH_DASH
import sv.com.tarjetaone.common.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.presentation.compose.theme.LocalCustomColors
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.OneAppSnackBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.helpers.capitalize

@Composable
fun DeliveryDetailScreen(viewModel: BaseAddressConfirmationViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    SideEffectHandler(viewModel.sideEffects) {
        DeliveryDetailScreen(
            uiState = uiState,
            onEvent = viewModel::onEvent
        )
    }
}

@Composable
fun DeliveryDetailScreen(
    uiState: DeliveryDetailUiState,
    onEvent: (DeliveryDetailUiEvent) -> Unit = {}
) {
    LaunchedEffect(Unit) {
        onEvent(DeliveryDetailUiEvent.OnStart)
    }
    ScreenWithTopAppBar(
        onLeftButtonClick = { onEvent(DeliveryDetailUiEvent.OnBackPressed) },
        onRightButtonClick = { onEvent(DeliveryDetailUiEvent.OnTwilioClick) },
        progress = EIGHT_VALUE_PERCENT,
        title = stringResource(id = R.string.delivery_title),
        isProgressbarVisible = true,
        isLeftButtonVisible = true
    ) {
        DeliveryDetailContent(
            uiState = uiState,
            onEvent = onEvent
        )
    }
}

@Composable
fun DeliveryDetailContent(
    uiState: DeliveryDetailUiState,
    onEvent: (DeliveryDetailUiEvent) -> Unit
) {
    Column(
        Modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.surface)
    ) {
        Spacer16()
        Column(
            modifier = Modifier
                .padding(horizontal = MaterialTheme.customDimens.dimen35)
                .weight(1f)
        ) {
            Text(
                text = uiState.address,
                style = MaterialTheme.typography.bodyLarge.copy(
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.customColors.gray5
                ),
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
            )
            Text(
                text = stringResource(id = R.string.address_label),
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.customColors.gray5,
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
            )
            if (uiState.schedule.isNotBlank()) {
                Spacer16()
                Text(
                    text = uiState.schedule,
                    style = MaterialTheme.typography.bodyLarge.copy(
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.customColors.gray5
                    ),
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                )
                Text(
                    text = stringResource(id = R.string.time),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.customColors.gray5,
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                )
            }
            Spacer16()
            Text(
                text = uiState.date.getFormattedDateFromTo(
                    YEAR_MONTH_DAY_WITH_DASH,
                    DAY_OF_WEEK
                ).capitalize(),
                style = MaterialTheme.typography.bodyLarge.copy(
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.customColors.gray5
                ),
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
            )
            Text(
                text = stringResource(id = R.string.date),
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.customColors.gray5,
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
            )
        }

        Column {
            if (uiState.dialogVisibility) {
                OneAppSnackBar(
                    icon = R.drawable.ic_check_circle_white,
                    containerColor = LocalCustomColors.current.cardLightBackground,
                    message = stringResource(id = R.string.delivery_address_warning),
                    onCancelClick = { onEvent(DeliveryDetailUiEvent.OnDismiss) },
                    modifier = Modifier
                        .wrapContentHeight()
                        .fillMaxWidth()
                        .padding(horizontal = MaterialTheme.customDimens.dimen35)
                )
            }

            Spacer32()

            OneButton(
                modifier = Modifier
                    .padding(
                        top = MaterialTheme.customDimens.dimen2,
                        bottom = MaterialTheme.customDimens.dimen16,
                        end = MaterialTheme.customDimens.dimen35,
                        start = MaterialTheme.customDimens.dimen35
                    )
                    .fillMaxWidth()
                    .wrapContentHeight(),
                text = stringResource(id = R.string.send_my_card),
                onClick = {
                    onEvent(DeliveryDetailUiEvent.OnClickContinue)
                }
            )
            OneButton(
                modifier = Modifier
                    .padding(
                        top = MaterialTheme.customDimens.dimen2,
                        bottom = MaterialTheme.customDimens.dimen16,
                        end = MaterialTheme.customDimens.dimen35,
                        start = MaterialTheme.customDimens.dimen35
                    )
                    .fillMaxWidth()
                    .wrapContentHeight(),
                buttonVariant = ButtonVariant.SECONDARY_VARIANT,
                text = stringResource(id = R.string.delivery_another_date),
                onClick = {
                    onEvent(DeliveryDetailUiEvent.OnSelectAnotherDate)
                }
            )
            Spacer16()
        }
    }
}

@Composable
@Preview
fun DeliveryDetailScreenPreview() {
    OneAppTheme {
        DeliveryDetailScreen(
            uiState = DeliveryDetailUiState(
                "address placeholder",
                "schedule placeholder",
                "date placeholder"
            )
        )
    }
}
