package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_cancel.survey.completed

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer64

@Composable
fun CardCancelCompletedSurveyScreen(viewModel: CardCancelCompletedSurveyViewModel) {
    CardCancelCompletedSurveyScreenContent(onEvent = viewModel::onEvent)
}

@Composable
fun CardCancelCompletedSurveyScreenContent(onEvent: (CardCancelCompletedSurveyUiEvent) -> Unit = {}) {
    ScreenWithTopAppBar(
        isLeftButtonVisible = false,
        onLeftButtonClick = {},
        onRightButtonClick = {
            onEvent(CardCancelCompletedSurveyUiEvent.OnTwilioClick)
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = MaterialTheme.customDimens.dimen24)
        ) {
            Column(
                modifier = Modifier.weight(ONE_FLOAT_VALUE),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Icon(
                    imageVector = ImageVector.vectorResource(
                        id = R.drawable.ic_checkbox_green_rounded_corners
                    ),
                    tint = MaterialTheme.customColors.successContainer,
                    contentDescription = null,
                )
                Spacer16()
                Text(
                    text = stringResource(R.string.finish_survey_pleased_title),
                    style = MaterialTheme.typography.displayMedium.copy(
                        color = MaterialTheme.customColors.successContainer
                    )
                )
                Spacer16()
                Text(
                    text = stringResource(R.string.finish_survey_pleased_subtitle),
                    style = MaterialTheme.typography.headlineSmall,
                    textAlign = TextAlign.Center
                )
                Spacer64()
                Text(
                    text = stringResource(R.string.finish_survey_pleased_description),
                    style = MaterialTheme.typography.bodySmall,
                    textAlign = TextAlign.Center
                )
            }
            OneButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = MaterialTheme.customDimens.dimen16),
                text = stringResource(R.string.back_to_my_account),
                onClick = { onEvent(CardCancelCompletedSurveyUiEvent.OnReturnHome) }
            )
        }
    }
}

@Preview
@Composable
fun CardCancelCompletedSurveyScreenContentPreview() {
    OneAppTheme {
        CardCancelCompletedSurveyScreenContent()
    }
}