package sv.com.tarjetaone.presentation.compose.util

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import sv.com.tarjetaone.presentation.compose.theme.customColors

enum class InfoStatus {
    None,
    Warning,
    Error,
    Success
}

@Composable
fun InfoStatus.statusColor(): Color {
    return when (this) {
        InfoStatus.None -> MaterialTheme.colorScheme.tertiary
        InfoStatus.Warning -> MaterialTheme.customColors.alertVariant
        InfoStatus.Error -> MaterialTheme.colorScheme.error
        InfoStatus.Success -> MaterialTheme.customColors.successContainer
    }
}
