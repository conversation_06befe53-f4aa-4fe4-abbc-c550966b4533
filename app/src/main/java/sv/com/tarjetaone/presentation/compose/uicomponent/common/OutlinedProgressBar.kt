package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.tooling.preview.Preview
import kotlinx.coroutines.delay
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.FIFTY_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ONE_SECOND
import sv.com.tarjetaone.common.utils.AppConstants.THREE_HUNDRED_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_FLOAT_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens

/**
 * @param progressBarTime Duration in seconds of the loading animation. If zero then an
 * indeterminate progress bar is rendered
 */
@Composable
fun OutlinedProgressBar(
    modifier: Modifier = Modifier,
    progressBarTime: Int = ZERO_VALUE
) {
    var currentTime by remember { mutableFloatStateOf(ONE_FLOAT_VALUE) }
    var progress by remember { mutableFloatStateOf(ZERO_FLOAT_VALUE) }
    val progressAnimate by animateFloatAsState(
        targetValue = progress,
        animationSpec = tween(
            durationMillis = THREE_HUNDRED_VALUE,//animation duration
            delayMillis = FIFTY_VALUE,//delay before animation start
            easing = LinearOutSlowInEasing
        ),
        label = EMPTY_STRING
    )
    Box(
        modifier = modifier
            .wrapContentSize()
            .clip(CircleShape)
            .background(MaterialTheme.customColors.progressBackgroundColor)
    ) {
        if (progressBarTime > ZERO_VALUE) {
            // Progressbar with progressBarTime animation
            LinearProgressIndicator(
                progress = { progressAnimate },
                modifier = Modifier
                    .height(MaterialTheme.customDimens.dimen10)
                    .padding(MaterialTheme.customDimens.dimen2)
                    .clip(MaterialTheme.shapes.large)
                    .fillMaxWidth(),
                color = MaterialTheme.customColors.progressColor,
                trackColor = MaterialTheme.customColors.progressBackgroundColor,
                drawStopIndicator = {}
            )
            LaunchedEffect(true) {
                while (currentTime > ZERO_VALUE) {
                    delay(ONE_SECOND)
                    currentTime++
                    progress = currentTime / progressBarTime
                    if (currentTime == progressBarTime.toFloat()) {
                        progress = ZERO_FLOAT_VALUE
                        currentTime = ONE_FLOAT_VALUE
                    }
                }
            }
        } else {
            // Indeterminate progress bar
            LinearProgressIndicator(
                color = MaterialTheme.customColors.progressColor,
                modifier = Modifier
                    .height(MaterialTheme.customDimens.dimen10)
                    .padding(MaterialTheme.customDimens.dimen2)
                    .clip(MaterialTheme.shapes.large)
                    .fillMaxWidth(),
                trackColor = MaterialTheme.customColors.progressBackgroundColor
            )
        }
    }
}

@Composable
@Preview
fun OutlinedProgressBarPreview() {
    OneAppTheme {
        OutlinedProgressBar()
    }
}
