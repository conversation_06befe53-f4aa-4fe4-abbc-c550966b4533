package sv.com.tarjetaone.presentation.compose.util.input

import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.input.OffsetMapping
import androidx.compose.ui.text.input.TransformedText
import androidx.compose.ui.text.input.VisualTransformation
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.presentation.helpers.filterDigits
import java.text.DecimalFormat
import java.text.NumberFormat
import java.util.Locale
import kotlin.math.max

/**
 * Visual transformation to format a number input as a currency.
 *
 * **See also:** [Currency input in compose](https://github.com/banmarkovic/CurrencyAmountInput)
 *
 * @param displayIfEmpty If true, the transformation will be applied and displayed even if the input is empty.
 * @param currencySymbol The currency symbol to be displayed before the number, none by default.
 * @param numberOfDecimals The number of decimal places to display, 2 by default.
 */
class CurrencyVisualTransformation(
    private val fixedCursorAtTheEnd: Boolean = true,
    private val displayIfEmpty: Boolean = true,
    private val currencySymbol: String? = null,
    private val numberOfDecimals: Int = DEFAULT_DECIMAL_DIGITS
) : VisualTransformation {
    private val symbols = DecimalFormat().decimalFormatSymbols

    override fun filter(text: AnnotatedString): TransformedText {
        val thousandsSeparator = symbols.groupingSeparator
        val decimalSeparator = symbols.decimalSeparator
        val zero = symbols.zeroDigit

        val inputText = text.text

        val intPart = inputText
            .dropLast(numberOfDecimals)
            .reversed()
            .chunked(THOUSANDS_SEPARATOR_THRESHOLD)
            .joinToString(thousandsSeparator.toString())
            .reversed()
            .ifEmpty {
                zero.toString()
            }

        val fractionPart = inputText.takeLast(numberOfDecimals).let {
            if (it.length != numberOfDecimals) {
                List(numberOfDecimals - it.length) {
                    zero
                }.joinToString(EMPTY_STRING) + it
            } else {
                it
            }
        }

        val formattedNumber = buildString {
            append(intPart)
            if (numberOfDecimals > 0) {
                append(decimalSeparator)
                append(fractionPart)
            }
        }
        val numberWithSymbol = currencySymbol?.let { it + formattedNumber } ?: formattedNumber
        val ignoreTransformation = !displayIfEmpty && inputText.isEmpty()

        val newText = AnnotatedString(
            text = if (ignoreTransformation) inputText else numberWithSymbol,
            spanStyles = text.spanStyles,
            paragraphStyles = text.paragraphStyles
        )

        val offsetMapping = if (fixedCursorAtTheEnd) {
            FixedCursorOffsetMapping(
                contentLength = inputText.length,
                formattedContentLength = if (ignoreTransformation) inputText.length
                else numberWithSymbol.length
            )
        } else {
            MovableCursorOffsetMapping(
                unmaskedText = text.toString(),
                maskedText = newText.toString(),
                decimalDigits = numberOfDecimals
            )
        }

        return TransformedText(newText, offsetMapping)
    }

    private class FixedCursorOffsetMapping(
        private val contentLength: Int,
        private val formattedContentLength: Int,
    ) : OffsetMapping {
        override fun originalToTransformed(offset: Int): Int = formattedContentLength
        override fun transformedToOriginal(offset: Int): Int = contentLength
    }

    private class MovableCursorOffsetMapping(
        private val unmaskedText: String,
        private val maskedText: String,
        private val decimalDigits: Int
    ) : OffsetMapping {
        override fun originalToTransformed(offset: Int): Int =
            when {
                unmaskedText.length <= decimalDigits -> {
                    maskedText.length - (unmaskedText.length - offset)
                }

                else -> {
                    offset + offsetMaskCount(offset, maskedText)
                }
            }

        override fun transformedToOriginal(offset: Int): Int =
            when {
                unmaskedText.length <= decimalDigits -> {
                    max(unmaskedText.length - (maskedText.length - offset), 0)
                }

                else -> {
                    offset - maskedText.take(offset).count { !it.isDigit() }
                }
            }

        private fun offsetMaskCount(offset: Int, maskedText: String): Int {
            var maskOffsetCount = 0
            var dataCount = 0
            for (maskChar in maskedText) {
                if (!maskChar.isDigit()) {
                    maskOffsetCount++
                } else if (++dataCount > offset) {
                    break
                }
            }
            return maskOffsetCount
        }
    }

    companion object {
        private const val DEFAULT_DECIMAL_DIGITS = 2
        private const val THOUSANDS_SEPARATOR_THRESHOLD = 3
    }
}

class CurrencyLimitVisualTransformation(
    private val locale: Locale = Locale.US
) : VisualTransformation {
    private val numberFormat = NumberFormat.getCurrencyInstance(locale).apply {
        maximumFractionDigits = 0 // Ensure only integers are displayed
    }

    override fun filter(text: AnnotatedString): TransformedText {
        val originalText = text.text.filterDigits()
        val parsedNumber = originalText.toLongOrNull() ?: 0L
        val formattedText = numberFormat.format(parsedNumber)// Add space and dollar symbol

        val offsetMapping = object : OffsetMapping {
            override fun originalToTransformed(offset: Int): Int {
                return if (originalText.isEmpty()) originalText.length
                else formattedText.length
            }

            override fun transformedToOriginal(offset: Int): Int {
                return originalText.length
            }
        }

        return TransformedText(
            AnnotatedString(
                if (originalText.isEmpty()) originalText else formattedText
            ),
            offsetMapping
        )
    }

    private class FixedCursorOffsetMapping(
        private val contentLength: Int,
        private val formattedContentLength: Int,
    ) : OffsetMapping {
        override fun originalToTransformed(offset: Int): Int = formattedContentLength
        override fun transformedToOriginal(offset: Int): Int = contentLength
    }
}
