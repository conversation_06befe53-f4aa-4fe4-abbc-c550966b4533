package sv.com.tarjetaone.presentation.view.common.reference_overview

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.extensions.formatPhoneNumber
import sv.com.tarjetaone.domain.entities.response.ReferenceUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.SolidLargeButton
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.view.common.reference_form.components.ReferenceItem

@Composable
fun ReferenceOverviewContent(
    uiState: ReferenceOverviewUiState,
    onEvent: (ReferenceOverviewEvent) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.customDimens.dimen32)
    ) {
        Spacer16()
        ReferenceItem(
            fullName = uiState.reference?.fullName.orEmpty(),
            phoneNumber = uiState.reference?.phoneNumber.formatPhoneNumber().orEmpty(),
            referenceTypeName = uiState.reference?.referenceTypeName.orEmpty(),
            showDeleteTextButton = false,
            onModifyReference = { onEvent(ReferenceOverviewEvent.OnModifyReference) },
        )
        Spacer32()
        Spacer1f()
        SolidLargeButton(
            text = stringResource(id = R.string.continue_button_label)
        ) {
            onEvent(ReferenceOverviewEvent.OnContinueClick)
        }
        Spacer32()
    }
}

@Preview(
    showSystemUi = true,
    showBackground = true
)
@Composable
private fun ReferenceOverviewContentPreview() {
    OneAppTheme {
        ReferenceOverviewContent(
            uiState = ReferenceOverviewUiState(
                reference = ReferenceUI(
                    fullName = "John Doe",
                    phoneNumber = "7777 7777",
                    referenceTypeName = "Amigo(a)"
                )
            ),
            onEvent = { }
        )
    }
}
