package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.phonenotifications

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.domain.entities.response.OtpMethod
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.DialogConfirmation
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer12
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.helpers.UiText

@Composable
fun AdditionalCardPhoneNotificationsScreen(
    viewModel: AdditionalCardPhoneNotificationsViewModel
) {
    AdditionalCardPhoneNotificationsContent(
        onEvent = viewModel::onEvent
    )
}

@Composable
fun AdditionalCardPhoneNotificationsContent(
    onEvent: (AdditionalCardPhoneNotificationsUiEvent) -> Unit = {}
) {
    ScreenWithTopAppBar(
        title = stringResource(R.string.ask_for_mobile_notifications_title),
        progress = AppConstants.EIGHT_VALUE_PERCENT,
        isProgressbarVisible = true,
        onLeftButtonClick = {
            onEvent(AdditionalCardPhoneNotificationsUiEvent.OnBackClick)
        },
        onRightButtonClick = {
            onEvent(AdditionalCardPhoneNotificationsUiEvent.OnTwilioClick)
        }
    ) {
        var showOtpMethodDialog by remember { mutableStateOf(false) }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = MaterialTheme.customDimens.dimen24)
        ) {
            Spacer1f()
            OneButton(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(R.string.yes),
                onClick = {
                    showOtpMethodDialog = true
                }
            )
            Spacer12()
            OneButton(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(R.string.no),
                buttonVariant = ButtonVariant.SECONDARY_VARIANT,
                onClick = {
                    onEvent(
                        AdditionalCardPhoneNotificationsUiEvent.OnDenyNotificationsButtonClick
                    )
                }
            )
            Spacer1f()
        }

        if (showOtpMethodDialog) {
            DialogConfirmation(
                messageStyle = MaterialTheme.typography.labelMedium,
                message = UiText.StringResource(R.string.otp_channel_string),
                primaryButtonText = UiText.StringResource(R.string.otp_whatsapp),
                secondaryButtonText = UiText.StringResource(R.string.otp_sms),
                onPositiveClick = {
                    onEvent(
                        AdditionalCardPhoneNotificationsUiEvent.OnSelectOtpMethod(
                            OtpMethod.WHATSAPP
                        )
                    )
                },
                onNegativeClick = {
                    onEvent(
                        AdditionalCardPhoneNotificationsUiEvent.OnSelectOtpMethod(
                            OtpMethod.SMS
                        )
                    )
                },
                onDismissRequest = {
                    showOtpMethodDialog = false
                }
            )
        }
    }
}

@Preview
@Composable
private fun AdditionalCardPhoneNotificationsPreview() {
    OneAppTheme {
        AdditionalCardPhoneNotificationsContent()
    }
}