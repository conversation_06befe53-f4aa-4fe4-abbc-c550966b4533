package sv.com.tarjetaone.presentation.view.common.survey.row

import androidx.compose.runtime.Stable
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.SURVEY_MAX_CHARACTERS
import sv.com.tarjetaone.domain.entities.request.SurveyOptionType.OPTION_OPEN
import sv.com.tarjetaone.domain.entities.response.OptionsSurveyUI

@Stable
data class SurveyRowTypeUiState(
    val surveyOptions: List<OptionsSurveyUI> = emptyList(),
    val description: String = EMPTY_STRING,
    val selectedOption: OptionsSurveyUI? = null,
    val additionalComments: String = EMPTY_STRING
) {
    private val isOptionSelected = selectedOption != null
    private val isOptionOpenType =
        selectedOption?.type == OPTION_OPEN.type
    private val isAdditionalCommentValid =
        additionalComments.isNotEmpty() && additionalComments.length <= SURVEY_MAX_CHARACTERS

    val isSendButtonEnabled = if (isOptionOpenType) {
        isAdditionalCommentValid
    } else {
        isOptionSelected
    }
}