package sv.com.tarjetaone.presentation.view.lobby.menu.dataedit

import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING

data class PersonalDataEditUiState(
    val name: String = EMPTY_STRING,
    val lastName: String = EMPTY_STRING,
    val dui: String = EMPTY_STRING,
    val duiError: DuiError = DuiError.None,
    val errorList: List<Boolean> = listOf(),
    val isSaveEnabled: Boolean = false
)

sealed class DuiError {
    data object None : DuiError()
    data object InvalidCharacters : DuiError()
    data object Incomplete : DuiError()
}