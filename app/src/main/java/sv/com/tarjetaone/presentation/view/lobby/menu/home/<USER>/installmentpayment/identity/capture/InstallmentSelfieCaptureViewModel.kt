package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.installmentpayment.identity.capture

import android.graphics.Bitmap
import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.presentation.view.common.selfiecapture.SelfieCaptureBaseViewModel
import javax.inject.Inject

@HiltViewModel
class InstallmentSelfieCaptureViewModel @Inject constructor(
    facephiResultHandler: FacephiResultHandler,
    savedStateHandle: SavedStateHandle
) : SelfieCaptureBaseViewModel(facephiResultHandler) {

    private val args = InstallmentSelfieCaptureFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onSuccessfulSelfieCapture(selfie: Bitmap) {
        sendEvent(
            UiEvent.Navigate(
                InstallmentSelfieCaptureFragmentDirections
                    .navigateToInstallmentPaymentSelfieConfirmationFragment(
                        transaction = args.transaction,
                        selectedInstallment = args.selectedInstallment,
                        totalAmount = args.totalAmount,
                    )
            )
        )
    }
}
