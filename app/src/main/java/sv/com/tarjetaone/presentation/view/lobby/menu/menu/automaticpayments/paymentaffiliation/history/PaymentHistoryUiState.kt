package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.history

import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.core.utils.extensions.CalendarFormat.DAY_MONTH_YEAR
import sv.com.tarjetaone.core.utils.extensions.toDate
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.transactionlist.RecurringTransactionUI

data class PaymentHistoryUiState(
    val openDialog: Boolean = false,
    // Indicate if the user selected a custom date range over the predefined ones
    val customDateIsSelected: Boolean = false,
    val dateInputTypeSelected: PaymentHistoryDateType? = null,
    val serviceName: String = EMPTY_STRING,
    val startDate: String = EMPTY_STRING,
    val endDate: String = EMPTY_STRING,
    val transactionList: List<RecurringTransactionUI> = emptyList()
)

fun PaymentHistoryUiState.getDateInMillis(): Long? {
    return when (this.dateInputTypeSelected) {
        PaymentHistoryDateType.START_DATE -> startDate.toDate(DAY_MONTH_YEAR)?.time
        PaymentHistoryDateType.END_DATE -> endDate.toDate(DAY_MONTH_YEAR)?.time
        else -> null
    }
}

enum class PaymentHistoryDateType {
    START_DATE,
    END_DATE
}

object RecurringTransactionDummyItems {
    val dummyTransaction = RecurringTransactionUI(
        transactionDate = "03/01/2024",
        transactionTime = "12:00",
        description = "Pago de tarjeta",
        responseType = "Aprobado",
        amount = "90.00",
    )

    val dummyTransactionList = listOf(
        dummyTransaction,
        dummyTransaction.copy(
            transactionDate = "03/02/2024",
            amount = "80.00"
        ),
        dummyTransaction.copy(
            transactionDate = "03/03/2024",
            amount = "75.00"
        ),
        dummyTransaction.copy(
            transactionDate = "03/04/2024",
            amount = "100.00"
        )
    )
}
