package sv.com.tarjetaone.presentation.compose.uicomponent.common

import android.graphics.Bitmap
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens

/**
 * Reusable DUI Card component to be displayed on DUI confirmation screens.
 */
@Composable
fun DuiCardImageComponent(
    bitmap: Bitmap,
    modifier: Modifier = Modifier
) {
    Image(
        bitmap = bitmap.asImageBitmap(),
        contentScale = ContentScale.Crop,
        contentDescription = null,
        modifier = modifier
            .fillMaxWidth()
            .heightIn(max = MaterialTheme.customDimens.dimen220)
            .padding(horizontal = MaterialTheme.customDimens.dimen16)
            .clip(MaterialTheme.shapes.medium),
    )
}

@Preview
@Composable
fun DuiCardImageComponentPreview() {
    OneAppTheme {
        DuiCardImageComponent(
            bitmap = Bitmap.createBitmap(1, 1, Bitmap.Config.ALPHA_8)
        )
    }
}
