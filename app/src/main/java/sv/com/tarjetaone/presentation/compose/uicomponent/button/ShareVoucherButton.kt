package sv.com.tarjetaone.presentation.compose.uicomponent.button

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens

@Composable
fun ShareVoucherButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {}
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .clickable { onClick() },
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_share),
            contentDescription = null,
            modifier = Modifier.padding(end = MaterialTheme.customDimens.dimen16)
        )
        Text(
            text = stringResource(id = R.string.share_receipt),
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.primary,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = MaterialTheme.customDimens.dimen4)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun ShareVoucherButtonPreview() {
    OneAppTheme {
        ShareVoucherButton(
            modifier = Modifier.padding(MaterialTheme.customDimens.dimen16),
        )
    }
}
