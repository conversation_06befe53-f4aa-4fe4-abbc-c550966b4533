package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_cancel.carousel.component

import com.google.gson.Gson
import sv.com.tarjetaone.common.utils.AppConstants.CARD_LAST_DIGITS
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.CardColor
import sv.com.tarjetaone.data.api.models.ArrayColor
import sv.com.tarjetaone.domain.entities.response.AdditionalCancelCardColorUI
import sv.com.tarjetaone.domain.entities.response.MainCancelCardColorUI
import sv.com.tarjetaone.domain.entities.response.MainCardUI

private fun createMainCardUI(
    creditCardId: Int?,
    balanceAvailable: Double?,
    cardNumMasked: String?,
    currentBalance: Double?,
    expirationDate: String?,
    colors: CardColor,
    isMainCard: Boolean
): MainCardUI = MainCardUI(
    creditCardId = creditCardId,
    balanceAvailable = balanceAvailable,
    cardNumMasked = cardNumMasked?.getCardLastDigits(),
    currentBalance = currentBalance,
    expirationDate = expirationDate,
    colors = colors,
    mainCard = isMainCard
)

fun MainCancelCardColorUI?.toMainCardUI() = createMainCardUI(
    creditCardId = this?.creditCardId,
    balanceAvailable = this?.balanceAvailable,
    cardNumMasked = this?.cardNumMasked,
    currentBalance = this?.currentBalance,
    expirationDate = this?.expirationDate,
    colors = getColors(
        this?.cardColor,
        this?.textColor,
        this?.visaColor
    ),
    isMainCard = true
)

fun AdditionalCancelCardColorUI?.toMainCardUI() = createMainCardUI(
    creditCardId = this?.creditCardId,
    balanceAvailable = this?.creditLimit,
    cardNumMasked = this?.cardNumMasked,
    currentBalance = null,
    expirationDate = this?.expirationDate,
    colors = getColors(
        this?.cardColor,
        this?.textColor,
        this?.visaColor
    ),
    isMainCard = false
)

private fun getColors(cardColor: String?, textColor: String?, visaColor: String?): CardColor {
    val colorJson = "{\"Colors\":[${cardColor}]}"
    val colorCardCatalog = Gson().fromJson(colorJson, ArrayColor::class.java)
    return CardColor(
        colors = colorCardCatalog.colors.map { it.color },
        iconColor = visaColor.orEmpty(),
        textColor = textColor.orEmpty(),
        id = EMPTY_STRING
    )
}

private fun String.getCardLastDigits() = if (length >= CARD_LAST_DIGITS) {
    takeLast(CARD_LAST_DIGITS)
} else {
    this
}