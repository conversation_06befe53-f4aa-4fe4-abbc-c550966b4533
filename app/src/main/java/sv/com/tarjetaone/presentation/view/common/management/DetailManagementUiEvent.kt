package sv.com.tarjetaone.presentation.view.common.management

import android.graphics.Bitmap

sealed class DetailManagementUiEvent {
    data object OnStart : DetailManagementUiEvent()
    data object OnPrimaryButtonClick : DetailManagementUiEvent()
    data object OnSecondaryButtonClick : DetailManagementUiEvent()
    data class OnStoragePermissionGranted(val bitmap: Bitmap?) : DetailManagementUiEvent()
    data class OnStoragePermissionDenied(val showRationale: Boolean) : DetailManagementUiEvent()
    data object OnHideComponentBeforeCapture : DetailManagementUiEvent()
    data class OnShareVoucherClick(
        val requestPermissionCallback: () -> Unit
    ) : DetailManagementUiEvent()
    data object OnBackButtonCLick : DetailManagementUiEvent()
    data object OnTwilioClick : DetailManagementUiEvent()
    data object OnExitButtonClick: DetailManagementUiEvent()
}