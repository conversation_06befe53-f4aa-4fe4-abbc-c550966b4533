package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.contact_channel

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.request.ContactNotificationType
import sv.com.tarjetaone.domain.entities.request.CustomerNotificationRequestUI
import sv.com.tarjetaone.domain.usecases.personalData.contacts.GetCustomerContactsUseCase
import sv.com.tarjetaone.domain.usecases.personalData.contacts.UpdateCustomerNotificationUseCase
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.contact_channel.component.ContactItemUiState
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.contact_channel.component.updateFlags

@HiltViewModel
class ContactChannelViewModel @Inject constructor(
    private val getCustomerContactsUseCase: GetCustomerContactsUseCase,
    private val updateCustomerNotificationUseCase: UpdateCustomerNotificationUseCase,
    private val sharedPrefs: SecureSharedPreferencesRepository
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(ContactChannelsUiState())
    val uiState = _uiState.asStateFlow()

    init {
        getCustomerContacts()
    }

    private fun getCustomerContacts() {
        sendEvent(UiEvent.Loading(true))
        viewModelScope.launch {
            getCustomerContactsUseCase(sharedPrefs.getCustomerId()).executeUseCase {
                sendEvent(UiEvent.Loading(false))
                if (it.statusResponse?.responseStatus?.code == AppConstants.SUCCESS_RESPONSE_CODE) {
                    val contacts = it.data.contactTypes?.flatMap { contactType ->
                        contactType.customerContacts?.map { customerContact ->
                            ContactItemUiState(
                                contactType = contactType.contactType,
                                contact = customerContact
                            )
                        }.orEmpty()
                    }.orEmpty()
                    _uiState.value.contactChannels.addAll(contacts)
                } else {
                    showUpsErrorMessage(false) { onBackClick() }
                }
            }
        }
    }

    private fun onToggleNotification(
        isActive: Boolean,
        contactIndex: Int,
        contactItem: ContactItemUiState,
        notificationType: ContactNotificationType
    ) {
        // Visually update the contact item to reflect the toggle
        updateContactChannelFlags(contactItem, contactIndex, isActive, notificationType)

        viewModelScope.launch {
            sendEvent(UiEvent.Loading(true))
            val request = CustomerNotificationRequestUI(
                customerId = sharedPrefs.getCustomerId().orZero(),
                customerContactId = contactItem.contact.customerContactId.orZero(),
                notificationType = notificationType.type,
                isActive = isActive
            )
            updateCustomerNotificationUseCase(request).executeUseCase(
                onNetworkErrorAction = {
                    showUpsErrorMessage()
                    updateContactChannelFlags(contactItem, contactIndex, !isActive, notificationType)
                },
                onApiErrorAction = { _, _, _, _ ->
                    showUpsErrorMessage()
                    updateContactChannelFlags(contactItem, contactIndex, !isActive, notificationType)
                },
                onSuccessAction = {
                    sendEvent(UiEvent.Loading(false))
                    if (it.statusResponse?.responseStatus?.code != AppConstants.SUCCESS_RESPONSE_CODE) {
                        showUpsErrorMessage()
                        // If the request returns an error or fails, revert the visual change
                        updateContactChannelFlags(contactItem, contactIndex, !isActive, notificationType)
                    } // if it succeeds, do nothing
                }
            )
        }
    }

    private fun updateContactChannelFlags(
        contactItem: ContactItemUiState,
        index: Int,
        isActive: Boolean,
        notificationType: ContactNotificationType
    ) {
        _uiState.value.contactChannels[index] = contactItem.copy(
            contact = contactItem.contact.updateFlags(isActive, notificationType)
        )
    }

    private fun onModifyClick(contactItem: ContactItemUiState) {
        sendEvent(
            UiEvent.Navigate(
                ContactChannelOverviewFragmentDirections
                    .actionContactChannelOverviewFragmentToPrepareForPictureUpdateContactFragment(
                        contactItem.contactType,
                        contactItem.contact.customerContactId.orZero()
                    )
            )
        )
    }

    private fun onBackClick() {
        sendEvent(
            UiEvent.Navigate(
                ContactChannelOverviewFragmentDirections
                    .actionContactChannelOverviewFragmentToPersonalDataFragment()
            )
        )
    }

    fun onEvent(event: ContactChannelsUiEvent) {
        when (event) {
            ContactChannelsUiEvent.OnBackClick -> onBackClick()
            ContactChannelsUiEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
            is ContactChannelsUiEvent.OnModifyClick -> onModifyClick(event.contactItem)
            is ContactChannelsUiEvent.OnToggleNotification -> onToggleNotification(
                event.isActive,
                event.contactIndex,
                event.contactItem,
                event.notificationType
            )
        }
    }
}
