package sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.contract.contractsunsign

import androidx.lifecycle.SavedStateHandle
import androidx.navigation.NavDirections
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.helpers.CryptoHelper
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.core.utils.files.FileUtils
import sv.com.tarjetaone.domain.entities.response.ContractsMode
import sv.com.tarjetaone.domain.entities.response.DataContractUI
import sv.com.tarjetaone.domain.usecases.contracts.GetContractsUseCase
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.common.contracts.BaseContractsViewModel
import javax.inject.Inject

@HiltViewModel
class ProgramProtectionContractsViewModel @Inject constructor(
    getContractsUseCase: GetContractsUseCase,
    fileUtils: FileUtils,
    cryptoHelper: CryptoHelper,
    savedStateHandle: SavedStateHandle
) : BaseContractsViewModel(getContractsUseCase, fileUtils, cryptoHelper) {

    private val args = ProgramProtectionContractsFragmentArgs
        .fromSavedStateHandle(savedStateHandle)

    override fun onStart() {
        if (_uiState.value.isContractsDataLoaded) return

        _uiState.update {
            it.copy(
                isBackEnabled = true,
                primaryButtonText = UiText.StringResource(R.string.hire_label),
                contracts = getReadableContract()
            )
        }

        getContracts(
            mode = ContractsMode.ENVELOP,
            queryId = args.fraud?.envelopeId.orZero()
        )
    }

    private fun getReadableContract(): List<DataContractUI> =
        args.fraud?.document?.let { contract ->
            listOf(contract.copy(isReadOnly = true, checked = true))
        }.orEmpty()

    override fun getSeeContractDestination(): NavDirections =
        ProgramProtectionContractsFragmentDirections
            .actionProgramProtectionContractsFragmentToProgramProtectionContractsViewerFragment(
                fileName = _uiState.value.documentName,
                fileUri = _uiState.value.documentUri
            )

    override fun onPrimaryClick() {
        sendEvent(
            UiEvent.Navigate(
                ProgramProtectionContractsFragmentDirections
                    .actionProgramProtectionContractsFragmentToSignProtectionProgramFragment(
                        args.fraud
                    )
            )
        )
    }

    override fun onAllContractsChecked() = Unit

    override fun onBackAction() {
        sendEvent(UiEvent.Navigate(ProgramProtectionContractsFragmentDirections.actionHome()))
    }
}