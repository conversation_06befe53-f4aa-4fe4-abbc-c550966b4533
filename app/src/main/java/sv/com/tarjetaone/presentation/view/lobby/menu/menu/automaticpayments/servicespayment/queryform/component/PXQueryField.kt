package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.queryform.component

import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.input.ImeAction
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldType
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldValueType
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedDropdown
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.helpers.capitalize
import sv.com.tarjetaone.presentation.helpers.dropLeadingZeros
import sv.com.tarjetaone.presentation.helpers.filterDigits

@Composable
fun PXQueryField(
    modifier: Modifier = Modifier,
    fieldState: PXFieldUiState,
    onTypedValueChange: (String) -> Unit,
    onSelectedValueChange: (Pair<String, PXFieldUI>) -> Unit,
    isLastField: Boolean
) {
    when (fieldState.pxField.type) {
        PXFieldType.SelectBox -> {
            SimpleElevatedDropdown(
                modifier = modifier,
                items = fieldState.pxField.valueList?.value?.map { it.toPair() }.orEmpty(),
                itemLabel = { it.second.label?.lowercase()?.capitalize().orEmpty() },
                value = fieldState.selectedValue,
                onValueChange = onSelectedValueChange,
                label = fieldState.pxField.label?.lowercase()?.capitalize(),
                placeholder = fieldState.pxField.tooltip?.lowercase()?.capitalize()
            )
        }
        PXFieldType.Input -> {
            val focusManager = LocalFocusManager.current
            SimpleElevatedTextField(
                modifier = modifier,
                value = fieldState.typedValue,
                onValueChange = {
                    // Apply a filter to the input based on the value type
                    val filteredValue = when (fieldState.pxField.valueString?.type) {
                        PXFieldValueType.Decimal,
                        PXFieldValueType.Double,
                        PXFieldValueType.Money,
                        PXFieldValueType.Integer,
                        PXFieldValueType.Long -> it.filterDigits().dropLeadingZeros()

                        else -> it
                    }
                    onTypedValueChange(filteredValue)
                },
                placeholder = fieldState.pxField.label?.lowercase()?.capitalize(),
                label = fieldState.pxField.label?.lowercase()?.capitalize(),
                keyboardOptions = KeyboardOptions(
                    imeAction = if (isLastField) ImeAction.Done else ImeAction.Next,
                    keyboardType = fieldState.pxField.valueString?.type.keyboardType()
                ),
                keyboardActions = KeyboardActions(
                    onNext = { focusManager.moveFocus(FocusDirection.Down) },
                    onDone = { focusManager.clearFocus() }
                ),
                visualTransformation = fieldState.pxField.valueString?.type.visualTransformation()
            )
        }
        // The rest of the cases will not be part of a query form
        else -> Unit
    }
}
