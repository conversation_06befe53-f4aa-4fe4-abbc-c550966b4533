package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.references.referenceform

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.request.CustomerReferenceMUI
import sv.com.tarjetaone.domain.entities.request.CustomerReferenceModRequestUI
import sv.com.tarjetaone.domain.entities.request.CustomerReferencePostRequestUI
import sv.com.tarjetaone.domain.entities.request.CustomerReferenceRequestUI
import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI
import sv.com.tarjetaone.domain.entities.response.CustomerContactValidationResponseUI
import sv.com.tarjetaone.domain.entities.response.ReferenceUI
import sv.com.tarjetaone.domain.usecases.catalog.GetCatalogUseCase
import sv.com.tarjetaone.domain.usecases.personalData.refereces.PostCustomerReferenceUseCase
import sv.com.tarjetaone.domain.usecases.personalData.refereces.PutCustomerReferenceUseCase
import sv.com.tarjetaone.domain.usecases.requestcard.validatecustomercontact.ValidateCustomerContactUseCase
import sv.com.tarjetaone.domain.utils.onApiError
import sv.com.tarjetaone.domain.utils.onNetworkError
import sv.com.tarjetaone.domain.utils.onSuccess
import sv.com.tarjetaone.presentation.helpers.ReferenceType
import sv.com.tarjetaone.presentation.helpers.hasInvalidCharsOrEmojis
import sv.com.tarjetaone.presentation.helpers.hasInvalidPhoneNumber
import sv.com.tarjetaone.presentation.view.common.reference_form.ReferenceFormBaseViewModel
import sv.com.tarjetaone.presentation.view.common.reference_form.ReferenceFormEvent
import sv.com.tarjetaone.presentation.view.common.reference_form.ReferenceFormUiState
import javax.inject.Inject

@HiltViewModel
class ReferencesFormViewModel @Inject constructor(
    getCatalogUseCase: GetCatalogUseCase,
    validateCustomerContactUseCase: ValidateCustomerContactUseCase,
    private val postCustomerReferenceUseCase: PostCustomerReferenceUseCase,
    private val putCustomerReferenceUseCase: PutCustomerReferenceUseCase,
    private val sharedPrefs: SecureSharedPreferencesRepository,
    savedStateHandle: SavedStateHandle
) : ReferenceFormBaseViewModel(
    getCatalogUseCase,
    validateCustomerContactUseCase,
    sharedPrefs
) {
    private val args = ReferenceFormOverviewFragmentArgs.fromSavedStateHandle(savedStateHandle)

    private val _uiState: MutableStateFlow<ReferenceFormUiState> =
        MutableStateFlow(ReferenceFormUiState())
    val uiState = _uiState.asStateFlow()

    private fun onStart() {
        _uiState.update { state ->
            state.copy(
                referenceTypeLabel = when (args.referenceType) {
                    ReferenceType.FAMILY -> R.string.family_parent
                    ReferenceType.PERSONAL -> R.string.relation_ship_label_personal
                    else -> R.string.family_parent
                },
                referenceName = args.referenceData?.fullName.orEmpty(),
                referencePhoneNumber = args.referenceData?.phoneNumber.orEmpty(),
                isDropdownEnabled = args.referenceData == null
            )
        }
    }

    private fun onContinueButtonClicked() {
        args.referenceData?.let { reference ->
            onModifyReference(reference = reference)
        } ?: run {
            onSaveReference()
        }
    }

    private fun onModifyReference(reference: ReferenceUI) {
        viewModelScope.launch {
            sendEvent(SideEffect.Loading(true))
            val customerId = sharedPrefs.getCustomerId().orZero()
            val request = CustomerReferenceModRequestUI(
                biometryId = args.biometryId.orZero(),
                customerId = customerId.orZero(),
                customerReference = CustomerReferenceMUI(
                    fullName = _uiState.value.referenceName,
                    phoneNumber = _uiState.value.referencePhoneNumber,
                    referenceTypeId = _uiState.value.referenceValueSelected?.id?.toIntOrNull()
                        .orZero(),
                    customerReferenceId = reference.customerReferenceId.orZero()
                )
            )
            putCustomerReferenceUseCase(request)
                .onSuccess { _ ->
                    sendEvent(SideEffect.Loading(false))
                    sendEvent(
                        UiEvent.Navigate(
                            ReferenceFormOverviewFragmentDirections
                                .actionReferencesFormFragmentToSuccessUpdateReferencesFragment(
                                    referenceAdded = false
                                )
                        )
                    )
                }
                .onApiError { _, _, _, _ ->
                    showUpsErrorMessage()
                }
                .onNetworkError {
                    showUpsErrorMessage()
                }
        }
    }

    private fun onSaveReference() {
        viewModelScope.launch {
            sendEvent(SideEffect.Loading(true))
            val customerId = sharedPrefs.getCustomerId().orZero()
            val request = CustomerReferenceRequestUI(
                biometryId = args.biometryId.orZero(),
                customerId = customerId.orZero(),
                customerReference = CustomerReferencePostRequestUI(
                    fullName = _uiState.value.referenceName,
                    phoneNumber = _uiState.value.referencePhoneNumber,
                    referenceTypeId = _uiState.value.referenceValueSelected?.id?.toIntOrNull()
                        .orZero()
                )
            )
            postCustomerReferenceUseCase(request)
                .onSuccess { _ ->
                    sendEvent(SideEffect.Loading(false))
                    sendEvent(
                        UiEvent.Navigate(
                            ReferenceFormOverviewFragmentDirections
                                .actionReferencesFormFragmentToSuccessUpdateReferencesFragment(
                                    referenceAdded = true
                                )
                        )
                    )
                }
                .onApiError { _, _, _, _ ->
                    showUpsErrorMessage()
                }
                .onNetworkError {
                    showUpsErrorMessage()
                }
        }
    }

    private fun onNameChanged(name: String) {
        _uiState.update { state ->
            state.copy(
                referenceName = name,
                referenceNameError = name.isBlank() || name.hasInvalidCharsOrEmojis()
            )
        }
    }

    private fun onPhoneNumberChanged(phoneNumber: String) {
        _uiState.update { state ->
            state.copy(
                referencePhoneNumber = phoneNumber,
                referencePhoneNumberError = phoneNumber.isBlank()
                        || phoneNumber.hasInvalidPhoneNumber()
            )
        }
    }

    private fun onReferenceValueChanged(value: CatalogItemsCollectionUI?) {
        _uiState.update { state ->
            state.copy(referenceValueSelected = value)
        }
    }

    override fun onFailedFetchCatalog() {
        sendEvent(UiEvent.NavigateBackTo(R.id.customerReferencesOverview))
    }

    override fun onCatalogFetched(referencesList: List<CatalogItemsCollectionUI>) {
        _uiState.update { state ->
            state.copy(
                referencesList = referencesList,
                referenceValueSelected = referencesList.firstOrNull { item ->
                    item.id == args.referenceData?.referenceTypeId?.toString()
                } ?: referencesList.first()
            )
        }
    }

    private fun onValidatePhoneNumber(phoneNumber: String) {
        if (phoneNumber == args.referenceData?.phoneNumber) return
        _uiState.update { state ->
            state.copy(
                isValidatingPhone = true,
                referencePhoneNumberError = false,
                phoneNumberErrorMessage = AppConstants.EMPTY_STRING,
            )
        }
        validateCustomerContact(phoneNumber)
    }

    override fun onPhoneNumberValidated(response: CustomerContactValidationResponseUI) {
        val isValidPhone = response.data
        val responseStatus = response.statusResponse?.responseStatus
        _uiState.update { state ->
            state.copy(
                referencePhoneNumberError = isValidPhone.not(),
                phoneNumberErrorMessage = if (isValidPhone) {
                    AppConstants.EMPTY_STRING
                } else {
                    responseStatus?.message.orEmpty()
                },
                isValidatingPhone = false
            )
        }
        if (responseStatus?.code == AppConstants.ONE_VALUE) onDefaultApiError()
    }

    fun onEvent(event: ReferenceFormEvent) {
        when (event) {
            is ReferenceFormEvent.OnStart -> onStart()
            is ReferenceFormEvent.OnFetchReferences -> fetchReferences(
                referenceType = args.referenceType
            )

            is ReferenceFormEvent.OnNameChanged -> onNameChanged(event.name)
            is ReferenceFormEvent.OnPhoneNumberChanged -> onPhoneNumberChanged(event.phoneNumber)
            is ReferenceFormEvent.OnReferenceValueChanged -> onReferenceValueChanged(event.item)
            is ReferenceFormEvent.OnContinueButtonClicked -> onContinueButtonClicked()
            is ReferenceFormEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
            is ReferenceFormEvent.OnValidatePhoneNumber -> onValidatePhoneNumber(event.phoneNumber)
            else -> Unit
        }
    }
}
