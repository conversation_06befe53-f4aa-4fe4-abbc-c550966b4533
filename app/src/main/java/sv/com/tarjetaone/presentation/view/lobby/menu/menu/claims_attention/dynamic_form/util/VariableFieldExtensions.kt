package sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.dynamic_form.util

import androidx.compose.ui.text.input.KeyboardType
import sv.com.tarjetaone.domain.entities.request.VariableDefUI
import sv.com.tarjetaone.domain.entities.response.FieldDataType
import sv.com.tarjetaone.domain.entities.response.FieldType
import sv.com.tarjetaone.domain.entities.response.VariableFieldUI

fun VariableFieldUiState.isValidInput(): Boolean {
    return (variableField.isOptional == true) || ((variableField.isOptional == false) && (typedValue.isNotBlank() || selectedValue != null))
}

fun VariableFieldUiState.toVariableDefUI(): VariableDefUI {
    return VariableDefUI(
        id = variableField.manAttributeId,
        value = if (variableField.fieldType == FieldType.TypedValue) typedValue else selectedValue?.fieldTypeCatalogId?.toString()
    )
}

fun VariableFieldUI.keyboardType(): KeyboardType {
    return when (dataType) {
        FieldDataType.Text -> KeyboardType.Text
        FieldDataType.Email -> KeyboardType.Email
        FieldDataType.Integer -> KeyboardType.Number
        FieldDataType.Money -> KeyboardType.Decimal
        else -> KeyboardType.Text // Date, Address or null
    }
}
