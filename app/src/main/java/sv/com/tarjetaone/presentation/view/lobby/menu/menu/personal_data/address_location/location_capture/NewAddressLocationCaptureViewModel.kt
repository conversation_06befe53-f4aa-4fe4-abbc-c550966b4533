package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.address_location.location_capture

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.entities.request.UserLocationUI
import sv.com.tarjetaone.domain.usecases.catalog.GetCatalogByParentUseCase
import sv.com.tarjetaone.domain.usecases.catalog.GetCatalogUseCase
import sv.com.tarjetaone.domain.usecases.requestcard.locationcapture.GetGeoZonesUseCase
import sv.com.tarjetaone.presentation.view.common.locationcapture.LocationCaptureBaseViewModel
import javax.inject.Inject

@HiltViewModel
class NewAddressLocationCaptureViewModel @Inject constructor(
    getGeoZonesUseCase: GetGeoZonesUseCase,
    getCatalogUseCase: GetCatalogUseCase,
    getCatalogByParentUseCase: GetCatalogByParentUseCase,
    savedStateHandle: SavedStateHandle
) : LocationCaptureBaseViewModel(getGeoZonesUseCase, getCatalogUseCase, getCatalogByParentUseCase) {

    private val args = NewAddressLocationCaptureFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onScreenStart() {
        super.onScreenStart()
        updateInitialAlias(args.userLocation.alias)
        _uiState.update {
            it.copy(userAddress = args.userLocation, shouldShowForm = true)
        }
    }

    override fun goToNextScreen(address: UserLocationUI) {
        sendEvent(
            UiEvent.Navigate(
                NewAddressLocationCaptureFragmentDirections
                    .actionNewAddressLocationCaptureFragmentToNewAddressLocationConfirmationFragment(
                        userLocation = address,
                        action = args.action
                    )
            )
        )
    }
}
