package sv.com.tarjetaone.presentation.compose.uicomponent.button

import androidx.compose.foundation.clickable
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.util.TextButtonVariant

/**
 * Button to be used as clickable text button.
 * This component supports two types of text button variants.
 * If you need don't want a padding for your TextButton, you can use [TextButtonVariant.SMALL_VARIANT].
 * In the other hand, if you need a padding for your TextButton, you can use [TextButtonVariant.NORMAL_VARIANT].
 */
@Suppress("kotlin:S107")
@Composable
fun HyperLinkTextButton(
    modifier: Modifier = Modifier,
    text: String,
    textButtonVariant: TextButtonVariant = TextButtonVariant.NORMAL_VARIANT,
    enabled: Boolean = true,
    textColor: Color = MaterialTheme.colorScheme.primary,
    colors: ButtonColors = ButtonDefaults.buttonColors(
        containerColor = Color.Transparent,
        contentColor = textColor,
        disabledContentColor = MaterialTheme.customColors.gray600,
        disabledContainerColor = Color.Transparent
    ),
    textStyle: TextStyle = MaterialTheme.typography.labelMedium.copy(
        textDecoration = TextDecoration.Underline,
        color = textColor,
        lineHeight = MaterialTheme.customDimensSp.sp14,
        textAlign = TextAlign.Center
    ),
    onClick: () -> Unit,
) {
    if (textButtonVariant == TextButtonVariant.SMALL_VARIANT) {
        Text(
            modifier = Modifier
                .clickable { if (enabled) onClick() }
                .then(modifier),
            text = text,
            style = textStyle,
        )
    } else {
        TextButton(
            modifier = modifier,
            enabled = enabled,
            colors = colors,
            onClick = onClick,
        ) {
            Text(
                text = text,
                style = textStyle
            )
        }
    }
}

@Preview
@Composable
fun SmallTextButtonPreview() {
    OneAppTheme {
        HyperLinkTextButton(
            modifier = Modifier,
            textButtonVariant = TextButtonVariant.SMALL_VARIANT,
            text = "Simple HyperLink Text",
        ) { }
    }
}

@Preview
@Composable
fun NormalTextButtonPreview() {
    OneAppTheme {
        HyperLinkTextButton(
            modifier = Modifier,
            text = "Normal HyperLink Text",
        ) { }
    }
}
