package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.references.successupdate

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import javax.inject.Inject

@HiltViewModel
class SuccessUpdateReferencesViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {
    private val args = SuccessUpdateReferencesFragmentArgs.fromSavedStateHandle(savedStateHandle)
    private val _uiState = MutableStateFlow(SuccessUpdateReferencesUiState())
    val uiState = _uiState.asStateFlow()

    private fun onStart() {
        _uiState.update { it.copy(isReferenceAdded = args.referenceAdded) }
    }

    private fun onDoneClick() {
        sendEvent(
            UiEvent.Navigate(
                SuccessUpdateReferencesFragmentDirections
                    .actionSuccessUpdateReferencesFragmentToCustomerReferencesOverview()
            )
        )
    }

    fun onEvent(event: SuccessUpdateReferencesUiEvent) {
        when (event) {
            is SuccessUpdateReferencesUiEvent.OnStart -> onStart()
            SuccessUpdateReferencesUiEvent.OnDoneClick -> onDoneClick()
        }
    }
}
