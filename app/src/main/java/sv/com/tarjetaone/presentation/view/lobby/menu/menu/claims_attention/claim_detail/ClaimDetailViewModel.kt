package sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.claim_detail

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.presentation.view.common.management.ManagementDetailBaseViewModel
import javax.inject.Inject

@HiltViewModel
class ClaimDetailViewModel @Inject constructor(
    imageUtils: ImageUtils,
    savedStateHandle: SavedStateHandle
) : ManagementDetailBaseViewModel(imageUtils) {
    private val args = ClaimDetailFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onStart() {
        args.claimDetail?.let { claimDetail ->
            _uiState.update {
                it.copy(
                    managementNumber = claimDetail.manRequestId.toString(),
                    managementStatus = claimDetail.mrStatusNameApp.orEmpty(),
                    managementStatusColor = claimDetail.mrStatusTextColor.orEmpty(),
                    managementName = claimDetail.manTypeNameApp.orEmpty(),
                    customerName = claimDetail.clientName.orEmpty(),
                    creditCardNumber = claimDetail.cardNumMasked.orEmpty(),
                    creditCardType = claimDetail.typeCardText.orEmpty(),
                    requestStartDate = claimDetail.getFormattedDateInit(),
                    requestEndDate = claimDetail.getFormattedDateEnd(),
                    description = claimDetail.description,
                    resolutionDays = claimDetail.availableDay,
                    isBackButtonVisible = true
                )
            }
        }
    }

    override fun onBackButtonClick() {
        sendEvent(UiEvent.Navigate(ClaimDetailFragmentDirections.actionHome()))
    }

    override fun onPrimaryButtonClick() {
        sendEvent(UiEvent.Navigate(ClaimDetailFragmentDirections.actionHome()))
    }
}