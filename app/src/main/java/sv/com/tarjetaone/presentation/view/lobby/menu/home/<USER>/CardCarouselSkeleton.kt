package sv.com.tarjetaone.presentation.view.lobby.menu.home.skeleton

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.modifier.shimmerEffect
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.ShimmerSize
import sv.com.tarjetaone.presentation.compose.util.ShimmerStyle
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer40
import sv.com.tarjetaone.presentation.compose.util.setBackgroundFromList

/**
 * This represents a custom CardCarousel skeleton component that is displayed on the HomeScreen.
 */
@Composable
fun CardCarouselSkeleton(modifier: Modifier = Modifier) {
    Column(
        modifier = modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer16()
        EmptyHomCreditCardSkeleton()
        Spacer16()
        Box(
            modifier = Modifier
                .size(MaterialTheme.customDimens.dimen8)
                .background(
                    color = MaterialTheme.customColors.gray600,
                    shape = CircleShape
                )
        )
    }
}

@Composable
fun HomeCardContent(
    modifier: Modifier = Modifier,
    content: (@Composable () -> Unit)? = null
) {
    Box(
        modifier = modifier
            .size(
                MaterialTheme.customDimens.dimen230,
                MaterialTheme.customDimens.dimen285
            )
            .setBackgroundFromList(
                colors = listOf(MaterialTheme.customColors.gray300),
                shape = MaterialTheme.shapes.large
            )
    ) {
        content?.let { it() }
    }
}

/**
 * This represents an empty card skeleton component for the HomeCreditCard UI.
 */
@Composable
fun EmptyHomCreditCardSkeleton(modifier: Modifier = Modifier) {
    HomeCardContent(
        modifier = modifier
            .shimmerEffect(
                shimmerSize = ShimmerSize.LARGE,
                shimmerStyle = ShimmerStyle.LIGHT_BACKGROUND,
            )
    )
}

/**
 * This represents a custom skeleton component for the HomeCreditCard UI.
 */
@Composable
fun HomeCreditCardSkeleton(
    modifier: Modifier = Modifier,
) {
    HomeCardContent(modifier = modifier) {
        Column(
            Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.customDimens.dimen16)
        ) {
            Box(
                modifier = Modifier
                    .background(
                        color = MaterialTheme.customColors.gray400,
                        shape = MaterialTheme.shapes.extraSmall
                    )
                    .width(MaterialTheme.customDimens.dimen108)
                    .height(MaterialTheme.customDimens.dimen12)
                    .align(Alignment.End)
                    .shimmerEffect(
                        shimmerSize = ShimmerSize.LARGE,
                        shimmerStyle = ShimmerStyle.DARK_BACKGROUND,
                    )
            )
            Spacer40()
            Text(
                text = stringResource(id = R.string.available_credit),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.customColors.gray700
            )
            Box(
                modifier = Modifier
                    .background(
                        color = MaterialTheme.customColors.gray400,
                        shape = MaterialTheme.shapes.extraSmall
                    )
                    .width(MaterialTheme.customDimens.dimen132)
                    .height(MaterialTheme.customDimens.dimen24)
                    .align(Alignment.Start)
                    .shimmerEffect(
                        shimmerSize = ShimmerSize.LARGE,
                        shimmerStyle = ShimmerStyle.DARK_BACKGROUND,
                    )
            )
            Spacer24()
            Text(
                text = stringResource(id = R.string.used_credit),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.customColors.gray700
            )
            Box(
                modifier = Modifier
                    .background(
                        color = MaterialTheme.customColors.gray400,
                        shape = MaterialTheme.shapes.extraSmall
                    )
                    .width(MaterialTheme.customDimens.dimen132)
                    .height(MaterialTheme.customDimens.dimen24)
                    .align(Alignment.Start)
                    .shimmerEffect(
                        shimmerSize = ShimmerSize.LARGE,
                        shimmerStyle = ShimmerStyle.DARK_BACKGROUND,
                    )
            )
            Spacer1f()
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.Bottom,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Icon(
                    imageVector = ImageVector.vectorResource(id = R.drawable.ic_atlantida_logo),
                    contentDescription = null,
                    tint = MaterialTheme.customColors.gray600
                )
                Icon(
                    imageVector = ImageVector.vectorResource(id = R.drawable.ic_visa_icon_2),
                    contentDescription = null,
                    tint = MaterialTheme.customColors.gray600
                )
            }
        }
    }
}

@Preview
@Composable
private fun CardCarouselSkeletonPreview() {
    OneAppTheme {
        CardCarouselSkeleton()
    }
}
