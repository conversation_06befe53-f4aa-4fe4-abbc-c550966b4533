package sv.com.tarjetaone.presentation.compose.util

import android.graphics.Typeface
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.BulletSpan
import android.text.style.StyleSpan
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.text.HtmlCompat
import sv.com.tarjetaone.common.utils.AppConstants.MOCK_HTML_FORMATTED_TEXT
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme

/**
 * Composable function for displaying text in HTML format.
 * Use this function when rendering a string that contains HTML markup.
 *
 * @param modifier Modifier to apply custom styling or layout modifications to the component.
 * @param text The string value to be transformed and displayed in HTML format.
 * @param style The desired style for the Text component.
 */
@Composable
fun TextFromHtml(
    modifier: Modifier = Modifier,
    text: String,
    style: TextStyle = MaterialTheme.typography.labelMedium
) {

    val spannableString = SpannableStringBuilder(text).toString()
    val spannedText = HtmlCompat.fromHtml(spannableString, HtmlCompat.FROM_HTML_MODE_COMPACT)

    Text(
        modifier = modifier,
        text = spannedText.toAnnotatedString(),
        style = style,
    )
}

/**
 * Converts spanned text to HTML-style. If the text contains <strong> or <b> formatting,
 * the resulting text will be styled as bold.
 */
private fun Spanned.toAnnotatedString(): AnnotatedString = buildAnnotatedString {
    val spanned = <EMAIL>()
    var lastIndex = ZERO_VALUE

    getSpans(ZERO_VALUE, spanned.length, Any::class.java).forEach { span ->
        val start = getSpanStart(span)
        val end = getSpanEnd(span)

        // Append any plain text before the span
        if (lastIndex < start) {
            append(spanned.subSequence(lastIndex, start).toString())
        }

        when (span) {
            is StyleSpan -> when (span.style) {
                Typeface.BOLD -> {
                    withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                        append(spanned.subSequence(start, end).toString())
                    }
                }
            }
            is BulletSpan -> {
                append("• ${spanned.subSequence(start, end)}")
            }
        }
        lastIndex = end
    }

    // Append remaining text below bullets
    if (lastIndex < spanned.length) {
        append(spanned.subSequence(lastIndex, spanned.length).toString())
    }
}

@Preview(showBackground = true)
@Composable
fun TextFromHtmlPreview() {
    OneAppTheme {
        TextFromHtml(
            modifier = Modifier.padding(8.dp),
            text = MOCK_HTML_FORMATTED_TEXT
        )
    }
}
