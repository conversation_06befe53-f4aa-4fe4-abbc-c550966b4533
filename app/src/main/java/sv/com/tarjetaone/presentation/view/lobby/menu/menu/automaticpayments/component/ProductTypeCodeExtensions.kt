package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.component

import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.utils.automaticpayments.servicespayment.invoiceselection.ProductTypeCode
import sv.com.tarjetaone.presentation.helpers.UiText

fun ProductTypeCode.getLabel(appendOneLabel: Boolean = false): UiText? {
    return when (this) {
        ProductTypeCode.CreditCards -> {
            if (appendOneLabel) UiText.StringResource(R.string.payment_service_tc_label)
            else UiText.StringResource(R.string.payment_products_credit_card)
        }

        ProductTypeCode.BankAccounts -> UiText.StringResource(R.string.payment_products_bank_account)
        else -> null
    }
}
