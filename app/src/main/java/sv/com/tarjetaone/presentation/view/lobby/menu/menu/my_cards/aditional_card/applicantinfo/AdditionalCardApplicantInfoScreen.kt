package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.applicantinfo

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.EIGHT_VALUE_PERCENT
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.PhoneTextField
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedDropdown
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer40

@Composable
fun AdditionalCardApplicantInfoScreen(viewModel: AdditionalCardApplicantInfoViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    SideEffectHandler(events = viewModel.sideEffects) {
        AdditionalCardApplicantInfoScreen(uiState = uiState, onEvent = viewModel::onEvent)
    }
}

@Composable
fun AdditionalCardApplicantInfoScreen(
    uiState: AdditionalCardApplicantInfoUiState,
    onEvent: (event: AdditionalCardApplicantInfoUiEvent) -> Unit = {}
) {
    LaunchedEffect(Unit) {
        onEvent(AdditionalCardApplicantInfoUiEvent.OnStart)
    }

    ScreenWithTopAppBar(
        onLeftButtonClick = { onEvent(AdditionalCardApplicantInfoUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(AdditionalCardApplicantInfoUiEvent.OnTwilioClick) },
        progress = EIGHT_VALUE_PERCENT,
        title = stringResource(id = R.string.additional_data_title),
        isProgressbarVisible = true,
        isLeftButtonVisible = true
    ) {
        AdditionalCardApplicantInfoScreenContent(
            uiState = uiState,
            onEvent = onEvent
        )
    }
}

@Composable
fun AdditionalCardApplicantInfoScreenContent(
    uiState: AdditionalCardApplicantInfoUiState,
    onEvent: (event: AdditionalCardApplicantInfoUiEvent) -> Unit = {}
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = MaterialTheme.customDimens.dimen24)
    ) {
        Text(
            text = stringResource(id = R.string.additional_data_description),
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.bodyMedium
        )
        Spacer40()
        PhoneTextField(
            value = uiState.phone,
            onValueChange = { onEvent(AdditionalCardApplicantInfoUiEvent.OnPhoneChange(it)) },
            label = stringResource(id = R.string.cellphone_number_label),
            placeholder = stringResource(id = R.string.phone_number_placeholder),
            hasError = uiState.isPhoneValid.not() && uiState.phone.isNotEmpty(),
            error = uiState.phoneError?.asString()
        )
        Spacer16()
        Text(
            text = stringResource(id = R.string.relationship_label),
            style = MaterialTheme.typography.bodyMedium
        )
        SimpleElevatedDropdown(
            placeholder = stringResource(R.string.select),
            items = uiState.relationships,
            itemLabel = { it.name.orEmpty() },
            value = uiState.selectedItem,
            onValueChange = { onEvent(AdditionalCardApplicantInfoUiEvent.OnDropdownItemSelected(it)) }
        )
        Spacer1f()
        OneButton(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    top = MaterialTheme.customDimens.dimen2,
                    bottom = MaterialTheme.customDimens.dimen16,
                ),
            text = stringResource(id = R.string.continue_button_label),
            enabled = uiState.isFormValid,
            onClick = {
                onEvent(AdditionalCardApplicantInfoUiEvent.OnContinueClick)
            }
        )
    }
}

@Preview
@Composable
fun AdditionalCardApplicantInfoScreenPreview() {
    OneAppTheme {
        AdditionalCardApplicantInfoScreen(uiState = AdditionalCardApplicantInfoUiState())
    }
}