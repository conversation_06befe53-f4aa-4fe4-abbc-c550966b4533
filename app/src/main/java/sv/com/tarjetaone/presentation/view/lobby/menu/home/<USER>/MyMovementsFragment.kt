package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements

import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.core.util.Pair
import androidx.fragment.app.viewModels
import com.google.android.material.datepicker.DateValidatorPointBackward
import com.google.android.material.datepicker.MaterialDatePicker
import dagger.hilt.android.AndroidEntryPoint
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.base.BaseFragment
import sv.com.tarjetaone.core.utils.CalendarConstraintsParams
import sv.com.tarjetaone.core.utils.CustomDateRangePicker
import sv.com.tarjetaone.core.utils.CustomDateRangePickerParams
import sv.com.tarjetaone.databinding.FragmentComposeBinding
import sv.com.tarjetaone.presentation.view.utils.requireLobbyActivity
import java.util.Calendar

@AndroidEntryPoint
class MyMovementsFragment : BaseFragment<FragmentComposeBinding>(
    R.layout.fragment_compose
) {
    override val viewModel: MyMovementsViewModel by viewModels()
    private var dateRangePicker: MaterialDatePicker<Pair<Long, Long>>? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requireLobbyActivity().hideNavigationBottom()

        viewModel.displayDateRangePicker.collectLatestWithLifecycle {
            if (it) setupRangePickerDialog()
        }

        binding.composeView.setContentScreen {
            MyMovementsScreen(viewModel)
        }
    }

    // TODO Migrate dateRangePicker to compose and handle dialog with uiState
    private fun setupRangePickerDialog() {
        try {
            dateRangePicker?.dismiss()
            dateRangePicker = CustomDateRangePicker(
                CustomDateRangePickerParams(
                    calendarConstraints = CalendarConstraintsParams(
                        end = Calendar.getInstance().timeInMillis,
                        validator = DateValidatorPointBackward.now()
                    ),
                    positiveButtonText = getString(R.string.accept_label),
                    negativeButtonText = getString(R.string.exit_label),
                    onPositiveButtonAction = { selectedDates ->
                        viewModel.onEvent(
                            MyMovementsEvent.OnDateRangeChange(
                                selectedDates.first,
                                selectedDates.second
                            )
                        )
                    },
                    onNegativeButtonAction = {
                        viewModel.onEvent(MyMovementsEvent.OnDateRangeDialogDismiss)
                    },
                    onDismissAction = {
                        viewModel.onEvent(MyMovementsEvent.OnDateRangeDialogDismiss)
                    }
                )
            ).createDateRangePicker().also { it.show(childFragmentManager, CUSTOM_CALENDAR_TAG) }
        } catch (e: IllegalAccessError) {
            Log.e(CUSTOM_CALENDAR_TAG, e.toString())
        }
    }

    companion object {
        const val CUSTOM_CALENDAR_TAG = "CUSTOM_MATERIAL_DATE_PICKER"
    }
}
