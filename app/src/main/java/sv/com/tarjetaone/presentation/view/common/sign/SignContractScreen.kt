package sv.com.tarjetaone.presentation.view.common.sign

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.THREE_VALUE_PERCENT
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.theme.seaweedScriptFamily
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.card.BaesSelectableCard
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.Spacer24

@Composable
fun SignContractScreen(
    viewModel: SignContractViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    SignContractScreenContent(
        uiState = uiState,
        onEvent = viewModel::onEvent,
    )
}

@Composable
fun SignContractScreenContent(
    uiState: SignContractUiState,
    onEvent: (SignContractUiEvent) -> Unit = {}
) {
    LaunchedEffect(Unit) {
        onEvent(SignContractUiEvent.OnStart)
    }
    ScreenWithTopAppBar(
        title = stringResource(R.string.sign_documents_title),
        onLeftButtonClick = {
            onEvent(SignContractUiEvent.OnBackClick)
        },
        onRightButtonClick = {
            onEvent(SignContractUiEvent.OnTwilioClick)
        },
        isProgressbarVisible = uiState.isProgressVisible,
        progress = uiState.progress
    ) {
        Column(
            modifier = Modifier
                .background(MaterialTheme.colorScheme.surface)
                .padding(
                    horizontal = MaterialTheme.customDimens.dimen36,
                    vertical = MaterialTheme.customDimens.dimen16
                )
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            Text(
                text = stringResource(id = R.string.subtitle_contracts_sign_check),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodySmall.copy(
                    color = MaterialTheme.colorScheme.onSurface
                )
            )
            Spacer24()
            BaesSelectableCard(
                cardElevation = MaterialTheme.customDimens.dimen20,
                isSelected = uiState.isSignSelected,
                onClick = { onEvent(SignContractUiEvent.OnSignSelected) },
            ) {
                Text(
                    text = uiState.customerName,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontSize = MaterialTheme.customDimensSp.sp37,
                    fontFamily = seaweedScriptFamily,
                    fontWeight = FontWeight.Normal
                )
            }
            Spacer(modifier = Modifier.weight(THREE_VALUE_PERCENT))
            Text(
                text = stringResource(id = R.string.only_documents_to_sign_subtitle),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodySmall.copy(
                    color = MaterialTheme.colorScheme.onSurface
                )
            )
            Spacer24()
            OneButton(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(uiState.actionButtonRes),
                enabled = uiState.isSignSelected,
                onClick = {
                    onEvent(SignContractUiEvent.OnSignClick)
                }
            )
        }
    }
}

@Preview
@Composable
fun SignScreenUnselectedPreview() {
    OneAppTheme {
        SignContractScreenContent(
            uiState = SignContractUiState(
                customerName = "John Doe",
                isSignSelected = false
            )
        )
    }
}

@Preview
@Composable
fun SignScreenSelectedPreview() {
    OneAppTheme {
        SignContractScreenContent(
            uiState = SignContractUiState(
                customerName = "John Doe",
                isSignSelected = true
            )
        )
    }
}