package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform

import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.common.utils.AppConstants.ONE_HUNDRED_VALUE
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.CommonDialogWithIconParams
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.entities.request.automaticpayments.paymentaffiliation.affiliationform.NotificationChannelsRequestUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.affiliationform.NotificationChannelUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.invoiceselection.infoproducts.PXPaymentMethodUI
import sv.com.tarjetaone.domain.usecases.automaticpayments.paymentaffiliation.affiliationform.NotificationChannelsUseCase
import sv.com.tarjetaone.domain.usecases.automaticpayments.servicespayment.invoiceselection.infoproducts.InfoProductsUseCase
import sv.com.tarjetaone.domain.utils.automaticpayments.servicespayment.invoiceselection.InfoProductListType
import sv.com.tarjetaone.domain.utils.automaticpayments.servicespayment.invoiceselection.ProductTypeCode
import sv.com.tarjetaone.domain.utils.onSuccess
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.invoiceselection.filterProductsByCode

abstract class AffiliationFormViewModel(
    private val infoProductsUseCase: InfoProductsUseCase,
    private val notificationChannelsUseCase: NotificationChannelsUseCase
) : BaseViewModel() {

    protected val _uiState = MutableStateFlow(AffiliationFormUiState())
    val uiState = _uiState.asStateFlow()

    protected open fun onStart() {
        sendEvent(UiEvent.Loading(true))
        viewModelScope.launch {
            val infoProductsDeferred = async {
                infoProductsUseCase(
                    documentType = AppConstants.DUI,
                    documentNumber = baseSharedPrefs.dui(),
                    infoProductListType = InfoProductListType.ALL
                ).onSuccess { response ->
                    val invalidPaymentMethods = response.none {
                        it.productTypeCode == ProductTypeCode.CreditCards || it.productTypeCode == ProductTypeCode.BankAccounts
                    }
                    if (response.isEmpty() || invalidPaymentMethods) {
                        showEmptyPaymentMethodsMessage()
                        return@onSuccess
                    }
                    _uiState.update { state ->
                        state.copy(
                            userProducts = response,
                            userProductsTypes = response
                                .filter { it.productTypeCode != ProductTypeCode.Unknown }
                                .distinctBy { it.productTypeCode }
                                .map { it.productTypeCode }
                        )
                    }
                }
            }
            val notificationChannelsDeferred = async {
                notificationChannelsUseCase(
                    NotificationChannelsRequestUI(
                        action = ACTION,
                        listCode = LIST_CODE
                    )
                ).onSuccess { response ->
                    _uiState.update { state ->
                        state.copy(notificationChannels = response)
                    }
                }
            }

            awaitAll(
                infoProductsDeferred,
                notificationChannelsDeferred
            ).any {
                it.handleErrors(
                    onApiErrorAction = { _, error, _, _ ->
                        if (error?.code == ONE_HUNDRED_VALUE.toString()) showEmptyPaymentMethodsMessage()
                        else showUpsErrorMessage()
                    },
                )
            }

            // Update the UI state with the service and category name only if the user is affiliating a new service.
            updateUiStateOnInit()
            sendEvent(UiEvent.Loading(false))
        }
    }

    abstract fun updateUiStateOnInit()

    private fun showEmptyPaymentMethodsMessage() {
        sendEvent(
            UiEvent.ShowCommonDialog(
                CommonDialogWithIconParams(
                    isDismissible = false,
                    title = UiText.StringResource(R.string.payment_method_error_title),
                    message = UiText.StringResource(R.string.invalid_payment_method_message),
                    icon = R.drawable.ic_error_red_light,
                    buttonText = UiText.StringResource(R.string.back_to_my_account),
                    buttonColor = R.color.error_red,
                    onButtonClick = {
                        sendEvent(
                            UiEvent.NavigateBackTo(
                                R.id.servicePaymentHomeFragment,
                                inclusive = false
                            )
                        )
                    }
                )
            )
        )
    }

    private fun onSelectNotificationChannel(notificationChannel: NotificationChannelUI) {
        _uiState.update { state ->
            state.copy(selectedNotificationChannel = notificationChannel)
        }
    }

    private fun onProductTypeChange(productType: ProductTypeCode) {
        _uiState.update { state ->
            state.copy(
                selectedProductType = productType,
                userProductsFiltered = state.userProducts.filter { it.productTypeCode == productType },
                selectedProduct = state.userProducts
                    .filterProductsByCode(productType)
                    .firstOrNull()
            )
        }
    }

    private fun onSelectProduct(product: PXPaymentMethodUI) =
        _uiState.update { it.copy(selectedProduct = product) }

    private fun onMaxChargeOptionChange(value: Boolean) =
        _uiState.update { it.copy(isMaxAmountEnabled = value) }

    private fun onChargeAmountChange(chargeAmount: String) =
        _uiState.update { it.copy(maxChargeAmount = chargeAmount) }

    private fun onTermsCheckChange(value: Boolean) =
        _uiState.update { it.copy(acceptedTermsAndConditions = value) }


    protected abstract fun navigateToAffiliationTerms()

    protected abstract fun onContinueClick()

    fun onEvent(event: AffiliationFormUiEvent) {
        when (event) {
            is AffiliationFormUiEvent.OnStart -> onStart()
            is AffiliationFormUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            is AffiliationFormUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            is AffiliationFormUiEvent.OnSelectNotificationChannel -> onSelectNotificationChannel(
                event.notificationChannel
            )

            is AffiliationFormUiEvent.OnSelectProductType -> onProductTypeChange(event.productType)
            is AffiliationFormUiEvent.OnSelectedProduct -> onSelectProduct(event.product)
            is AffiliationFormUiEvent.OnMaxChargeOptionChange -> onMaxChargeOptionChange(event.value)
            is AffiliationFormUiEvent.OnChargeAmountChange -> onChargeAmountChange(event.chargeAmount)
            is AffiliationFormUiEvent.OnTermsCheckChange -> onTermsCheckChange(event.value)
            is AffiliationFormUiEvent.NavigateToAffiliationTerms -> navigateToAffiliationTerms()
            is AffiliationFormUiEvent.OnContinueClick -> onContinueClick()
        }
    }

    companion object {
        private const val ACTION = "INFOLIST"
        private const val LIST_CODE = "LISTNO"
        const val NAVIGATE_TO_TERMS_TAG = "NAVIGATE_TO_TERMS_AND_CONDITIONS_SCREEN"
    }
}
