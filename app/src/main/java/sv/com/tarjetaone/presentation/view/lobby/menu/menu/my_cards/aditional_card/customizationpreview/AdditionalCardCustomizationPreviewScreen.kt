package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.customizationpreview

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.personalization.CardPersonalizationDummyData
import sv.com.tarjetaone.presentation.compose.uicomponent.personalization.CreditCardComponent
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer32

@Composable
fun AdditionalCardCustomizationPreviewScreen(viewModel: AdditionalCardCustomizationPreviewViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LaunchedEffect(Unit) {
        viewModel.onEvent(AdditionalCardCustomizationPreviewUiEvent.OnStart)
    }
    AdditionalCardCustomizationPreviewScreenContent(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@Composable
fun AdditionalCardCustomizationPreviewScreenContent(
    uiState: AdditionalCardCustomizationPreviewUiState,
    onEvent: (AdditionalCardCustomizationPreviewUiEvent) -> Unit = {}
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.surface)
            .safeDrawingPadding()
            .verticalScroll(rememberScrollState())
            .padding(
                vertical = MaterialTheme.customDimens.dimen16,
                horizontal = MaterialTheme.customDimens.dimen24,
            )
    ) {
        Text(
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.Center,
            text = stringResource(R.string.final_card_personalization_header),
            style = MaterialTheme.typography.displayMedium.copy(
                fontSize = MaterialTheme.customDimensSp.sp32,
                lineHeight = MaterialTheme.customDimensSp.sp44
            ),
            color = MaterialTheme.colorScheme.primary
        )
        Spacer16()
        Text(
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.Center,
            text = stringResource(R.string.final_card_personalization),
            style = MaterialTheme.typography.titleMedium.copy(fontSize = MaterialTheme.customDimensSp.sp14)
        )
        Spacer32()
        CreditCardComponent(
            cardColors = uiState.cardColors,
            nameOnCard = uiState.nameOnCard
        )
        Spacer32()
        Text(
            text = stringResource(R.string.delivery_date),
            style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Bold)
        )
        Text(text = uiState.deliveryDate, style = MaterialTheme.typography.bodyMedium)
        Spacer32()
        Spacer1f()
        OneButton(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(R.string.save_and_send_my_card),
            onClick = {
                onEvent(AdditionalCardCustomizationPreviewUiEvent.OnFinishAndSendCardClick)
            }
        )
        Spacer16()
        OneButton(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(R.string.finish_personalization_card_personalize_again),
            buttonVariant = ButtonVariant.SECONDARY_VARIANT,
            onClick = {
                onEvent(AdditionalCardCustomizationPreviewUiEvent.OnRetryCardCustomizationClick)
            }
        )
    }
}

@Preview
@Composable
fun AdditionalCardCustomizationPreviewScreenPreview() {
    OneAppTheme {
        val uiState =
            AdditionalCardCustomizationPreviewUiState(
                cardColors = CardPersonalizationDummyData.colors.first(),
                deliveryDate = "Lunes 26 de septiembre",
                nameOnCard = "Juan Perez"
            )
        AdditionalCardCustomizationPreviewScreenContent(uiState)
    }
}