package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.TileMode
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.COLLAPSE_ICON_ROTATION
import sv.com.tarjetaone.common.utils.AppConstants.EXPANDED_ICON_ROTATION
import sv.com.tarjetaone.common.utils.AppConstants.HALF_COLOR_TRANSPARENCY
import sv.com.tarjetaone.core.utils.extensions.formatAsString
import sv.com.tarjetaone.domain.entities.response.PointStatementUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.Spacer16

@Composable
fun MegaPointsDetailCard(
    modifier: Modifier = Modifier,
    pointStatement: PointStatementUI?,
    isExpanded: Boolean,
    onClick: () -> Unit = {}
) {
    ElevatedCard(
        modifier = modifier,
        shape = MaterialTheme.shapes.medium,
        colors = CardDefaults.elevatedCardColors(containerColor = MaterialTheme.colorScheme.background),
        onClick = onClick
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.customDimens.dimen16)
        ) {
            MegaPointsDetailHeader(isExpanded = isExpanded)
            pointStatement?.let {
                AnimatedVisibility(visible = isExpanded) {
                    Divider(
                        thickness = MaterialTheme.customDimens.dimen1,
                        color = MaterialTheme.customColors.tertiaryDark.copy(alpha = HALF_COLOR_TRANSPARENCY),
                        modifier = Modifier.padding(top = MaterialTheme.customDimens.dimen8)
                    )
                    MegaPointsDetailContent(
                        modifier = Modifier.padding(
                            top = MaterialTheme.customDimens.dimen16,
                            bottom = MaterialTheme.customDimens.dimen8
                        ),
                        pointStatement = it
                    )
                }
            }
        }
    }
}

@Composable
private fun MegaPointsDetailHeader(
    modifier: Modifier = Modifier,
    isExpanded: Boolean
) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier.fillMaxWidth()
    ) {
        Text(
            text = stringResource(id = R.string.megaPoints_cardDetail_title),
            style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Medium),
            color = MaterialTheme.colorScheme.onSurface
        )
        val rotation by animateFloatAsState(
            targetValue = if (isExpanded) EXPANDED_ICON_ROTATION else COLLAPSE_ICON_ROTATION,
            label = "icon_rotation"
        )
        Icon(
            painter = painterResource(id = R.drawable.ic_arrow_down_stroke),
            contentDescription = null,
            tint = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.rotate(rotation)
        )
    }
}

@Composable
private fun MegaPointsDetailContent(
    modifier: Modifier = Modifier,
    pointStatement: PointStatementUI
) {
    val horizontalPadding = Modifier.padding(horizontal = MaterialTheme.customDimens.dimen16)

    Column(
        modifier = modifier
    ) {
        MegaPointsDetailContentItem(
            title = stringResource(id = R.string.megaPoints_cardDetail_past_balance),
            value = pointStatement.previousBalance.formatAsString(),
            modifier = horizontalPadding,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer16()
        MegaPointsDetailContentItem(
            title = stringResource(id = R.string.megaPoints_cardDetail_redeemed),
            value = pointStatement.redeemed.formatAsString(),
            color = MaterialTheme.colorScheme.primary,
            modifier = horizontalPadding
        )
        Spacer16()
        MegaPointsDetailContentItem(
            title = stringResource(id = R.string.megaPoints_cardDetail_accumulated),
            value = stringResource(
                id = R.string.positive_transaction,
                pointStatement.accumulated.formatAsString()
            ),
            color = MaterialTheme.customColors.successContainer,
            modifier = horizontalPadding
        )
        Spacer16()
        MegaPointsDetailContentItem(
            title = stringResource(id = R.string.megaPoints_cardDetail_current_balance),
            value = pointStatement.currentBalance.formatAsString(),
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier
                .border(
                    width = MaterialTheme.customDimens.dimen1,
                    brush = Brush.horizontalGradient(
                        MaterialTheme.customColors.primaryGradientReversed,
                        tileMode = TileMode.Repeated
                    ),
                    shape = MaterialTheme.shapes.medium
                )
                .padding(
                    vertical = MaterialTheme.customDimens.dimen6,
                    horizontal = MaterialTheme.customDimens.dimen16
                )
        )
    }
}

@Composable
private fun MegaPointsDetailContentItem(
    modifier: Modifier = Modifier,
    title: String,
    value: String,
    color: Color = MaterialTheme.colorScheme.onSurfaceVariant
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = modifier.fillMaxWidth()
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.SemiBold),
            color = MaterialTheme.colorScheme.onSurface
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.SemiBold),
            color = color
        )
    }
}

@Preview
@Composable
private fun MegaPointsDetailCardExpandedPreview() {
    OneAppTheme {
        MegaPointsDetailCard(
            isExpanded = true,
            pointStatement = PointStatementUI(
                cutOffDateName = "Corte 1",
                cutOffDate = "2021-08-01",
                previousBalance = 2500,
                redeemed = 1000,
                accumulated = 4000,
                currentBalance = 5500,
                pointsProgram = "Compras"
            )
        )
    }
}

@Preview
@Composable
private fun MegaPointsDetailCardCollapsedPreview() {
    OneAppTheme {
        MegaPointsDetailCard(
            isExpanded = false,
            pointStatement = PointStatementUI(
                cutOffDateName = "Corte 1",
                cutOffDate = "2021-08-01",
                previousBalance = 1000,
                redeemed = 100,
                accumulated = 200,
                currentBalance = 1100,
                pointsProgram = "Compras"
            )
        )
    }
}
