package sv.com.tarjetaone.presentation.view.common.selfiecapture

import android.Manifest
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.rememberPermissionState
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.core.utils.facephi.CaptureType
import sv.com.tarjetaone.core.utils.facephi.rememberFacephiWidgetLauncher
import sv.com.tarjetaone.core.utils.shouldShowRationale
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.card.HorizontalIconWithText
import sv.com.tarjetaone.presentation.compose.uicomponent.card.SimpleCardComponent
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.compose.util.TextFromHtml
import sv.com.tarjetaone.presentation.helpers.UiText

@Composable
fun SelfieCaptureScreen(viewModel: SelfieCaptureBaseViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LaunchedEffect(Unit) {
        viewModel.onEvent(SelfieCaptureUiEvent.OnStart)
    }

    SelfieCaptureContent(
        uiState = uiState,
        onEvent = viewModel::onEvent,
    )
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun SelfieCaptureContent(
    uiState: SelfieCaptureUiState,
    onEvent: (SelfieCaptureUiEvent) -> Unit,
) {
    ScreenWithTopAppBar(
        isLeftButtonVisible = uiState.showLeftButton,
        isRightButtonVisible = uiState.showRightButton,
        isProgressbarVisible = uiState.progressValue != null,
        progress = uiState.progressValue.orZero(),
        onLeftButtonClick = { onEvent(SelfieCaptureUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(SelfieCaptureUiEvent.OnTwilioClick) },
        title = stringResource(id = R.string.identity_validation),
    ) {
        val cameraPermission = Manifest.permission.CAMERA
        val context = LocalContext.current
        val facephiLauncher = rememberFacephiWidgetLauncher(
            type = CaptureType.Selfie,
            onFailure = { onEvent(SelfieCaptureUiEvent.OnSelfieCaptureFailed(it)) },
            onCaptured = { onEvent(SelfieCaptureUiEvent.OnSelfieCaptured(it)) }
        )
        val cameraPermissionState = rememberPermissionState(cameraPermission) { isGranted ->
            if (isGranted) {
                facephiLauncher.initWidget(context)
            } else {
                onEvent(
                    SelfieCaptureUiEvent
                        .OnCameraPermissionDenied(context.shouldShowRationale(cameraPermission))
                )
            }
        }
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(
                    vertical = MaterialTheme.customDimens.dimen24,
                    horizontal = MaterialTheme.customDimens.dimen40
                )
        ) {
            Image(
                imageVector = ImageVector.vectorResource(id = R.drawable.ic_file_person),
                contentDescription = null
            )
            Spacer16()
            Text(
                text = stringResource(id = R.string.smile_title),
                style = MaterialTheme.typography.displaySmall,
                color = MaterialTheme.colorScheme.secondary
            )
            Spacer16()
            TextFromHtml(
                text = uiState.description.asString(),
                style = MaterialTheme.typography.bodySmall.copy(
                    color = MaterialTheme.colorScheme.secondary,
                    textAlign = TextAlign.Center
                )
            )
            Spacer16()
            SimpleCardComponent(
                elevation = CardDefaults.cardElevation(
                    defaultElevation = MaterialTheme.customDimens.dimen2
                )
            ) {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = stringResource(id = R.string.remember),
                        style = MaterialTheme.typography.labelLarge,
                        color = MaterialTheme.colorScheme.onBackground,
                        textAlign = TextAlign.Center
                    )
                    Spacer24()
                    HorizontalIconWithText(
                        style = MaterialTheme.typography.labelMedium,
                        leadingIcon = R.drawable.ic_sun,
                        text = UiText.StringResource(R.string.tip1_take_selfie),
                    )
                    Spacer24()
                    HorizontalIconWithText(
                        style = MaterialTheme.typography.labelMedium,
                        leadingIcon = R.drawable.ic_x_circle,
                        text = UiText.StringResource(R.string.tip2_take_selfie),
                    )
                    Spacer24()
                    HorizontalIconWithText(
                        style = MaterialTheme.typography.labelMedium,
                        leadingIcon = R.drawable.ic_landscape_image,
                        text = UiText.StringResource(R.string.tip3_take_selfie),
                    )
                }
            }
            if (uiState.showTutorialOption) {
                Spacer8()
                Text(
                    text = stringResource(id = R.string.show_tutorial_label),
                    style = MaterialTheme.typography.bodySmall.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    color = MaterialTheme.colorScheme.primary,
                    textDecoration = TextDecoration.Underline,
                    modifier = Modifier.clickable {
                        onEvent(SelfieCaptureUiEvent.OnShowTutorialClick)
                    }
                )
            }
            Spacer(modifier = Modifier.weight(1f))
            OneButton(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(id = R.string.continue_button_label),
                onClick = {
                    onEvent(
                        SelfieCaptureUiEvent
                            .OnContinueClick(cameraPermissionState::launchPermissionRequest)
                    )
                }
            )
        }
    }
}

@Preview
@Composable
fun SelfieCaptureScreenPreview() {
    OneAppTheme {
        SelfieCaptureContent(
            uiState = SelfieCaptureUiState(
                description = UiText.StringResource(R.string.photo_verification_additional_card),
                showTutorialOption = true,
            ),
            onEvent = {}
        )
    }
}
