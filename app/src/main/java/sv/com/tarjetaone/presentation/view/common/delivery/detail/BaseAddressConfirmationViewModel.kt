package sv.com.tarjetaone.presentation.view.common.delivery.detail

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.analytics.dynatrace.DynatraceEvent
import sv.com.tarjetaone.analytics.dynatrace.DynatraceManager
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent

abstract class BaseAddressConfirmationViewModel(
    private val dynatraceManager: DynatraceManager,
): BaseViewModel() {

    protected val _uiState = MutableStateFlow(DeliveryDetailUiState())
    val uiState = _uiState.asStateFlow()

    protected open fun onStart() {
        dynatraceManager.sendInAppEvent(DynatraceEvent.Trasiego.ViewDeliveryDetail)
    }

    protected open fun onClickContinue() {
        dynatraceManager.sendInAppEvent(DynatraceEvent.Trasiego.DeliveryDetailContinue)
    }

    protected fun loadScreenInformation(
        address: String?,
        schedule: String?,
        date: String?,
    ) {
        _uiState.update { state ->
            state.copy(
                address = address.orEmpty(),
                schedule = schedule.orEmpty(),
                date = date.orEmpty(),
            )
        }
    }

    private fun onSelectAnotherDate() {
        dynatraceManager.sendInAppEvent(DynatraceEvent.Trasiego.SelectOtherDate)
        sendEvent(UiEvent.NavigateBack)
    }

    fun onEvent(event: DeliveryDetailUiEvent) {
        when (event) {
            DeliveryDetailUiEvent.OnStart -> onStart()
            DeliveryDetailUiEvent.OnClickContinue -> onClickContinue()
            DeliveryDetailUiEvent.OnDismiss -> _uiState.update { it.copy(dialogVisibility = false) }
            DeliveryDetailUiEvent.OnBackPressed -> sendEvent(UiEvent.NavigateBack)
            DeliveryDetailUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            DeliveryDetailUiEvent.OnSelectAnotherDate -> onSelectAnotherDate()
        }
    }
}