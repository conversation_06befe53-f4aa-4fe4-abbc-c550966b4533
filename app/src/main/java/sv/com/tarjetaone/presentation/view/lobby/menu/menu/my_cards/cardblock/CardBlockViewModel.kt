package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.cardblock

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI
import sv.com.tarjetaone.domain.entities.response.CatalogType.GC_BLOCK_REASON
import sv.com.tarjetaone.domain.entities.response.CreditCardStatus.Active
import sv.com.tarjetaone.domain.entities.response.CustomerCCardType.MAIN
import sv.com.tarjetaone.domain.entities.response.CustomerCCardUI
import sv.com.tarjetaone.domain.usecases.catalog.GetCatalogUseCase
import sv.com.tarjetaone.domain.usecases.general.CustomerCCardUseCase
import sv.com.tarjetaone.domain.utils.onSuccess
import javax.inject.Inject

@HiltViewModel
class CardBlockViewModel @Inject constructor(
    private val getCatalogUseCase: GetCatalogUseCase,
    private val customerCCardUseCase: CustomerCCardUseCase
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(CardBlockUiState())
    val uiState = _uiState.asStateFlow()

    private fun onStart() {
        if (_uiState.value.hasLoadedData) return
        viewModelScope.launch {
            sendEvent(SideEffect.Loading(true))
            val blockReasonsDeferred = async { fetchBlockReasons() }
            val userCreditCardsDeferred = async { getUserCreditCards() }
            awaitAll(blockReasonsDeferred, userCreditCardsDeferred).any { it.handleErrors() }
            sendEvent(SideEffect.Loading(false))
        }
    }

    /**
     * get the list of card lock reasons from the api service.
     */
    private suspend fun fetchBlockReasons() =
        getCatalogUseCase(GC_BLOCK_REASON)
            .onSuccess { response ->
                setupLockCardReasonList(
                    response.dataCatalog?.catalogItemsCollection.orEmpty()
                )
            }

    /**
     * get all available user's active credit cards.
     */
    private suspend fun getUserCreditCards() =
        customerCCardUseCase(baseSharedPrefs.getCustomerId().orZero())
            .onSuccess { response ->
                val userActiveCards = response.creditCards.filter { card ->
                    card.cCardStatus == Active
                }

                val canBlockCards = userActiveCards.isNotEmpty()
                _uiState.update {
                    it.copy(
                        cardList = userActiveCards,
                        selectedCard = getDefaultCard(userActiveCards),
                        canBlockCards = canBlockCards,
                        screenTitle = when {
                            canBlockCards -> R.string.lock_card_reason_title
                            !canBlockCards -> R.string.empty_state_card_not_active_title
                            else -> null
                        },
                    )
                }
            }

    /**
     * update the list of card lock reasons into to the ui state*
     */
    private fun setupLockCardReasonList(reasons: List<CatalogItemsCollectionUI>) {
        _uiState.update {
            it.copy(blockReasonList = reasons)
        }
    }

    /**
     * mark this card as the selected card when entering to the screen.
     * The default card will be represented as the one that has the "titular" title on its type.
     */
    private fun getDefaultCard(userActiveCards: List<CustomerCCardUI>) =
        userActiveCards.find { it.cCardType == MAIN }
            ?: userActiveCards.firstOrNull()

    /**
     * update selected card whenever selecting an item from the dropdown.
     */
    private fun onCardItemSelected(card: CustomerCCardUI) {
        _uiState.update {
            it.copy(selectedCard = card)
        }
    }

    /**
     * navigate to biometry screen after tapping on a card lock reason item.
     */
    private fun onCardLockReasonSelected(cardLockReason: CatalogItemsCollectionUI) {
        sendEvent(
            UiEvent.Navigate(
                CardBlockFragmentDirections.actionNavigationLockCardsToPrepareForPictureCardLockFragment(
                    cardBlockId = _uiState.value.selectedCard?.creditCardId.orZero(),
                    cardBlockReasonId = cardLockReason.id.toIntOrNull().orZero()
                )
            )
        )
    }

    fun onScreenUiEvent(screenUiEvent: CardBlockUiEvent) {
        when (screenUiEvent) {
            is CardBlockUiEvent.OnStart -> onStart()
            is CardBlockUiEvent.OnCardChange -> onCardItemSelected(screenUiEvent.card)
            is CardBlockUiEvent.OnSelectedBlockReason -> onCardLockReasonSelected(screenUiEvent.cardBlockReason)
            CardBlockUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            CardBlockUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
        }
    }
}