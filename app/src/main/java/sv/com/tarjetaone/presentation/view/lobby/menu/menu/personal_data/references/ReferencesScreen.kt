package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.references

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.SnackbarResult
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.common.utils.extensions.formatPhoneNumber
import sv.com.tarjetaone.domain.entities.response.ReferenceUI
import sv.com.tarjetaone.domain.entities.response.ReferencesTypesUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.HyperLinkTextButton
import sv.com.tarjetaone.presentation.compose.uicomponent.card.HorizontalIconWithText
import sv.com.tarjetaone.presentation.compose.uicomponent.common.OneAppSnackBar
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.DialogConfirmation
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.compose.util.TextButtonVariant
import sv.com.tarjetaone.presentation.compose.util.remember.rememberSnackBarHostState
import sv.com.tarjetaone.presentation.helpers.ReferenceType
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.common.reference_form.components.ReferenceItem

@Composable
fun ReferencesScreen(
    viewModel: ReferencesViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    LaunchedEffect(Unit) {
        viewModel.onUiEvent(ReferencesEvent.OnStart)
    }
    ReferencesContent(
        uiState = uiState,
        onEvent = viewModel::onUiEvent
    )
}

@Composable
fun ReferencesContent(
    uiState: ReferencesUiState,
    onEvent: (ReferencesEvent) -> Unit
) {
    val snackBarHostState = rememberSnackBarHostState()
    ScreenWithTopAppBar(
        title = stringResource(id = R.string.references),
        onLeftButtonClick = { onEvent(ReferencesEvent.OnBackClick) },
        onRightButtonClick = { onEvent(ReferencesEvent.OnSupportClick) },
    ) {
        if (uiState.showDeleteConfirmationDialog) {
            DialogConfirmation(
                message = UiText.StringResource(R.string.dialog_delete_reference_message),
                primaryButtonText = UiText.StringResource(R.string.delete_label),
                secondaryButtonText = UiText.StringResource(R.string.cancel),
                onPositiveClick = { onEvent(ReferencesEvent.OnDeleteReferenceConfirmed) },
                onNegativeClick = { onEvent(ReferencesEvent.OnDismissDeleteDialog) }
            )
        }
        Box(modifier = Modifier.fillMaxSize()) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(bottom = MaterialTheme.customDimens.dimen24)
                    .verticalScroll(rememberScrollState())
            ) {
                if (uiState.references.isNotEmpty()) {
                    ReferencesOverviewSection(
                        referenceType = ReferenceType.FAMILY,
                        title = stringResource(id = R.string.family),
                        maxReferencesDisclaimer = stringResource(R.string.maximum_family_references_disclaimer),
                        maxReferencesCount = uiState.getMaxFamilyReferences(),
                        references = uiState.getFamilyReferences(),
                        onEvent = onEvent
                    )
                    Spacer32()
                    ReferencesOverviewSection(
                        referenceType = ReferenceType.PERSONAL,
                        title = stringResource(id = R.string.personal),
                        maxReferencesDisclaimer = stringResource(id = R.string.maximum_personal_references_disclaimer),
                        maxReferencesCount = uiState.getMaxPersonalReferences(),
                        references = uiState.getPersonalReferences(),
                        onEvent = onEvent
                    )
                }
            }
            ReferencesSnackBarSection(
                modifier = Modifier.align(Alignment.BottomCenter),
                isSnackBarVisible = uiState.isSnackBarVisible,
                snackBarHostState = snackBarHostState,
                onCancelClick = { onEvent(ReferencesEvent.OnHideSnackBar) }
            )
        }
    }
}

@Composable
private fun ReferencesSnackBarSection(
    modifier: Modifier = Modifier,
    isSnackBarVisible: Boolean,
    snackBarHostState: SnackbarHostState,
    onCancelClick: () -> Unit
) {
    LaunchedEffect(isSnackBarVisible) {
        if (isSnackBarVisible) {
            val result = snackBarHostState.showSnackbar(message = EMPTY_STRING)
            if (result == SnackbarResult.Dismissed) {
                onCancelClick()
            }
        }
    }
    SnackbarHost(
        modifier = modifier.fillMaxWidth(),
        hostState = snackBarHostState
    ) {
        OneAppSnackBar(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.customDimens.dimen32)
                .padding(bottom = MaterialTheme.customDimens.dimen16),
            icon = R.drawable.ic_check_circle_white,
            message = stringResource(id = R.string.reference_deleted_successfully),
            containerColor = MaterialTheme.customColors.successContainer,
            onCancelClick = { snackBarHostState.currentSnackbarData?.dismiss() }
        )
    }
}

@Composable
fun ReferencesOverviewSection(
    referenceType: ReferenceType,
    title: String,
    maxReferencesDisclaimer: String,
    maxReferencesCount: Int,
    references: List<ReferenceUI> = emptyList(),
    onEvent: (ReferencesEvent) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.customDimens.dimen32)
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.headlineSmall.copy(
                fontWeight = FontWeight.Bold,
            )
        )
        Spacer8()
        references.forEach { reference ->
            ReferenceItem(
                fullName = reference.fullName.orEmpty(),
                phoneNumber = reference.phoneNumber.formatPhoneNumber().orEmpty(),
                referenceTypeName = reference.referenceTypeName.orEmpty(),
                showDeleteTextButton = references.size > ONE_VALUE,
                onModifyReference = {
                    onEvent(
                        ReferencesEvent.OnModifyReference(
                            referenceType,
                            reference
                        )
                    )
                },
                onDeleteReference = { onEvent(ReferencesEvent.OnDeleteReference(reference)) }
            )
            Spacer16()
        }
        ReferenceOverviewFooter(
            showDisclaimer = references.size >= maxReferencesCount,
            maxReferencesDisclaimer = maxReferencesDisclaimer,
            referenceType = referenceType,
            onEvent = onEvent
        )
    }
}

@Composable
fun ReferenceOverviewFooter(
    showDisclaimer: Boolean,
    maxReferencesDisclaimer: String,
    referenceType: ReferenceType,
    onEvent: (ReferencesEvent) -> Unit
) {
    if (showDisclaimer) {
        HorizontalIconWithText(
            modifier = Modifier.padding(horizontal = MaterialTheme.customDimens.dimen24),
            text = UiText.DynamicString(maxReferencesDisclaimer),
            textColor = MaterialTheme.customColors.disclaimer,
            leadingIcon = R.drawable.ic_info_2,
            iconTextSpacing = MaterialTheme.customDimens.dimen8,
            style = MaterialTheme.typography.labelMedium
        )
    } else {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            HyperLinkTextButton(
                textButtonVariant = TextButtonVariant.SMALL_VARIANT,
                text = stringResource(id = R.string.add_another_reference_label),
            ) {
                onEvent(ReferencesEvent.OnAddNewReference(referenceType))
            }
        }
    }
}

@Preview(
    showBackground = true,
    showSystemUi = true
)
@Composable
fun ReferencesScreenPreview() {
    OneAppTheme {
        ReferencesContent(
            uiState = ReferencesUiState(
                references = mutableListOf(
                    ReferencesTypesUI(
                        globalReferenceTypeName = ReferenceType.FAMILY.value,
                        maxActiveRows = 2,
                        reference = listOf(
                            ReferenceUI(
                                fullName = "Juan Perez",
                                referenceTypeName = "Padre",
                                phoneNumber = "7745 9090"
                            ),
                            ReferenceUI(
                                fullName = "Luis Perez",
                                referenceTypeName = "Tio",
                                phoneNumber = "7700 9090"
                            ),
                        )
                    ),
                    ReferencesTypesUI(
                        globalReferenceTypeName = ReferenceType.PERSONAL.value,
                        maxActiveRows = 2,
                        reference = listOf(
                            ReferenceUI(
                                fullName = "Marta Diaz",
                                referenceTypeName = "Amiga",
                                phoneNumber = "7767 9090"
                            ),
                            ReferenceUI(
                                fullName = "Alberto Serrano",
                                referenceTypeName = "Amigo",
                                phoneNumber = "7722 9090"
                            ),
                        )
                    )
                )
            ),
            onEvent = { }
        )
    }
}
