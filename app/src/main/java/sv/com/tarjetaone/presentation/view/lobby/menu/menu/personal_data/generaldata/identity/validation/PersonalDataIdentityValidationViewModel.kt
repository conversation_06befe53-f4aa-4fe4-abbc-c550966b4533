package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.generaldata.identity.validation

import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.usecases.auth.FaceAuthenticationUseCase
import sv.com.tarjetaone.presentation.view.common.identity_validation.IdentityValidationBaseViewModel

@HiltViewModel
class PersonalDataIdentityValidationViewModel @Inject constructor(
    faceAuthenticationUseCase: FaceAuthenticationUseCase,
    sharedPreferences: SecureSharedPreferencesRepository,
) : IdentityValidationBaseViewModel(faceAuthenticationUseCase, sharedPreferences) {

    override val onBlockedAction = {
        sendEvent(UiEvent.NavigateBackTo(R.id.navigation_home))
    }

    override fun operation(biometryId: Int) {
        navigateToCaptureDui(biometryId)
    }

    private fun navigateToCaptureDui(biometryId: Int) {
        sendEvent(
            UiEvent.Navigate(
                PersonalDataIdentityValidationFragmentDirections
                    .actionPersonalDataIdentityValidationFragmentToPersonalDataCaptureDuiFragment(
                        biometryProcessId = biometryId
                    )
            )
        )
    }

    override fun onFailedIdentityValidation(resultCode: String?) {
        showErrorMessage()
    }

    private fun showErrorMessage() {
        showUpsErrorMessage(
            isDismissible = false,
            onButtonClick = {
                sendEvent(UiEvent.NavigateBackTo(R.id.prepareForPicturePersonalInfoFragment))
            }
        )
    }
}
