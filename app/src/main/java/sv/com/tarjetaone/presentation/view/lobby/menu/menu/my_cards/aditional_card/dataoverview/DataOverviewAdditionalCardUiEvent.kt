package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.dataoverview

import com.facephi.fphiselphidwidgetcore.WidgetSelphIDResult

sealed interface DataOverviewAdditionalCardUiEvent {
    data object OnStart : DataOverviewAdditionalCardUiEvent
    data object OnEditClick : DataOverviewAdditionalCardUiEvent
    data object OnConfirmButtonClick : DataOverviewAdditionalCardUiEvent
    data object OnBackClick : DataOverviewAdditionalCardUiEvent
    data object OnTwilioButtonClick : DataOverviewAdditionalCardUiEvent
    data class OnWrongDataButtonClick(
        val requestPermission: () -> Unit
    ) : DataOverviewAdditionalCardUiEvent
    data class OnDocumentCapture(
        val result: WidgetSelphIDResult
    ) : DataOverviewAdditionalCardUiEvent
    data class OnCameraPermissionDenied(
        val showRationale: Boolean
    ) : DataOverviewAdditionalCardUiEvent
}
