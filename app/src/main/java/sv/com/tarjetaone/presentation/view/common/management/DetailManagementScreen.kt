package sv.com.tarjetaone.presentation.view.common.management

import android.view.View
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.common.utils.extensions.configCurrencyWithFractions
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.CaptureAndroidViewWrapper
import sv.com.tarjetaone.presentation.compose.uicomponent.button.HyperLinkTextButton
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.button.ShareVoucherButton
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.compose.util.handleShareVoucherCallback
import sv.com.tarjetaone.presentation.compose.util.handleVoucherSharingPermission
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.common.management.component.DetailManagementCard
import sv.com.tarjetaone.presentation.view.common.management.component.DetailManagementHead
import sv.com.tarjetaone.presentation.view.common.management.component.ManagementFeedbackMessage
import sv.com.tarjetaone.presentation.view.utils.ManAttributeType

@Composable
fun DetailManagementScreen(viewModel: ManagementDetailBaseViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    DetailManagementContent(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun DetailManagementContent(
    uiState: DetailManagementUiState = DetailManagementUiState(),
    onEvent: (DetailManagementUiEvent) -> Unit,
) {
    LaunchedEffect(Unit) {
        onEvent(DetailManagementUiEvent.OnStart)
    }
    ScreenWithTopAppBar(
        isLeftButtonVisible = uiState.isBackButtonVisible,
        onLeftButtonClick = {
            onEvent(DetailManagementUiEvent.OnBackButtonCLick)
        },
        onRightButtonClick = {
            onEvent(DetailManagementUiEvent.OnTwilioClick)
        }
    ) {
        Column(
            modifier = Modifier
                .background(MaterialTheme.colorScheme.surface)
                .padding(top = MaterialTheme.customDimens.dimen32),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Voucher view
            var voucherView by remember { mutableStateOf<View?>(null) }
            val context = LocalContext.current

            val storagePermissionState = handleVoucherSharingPermission(
                voucherView = voucherView,
                onHideComponentBeforeCapture = {
                    DetailManagementUiEvent.OnHideComponentBeforeCapture
                },
                onPermissionGranted = { bitmap ->
                    DetailManagementUiEvent.OnStoragePermissionGranted(bitmap)
                },
                onPermissionDenied = { showRationale ->
                    DetailManagementUiEvent.OnStoragePermissionDenied(showRationale)
                },
                onEvent = onEvent
            )

            val shareVoucherCallback = handleShareVoucherCallback(
                context = context,
                voucherView = voucherView,
                storagePermissionState = storagePermissionState,
                onEvent = onEvent,
                hideBeforeCaptureEvent = DetailManagementUiEvent.OnHideComponentBeforeCapture,
                onGrantedEvent = { bitmap ->
                    DetailManagementUiEvent.OnStoragePermissionGranted(bitmap)
                },
                onShareVoucherClickEvent = { requestPermissionCallback ->
                    DetailManagementUiEvent.OnShareVoucherClick(requestPermissionCallback)
                }
            )
            Column(
                modifier = Modifier
                    .weight(ONE_FLOAT_VALUE)
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
            ) {
                CaptureAndroidViewWrapper(view = { voucherView = it }) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(MaterialTheme.colorScheme.surface)
                    ) {
                        DetailManagementHead(
                            modifier = Modifier.align(Alignment.CenterHorizontally),
                            managementNumber = uiState.managementNumber,
                            managementStatus = uiState.managementStatus,
                            managementStatusColor = uiState.managementStatusColor
                        )
                        Spacer8()
                        DetailManagementCard(
                            modifier = Modifier.align(Alignment.Start),
                            uiState = uiState,
                            onShareVoucherButtonClick = {
                                shareVoucherCallback()
                            }
                        )
                    }
                }
                Spacer1f()
                if (uiState.showFeedbackMessage) {
                    Spacer16()
                    ManagementFeedbackMessage(
                        feedbackIcon = uiState.feedbackMessageIcon,
                        feedbackMessageTitle = uiState.feedbackMessageTitle,
                        feedbackMessageDescription = uiState.feedbackMessageDescription
                    )
                }
                if (uiState.showPrimaryButton) {
                    Spacer16()
                    OneButton(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = MaterialTheme.customDimens.dimen32),
                        text = stringResource(id = uiState.primaryButtonText),
                        onClick = {
                            onEvent(DetailManagementUiEvent.OnPrimaryButtonClick)
                        },
                    )
                }
                uiState.secondaryButtonText?.let {
                    Spacer8()
                    OneButton(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = MaterialTheme.customDimens.dimen32),
                        text = stringResource(id = it),
                        onClick = {
                            onEvent(DetailManagementUiEvent.OnSecondaryButtonClick)
                        },
                        buttonVariant = ButtonVariant.SECONDARY_VARIANT
                    )
                }
                if (uiState.showExitButton) {
                    Spacer8()
                    HyperLinkTextButton(
                        modifier = Modifier.align(Alignment.CenterHorizontally),
                        text = stringResource(R.string.cancel_card_tracking_detail_exit_button),
                        onClick = {
                            onEvent(DetailManagementUiEvent.OnExitButtonClick)
                        }
                    )
                }
                Spacer16()
            }
        }
    }
}

@Preview
@Composable
fun ShareVoucherPreview() {
    OneAppTheme {
        ShareVoucherButton()
    }
}

@Preview
@Composable
fun DetailManagementScreenPreview() {
    OneAppTheme {
        DetailManagementContent(
            uiState = DetailManagementUiState(
                managementNumber = "234.554.645",
                managementStatus = "En proceso",
                managementStatusColor = "#51DEA3",
                managementName = "Gestión de desactivación de protección",
                customerName = "Juan Pérez",
                creditCardNumber = "**** **** **** 1234",
                creditCardType = "Crédito Visa Titular",
                requestStartDate = "Lunes 21 de mayo, 11:52 PM",
                requestEndDate = "Lunes 21 de mayo, 11:52 PM",
                description = "Ya no necesito el programa de proteccion",
                resolutionDays = 5,
                secondaryButtonText = R.string.go_to_account,
                showExitButton = true,
                isCapturingVoucherView = false,
                managementAttributes = listOf(
                    ManagementAttributesUiModel(
                        managementAttributeDisplayName = UiText.StringResource(R.string.total_payment_label),
                        managementAttributeValue = UiText.DynamicString(
                            "1500".toDoubleOrNull()?.configCurrencyWithFractions().orEmpty()
                        ),
                        managementAttributeType = ManAttributeType.AmountPaid
                    ),
                    ManagementAttributesUiModel(
                        managementAttributeDisplayName = UiText.DynamicString("Fecha de pago"),
                        managementAttributeValue = UiText.DynamicString("23 junio 2021")
                    ),
                    ManagementAttributesUiModel(
                        managementAttributeDisplayName = UiText.DynamicString("Cuota Mensual"),
                        managementAttributeValue = UiText.DynamicString("$1.99 + IVA")
                    )
                )
            ),
            onEvent = {}
        )
    }
}
