package sv.com.tarjetaone.presentation.view.common.cardpin

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpSize
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.FOUR_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.card.EmptyStateCardVariant
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ActionNotAllowedDescription
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.EmptyStateScreen
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.compose.util.Spacer88
import sv.com.tarjetaone.presentation.view.common.cardpin.component.PinTextField

@Composable
fun CardPinScreen(viewModel: CardPinBaseViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    CardPinScreen(uiState = uiState, onEvent = viewModel::onUiEvent)
}

@Composable
fun CardPinScreen(uiState: CardPinUiState, onEvent: (CardPinUiEvent) -> Unit = {}) {
    LaunchedEffect(Unit) {
        onEvent(CardPinUiEvent.OnStart)
    }
    ScreenWithTopAppBar(
        onLeftButtonClick = { onEvent(CardPinUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(CardPinUiEvent.OnSupportClick) },
        title = stringResource(id = R.string.change_pin_number)
    ) {
        Column(
            modifier = Modifier
                .padding(horizontal = MaterialTheme.customDimens.dimen24)
                .fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (!uiState.canModifyPin) {
                EmptyStateScreen(
                    modifier = Modifier.background(MaterialTheme.colorScheme.surface),
                    primaryActionText = stringResource(id = R.string.go_back),
                    onPrimaryAction = { onEvent(CardPinUiEvent.OnBackClick) },
                    variant = EmptyStateCardVariant.SECONDARY,
                    contentDescription = { ActionNotAllowedDescription() }
                )
            } else {
                CardPinScreenContent(uiState = uiState, onEvent = onEvent)
            }
        }
    }
}

@Composable
private fun CardPinScreenContent(
    uiState: CardPinUiState,
    onEvent: (CardPinUiEvent) -> Unit = {}
) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Spacer88()
        Text(
            text = uiState.descriptionLabel?.let { stringResource(id = it) }.orEmpty(),
            style = MaterialTheme.typography.bodySmall
        )
        Spacer32()
        PinTextField(
            modifier = Modifier.fillMaxWidth(),
            value = uiState.pin,
            digitFieldSize = DpSize(
                width = MaterialTheme.customDimens.dimen60,
                height = MaterialTheme.customDimens.dimen64
            ),
            hasError = uiState.hasError,
            length = FOUR_VALUE,
            onValueChange = {
                onEvent(CardPinUiEvent.OnPinChange(it))
            }
        )
        if (uiState.hasError) {
            Spacer8()
            Text(
                text = stringResource(id = R.string.pin_error),
                style = MaterialTheme.typography.labelSmall.copy(color = MaterialTheme.colorScheme.error)
            )
        }
        Spacer32()
        OneButton(
            modifier = Modifier.fillMaxWidth(),
            enabled = uiState.canEnableContinueButton,
            text = uiState.continueButtonLabel?.let { stringResource(id = it) }.orEmpty(),
            onClick = {
                onEvent(CardPinUiEvent.OnContinueClick)
            }
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun NewPinScreenPreview() {
    OneAppTheme {
        CardPinScreen(
            uiState = CardPinUiState(
                continueButtonLabel = R.string.continue_button_label,
                descriptionLabel = R.string.enter_your_new_pin,
                hasError = true
            )
        )
    }
}