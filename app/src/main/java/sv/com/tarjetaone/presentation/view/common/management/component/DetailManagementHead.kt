package sv.com.tarjetaone.presentation.view.common.management.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.util.toColor

@Composable
fun DetailManagementHead(
    modifier: Modifier = Modifier,
    managementNumber: String,
    managementStatus: String,
    managementStatusColor: String
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(id = R.string.details_process),
            style = MaterialTheme.typography.displaySmall,
            color = MaterialTheme.colorScheme.onSurface
        )
        Text(
            text = stringResource(
                id = R.string.process_number_label,
                managementNumber
            ),
            style = MaterialTheme.typography.bodyMedium.copy(
                fontWeight = FontWeight.Normal,
                lineHeight = MaterialTheme.customDimensSp.sp25
            ),
            color = MaterialTheme.colorScheme.onSurface
        )
        Text(
            text = stringResource(id = R.string.status),
            modifier = modifier.padding(top = MaterialTheme.customDimens.dimen16),
            style = MaterialTheme.typography.bodySmall.copy(
                fontSize = MaterialTheme.customDimensSp.sp12,
                lineHeight = MaterialTheme.customDimensSp.sp14
            ),
            color = MaterialTheme.customColors.gray5
        )
        Text(
            text = managementStatus,
            style = MaterialTheme.typography.displayMedium.copy(
                lineHeight = MaterialTheme.customDimensSp.sp38
            ),
            color = managementStatusColor.toColor(MaterialTheme.customColors.onDefaultSurface)
        )
    }
}

@Composable
@Preview(showBackground = true)
fun DetailManagementHeadPreview() {
    OneAppTheme {
        DetailManagementHead(
            managementNumber = "123456789",
            managementStatus = "En proceso",
            managementStatusColor = "#51DEA3"
        )
    }
}