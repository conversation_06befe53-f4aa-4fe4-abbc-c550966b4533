package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_cancel.carousel

import androidx.compose.runtime.Stable
import sv.com.tarjetaone.domain.entities.response.MainCardUI

@Stable
data class CardCancelCarouselUiState(
    val isPendingBalanceDialogVisible: Boolean = false,
    val cardsToCancel: List<MainCardUI> = emptyList(),
    val cardSelected: MainCardUI? = null,
    val isMainCardCancellable: Boolean = false
)