package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.cashback.description

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer12
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer20

@Composable
fun CashbackDescriptionScreen(
    buttonTitle: Int = R.string.ok_message,
    buttonVariant: ButtonVariant = ButtonVariant.PRIMARY_VARIANT,
    buttonAction: () -> Unit = { },
) {
    Column(modifier = Modifier.fillMaxHeight()) {
        Text(
            text = stringResource(R.string.cashback_information_how_to_use),
            style = MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.customColors.secondaryDark
            )
        )
        Spacer16()
        Row(verticalAlignment = Alignment.CenterVertically) {
            Icon(
                imageVector = ImageVector.vectorResource(id = R.drawable.ic_laptop_circle),
                contentDescription = null,
                tint = Color.Unspecified
            )
            Spacer12()
            Text(
                text = stringResource(R.string.cashback_information_description_one),
                style = MaterialTheme.typography.bodySmall.copy(
                    color = MaterialTheme.customColors.secondaryDark
                )
            )
        }
        Spacer16()
        Row(verticalAlignment = Alignment.CenterVertically) {
            Icon(
                imageVector = ImageVector.vectorResource(id = R.drawable.ic_cards_circle),
                contentDescription = null,
                tint = Color.Unspecified
            )
            Spacer12()
            Text(
                text = stringResource(R.string.cashback_information_description_two),
                style = MaterialTheme.typography.bodySmall.copy(
                    color = MaterialTheme.customColors.secondaryDark
                )
            )
        }
        Spacer16()
        Row(verticalAlignment = Alignment.CenterVertically) {
            Icon(
                imageVector = ImageVector.vectorResource(id = R.drawable.ic_share_circle),
                contentDescription = null,
                tint = Color.Unspecified
            )
            Spacer12()
            Text(
                text = stringResource(R.string.cashback_information_description_three),
                style = MaterialTheme.typography.bodySmall.copy(
                    color = MaterialTheme.customColors.secondaryDark
                )
            )
        }
        Spacer16()
        Spacer1f()
        OneButton(
            buttonVariant = buttonVariant,
            text = stringResource(buttonTitle),
            trailingIcon = {
                Icon(
                    modifier = Modifier.padding(start = MaterialTheme.customDimens.dimen8),
                    imageVector = ImageVector.vectorResource(id = R.drawable.ic_chevron_right),
                    contentDescription = null
                )
            },
            onClick = { buttonAction() },
            modifier = Modifier.fillMaxWidth()
        )
        Spacer20()
    }
}
