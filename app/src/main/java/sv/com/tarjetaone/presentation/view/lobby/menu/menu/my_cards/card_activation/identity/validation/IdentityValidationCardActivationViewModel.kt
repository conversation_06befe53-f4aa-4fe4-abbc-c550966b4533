package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_activation.identity.validation

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.IdentityValidationResult.TRY_FACPHI
import sv.com.tarjetaone.core.utils.IdentityValidationResult.TRY_FAILD
import sv.com.tarjetaone.core.utils.IdentityValidationResult.TRY_FRD
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.usecases.auth.FaceAuthenticationUseCase
import sv.com.tarjetaone.presentation.view.common.identity_validation.IdentityValidationBaseViewModel
import javax.inject.Inject

@HiltViewModel
class IdentityValidationCardActivationViewModel @Inject constructor(
    faceAuthenticationUseCase: FaceAuthenticationUseCase,
    sharedPrefs: SecureSharedPreferencesRepository,
    savedStateHandle: SavedStateHandle
) : IdentityValidationBaseViewModel(faceAuthenticationUseCase, sharedPrefs) {

    private val args = IdentityValidationCardActivationFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override val onBlockedAction = {
        sendEvent(UiEvent.NavigateBackTo(R.id.navigation_home))
    }

    override fun operation(biometryId: Int) {
        sendEvent(
            UiEvent.Navigate(
                IdentityValidationCardActivationFragmentDirections
                    .actionIdentityValidationCardActivationFragmentToConfirmLastNumberCardActivate(
                        args.cardId,
                        biometryId
                    )
            )
        )
    }

    override fun onFailedIdentityValidation(resultCode: String?) {
        when (resultCode) {
            TRY_FRD.name, TRY_FACPHI.name, TRY_FAILD.name -> sendEvent(UiEvent.NavigateBack)
            else -> {
                sendEvent(
                    UiEvent.Navigate(
                        IdentityValidationCardActivationFragmentDirections.actionHome()
                    )
                )
            }
        }
    }
}
