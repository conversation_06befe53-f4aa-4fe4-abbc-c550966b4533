package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.generaldata.dataoverview

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.THREE_FLOAT_VALUE
import sv.com.tarjetaone.core.utils.extensions.setGender
import sv.com.tarjetaone.domain.entities.response.CustomerDataUI
import sv.com.tarjetaone.presentation.compose.modifier.conditional
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.HeadingWithLabelText
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer24

/**
 * Reusable Personal Data Overview content, since the UI is the same across different screens.
 *
 * @param customerData data object containing the user's information to fill up the screen content.
 * @param modifier to apply composable customizations.
 * @param addressLabel it can be different for each screen content.
 * @param breakdownAddress if the Address and municipality need to be shown separately.
 */
@Composable
fun PersonalDataOverviewSharedContent(
    customerData: CustomerDataUI,
    modifier: Modifier = Modifier,
    addressLabel: String? = stringResource(id = R.string.personal_overview_data_dui_address_label),
    breakdownAddress: Boolean = false,
    canEditInformation: Boolean = false,
    onEditButtonClick: () -> Unit = {}
) {
    Column(modifier) {
        customerData.genderCode?.let {
            Spacer24()
            FullNameSection(
                fullName = customerData.fullName,
                canEditInformation = canEditInformation,
                onEditButtonClick = onEditButtonClick
            )
            Spacer24()
            HeadingWithLabelText(
                headingText = customerData.genderCode.orEmpty(),
                label = stringResource(id = R.string.personal_overview_data_gender_label),
            )
            Spacer24()
            DocumentSection(
                document = customerData.dui,
                canEditInformation = canEditInformation,
                onEditButtonClick = onEditButtonClick
            )
            Spacer24()
            HeadingWithLabelText(
                headingText = customerData.duiExpirationDate.orEmpty(),
                label = stringResource(id = R.string.personal_overview_data_dui_expiration_date_label),
            )
            Spacer24()
            HeadingWithLabelText(
                headingText = customerData.birthDate.orEmpty(),
                label = stringResource(id = R.string.personal_overview_data_dui_birthdate_date_label),
            )
            Spacer24()
            addressLabel?.let { address ->
                HeadingWithLabelText(
                    headingText = customerData.address.orEmpty(),
                    label = address,
                )
            }
            if (breakdownAddress) {
                Spacer24()
                HeadingWithLabelText(
                    headingText = customerData.department.orEmpty(),
                    label = stringResource(id = R.string.departament),
                )
                Spacer24()
                HeadingWithLabelText(
                    headingText = customerData.municipality.orEmpty(),
                    label = stringResource(id = R.string.municipality_label),
                )
            }
        }
    }
}

@Composable
private fun FullNameSection(
    fullName: String?,
    canEditInformation: Boolean = false,
    onEditButtonClick: () -> Unit = {}
) {
    Row(modifier = Modifier.fillMaxWidth()) {
        HeadingWithLabelText(
            modifier = Modifier
                .conditional(
                    condition = canEditInformation,
                    onTrue = { this.weight(THREE_FLOAT_VALUE) }
                )
            ,
            headingText = fullName.orEmpty(),
            label = stringResource(id = R.string.personal_overview_data_name_label),
        )
        if (canEditInformation) {
            OneButton(
                text = stringResource(id = R.string.document_edit),
                buttonVariant = ButtonVariant.SECONDARY_VARIANT,
                onClick = onEditButtonClick
            )
        }
    }
}

@Composable
private fun DocumentSection(
    document: String?,
    canEditInformation: Boolean = false,
    onEditButtonClick: () -> Unit = {}
) {
    Row(modifier = Modifier.fillMaxWidth()) {
        HeadingWithLabelText(
            modifier = Modifier.conditional(
                condition = canEditInformation,
                onTrue = { this.weight(THREE_FLOAT_VALUE) }
            ),
            headingText = document.orEmpty(),
            label = stringResource(id = R.string.personal_overview_data_dui_label),
        )
        if (canEditInformation) {
            OneButton(
                text = stringResource(id = R.string.document_edit),
                buttonVariant = ButtonVariant.SECONDARY_VARIANT,
                onClick = onEditButtonClick
            )
        }
    }
}

@Preview(showSystemUi = false)
@Composable
private fun PersonalDataOverviewSharedContentPreview() {
    OneAppTheme {
        PersonalDataOverviewSharedContent(
            modifier = Modifier
                .background(MaterialTheme.colorScheme.surface)
                .padding(horizontal = MaterialTheme.customDimens.dimen32),
            customerData = CustomerDataUI(
                customerId = 0,
                fullName = "Juan Daniel Perez Smith",
                genderCode = "F".setGender(),
                dui = "0000000-0",
                duiExpirationDate = "01 jun 2024",
                birthDate = "01 julio 1990",
                address = "Col. Escalón, Calle Francisco Gavidia Casa #4, San Salvador, San Salvador",
                municipality = "San Salvador",
                department = "San Salvador"
            ),
            breakdownAddress = true,
            canEditInformation = true
        )
    }
}
