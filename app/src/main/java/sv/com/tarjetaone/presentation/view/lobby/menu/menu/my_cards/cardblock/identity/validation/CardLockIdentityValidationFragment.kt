package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.cardblock.identity.validation

import android.os.Bundle
import android.view.View
import androidx.fragment.app.viewModels
import dagger.hilt.android.AndroidEntryPoint
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.base.BaseFragment
import sv.com.tarjetaone.databinding.FragmentComposeBinding
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.loading.IdentityValidationLoadingScreen

/**
 * Fragment that will hold the logic to to lock a card. As soon as the user reaches this screen, the 'Cardblock'
 * endpoint will be executed and a loading screen will be shown to the user.
 */

@AndroidEntryPoint
class CardLockIdentityValidationFragment : BaseFragment<FragmentComposeBinding>(
    R.layout.fragment_compose
) {
    override val viewModel: CardLockIdentityValidationViewModel by viewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.composeView.setContentScreen {
            IdentityValidationLoadingScreen(viewModel = viewModel)
        }
    }
}
