package sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.common.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.card.HorizontalIconWithText
import sv.com.tarjetaone.presentation.compose.uicomponent.card.SimpleCardComponent
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.compose.util.Spacer4
import sv.com.tarjetaone.presentation.compose.util.Spacer40
import sv.com.tarjetaone.presentation.compose.util.Spacer56
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.common.ProgramProtectionState

@Composable
fun ProtectionEnrollActivateContent(
    modifier: Modifier,
    programProtectionState: ProgramProtectionState? = null,
    amountToCharge: String,
    isActionButtonEnabled: Boolean,
    onPrimaryButtonClick: () -> Unit,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        ActivateProtectionProgramHeader()
        Spacer32()
        ActivateProtectionProgramBody(
            chargeText = amountToCharge
        )
        Spacer1f()
        Spacer40()
        Text(
            modifier = Modifier.fillMaxWidth(),
            text = if (programProtectionState == ProgramProtectionState.Enroll) {
                stringResource(id = R.string.do_you_want_to_get_your_program_protection)
            } else {
                stringResource(id = R.string.ask_for_activate_protection_program)
            },
            style = MaterialTheme.typography.displaySmall.copy(
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.secondary
            ),
            textAlign = TextAlign.Center
        )
        Spacer24()
        OneButton(
            enabled = isActionButtonEnabled,
            modifier = Modifier.fillMaxWidth(),
            text = if (programProtectionState == ProgramProtectionState.Enroll) {
                stringResource(id = R.string.hire_label)
            } else {
                stringResource(id = R.string.activate)
            },
            onClick = onPrimaryButtonClick,
        )
        Spacer32()
    }
}

@Composable
private fun ActivateProtectionProgramHeader() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            modifier = Modifier.size(MaterialTheme.customDimens.dimen48),
            imageVector = ImageVector.vectorResource(id = R.drawable.ic_shield_lock),
            tint = MaterialTheme.customColors.successContainer,
            contentDescription = null
        )
        Spacer32()
        Text(
            text = stringResource(id = R.string.protection_plan),
            style = MaterialTheme.typography.displaySmall,
            color = MaterialTheme.colorScheme.secondary
        )
        Spacer4()
        Text(
            text = stringResource(id = R.string.credit_card_program_protection),
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.secondary
        )
    }
}

@Composable
private fun ActivateProtectionProgramBody(
    chargeText: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        ProtectionProgramBenefitsCard()
        Spacer56()
        Text(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(id = R.string.charge_method_label),
            style = MaterialTheme.typography.titleMedium.copy(
                lineHeight = MaterialTheme.customDimensSp.sp14,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            ),
            textAlign = TextAlign.Center
        )
        Spacer8()
        SimpleCardComponent(
            modifier = Modifier.fillMaxWidth(),
            shape = MaterialTheme.shapes.medium,
            contentPadding = PaddingValues(
                horizontal = MaterialTheme.customDimens.dimen12,
                vertical = MaterialTheme.customDimens.dimen8
            )
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = chargeText,
                    style = MaterialTheme.typography.displaySmall.copy(
                        fontWeight = FontWeight.Normal,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    ),
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Composable
private fun ProtectionProgramBenefitsCard() {
    SimpleCardComponent(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(
            defaultElevation = MaterialTheme.customDimens.dimen2
        )
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen32)
        ) {
            HorizontalIconWithText(
                leadingIcon = R.drawable.ic_check_circle_white,
                iconSize = MaterialTheme.customDimens.dimen24,
                tint = MaterialTheme.customColors.successContainer,
                text = UiText.StringResource(R.string.coverage_program_protection),
                style = MaterialTheme.typography.labelMedium.copy(
                    color = MaterialTheme.colorScheme.secondary,
                    lineHeight = MaterialTheme.customDimensSp.sp20
                )
            )
            HorizontalIconWithText(
                leadingIcon = R.drawable.ic_check_circle_white,
                iconSize = MaterialTheme.customDimens.dimen24,
                tint = MaterialTheme.customColors.successContainer,
                text = UiText.StringResource(R.string.coverage_for_fraudulent_payments),
                style = MaterialTheme.typography.labelMedium.copy(
                    color = MaterialTheme.colorScheme.secondary,
                    lineHeight = MaterialTheme.customDimensSp.sp20
                )
            )
            HorizontalIconWithText(
                leadingIcon = R.drawable.ic_check_circle_white,
                iconSize = MaterialTheme.customDimens.dimen24,
                tint = MaterialTheme.customColors.successContainer,
                text = UiText.StringResource(R.string.report_directly_from_the_app),
                style = MaterialTheme.typography.labelMedium.copy(
                    color = MaterialTheme.colorScheme.secondary,
                    lineHeight = MaterialTheme.customDimensSp.sp20
                )
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewProtectionEnrollStateContent() {
    OneAppTheme {
        ProtectionEnrollActivateContent(
            modifier = Modifier
                .padding(horizontal = MaterialTheme.customDimens.dimen32)
                .fillMaxSize(),
            programProtectionState = ProgramProtectionState.Enroll,
            amountToCharge = "Mensual $1.99 + IVA",
            isActionButtonEnabled = true,
            onPrimaryButtonClick = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewProtectionActivateStateContent() {
    OneAppTheme {
        ProtectionEnrollActivateContent(
            modifier = Modifier
                .padding(horizontal = MaterialTheme.customDimens.dimen32)
                .fillMaxSize(),
            programProtectionState = ProgramProtectionState.Activate,
            amountToCharge = "Mensual $1.99 + IVA",
            isActionButtonEnabled = true,
            onPrimaryButtonClick = {}
        )
    }
}