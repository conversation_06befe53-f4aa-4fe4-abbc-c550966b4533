package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.cardpay.detail

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.extensions.configCurrencyWithFractions
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.domain.entities.response.ManAttributesUI
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.common.management.ManagementDetailBaseViewModel
import sv.com.tarjetaone.presentation.view.common.management.toAttributeUiModel
import sv.com.tarjetaone.presentation.view.utils.DAY_OF_WEEK_MONTH_TIME_FORMAT
import sv.com.tarjetaone.presentation.view.utils.ManAttributeType
import sv.com.tarjetaone.presentation.view.utils.ManAttributeType.Companion.getAttributeType
import sv.com.tarjetaone.presentation.view.utils.YEAR_MONTH_DAY_TIME_FORMAT
import javax.inject.Inject

@HiltViewModel
class CardPayDetailViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    imageUtils: ImageUtils
) : ManagementDetailBaseViewModel(imageUtils) {
    private val args = CardPayDetailFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onStart() {
        args.cardReplacement?.let {
            _uiState.update { state ->
                state.copy(
                    isBackButtonVisible = false,
                    managementNumber = it.manRequestId.toString(),
                    managementStatus = it.mrStatusNameApp.orEmpty(),
                    managementStatusColor = it.mrStatusTextColor.orEmpty(),
                    managementName = it.manTypeNameApp.orEmpty(),
                    customerName = it.clientName.orEmpty(),
                    creditCardNumber = it.cardNumMasked.orEmpty(),
                    creditCardType = it.typeCardText.orEmpty(),
                    requestStartDate = it.processDate?.getFormattedDateFromTo(
                        YEAR_MONTH_DAY_TIME_FORMAT,
                        DAY_OF_WEEK_MONTH_TIME_FORMAT
                    ).orEmpty(),
                    description = it.description,
                    resolutionDays = it.availableDay,
                    managementAttributes = getManAttributes(it.manAttributes)
                )
            }
        }
    }

    override fun onPrimaryButtonClick() {
        sendEvent(UiEvent.Navigate(CardPayDetailFragmentDirections.actionHome()))
    }

    private fun getManAttributes(attributes: List<ManAttributesUI>) = attributes.filter {
        getAttributeType(it.manAttributeTypeNameApp) == ManAttributeType.AmountPaid
    }.map {
        it.toAttributeUiModel().copy(
            managementAttributeDisplayName = UiText.StringResource(R.string.total_payment_label),
            managementAttributeValue = UiText.DynamicString(
                it.value?.toDoubleOrNull()?.configCurrencyWithFractions().orEmpty()
            )
        )
    }
}