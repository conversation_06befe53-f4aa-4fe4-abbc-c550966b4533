package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.generaldata.identity.capture

import android.graphics.Bitmap
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.presentation.view.common.selfiecapture.SelfieCaptureBaseViewModel
import javax.inject.Inject

@HiltViewModel
class PersonalInfoSelfieCaptureViewModel @Inject constructor(
    facephiResultHandler: FacephiResultHandler
) : SelfieCaptureBaseViewModel(facephiResultHandler) {

    override fun onSuccessfulSelfieCapture(selfie: Bitmap) {
        sendEvent(
            UiEvent.Navigate(
                PersonalInfoSelfieCaptureFragmentDirections
                    .actionPrepareForPicturePersonalInfoFragmentToSelfieConfirmationPersonalDataOverviewFragment()
            )
        )
    }
}
