package sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.management

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.presentation.view.common.management.ManagementDetailBaseViewModel
import javax.inject.Inject

@HiltViewModel
class ManagementDetailViewModel @Inject constructor(
    imageUtils: ImageUtils,
    savedStateHandle: SavedStateHandle
) : ManagementDetailBaseViewModel(imageUtils) {
    private val args = ManagementDetailFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onStart() {
        args.claimDetail?.let { claimDetail ->
            _uiState.update {
                it.copy(
                    isBackButtonVisible = true,
                    managementNumber = claimDetail.manRequestId.toString(),
                    managementStatus = claimDetail.mrStatusNameApp.orEmpty(),
                    managementStatusColor = claimDetail.mrStatusTextColor.orEmpty(),
                    managementName = claimDetail.manTypeNameApp.orEmpty(),
                    customerName = claimDetail.clientName.orEmpty(),
                    creditCardNumber = claimDetail.cardNumMasked.orEmpty(),
                    creditCardType = claimDetail.typeCardText.orEmpty(),
                    requestStartDate = claimDetail.getFormattedDateInit(),
                    requestEndDate = claimDetail.getFormattedDateEnd(),
                    description = claimDetail.description,
                    resolutionDays = claimDetail.availableDay,
                    showPrimaryButton = false
                )
            }
        }
    }

    override fun onPrimaryButtonClick() {
        sendEvent(UiEvent.NavigateBack)
    }
}