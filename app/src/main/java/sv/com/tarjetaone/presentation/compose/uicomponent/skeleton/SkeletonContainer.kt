package sv.com.tarjetaone.presentation.compose.uicomponent.skeleton

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.ContentTransform
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.layout.SubcomposeLayout
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.presentation.compose.modifier.shimmerEffect
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.util.ShimmerSize
import sv.com.tarjetaone.presentation.compose.util.ShimmerStyle

/**
 * A container that shows a skeleton loading effect while the content is being loaded.
 * It uses a [SubcomposeLayout] to measure and layout the content and the skeleton.
 * To control the [state] of the skeleton use the [SkeletonState] to determine the composable
 * that must be rendered. As of now, there are three possible states: Loading, Error and Success
 * If a [customSkeletonContent] is passed, then the [SkeletonContainer]
 * should only receive the [state] and [customSkeletonContent] params.
 *
 * @param modifier The [Modifier] to be applied to this container.
 * @param state The current state of the skeleton. It uses the [SkeletonState] sealed class.
 * @param shape The shape of the skeleton that matches the shape of the content.
 * @param shimmerSize The width of the shimmer effect.
 * @param shimmerStyle The theme of the shimmer effect (LIGHT_BACKGROUND or DARK_BACKGROUND).
 * @param backgroundColor The background color of the skeleton container.
 * @param content The content that will be displayed when not loading the skeleton.
 * @param customSkeletonContent The custom skeleton content to be displayed when loading.
 */
@Suppress("kotlin:S107")
@Composable
fun SkeletonContainer(
    modifier: Modifier = Modifier,
    state: SkeletonState = SkeletonState.Loading,
    shape: Shape = MaterialTheme.shapes.medium,
    shimmerSize: ShimmerSize = ShimmerSize.MEDIUM,
    shimmerStyle: ShimmerStyle = ShimmerStyle.LIGHT_BACKGROUND,
    backgroundColor: Color = MaterialTheme.customColors.gray300,
    transitionSpec: () -> ContentTransform = {
        fadeIn() togetherWith fadeOut()
    },
    customSkeletonContent: (@Composable () -> Unit)? = null,
    errorContent: (@Composable (DpSize) -> Unit)? = null,
    content: @Composable () -> Unit = {},
) {
    AnimatedContent(
        targetState = state,
        label = SKELETON_ANIMATED_CONTENT,
        transitionSpec = { transitionSpec() }
    ) { animatedContentState ->
        SubcomposeLayout(modifier = modifier) { constraints ->

            // Measure the content size dynamically.
            val contentMeasure = subcompose(CONTENT_TAG, content).map { it.measure(constraints) }

            val width = contentMeasure.maxOfOrNull { it.width }.orZero()
            val height = contentMeasure.maxOfOrNull { it.height }.orZero()

            layout(width, height) {

                when (animatedContentState) {
                    SkeletonState.Loading -> {
                        val skeletonMeasure = subcompose(SKELETON_TAG) {
                            customSkeletonContent?.let {
                                customSkeletonContent()
                            } ?: Box(
                                Modifier
                                    .background(color = backgroundColor, shape = shape)
                                    .size(width.toDp(), height.toDp())
                                    .shimmerEffect(shape, shimmerSize, shimmerStyle)
                            )
                        }.map { it.measure(constraints) }

                        skeletonMeasure.forEach { it.place(IntOffset.Zero.x, IntOffset.Zero.y) }
                    }

                    SkeletonState.Failure -> {
                        subcompose(SKELETON_TAG) {
                            errorContent?.invoke(DpSize(width.toDp(), height.toDp()))
                        }.map {
                            it.measure(constraints)
                        }.forEach {
                            it.place(IntOffset.Zero.x, IntOffset.Zero.y)
                        }
                    }

                    SkeletonState.Success -> {
                        contentMeasure.forEach { it.place(IntOffset.Zero.x, IntOffset.Zero.y) }
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun SkeletonContainerPreview() {
    OneAppTheme {
        SkeletonContainer(
            state = SkeletonState.Loading,
            shape = MaterialTheme.shapes.extraSmall,
            backgroundColor = MaterialTheme.customColors.gray400
        ) {
            // Represents the given content that will be displayed when not loading.
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(50.dp)
                    .background(MaterialTheme.customColors.successContainer)
            )
        }
    }
}

const val SKELETON_TAG = "Skeleton"
const val CONTENT_TAG = "Content"
const val SKELETON_ANIMATED_CONTENT = "SkeletonAnimatedContent"
