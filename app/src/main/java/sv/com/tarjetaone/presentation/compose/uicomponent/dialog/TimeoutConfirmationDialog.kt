package sv.com.tarjetaone.presentation.compose.uicomponent.dialog

import androidx.annotation.StringRes
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CornerSize
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.presentation.compose.theme.LocalCustomColors
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.theme.poppinsFamily
import sv.com.tarjetaone.presentation.compose.uicomponent.button.HyperLinkTextButton
import sv.com.tarjetaone.presentation.compose.util.Spacer8

@Composable
fun TimeoutConfirmationDialog(
    modifier: Modifier = Modifier,
    @StringRes title: Int,
    @StringRes textPrimaryButton: Int,
    remainingTime: Int = ZERO_VALUE,
    onDismissRequest: (() -> Unit)? = null,
    onKeepSessionPressed: () -> Unit = {},
    onCancelRequestPressed: (() -> Unit)? = null
) {
    Dialog(
        onDismissRequest = {
            onDismissRequest?.invoke() ?: onCancelRequestPressed?.invoke()
        },
        properties = DialogProperties(
            dismissOnBackPress = false,
            dismissOnClickOutside = false
        )
    ) {
        Box(
            modifier = modifier
                .fillMaxSize()
                .wrapContentHeight()
                .padding(
                    horizontal = MaterialTheme.customDimens.dimen16,
                    vertical = MaterialTheme.customDimens.dimen1
                )
        ) {
            Card(
                colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
                modifier = modifier
                    .heightIn(min = MaterialTheme.customDimens.dimen189)
                    .wrapContentWidth()
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            vertical = MaterialTheme.customDimens.dimen16,
                            horizontal = MaterialTheme.customDimens.dimen16
                        ),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        modifier = Modifier.size(
                            width = MaterialTheme.customDimens.dimen56,
                            height = MaterialTheme.customDimens.dimen50
                        ),
                        painter = painterResource(id = R.drawable.ic_dialog_session_timeout),
                        tint = LocalCustomColors.current.alertVariant,
                        contentDescription = stringResource(id = title),
                    )
                    Spacer8()
                    Text(
                        text = stringResource(
                            id = title,
                            remainingTime
                        ),
                        style = MaterialTheme.typography.headlineSmall,
                        color = MaterialTheme.colorScheme.onSurface,
                        textAlign = TextAlign.Center
                    )
                    Spacer8()
                    TimeoutConfirmationDialogButton(
                        text = stringResource(id = textPrimaryButton),
                        onClick = {
                            onKeepSessionPressed()
                        }
                    )
                    onCancelRequestPressed?.let {
                        HyperLinkTextButton(
                            text = stringResource(R.string.timeout_dialog_close_session_btn_text),
                            textStyle = MaterialTheme.typography.bodySmall.copy(
                                textDecoration = TextDecoration.Underline,
                                color = MaterialTheme.colorScheme.primary,
                                textAlign = TextAlign.Center
                            ),
                            onClick = {
                                onCancelRequestPressed()
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun TimeoutConfirmationDialogButton(
    modifier: Modifier = Modifier,
    text: String,
    onClick: () -> Unit = {},
) {
    Button(
        modifier = modifier.fillMaxWidth(),
        enabled = true,
        colors = ButtonDefaults.buttonColors(
            containerColor = LocalCustomColors.current.alertVariant,
            contentColor = LocalCustomColors.current.onInfo,
        ),
        shape = MaterialTheme.shapes.extraSmall.copy(all = CornerSize(MaterialTheme.customDimens.dimen6)),
        onClick = onClick,
    ) {
        Text(
            text = text,
            style = TextStyle(
                fontWeight = FontWeight.SemiBold,
                fontFamily = poppinsFamily,
                fontSize = MaterialTheme.customDimensSp.sp14,
                textAlign = TextAlign.Center
            ),
            textAlign = TextAlign.Center
        )
    }
}

@Preview(showBackground = true)
@Composable
fun TimeoutOnboardingConfirmationDialogPreview() {
    OneAppTheme {
        TimeoutConfirmationDialog(
            title = R.string.timeout_ob_confirmation_dialog_title,
            textPrimaryButton = R.string.timeout_ob_dialog_keep_session_btn_text,
            remainingTime = 10,
            onCancelRequestPressed = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
fun TimeoutSessionConfirmationDialogPreview() {
    OneAppTheme {
        TimeoutConfirmationDialog(
            title = R.string.timeout_session_confirmation_dialog_title,
            textPrimaryButton = R.string.timeout_session_dialog_keep_session_btn_text,
            remainingTime = 10,
        )
    }
}
