package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.cardblock.identity.capture

import android.graphics.Bitmap
import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.presentation.view.common.selfiecapture.SelfieCaptureBaseViewModel
import javax.inject.Inject

@HiltViewModel
class CardLockSelfieCaptureViewModel @Inject constructor(
    facephiResultHandler: FacephiResultHandler,
    savedStateHandle: SavedStateHandle
) : SelfieCaptureBaseViewModel(facephiResultHandler) {

    private val args = CardLockSelfieCaptureFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onSuccessfulSelfieCapture(selfie: Bitmap) {
        sendEvent(
            UiEvent.Navigate(
                CardLockSelfieCaptureFragmentDirections
                    .actionPrepareForPictureCardLockFragmentToSelfieConfirmationCardLockFragment(
                        cardBlockId = args.cardBlockId,
                        cardBlockReasonId = args.cardBlockReasonId
                    )
            )
        )
    }
}
