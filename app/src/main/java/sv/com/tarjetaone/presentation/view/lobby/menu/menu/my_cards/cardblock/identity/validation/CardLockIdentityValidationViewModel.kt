package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.cardblock.identity.validation

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.CHA_APP
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.request.CardBlockRequestUI
import sv.com.tarjetaone.domain.usecases.auth.FaceAuthenticationUseCase
import sv.com.tarjetaone.domain.usecases.mycards.cardlock.CardBlockUseCase
import sv.com.tarjetaone.domain.utils.onApiError
import sv.com.tarjetaone.domain.utils.onNetworkError
import sv.com.tarjetaone.domain.utils.onSuccess
import sv.com.tarjetaone.presentation.view.common.identity_validation.IdentityValidationBaseViewModel
import javax.inject.Inject

@HiltViewModel
class CardLockIdentityValidationViewModel @Inject constructor(
    faceAuthenticationUseCase: FaceAuthenticationUseCase,
    sharedPreferencesRepository: SecureSharedPreferencesRepository,
    private val cardBlockUseCase: CardBlockUseCase,
    savedStateHandle: SavedStateHandle
) : IdentityValidationBaseViewModel(faceAuthenticationUseCase, sharedPreferencesRepository) {
    override val onBlockedAction = {
        sendEvent(UiEvent.NavigateBackTo(R.id.navigation_home))
    }

    private val args = CardLockIdentityValidationFragmentArgs.fromSavedStateHandle(savedStateHandle)

    private fun blockCard(biometryId: Int) {
        val blockCardRequest = CardBlockRequestUI(
            cCardId = args.cardBlockId.orZero(),
            customerId = sharedPrefs.getCustomerId().orZero(),
            blockReason = args.cardBlockReasonId.orZero(),
            biometryId = biometryId,
            channelCode = CHA_APP
        )
        viewModelScope.launch {
            cardBlockUseCase(blockCardRequest)
                .onSuccess { response ->
                    baseSharedPrefs.cardBlockReasonId = args.cardBlockReasonId.orZero()
                    navigateToConfirmationScreen(response.data.orEmpty())
                }
                .onApiError { _, _, _, _ ->
                    showUpsErrorMessage(
                        isDismissible = false,
                        onButtonClick = { sendEvent(UiEvent.NavigateBack) }
                    )
                }
                .onNetworkError { _ ->
                    showUpsErrorMessage(
                        isDismissible = false,
                        onButtonClick = { sendEvent(UiEvent.NavigateBack) }
                    )
                }
        }
    }

    private fun navigateToConfirmationScreen(managementNumber: String) {
        sendEvent(
            UiEvent.Navigate(
                CardLockIdentityValidationFragmentDirections
                    .actionIdentityValidationCardLockFragmentToCardLockConfirmationFragment(
                        managmentNumber = managementNumber,
                        cardBlockId = args.cardBlockId.orZero()
                    )
            )
        )
    }

    override fun operation(biometryId: Int) {
        blockCard(biometryId)
    }

    override fun onFailedIdentityValidation(resultCode: String?) {
        sendEvent(UiEvent.NavigateBack)
    }
}
