package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.report.success

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.locationreceive.LocationReceiveCardAction
import javax.inject.Inject

@HiltViewModel
class ReportPurchaseSuccessViewModel @Inject constructor(savedStateHandle: SavedStateHandle) :
    BaseViewModel() {
    private val args = ReportPurchaseSuccessFragmentArgs.fromSavedStateHandle(savedStateHandle)
    private val _uiState = MutableStateFlow(ReportPurchaseSuccessUiState())
    val uiState: StateFlow<ReportPurchaseSuccessUiState> = _uiState.asStateFlow()

    private fun onStart() {
        _uiState.update {
            it.copy(managementId = args.managementId, cardBlockId = args.cardBlockId)
        }
    }

    private fun onCloseClick() {
        sendEvent(
            UiEvent.Navigate(
                ReportPurchaseSuccessFragmentDirections
                    .actionReportPurchaseSuccessFragmentToMyMovementsFragment()
            )
        )
    }

    private fun onSelectCardDeliveryClick() {
        sendEvent(
            UiEvent.Navigate(
                ReportPurchaseSuccessFragmentDirections
                    .actionReportPurchaseSuccessFragmentToNavigationMenuMyCardsLocation(
                        cardAction = LocationReceiveCardAction.CardReplacement(args.cardId)
                    )
            )
        )
    }

    fun onEvent(event: ReportPurchaseSuccessUiEvent) {
        when (event) {
            ReportPurchaseSuccessUiEvent.OnStart -> onStart()
            ReportPurchaseSuccessUiEvent.OnCloseClick -> onCloseClick()
            ReportPurchaseSuccessUiEvent.OnSelectCardADeliveryClick -> onSelectCardDeliveryClick()
        }
    }
}
