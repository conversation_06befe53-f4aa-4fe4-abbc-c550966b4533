package sv.com.tarjetaone.presentation.view.common.locationcapture

import com.google.android.gms.maps.model.LatLng
import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI
import sv.com.tarjetaone.domain.entities.response.SuggestionsItemUi
import sv.com.tarjetaone.presentation.view.common.locationcapture.helper.AddressAliasSuggestion

sealed class LocationCaptureUiEvent {
    data object OnBackClick : LocationCaptureUiEvent()
    data class OnStart(
        val showExcludedZones: Boolean,
    ) : LocationCaptureUiEvent()

    data class OnMapClick(val latLng: LatLng) : LocationCaptureUiEvent()
    data class OnSearchQueryChange(val query: String) : LocationCaptureUiEvent()
    data class OnPerformSearch(val query: String) : LocationCaptureUiEvent()
    data class OnSuggestionClick(val suggestion: SuggestionsItemUi) : LocationCaptureUiEvent()
    data object OnSaveClick : LocationCaptureUiEvent()

    data class OnAddressAliasChange(val alias: String) : LocationCaptureUiEvent()
    data class OnAddressAliasSuggestionChange(
        val suggestion: AddressAliasSuggestion
    ) : LocationCaptureUiEvent()

    data class OnStateChange(val state: CatalogItemsCollectionUI) : LocationCaptureUiEvent()
    data class OnMunicipalityChange(
        val municipality: CatalogItemsCollectionUI
    ) : LocationCaptureUiEvent()

    data class OnNeighborhoodChange(val neighborhood: String) : LocationCaptureUiEvent()
    data class OnStreetChange(val street: String) : LocationCaptureUiEvent()
    data class OnHomeNumberChange(val homeNumber: String) : LocationCaptureUiEvent()
    data class OnAdditionalInfoChange(val additionalInfo: String) : LocationCaptureUiEvent()
    data object OnContinueClick : LocationCaptureUiEvent()
    data object OnSearchMyAddressClick : LocationCaptureUiEvent()
    data class OnSnackBarVisibility(val visibility: Boolean) : LocationCaptureUiEvent()
}
