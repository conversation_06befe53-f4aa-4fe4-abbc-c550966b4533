package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.address_location.home_address_list

import android.Manifest
import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.rememberPermissionState
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.shouldShowRationale
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.OneAppSnackBar
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.DialogConfirmation
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.address_location.UserPersonalDataCardItem
import sv.com.tarjetaone.presentation.view.utils.PreviewUtils.ADDRESS

@Composable
fun UserAddressesScreen(viewModel: AddresLocationViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    SideEffectHandler(viewModel.sideEffects) {
        UserAddressesContent(
            uiState = uiState,
            onEvent = viewModel::onEvent
        )
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun UserAddressesContent(
    uiState: UserAddressesUiState,
    onEvent: (UserAddressesUiEvent) -> Unit
) {
    val context = LocalContext.current
    val snackBarHostState = remember { SnackbarHostState() }
    val locationPermission = Manifest.permission.ACCESS_FINE_LOCATION
    val locationPermissionState = rememberPermissionState(
        permission = locationPermission
    ) { isGranted ->
        if (isGranted) {
            onEvent(UserAddressesUiEvent.OnAddAddress)
        } else {
            onEvent(
                UserAddressesUiEvent.OnLocationPermissionDenied(
                    showRationale = context.shouldShowRationale(locationPermission)
                )
            )
        }
    }

    LaunchedEffect(Unit) {
        onEvent(UserAddressesUiEvent.OnStart)
    }
    LaunchedEffect(uiState.showDeleteConfirmationSnackBar) {
        if (uiState.showDeleteConfirmationSnackBar) {
            snackBarHostState.showSnackbar(
                message = context.getString(R.string.address_was_deleted_successfully),
                withDismissAction = true,
                duration = SnackbarDuration.Short
            )
        }
    }
    LaunchedEffect(snackBarHostState.currentSnackbarData) {
        // Reset the visibility flag when the snackbar is dismissed
        if (snackBarHostState.currentSnackbarData == null) {
            onEvent(UserAddressesUiEvent.OnResetSnackbar)
        }
    }
    if (uiState.showDeleteConfirmationDialog) {
        DialogConfirmation(
            message = UiText.StringResource(R.string.dialog_delete_address_message),
            primaryButtonText = UiText.StringResource(R.string.delete_label),
            secondaryButtonText = UiText.StringResource(R.string.cancel),
            onNegativeClick = {
                onEvent(UserAddressesUiEvent.OnDialogCancelClick)
            },
            onPositiveClick = {
                onEvent(UserAddressesUiEvent.OnDialogDeleteButtonClick)
            },
        )
    }
    ScreenWithTopAppBar(
        title = stringResource(R.string.address_location),
        onLeftButtonClick = { onEvent(UserAddressesUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(UserAddressesUiEvent.OnSupportClick) },
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = MaterialTheme.customDimens.dimen32),
        ) {
            UserAddressesList(
                modifier = Modifier.weight(1f),
                uiState = uiState,
                onModifyClick = { onEvent(UserAddressesUiEvent.OnEditAction(it)) },
                onDeleteClick = { onEvent(UserAddressesUiEvent.OnDeleteAction(it)) }
            )
            SnackbarHost(
                modifier = Modifier.fillMaxWidth(),
                hostState = snackBarHostState
            ) { data ->
                OneAppSnackBar(
                    icon = R.drawable.ic_check_circle_white,
                    message = data.visuals.message,
                    onCancelClick = { snackBarHostState.currentSnackbarData?.dismiss() }
                )
            }
            Spacer16()
            if (uiState.shouldShowAddAddressButton()) {
                OneButton(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(id = R.string.user_address_location_add_text_button),
                    onClick = {
                        locationPermissionState.launchPermissionRequest()
                    }
                )
            } else {
                MaxAddressMessage()
            }
            Spacer16()
        }
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun UserAddressesList(
    modifier: Modifier = Modifier,
    uiState: UserAddressesUiState,
    onModifyClick: (addressId: Int) -> Unit = { _ -> },
    onDeleteClick: (addressId: Int) -> Unit = { _ -> },
) {
    LazyColumn(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen16)
    ) {
        itemsIndexed(
            items = uiState.addressList,
            key = { _, item -> item.addressId }
        ) { _, item ->
            UserPersonalDataCardItem(
                modifier = Modifier.animateItem(
                    placementSpec = tween(
                        easing = LinearOutSlowInEasing
                    )
                ),
                userAddressData = item,
                icon = R.drawable.ic_house_door_fill,
                showDeleteOption = uiState.shouldShowDeleteOption(),
                onEditClick = { onModifyClick(it) },
                onDeleteClick = { onDeleteClick(it) }
            )
        }
    }
}

@Composable
fun MaxAddressMessage(
    modifier: Modifier = Modifier
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.customDimens.dimen32)
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_info_2),
            contentDescription = null
        )
        Spacer8()
        Text(
            text = stringResource(id = R.string.max_address_addedd),
            style = MaterialTheme.typography.labelMedium.copy(lineHeight = MaterialTheme.customDimensSp.sp14),
            color = MaterialTheme.customColors.disclaimer
        )
    }
}

@Preview
@Composable
fun UserAddressesScreenPreview() {
    OneAppTheme {
        UserAddressesContent(
            uiState = UserAddressesUiState(
                maxAddressItems = 2,
                addressList = remember {
                    mutableStateListOf(
                        UserAddress(
                            addressId = 1,
                            addressName = UiText.DynamicString("Home"),
                            fullAddress = UiText.DynamicString(
                                "Col. Escalón, San Salvador, " +
                                        "San Salvador, Calle 4, Casa #4"
                            ),
                        ),
                    )
                },
            ),
            onEvent = {},
        )
    }
}

@Preview
@Composable
fun UserAddressesScreenWithMaxPreview() {
    OneAppTheme {
        UserAddressesContent(
            uiState = UserAddressesUiState(
                maxAddressItems = 2,
                addressList = remember {
                    mutableStateListOf(
                        UserAddress(
                            addressId = 1,
                            addressName = UiText.DynamicString("Home"),
                            fullAddress = UiText.DynamicString(ADDRESS),
                        ),
                        UserAddress(
                            addressId = 2,
                            addressName = UiText.DynamicString("Work"),
                            fullAddress = UiText.DynamicString(ADDRESS),
                        ),
                    )
                }
            ),
            onEvent = {},
        )
    }
}
