package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.home

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.CommonDialogWithIconParams
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.openAppSettings
import sv.com.tarjetaone.domain.usecases.automaticpayments.servicespayment.invoiceselection.BalanceClientAutomaticUseCase
import sv.com.tarjetaone.presentation.helpers.UiText
import javax.inject.Inject

@HiltViewModel
class ServicePaymentHomeViewModel @Inject constructor(
    private val balanceClientAutomaticUseCase: BalanceClientAutomaticUseCase
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(ServicePaymentHomeUiState())
    val uiState = _uiState.asStateFlow()

    private val hasPaymentPenalty: Boolean
        get() = baseSharedPrefs.mainCard?.hasPaymentPenalty ?: false

    private fun onStart() {
        _uiState.update {
            it.copy(
                code = baseSharedPrefs.collectorsNpeCode,
                errorVisible = false,
                hasPaymentPenalty = hasPaymentPenalty
            )
        }
    }

    private fun onBack() {
        sendEvent(UiEvent.NavigateBack)
    }

    private fun onTwilio() {
        sendEvent(UiEvent.TwilioClick)
    }

    private fun onCodeChange(code: String) {
        _uiState.update {
            it.copy(
                code = code,
                errorVisible = false
            )
        }
    }

    override fun onCleared() {
        super.onCleared()
        baseSharedPrefs.removeCollectorsNpeCode()
    }

    private fun onTakePicture() {
        sendEvent(
            UiEvent.Navigate(
                ServicePaymentHomeFragmentDirections
                    .actionServicePaymentHomeFragmentToCodeScannerFragment()
            )
        )
    }

    private fun onPayment() {
        if (uiState.value.code.isEmpty()) {
            _uiState.update {
                it.copy(
                    error = UiText.StringResource(R.string.scan_code_payment_error),
                    errorVisible = true
                )
            }
        } else {
            sendEvent(UiEvent.Loading(isLoading = true))
            viewModelScope.launch {
                balanceClientAutomaticUseCase(uiState.value.code).executeUseCase(
                    onApiErrorAction = { _, error, _, _ ->
                        sendEvent(UiEvent.Loading(false))
                        _uiState.update {
                            it.copy(
                                error = UiText.DynamicString(
                                    error?.result.orEmpty()
                                ),
                                errorVisible = true
                            )
                        }
                    }
                ) {
                    sendEvent(UiEvent.Loading(false))
                    sendEvent(
                        UiEvent.Navigate(
                            ServicePaymentHomeFragmentDirections
                                .actionServicePaymentHomeFragmentToInvoiceSelectionFragment(
                                    balanceClient = it,
                                    isFromScanning = true
                                )
                        )
                    )
                }
            }
        }
    }

    private fun onAutomaticPayment() {
        sendEvent(
            UiEvent.Navigate(
                ServicePaymentHomeFragmentDirections
                    .actionServicePaymentHomeFragmentToMyAutomaticPaymentsFragment()
            )
        )
    }

    private fun onPaymentWithoutTicket() {
        sendEvent(
            UiEvent.Navigate(
                ServicePaymentHomeFragmentDirections
                    .actionServicePaymentHomeFragmentToServiceCatalogFragment()
            )
        )
    }

    private fun onCameraPermissionDenied(showRationale: Boolean) {
        sendEvent(
            UiEvent.ShowCommonDialog(
                CommonDialogWithIconParams(
                    title = UiText.StringResource(R.string.camera_access),
                    message = UiText.StringResource(R.string.camera_rationale_scan),
                    buttonText = UiText.StringResource(if (showRationale) R.string.accept_label else R.string.settings),
                    onButtonClick = {
                        if (!showRationale) sendEvent(UiEvent.StartIntent(openAppSettings()))
                    }
                )
            )
        )
    }

    fun onEvent(event: ServicePaymentHomeUiEvent) {
        when (event) {
            ServicePaymentHomeUiEvent.OnBackClick -> onBack()
            ServicePaymentHomeUiEvent.OnTwilioClick -> onTwilio()
            ServicePaymentHomeUiEvent.OnStart -> onStart()
            ServicePaymentHomeUiEvent.OnAutomaticPaymentClick -> onAutomaticPayment()
            is ServicePaymentHomeUiEvent.OnPaymentClick -> onPayment()
            ServicePaymentHomeUiEvent.OnPaymentWithoutTicketClick -> onPaymentWithoutTicket()
            ServicePaymentHomeUiEvent.OnCaptureCodeClick -> onTakePicture()
            is ServicePaymentHomeUiEvent.OnCodeChange -> onCodeChange(event.code)
            is ServicePaymentHomeUiEvent.OnCameraPermissionDenied -> {
                onCameraPermissionDenied(event.showRationale)
            }
        }
    }
}
