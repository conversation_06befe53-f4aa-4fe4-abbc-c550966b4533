package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.home

import android.Manifest
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.rememberPermissionState
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.shouldShowRationale
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.PaymentPenaltyEmptyState
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer8

@Composable
fun ServicePaymentHomeScreen(
    viewModel: ServicePaymentHomeViewModel
) {
    LaunchedEffect(Unit) {
        viewModel.onEvent(ServicePaymentHomeUiEvent.OnStart)
    }
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    ServicePaymentHomeContent(uiState, viewModel::onEvent)
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun ServicePaymentHomeContent(
    uiState: ServicePaymentHomeUiState,
    onEvent: (ServicePaymentHomeUiEvent) -> Unit
) {
    ScreenWithTopAppBar(
        title = stringResource(R.string.scan_code_payment_title).takeIf { !uiState.hasPaymentPenalty },
        onLeftButtonClick = {
            onEvent(ServicePaymentHomeUiEvent.OnBackClick)
        },
        onRightButtonClick = {
            onEvent(ServicePaymentHomeUiEvent.OnTwilioClick)
        }
    ) {
        if (!uiState.hasPaymentPenalty) {
            ServicePaymentHomeBody(
                uiState = uiState,
                onEvent = onEvent
            )
        } else {
            PaymentPenaltyEmptyState { onEvent(ServicePaymentHomeUiEvent.OnBackClick) }
        }
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
private fun ServicePaymentHomeBody(
    uiState: ServicePaymentHomeUiState,
    onEvent: (ServicePaymentHomeUiEvent) -> Unit
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    val cameraPermission = Manifest.permission.CAMERA
    val context = LocalContext.current
    val cameraPermissionState = rememberPermissionState(cameraPermission) { isGranted ->
        if (isGranted) {
            onEvent(ServicePaymentHomeUiEvent.OnCaptureCodeClick)
        } else {
            onEvent(
                ServicePaymentHomeUiEvent
                    .OnCameraPermissionDenied(context.shouldShowRationale(cameraPermission))
            )
        }
    }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(horizontal = MaterialTheme.customDimens.dimen24),
    ) {
        ElevatedCard(
            colors = CardDefaults.elevatedCardColors(
                containerColor = MaterialTheme.customColors.defaultSurface,
                contentColor = MaterialTheme.customColors.onDefaultSurface
            ),
            modifier = Modifier.fillMaxWidth()
        ) {
            Column {
                Image(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(MaterialTheme.customColors.softPrimary),
                    imageVector = ImageVector.vectorResource(id = R.drawable.ic_receipt),
                    contentDescription = null
                )
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(MaterialTheme.customDimens.dimen16),
                ) {
                    Text(
                        style = MaterialTheme.typography.bodyLarge.copy(
                            fontWeight = FontWeight.SemiBold
                        ),
                        text = stringResource(R.string.scan_code_payment_with_ticket)
                    )
                    Text(
                        style = MaterialTheme.typography.bodySmall.copy(
                            color = MaterialTheme.customColors.bodyLightGray,
                            fontWeight = FontWeight.Medium
                        ),
                        text = stringResource(R.string.scan_code_payment_with_ticket_subtitle)
                    )
                    Spacer8()
                    SimpleElevatedTextField(
                        value = uiState.code,
                        onValueChange = {
                            onEvent(ServicePaymentHomeUiEvent.OnCodeChange(it))
                        },
                        trailingIcon = {
                            Icon(
                                imageVector = ImageVector.vectorResource(R.drawable.ic_camera),
                                tint = MaterialTheme.colorScheme.onSecondaryContainer,
                                contentDescription = null,
                                modifier = Modifier.clickable {
                                    cameraPermissionState.launchPermissionRequest()
                                }
                            )
                        },
                        hasError = uiState.errorVisible,
                        error = uiState.error.asString(),
                        placeholder = stringResource(id = R.string.scan_code_payment_with_ticket_hint),
                        keyboardActions = KeyboardActions(
                            onDone = { keyboardController?.hide() }
                        )
                    )
                    Spacer16()
                    OneButton(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(R.string.scan_code_payment_button),
                        enabled = !uiState.errorVisible,
                        onClick = {
                            onEvent(ServicePaymentHomeUiEvent.OnPaymentClick)
                            keyboardController?.hide()
                        }
                    )
                }
            }
        }
        Spacer16()
        PaymentTypeCard(
            title = stringResource(R.string.scan_code_payment_without_ticket),
            onClick = { onEvent(ServicePaymentHomeUiEvent.OnPaymentWithoutTicketClick) }
        )
        Spacer16()
        PaymentTypeCard(
            title = stringResource(R.string.scan_code_payment_automatic),
            onClick = { onEvent(ServicePaymentHomeUiEvent.OnAutomaticPaymentClick) }
        )
    }
}

@Composable
private fun PaymentTypeCard(
    title: String,
    onClick: () -> Unit
) {
    ElevatedCard(
        modifier = Modifier.fillMaxWidth(),
        onClick = onClick,
        colors = CardDefaults.elevatedCardColors(
            containerColor = MaterialTheme.customColors.defaultSurface,
            contentColor = MaterialTheme.customColors.onDefaultSurface
        )
    ) {
        Row(
            modifier = Modifier.padding(MaterialTheme.customDimens.dimen16),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                style = MaterialTheme.typography.headlineSmall.copy(
                    fontSize = MaterialTheme.customDimensSp.sp18
                ),
                text = title
            )
            Spacer1f()
            Spacer8()
            Icon(
                imageVector = ImageVector.vectorResource(R.drawable.ic_chevron_right),
                contentDescription = null
            )
        }
    }
}

@Preview
@Composable
fun ServicePaymentHomePreview() {
    OneAppTheme {
        ServicePaymentHomeContent(
            uiState = ServicePaymentHomeUiState(),
            onEvent = {}
        )
    }
}
