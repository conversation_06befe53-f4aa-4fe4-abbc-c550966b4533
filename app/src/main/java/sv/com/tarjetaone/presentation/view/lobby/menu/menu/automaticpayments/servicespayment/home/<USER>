package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.home

import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.presentation.helpers.UiText

data class ServicePaymentHomeUiState(
    val code: String = EMPTY_STRING,
    val error: UiText = UiText.DynamicString(EMPTY_STRING),
    val errorVisible: <PERSON>olean = false,
    val hasPaymentPenalty: Boolean = false,
)
