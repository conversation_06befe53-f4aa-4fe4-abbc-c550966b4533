package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.home

sealed class ServicePaymentHomeUiEvent {
    data object OnBackClick : ServicePaymentHomeUiEvent()
    data object OnTwilioClick : ServicePaymentHomeUiEvent()
    data object OnStart : ServicePaymentHomeUiEvent()
    data object OnPaymentClick : ServicePaymentHomeUiEvent()
    data object OnPaymentWithoutTicketClick : ServicePaymentHomeUiEvent()
    data object OnAutomaticPaymentClick : ServicePaymentHomeUiEvent()
    data class OnCodeChange(val code: String) : ServicePaymentHomeUiEvent()
    data object OnCaptureCodeClick : ServicePaymentHomeUiEvent()
    data class OnCameraPermissionDenied(
        val showRationale: Boolean
    ) : ServicePaymentHomeUiEvent()
}
