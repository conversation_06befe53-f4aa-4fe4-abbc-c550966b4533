package sv.com.tarjetaone.presentation.view.common.sign

import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.bitmap.BitmapUtils

abstract class SignContractViewModel(
    private val bitmapUtils: BitmapUtils,
    private val imageUtils: ImageUtils
) : BaseViewModel() {
    protected val _uiState = MutableStateFlow(SignContractUiState())
    val uiState: StateFlow<SignContractUiState> = _uiState.asStateFlow()

    abstract fun onSignClickAction(signFileName: String)

    abstract fun getCustomerName(): String

    protected open fun onStart() {
        _uiState.update { state ->
            state.copy(customerName = getCustomerName())
        }
    }

    private fun onSignClick() {
        viewModelScope.launch {
            bitmapUtils.generateSignatureImage(getCustomerName())?.let { customerSign ->
                imageUtils.storeTempImageAsync(customerSign)?.let {
                    onSignClickAction(it)
                }
            }
        }
    }

    protected open fun onSignSelected() {
        _uiState.update { it.copy(isSignSelected = true) }
    }

    fun onEvent(screenUiEvent: SignContractUiEvent) {
        when (screenUiEvent) {
            is SignContractUiEvent.OnSignSelected -> onSignSelected()
            is SignContractUiEvent.OnSignClick -> onSignClick()
            SignContractUiEvent.OnStart -> onStart()
            SignContractUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            SignContractUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
        }
    }
}