package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.mypayments

import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.contractslist.RecurringChargeContractUI

sealed class MyAutomaticPaymentsUiEvent {
    data object OnStart : MyAutomaticPaymentsUiEvent()
    data object OnBackClick : MyAutomaticPaymentsUiEvent()
    data object OnTwilioClick : MyAutomaticPaymentsUiEvent()
    data class OnEditRecurringCharge(val contract: RecurringChargeContractUI) : MyAutomaticPaymentsUiEvent()
    data class OnViewPaymentHistory(val contract: RecurringChargeContractUI) : MyAutomaticPaymentsUiEvent()
    data class OnViewContractClick(val contract: RecurringChargeContractUI) : MyAutomaticPaymentsUiEvent()
    data object OnDismissContractDetails : MyAutomaticPaymentsUiEvent()
    data class OnCancelContractClick(val contract: RecurringChargeContractUI) : MyAutomaticPaymentsUiEvent()
    data object OnAcceptCancelContractClick : MyAutomaticPaymentsUiEvent()
    data object OnDismissCancelContractClick : MyAutomaticPaymentsUiEvent()
    data object OnAddContractClick : MyAutomaticPaymentsUiEvent()
}
