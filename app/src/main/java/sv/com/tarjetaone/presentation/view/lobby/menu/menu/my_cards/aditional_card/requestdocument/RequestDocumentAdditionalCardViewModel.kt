package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.requestdocument

import com.facephi.fphiselphidwidgetcore.WidgetSelphIDResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.core.utils.openAppSettings
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.request.UserAdditionalCard
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.DialogAction
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.MessageParams
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.OneDialogParams
import sv.com.tarjetaone.presentation.helpers.UiText
import javax.inject.Inject

@HiltViewModel
class RequestDocumentAdditionalCardViewModel @Inject constructor(
    private val facephiResultHandler: FacephiResultHandler,
    private val sharedPrefsRepo: SecureSharedPreferencesRepository
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(RequestDocumentAdditionalCardPreviewUiState())
    val uiState = _uiState.asStateFlow()

    private fun onConfirm(requestPermission: () -> Unit) {
        showOneDialog(
            OneDialogParams(
                icon = R.drawable.ic_aditional_card_dialog,
                title = UiText.StringResource(R.string.additional_card_dialog_title),
                message = MessageParams(
                    text = UiText.StringResource(R.string.additional_card_dialog_message),
                    size = MessageParams.MessageSize.Medium
                ),
                primaryAction = DialogAction(
                    text = UiText.StringResource(R.string.continue_button_label),
                    actionType = DialogAction.ActionType.SUCCESS,
                    onClick = requestPermission
                ),
                isDismissible = false
            )
        )
    }

    private fun onDismiss() {
        _uiState.update { state -> state.copy(isDialogVisible = false) }
    }

    private fun onOptionSelected(option: AnswerOption) {
        val isNoOptionSelected = option == AnswerOption.NO
        _uiState.update { state ->
            state.copy(
                selectedOption = option,
                isContinueButtonEnabled = !isNoOptionSelected,
                isDialogVisible = isNoOptionSelected
            )
        }
    }

    private fun onDocumentCaptured(result: WidgetSelphIDResult) {
        facephiResultHandler.onDocumentCaptured(result)?.let {
            setUpAdditionalCardData()
            sendEvent(
                UiEvent.Navigate(
                    RequestDocumentAdditionalCardFragmentDirections
                        .actionRequestDocumentAdditionalCardFragmentToAdditionalCardDocumentPreviewFragment(
                            isAdditionalCard = true,
                            shouldSkipAdditionalInformation = false
                        )
                )
            )
        } ?: showOneDialog(
            OneDialogParams(
                title = UiText.StringResource(R.string.error_doc_picture_title),
                message = MessageParams(UiText.StringResource(R.string.error_doc_picture_desc)),
                primaryAction = DialogAction(
                    text = UiText.StringResource(R.string.take_picture),
                    actionType = DialogAction.ActionType.WARNING
                ),
                isDismissible = false
            )
        )
    }

    private fun onCameraPermissionDenied(shouldShowRationale: Boolean) {
        showOneDialog(
            OneDialogParams(
                icon = R.drawable.circle_error_icon,
                title = UiText.StringResource(R.string.camera_access),
                message = MessageParams(UiText.StringResource(R.string.permission_camera)),
                primaryAction = DialogAction(
                    text = UiText.StringResource(
                        if (shouldShowRationale) R.string.accept_label else R.string.settings
                    ),
                    actionType = DialogAction.ActionType.ERROR,
                    onClick = {
                        if (!shouldShowRationale) {
                            sendEvent(SideEffect.StartIntent(openAppSettings()))
                        }
                    }
                )
            )
        )
    }

    private fun onBack(prevDestId: Int?) {
        when (prevDestId) {
            R.id.identityValidationACFragment -> sendEvent(
                UiEvent.Navigate(RequestDocumentAdditionalCardFragmentDirections.navigateToMenuCardFragment())
            )

            else -> prevDestId?.let { sendEvent(UiEvent.NavigateBackTo(R.id.navigation_menu_my_cards)) }
        }
    }

    private fun setUpAdditionalCardData() {
        sharedPrefsRepo.userAdditionalCardData = UserAdditionalCard(
            cCardId = sharedPrefsRepo.mainCard?.creditCardId.orZero()
        )
    }

    fun onEvent(event: RequestDocumentAdditionalCardUiEvent) {
        when (event) {
            is RequestDocumentAdditionalCardUiEvent.OnBack -> onBack(event.prevDestId)
            RequestDocumentAdditionalCardUiEvent.OnDismiss -> onDismiss()
            is RequestDocumentAdditionalCardUiEvent.OnOptionSelected -> onOptionSelected(event.option)
            is RequestDocumentAdditionalCardUiEvent.OnDocumentCaptured -> onDocumentCaptured(event.result)
            is RequestDocumentAdditionalCardUiEvent.OnCameraPermissionDenied -> onCameraPermissionDenied(
                event.shouldShowRationale
            )

            is RequestDocumentAdditionalCardUiEvent.OnConfirm -> onConfirm(event.requestPermission)
            RequestDocumentAdditionalCardUiEvent.OnTwilio -> sendEvent(UiEvent.TwilioClick)
        }
    }
}