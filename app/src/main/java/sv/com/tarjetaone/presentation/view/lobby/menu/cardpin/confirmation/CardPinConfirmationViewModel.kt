package sv.com.tarjetaone.presentation.view.lobby.menu.cardpin.confirmation

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.R
import sv.com.tarjetaone.analytics.dynatrace.DynatraceEvent
import sv.com.tarjetaone.analytics.dynatrace.DynatraceManager
import sv.com.tarjetaone.common.utils.AppConstants.FOUR_VALUE
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.presentation.view.common.cardpin.CardPinBaseViewModel
import javax.inject.Inject

@HiltViewModel
class CardPinConfirmationViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val dynatraceManager: DynatraceManager
) : CardPinBaseViewModel() {
    private val args = CardPinConfirmationFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onStart() {
        super.onStart()
        _uiState.update {
            it.copy(
                continueButtonLabel = R.string.save_new_pin,
                descriptionLabel = R.string.enter_your_new_pin_again
            )
        }
    }

    override fun onPinChange(pin: String) {
        super.onPinChange(pin)
        _uiState.update { it.copy(hasError = pin.length == FOUR_VALUE && pin != args.pin) }
    }

    override fun onContinueClick() {
        dynatraceManager.sendInAppEvent(DynatraceEvent.ChangePin.SavePin)
        sendEvent(
            UiEvent.Navigate(
                CardPinConfirmationFragmentDirections
                    .actionCardPinConfirmationFragmentToCardPinCaptureSelfieFragment(pin = args.pin)
            )
        )
    }
}
