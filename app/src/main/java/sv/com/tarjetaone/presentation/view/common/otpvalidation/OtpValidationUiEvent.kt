package sv.com.tarjetaone.presentation.view.common.otpvalidation

import sv.com.tarjetaone.domain.entities.response.ContactType
import sv.com.tarjetaone.domain.entities.response.OtpMethod

sealed class OtpValidationUiEvent {
    data object OnBackClick : OtpValidationUiEvent()
    data object OnTwilioClick : OtpValidationUiEvent()
    data class OnStart(
        val contactType: ContactType,
        val contact: String,
        val sharedKey: String
    ) : OtpValidationUiEvent()
    data class OnCodeChange(val code: String) : OtpValidationUiEvent()
    data class OnValidateOtp(val code: String) : OtpValidationUiEvent()
    data object OnResendOtpClick : OtpValidationUiEvent()
    data class OnSelectOtpMethod(val method: OtpMethod) : OtpValidationUiEvent()
    data object OnDismissSelectOtpMethod : OtpValidationUiEvent()
    data object OnHideSnackBar : OtpValidationUiEvent()
}
