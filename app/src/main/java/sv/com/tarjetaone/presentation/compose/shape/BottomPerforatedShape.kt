package sv.com.tarjetaone.presentation.compose.shape

import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Outline
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import sv.com.tarjetaone.common.utils.AppConstants.VALUE_180_F

/**
 * A [Shape] that draws a series of perforations along the bottom edge of the container, so that it
 * looks like a torn page of a notepad.
 */
class BottomPerforatedShape(
    private val perforationRadius: Dp,
    private val perforationSpacing: Dp = perforationRadius
) : Shape {
    override fun createOutline(
        size: Size,
        layoutDirection: LayoutDirection,
        density: Density
    ): Outline {
        val cutoutRadius = with(density) { perforationRadius.toPx() }
        val cutoutDiameter = cutoutRadius * 2
        val spacing = with(density) { perforationSpacing.toPx() }
        val cutouts = (size.width / (cutoutDiameter + spacing)).toInt()
        val cutoutsWidth = cutouts * cutoutDiameter + (cutouts - 1) * spacing
        val hPadding = (size.width - cutoutsWidth) / 2

        val page = Path().apply {
            addRect(
                rect = Rect(
                    left = 0f,
                    top = 0f,
                    right = size.width,
                    bottom = size.height
                )
            )
        }
        val perforations = Path().apply {
            repeat(cutouts) {
                val hOffset = it * (cutoutDiameter + spacing)
                arcTo(
                    rect = Rect(
                        left = hOffset + hPadding,
                        top = size.height - cutoutRadius,
                        right = hOffset + cutoutDiameter + hPadding,
                        bottom = size.height + cutoutRadius
                    ),
                    startAngleDegrees = VALUE_180_F,
                    sweepAngleDegrees = VALUE_180_F,
                    forceMoveTo = false
                )
            }
            close()
        }
        return Outline.Generic(page - perforations)
    }
}
