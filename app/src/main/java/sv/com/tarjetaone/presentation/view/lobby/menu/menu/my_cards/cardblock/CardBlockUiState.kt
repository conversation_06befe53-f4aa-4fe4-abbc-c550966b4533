package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.cardblock

import androidx.annotation.StringRes
import androidx.compose.runtime.Stable
import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI
import sv.com.tarjetaone.domain.entities.response.CustomerCCardUI

@Stable
data class CardBlockUiState(
    @StringRes
    val screenTitle: Int? = null,
    val blockReasonList: List<CatalogItemsCollectionUI> = emptyList(),
    val cardList: List<CustomerCCardUI> = emptyList(),
    val selectedCard: CustomerCCardUI? = null,
    val canBlockCards: Boolean = false,
) {
    val hasLoadedData = blockReasonList.isNotEmpty() && cardList.isNotEmpty()
}