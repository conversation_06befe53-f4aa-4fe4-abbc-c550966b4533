package sv.com.tarjetaone.presentation.view.common.tutorial

import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.helpers.UiText

sealed class TutorialType {
    data object Document: TutorialType()
    data class Selfie(val type: SelfieTutorialType): TutorialType()
}

enum class SelfieTutorialType {
    DEFAULT,
    QUALITY,
    FACE
}

val TutorialType.items: List<TutorialItem>
    get() = when (this) {
        TutorialType.Document -> listOf(
            TutorialItem(
                title = UiText.StringResource(R.string.document_tutorial_center_position_title),
                incorrectImageTip = R.drawable.document_tutorial_center_position,
                tips = listOf(
                    UiText.StringResource(R.string.document_tutorial_place_inside_tip),
                    UiText.StringResource(R.string.document_tutorial_avoid_cut_edges_tip),
                    UiText.StringResource(R.string.document_tutorial_straight_centered_tip),
                )
            ),
            TutorialItem(
                title = UiText.StringResource(R.string.document_tutorial_avoid_reflections_title),
                incorrectImageTip = R.drawable.document_tutorial_good_lightning,
                tips = listOf(
                    UiText.StringResource(R.string.document_tutorial_avoid_direct_light_tip),
                    UiText.StringResource(R.string.document_tutorial_avoid_glares_tip),
                    UiText.StringResource(R.string.document_tutorial_good_lighting_tip),
                )
            ),
            TutorialItem(
                title = UiText.StringResource(R.string.document_tutorial_info_readable_title),
                incorrectImageTip = R.drawable.document_tutorial_content_legible,
                tips = listOf(
                    UiText.StringResource(R.string.document_tutorial_avoid_blurry_tip),
                    UiText.StringResource(R.string.document_tutorial_no_cover_tip),
                    UiText.StringResource(R.string.document_tutorial_well_focused_tip),
                )
            )
        )

        is TutorialType.Selfie -> type.selfieTutorialItems
    }

val SelfieTutorialType.selfieTutorialItems: List<TutorialItem>
    get() = when (this) {
        SelfieTutorialType.DEFAULT -> listOf(
            TutorialItem(
                title = UiText.StringResource(R.string.selfie_tutorial_camera_position_title),
                incorrectImageTip = R.drawable.selfie_tutorial_low_angle,
                tips = listOf(
                    UiText.StringResource(R.string.selfie_tutorial_avoid_low_angle_tip),
                    UiText.StringResource(R.string.selfie_tutorial_camera_alignment_tip),
                    UiText.StringResource(R.string.selfie_tutorial_look_at_camera_tip),
                )
            ),
            TutorialItem(
                title = UiText.StringResource(R.string.selfie_tutorial_center_circle_title),
                incorrectImageTip = R.drawable.selfie_tutorial_center_circle,
                tips = listOf(
                    UiText.StringResource(R.string.selfie_tutorial_center_circle_tip),
                    UiText.StringResource(R.string.selfie_tutorial_solid_background_tip),
                    UiText.StringResource(R.string.selfie_tutorial_good_lighting_tip),
                )
            ),
            TutorialItem(
                title = UiText.StringResource(R.string.selfie_tutorial_remove_accessories_title),
                incorrectImageTip = R.drawable.selfie_tutorial_remove_accessories,
                tips = listOf(
                    UiText.StringResource(R.string.selfie_tutorial_avoid_headwear_tip),
                    UiText.StringResource(R.string.selfie_tutorial_no_other_people_tip),
                )
            ),
        )

        SelfieTutorialType.QUALITY -> listOf(
            TutorialItem(
                title = UiText.StringResource(R.string.selfie_tutorial_avoid_face_shadows_title),
                incorrectImageTip = R.drawable.selfie_tutorial_face_shadows,
                tips = listOf(
                    UiText.StringResource(R.string.selfie_tutorial_lighting_tip),
                    UiText.StringResource(R.string.selfie_tutorial_avoid_reflections_tip),
                    UiText.StringResource(R.string.selfie_tutorial_avoid_face_shadows_tip),
                )
            ),
            TutorialItem(
                title = UiText.StringResource(R.string.selfie_tutorial_avoid_bright_background_title),
                incorrectImageTip = R.drawable.selfie_tutorial_bright_background,
                tips = listOf(
                    UiText.StringResource(R.string.selfie_tutorial_backlight_tip),
                    UiText.StringResource(R.string.selfie_tutorial_face_light_tip),
                    UiText.StringResource(R.string.selfie_tutorial_solid_background_tip),
                )
            ),
            TutorialItem(
                title = UiText.StringResource(R.string.selfie_tutorial_avoid_dark_places_title),
                incorrectImageTip = R.drawable.selfie_tutorial_dark_places,
                tips = listOf(
                    UiText.StringResource(R.string.selfie_tutorial_find_good_lighting_tip),
                )
            ),
        )

        SelfieTutorialType.FACE -> listOf(
            TutorialItem(
                title = UiText.StringResource(R.string.selfie_tutorial_avoid_uncentered_angle_title),
                incorrectImageTip = R.drawable.selfie_tutorial_uncentered_angle,
                tips = listOf(
                    UiText.StringResource(R.string.selfie_tutorial_avoid_wrong_angles_tip),
                    UiText.StringResource(R.string.selfie_tutorial_center_camera_tip),
                    UiText.StringResource(R.string.selfie_tutorial_look_at_camera_tip),
                )
            ),
            TutorialItem(
                title = UiText.StringResource(R.string.selfie_tutorial_avoid_movement_title),
                incorrectImageTip = R.drawable.selfie_tutorial_avoid_movement,
                tips = listOf(
                    UiText.StringResource(R.string.selfie_tutorial_keep_face_in_circle_tip),
                    UiText.StringResource(R.string.selfie_tutorial_keep_eyes_open_tip),
                )
            ),
            TutorialItem(
                title = UiText.StringResource(R.string.selfie_tutorial_avoid_face_obstructions_title),
                incorrectImageTip = R.drawable.selfie_tutorial_remove_accessories,
                tips = listOf(
                    UiText.StringResource(R.string.selfie_tutorial_avoid_headwear_tip),
                    UiText.StringResource(R.string.selfie_tutorial_no_other_people_tip),
                )
            ),
        )
    }
