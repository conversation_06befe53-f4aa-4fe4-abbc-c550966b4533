package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.cardpay.paymentselection.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.extensions.configCurrencyWithFractions
import sv.com.tarjetaone.domain.entities.response.CardPaymentAccountsDebitDataUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.card.BaesSelectableCard
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedDropdown
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.compose.util.FieldDecorationType
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer4
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.cardpay.paymentselection.PaymentType

@Suppress("kotlin:S107")
@Composable
fun PaymentMethodForm(
    modifier: Modifier = Modifier,
    paymentAmount: Double,
    paymentType: PaymentType?,
    accountTypes: List<String>,
    selectedAccountType: String?,
    onAccountTypeChange: (String) -> Unit,
    accounts: List<CardPaymentAccountsDebitDataUI>,
    selectedAccount: CardPaymentAccountsDebitDataUI?,
    onAccountChange: (CardPaymentAccountsDebitDataUI) -> Unit,
    paymentDesc: String,
    onPaymentDescChange: (String) -> Unit
) {
    Column(
        modifier = modifier.verticalScroll(rememberScrollState())
    ) {
        BaesSelectableCard(
            isSelected = true,
            onClick = { }
        ) {
            Row {
                val paymentText = when (paymentType) {
                    PaymentType.FULL -> R.string.full_payment
                    PaymentType.MIN -> R.string.minimum_payment
                    else -> R.string.other_amount
                }
                Text(
                    text = stringResource(id = paymentText),
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.customColors.onSuccess
                )
                Spacer4()
                Text(
                    text = paymentAmount.configCurrencyWithFractions(),
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.customColors.onSuccess
                )
            }
        }
        Spacer8()
        Text(
            text = stringResource(id = R.string.select_payment_method_label),
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.SemiBold,
            color = MaterialTheme.colorScheme.secondary,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth()
        )
        Spacer16()
        SimpleElevatedDropdown(
            items = accountTypes,
            itemLabel = { it },
            value = selectedAccountType,
            onValueChange = onAccountTypeChange,
            label = stringResource(id = R.string.payment_method_label),
            decorationType = FieldDecorationType.OUTLINED
        )
        Spacer16()
        SimpleElevatedDropdown(
            items = accounts,
            itemLabel = { it.accountNumber.orEmpty() },
            value = selectedAccount,
            onValueChange = onAccountChange,
            label = stringResource(id = R.string.account_number_label),
            decorationType = FieldDecorationType.OUTLINED,
        )
        Spacer16()
        SimpleElevatedTextField(
            value = selectedAccount?.available ?: stringResource(id = R.string.hint_zero_dollars),
            onValueChange = {},
            label = stringResource(id = R.string.available_amount_label),
            decorationType = FieldDecorationType.OUTLINED,
            readOnly = true
        )
        Spacer16()
        SimpleElevatedTextField(
            value = paymentDesc,
            onValueChange = onPaymentDescChange,
            label = stringResource(id = R.string.description_label),
            decorationType = FieldDecorationType.OUTLINED
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun PaymentMethodFormPreview() {
    OneAppTheme {
        PaymentMethodForm(
            modifier = Modifier.padding(MaterialTheme.customDimens.dimen16),
            paymentAmount = 150.0,
            paymentType = PaymentType.FULL,
            accountTypes = emptyList(),
            selectedAccountType = null,
            accounts = emptyList(),
            selectedAccount = null,
            paymentDesc = "",
            onAccountTypeChange = {},
            onAccountChange = {},
            onPaymentDescChange = {}
        )
    }
}
