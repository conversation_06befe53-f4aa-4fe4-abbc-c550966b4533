package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.common.paymentdetail

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.Spacer12
import sv.com.tarjetaone.presentation.compose.util.Spacer4

/**
 * Component that displays the header of the payment service detail screen.
 */
@Composable
fun PaymentServiceDetailHeader(
    modifier: Modifier = Modifier,
    referenceNumber: String,
) {

    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(id = R.string.details_process),
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.onSurface
        )
        Spacer4()
        Text(
            text = stringResource(id = R.string.process_number_label, referenceNumber),
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface
        )
        Text(
            text = stringResource(id = R.string.status),
            modifier = modifier.padding(top = MaterialTheme.customDimens.dimen16),
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.customColors.gray5
        )
        Text(
            text = stringResource(id = R.string.payment_method_favorable_label),
            style = MaterialTheme.typography.displayMedium,
            color = MaterialTheme.customColors.successContainer,
        )
        Spacer12()
    }
}
