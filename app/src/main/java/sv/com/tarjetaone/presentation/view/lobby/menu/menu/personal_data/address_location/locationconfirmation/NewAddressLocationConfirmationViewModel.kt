package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.address_location.locationconfirmation

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.presentation.view.common.locationconfirmation.LocationConfirmationBaseViewModel
import javax.inject.Inject

@HiltViewModel
class NewAddressLocationConfirmationViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle
) : LocationConfirmationBaseViewModel() {
    private val args =
        NewAddressLocationConfirmationFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onStart() {
        super.onStart()
        _uiState.update {
            it.copy(
                userLocation = args.userLocation,
                showProgress = false,
                isModifyingAddress = true
            )
        }
    }

    override fun onContinueClick() {
        sendEvent(
            UiEvent.Navigate(
                NewAddressLocationConfirmationFragmentDirections
                    .actionNewAddressLocationConfirmationFragmentToPrepareForPicturePersonalDataAddressFragment(
                        userLocation = args.userLocation,
                        action = args.action
                    )
            )
        )
    }
}
