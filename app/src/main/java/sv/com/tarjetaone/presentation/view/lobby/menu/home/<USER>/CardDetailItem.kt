package sv.com.tarjetaone.presentation.view.lobby.menu.home.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Divider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.Spacer24

@Composable
fun CardDetailItem(
    modifier: Modifier = Modifier,
    label: String,
    value: String,
    valueColor: Color = MaterialTheme.colorScheme.primary,
    valueStyle: TextStyle = MaterialTheme.typography.bodySmall.copy(
        color = valueColor,
        fontWeight = FontWeight.SemiBold
    ),
    showDivider: Boolean = true
) {
    Column(
        modifier = modifier
    ) {
        if (showDivider) {
            Divider(
                color = MaterialTheme.customColors.gray400,
                thickness = MaterialTheme.customDimens.dimen1,
            )
        }
        Row(
            modifier = Modifier
                .heightIn(MaterialTheme.customDimens.dimen56)
                .padding(horizontal = MaterialTheme.customDimens.dimen16),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.labelMedium,
                color = MaterialTheme.customColors.onDefaultSurface,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.weight(1f)
            )
            Spacer24()
            Text(
                text = value,
                style = valueStyle
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun CardDetailItemPreview() {
    OneAppTheme {
        CardDetailItem(
            label = "Saldo actual",
            value = "$100.00",
            modifier = Modifier.padding(MaterialTheme.customDimens.dimen16)
        )
    }
}
