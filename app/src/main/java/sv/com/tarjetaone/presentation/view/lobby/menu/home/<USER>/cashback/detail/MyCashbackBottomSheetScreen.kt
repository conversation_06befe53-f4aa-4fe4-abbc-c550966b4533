package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.cashback.detail

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.cashback.description.CashbackDescriptionScreen

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MyCashbackBottomSheetContent(
    onDismissRequest: () -> Unit = {},
    onButtonAction: () -> Unit,
) {
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    ModalBottomSheet(
        modifier = Modifier
            .background(Color.Transparent)
            .safeDrawingPadding(),
        onDismissRequest = { onDismissRequest() },
        containerColor = MaterialTheme.colorScheme.surface,
        sheetState = sheetState
    ) {
        Image(
            imageVector = ImageVector.vectorResource(id = R.drawable.ic_cashback_header),
            contentDescription = null,
            contentScale = ContentScale.FillWidth,
            modifier = Modifier
                .fillMaxWidth()
                .clip(
                    RoundedCornerShape(MaterialTheme.customDimens.dimen16)
                )
        )
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(MaterialTheme.customColors.textSurfaceVariant)
                .padding(
                    horizontal = MaterialTheme.customDimens.dimen16
                )
        ) {
            Spacer16()
            Text(
                text = stringResource(R.string.cashback_bottom_sheet_title),
                style = MaterialTheme.typography.displayMedium.copy(
                    color = MaterialTheme.colorScheme.secondary
                )
            )
            Spacer16()
            Text(
                text = stringResource(R.string.cashback_bottom_sheet_subtitle),
                style = MaterialTheme.typography.bodySmall.copy(
                    color = MaterialTheme.colorScheme.secondary
                )
            )
            Spacer24()
            CashbackDescriptionScreen(
                buttonTitle = R.string.cashback_bottom_sheet_button,
                buttonVariant = ButtonVariant.SECONDARY_VARIANT,
                buttonAction = {
                    onButtonAction()
                }
            )
        }
    }
}

@Composable
@Preview
fun MyCashbackBottomSheetContentPreview() {
    OneAppTheme {
        MyCashbackBottomSheetContent(
            onButtonAction = {}
        )
    }
}