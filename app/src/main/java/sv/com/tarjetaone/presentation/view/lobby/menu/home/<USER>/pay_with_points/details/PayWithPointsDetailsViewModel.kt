package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.pay_with_points.details

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.presentation.view.common.management.ManagementDetailBaseViewModel
import javax.inject.Inject

@HiltViewModel
class PayWithPointsDetailsViewModel @Inject constructor(
    imageUtils: ImageUtils,
    savedStateHandle: SavedStateHandle
) : ManagementDetailBaseViewModel(imageUtils) {
    private val args = PayWithPointsDetailsFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onStart() {
        args.response.management?.let {
            _uiState.update { state ->
                state.copy(
                    managementNumber = it.manRequestId.toString(),
                    managementStatus = it.mrStatusNameApp.orEmpty(),
                    managementStatusColor = it.mrStatusTextColor.orEmpty(),
                    managementName = it.manTypeNameApp.orEmpty(),
                    customerName = it.clientName.orEmpty(),
                    creditCardNumber = it.cardNumMasked.orEmpty(),
                    creditCardType = it.typeCardText.orEmpty(),
                    requestStartDate = it.getFormattedDateInit(),
                    requestEndDate = it.getFormattedDateEnd(),
                    description = it.description,
                    resolutionDays = it.availableDay
                )
            }
        }
    }

    override fun onPrimaryButtonClick() {
        sendEvent(
            UiEvent.Navigate(
                PayWithPointsDetailsFragmentDirections.actionHome()
            )
        )
    }
}