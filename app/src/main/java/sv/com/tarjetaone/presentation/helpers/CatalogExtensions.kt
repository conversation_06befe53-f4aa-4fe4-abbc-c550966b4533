package sv.com.tarjetaone.presentation.helpers

import androidx.annotation.DrawableRes
import com.google.gson.Gson
import sv.com.tarjetaone.R
import sv.com.tarjetaone.data.api.models.CatalogIcon
import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI

fun CatalogItemsCollectionUI.catalogIcon(gson: Gson): CatalogIcon? {
    return gson.fromJson(behaviourInfo, CatalogIcon::class.java)
}

/**
 * Maps a string value provided by the backend to a drawable resource
 */
@DrawableRes
fun drawableResFromString(icon: String): Int {
    return when (icon) {
        "credit-card-2-back-red" -> R.drawable.ic_card_menu_icon
        "file-post-red" -> R.drawable.ic_file_post_claim
        "shield-check-red" -> R.drawable.ic_shield_check_claim
        "journal-plus-red" -> R.drawable.ic_journal_plus_claim
        "phone-red" -> R.drawable.ic_phone_claim
        "globe-red" -> R.drawable.ic_globe
        "exclamation-octagon-red" -> R.drawable.ic_exclamation_octagon
        "file-spreadsheet-red" -> R.drawable.ic_file_spreadsheet_claim
        "truck-red" -> R.drawable.ic_truck_claim
        "tarjeta-visa" -> R.drawable.ic_icon_alert
        "exclamation-triangle" -> R.drawable.ic_activation_alert
        "shield-frame" -> R.drawable.ic_shield_alert
        else -> R.drawable.ic_shield_check_claim
    }
}
