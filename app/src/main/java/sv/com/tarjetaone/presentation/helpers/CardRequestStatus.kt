package sv.com.tarjetaone.presentation.helpers

import androidx.navigation.AnimBuilder
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.response.DataCurrentProcessUI

// Important! Some values may appear to not be used, but are required for the method [getNextStep]
enum class CardRequestNextStep(val nextStep: String?, val nextStepDirection: Int) {
    INVALID(null, -1),
    NEW_REQUEST(null, 0),
    TEMP_BLOCK(null, -1),
    PERM_BLOCK(null, -1),
    EXISTING_PRODUCT(null, -1),
    // Values returned by BE
    DEMOGRAPHICS_CAPTURE("NS_CAPTURA_DEMOGRAFICOS", R.id.demographicData),
    INCOMES_CAPTURE("NS_CAPTURA_INGRESOS", R.id.incomesFromFragment),
    REFERENCES_CAPTURE("NS_CAPTURA_REFERENCIAS", R.id.referencesScreenFragment),
    AFP_VALIDATION("NS_AFP_VALIDACION", R.id.afpquery),
    AFP_VERIFICATION("NS_AFP_DOC_FIRMADOS", R.id.afpVerification),
    AFP_COMPLETED("NS_AFP_COMPLETADO", R.id.afpquerySmile),
    PEP_CAPTURE("NS_CAPTURA_PEP", R.id.pEPQuestionFragment),
    PENDING_OFFER("NS_PENDING", R.id.finalCardOfferPendingFragment),
    VALIDATE_REQUEST("NS_RESPUESTA_VALIDAR_DUI", R.id.almostThereFinalCard),
    REQUEST_APPROVED("NS_FORMALIZACION_ACEPTAR", R.id.creditCardOfferFragment),
    DELIVERY_ADDRESS("NS_FORMALIZACION_DIRECCION", R.id.finalCardOfferAcceptFragment),
    CONTRACTS_SIGNING("NS_FORMALIZACION_CONTRATOS", R.id.contractUnsignFragment2),
    CARD_PERSONALIZATION("NS_FORMALIZACION_EMBOZADO", R.id.addressSelectionFragment)
}

private const val PERMANENT_BLOCK_DATE = "1999-12-01"
private const val REQUEST_COMPLETE_STATUS = "CCAS_APP_FORM_COMPLETE"
private const val NEW_REQUEST_CC_APPLICATION_ID = 0

fun DataCurrentProcessUI.isPermanentlyBlocked(): Boolean = canAppliedUntil == PERMANENT_BLOCK_DATE

fun DataCurrentProcessUI.hasProduct(): Boolean = ccaStatusCode == REQUEST_COMPLETE_STATUS

fun DataCurrentProcessUI.getNextStep(): CardRequestNextStep = when {
    canApplied == false -> {
        if (isPermanentlyBlocked()) CardRequestNextStep.PERM_BLOCK
        else CardRequestNextStep.TEMP_BLOCK
    }
    hasProduct() -> CardRequestNextStep.EXISTING_PRODUCT
    ccApplicationId == NEW_REQUEST_CC_APPLICATION_ID -> CardRequestNextStep.NEW_REQUEST
    else -> ccaNextStep?.let { nextStep ->
        CardRequestNextStep.entries.find { it.nextStep == nextStep }
    } ?: CardRequestNextStep.INVALID
}

fun getAnimationOptionsByStep(nextStepDirection: Int): AnimBuilder = when (nextStepDirection) {
    CardRequestNextStep.VALIDATE_REQUEST.nextStepDirection,
    CardRequestNextStep.REQUEST_APPROVED.nextStepDirection -> AnimBuilder().apply {
        enter = R.anim.fade_in
        exit = R.anim.fade_out
        popEnter = R.anim.fade_in
        popExit = R.anim.fade_out
    }
    else -> AnimBuilder().apply {
        enter = R.anim.slide_in_right
        exit = R.anim.slide_out_left
        popEnter = R.anim.slide_in_left
        popExit = R.anim.slide_out_right
    }
}
