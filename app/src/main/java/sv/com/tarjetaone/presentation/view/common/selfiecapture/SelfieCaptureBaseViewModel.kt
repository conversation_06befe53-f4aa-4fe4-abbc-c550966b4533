package sv.com.tarjetaone.presentation.view.common.selfiecapture

import android.graphics.Bitmap
import com.facephi.fphiwidgetcore.WidgetResult
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.CommonDialogWithIconParams
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.core.utils.openAppSettings
import sv.com.tarjetaone.presentation.helpers.UiText

/**
 * Base class for VMs that handle the selfie capture step to handle common state and
 * facephi result handling.
 * For custom initial UiState, add an init block and update the state.
 */
abstract class SelfieCaptureBaseViewModel(
    private val facephiResultHandler: FacephiResultHandler
) : BaseViewModel() {
    protected val _uiState = MutableStateFlow(SelfieCaptureUiState())
    val uiState = _uiState.asStateFlow()

    protected open fun onStart() = Unit

    /**
     * Callback called when the selfie capture is successful and the facephi result has been processed.
     */
    protected abstract fun onSuccessfulSelfieCapture(selfie: Bitmap)

    protected open fun onShowTutorialClick() = Unit

    private fun onCameraPermissionDenied(showRationale: Boolean) {
        sendEvent(
            UiEvent.ShowCommonDialog(
                CommonDialogWithIconParams(
                    title = UiText.StringResource(R.string.camera_access),
                    message = UiText.StringResource(R.string.permission_camera),
                    buttonText = UiText.StringResource(
                        if (showRationale) R.string.accept_label else R.string.settings
                    ),
                    onButtonClick = {
                        if (showRationale.not()) sendEvent(UiEvent.StartIntent(openAppSettings()))
                    }
                )
            )
        )
    }

    private fun onSelfieCaptured(result: WidgetResult) {
        onSuccessfulSelfieCapture(facephiResultHandler.onSelfieCaptured(result))
    }

    /**
     * Override this method to handle other custom actions when the continue button is clicked.
     * Ex. Analytics events.
     */
    protected open fun onContinueClick(requestPermission: () -> Unit) {
        requestPermission()
    }

    fun onEvent(event: SelfieCaptureUiEvent) {
        when (event) {
            SelfieCaptureUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            SelfieCaptureUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            SelfieCaptureUiEvent.OnShowTutorialClick -> onShowTutorialClick()
            is SelfieCaptureUiEvent.OnContinueClick -> onContinueClick(event.requestPermission)
            is SelfieCaptureUiEvent.OnCameraPermissionDenied -> onCameraPermissionDenied(event.showRationale)
            is SelfieCaptureUiEvent.OnSelfieCaptureFailed -> Unit
            is SelfieCaptureUiEvent.OnSelfieCaptured -> onSelfieCaptured(event.result)
            SelfieCaptureUiEvent.OnStart -> onStart()
        }
    }
}
