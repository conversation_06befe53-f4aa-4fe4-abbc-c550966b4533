package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.contact_channel

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.request.CustomerContactsUI
import sv.com.tarjetaone.domain.entities.response.ContactType
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.contact_channel.component.ContactChannelItem
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.contact_channel.component.ContactItemUiState

@Composable
fun ContactChannelsScreen(viewModel: ContactChannelViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    ContactChannelsContent(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@Composable
fun ContactChannelsContent(
    uiState: ContactChannelsUiState,
    onEvent: (ContactChannelsUiEvent) -> Unit
) {
    ScreenWithTopAppBar(
        title = stringResource(id = R.string.my_principal_contact_channel),
        onLeftButtonClick = { onEvent(ContactChannelsUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(ContactChannelsUiEvent.OnSupportClick) },
    ) {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen16),
            contentPadding = PaddingValues(
                horizontal = MaterialTheme.customDimens.dimen48,
                vertical = MaterialTheme.customDimens.dimen16
            )
        ) {
            items(uiState.contactChannels.count()) { index ->
                ContactChannelItem(
                    contactItem = uiState.contactChannels[index],
                    onModifyClick = { onEvent(ContactChannelsUiEvent.OnModifyClick(it)) },
                    onToggleNotification = { isActive, contactItem, type ->
                        onEvent(
                            ContactChannelsUiEvent.OnToggleNotification(
                                isActive = isActive,
                                contactIndex = index,
                                contactItem = contactItem,
                                notificationType = type
                            )
                        )
                    }
                )
            }
        }
    }
}

@Preview
@Composable
private fun ContactChannelsScreenPreview() {
    OneAppTheme {
        ContactChannelsContent(
            uiState = ContactChannelsUiState(
                contactChannels = remember {
                    mutableStateListOf(
                        ContactItemUiState(
                            contactType = ContactType.Email,
                            contact = CustomerContactsUI(
                                contactValue = "<EMAIL>"
                            )
                        )
                    )
                }
            ),
            onEvent = {}
        )
    }
}
