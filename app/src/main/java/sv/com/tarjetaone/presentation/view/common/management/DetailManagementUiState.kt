package sv.com.tarjetaone.presentation.view.common.management

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.domain.entities.response.ManAttributesUI
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.utils.ManAttributeType

data class DetailManagementUiState(
    @StringRes val primaryButtonText: Int = R.string.back_to_my_account,
    val managementNumber: String = EMPTY_STRING,
    val managementStatus: String = EMPTY_STRING,
    val managementStatusColor: String = DEFAULT_STATUS_COLOR,
    val managementName: String = EMPTY_STRING,
    val customerName: String = EMPTY_STRING,
    val creditCardNumber: String = EMPTY_STRING,
    val creditCardType: String = EMPTY_STRING,
    val showTimeline: Boolean = true,
    val requestStartDate: String = EMPTY_STRING,
    val requestEndDate: String = EMPTY_STRING,
    val description: String? = null,
    @StringRes val descriptionResource: Int? = null,
    val resolutionDays: Int? = null,
    val managementAttributes: List<ManagementAttributesUiModel> = emptyList(),
    val isCapturingVoucherView: Boolean = false,
    val showPrimaryButton: Boolean = true,
    @StringRes val secondaryButtonText: Int? = null,
    val isBackButtonVisible: Boolean = false,
    val showFeedbackMessage: Boolean = false,
    @DrawableRes val feedbackMessageIcon: Int? = null,
    @StringRes val feedbackMessageTitle: Int? = null,
    @StringRes val feedbackMessageDescription: Int? = null,
    val showExitButton: Boolean = false
)

data class ManagementAttributesUiModel(
    val managementAttributeDisplayName: UiText,
    val managementAttributeValue: UiText,
    val managementAttributeType: ManAttributeType? = null
)

fun ManAttributesUI.toAttributeUiModel() = ManagementAttributesUiModel(
    managementAttributeDisplayName = UiText.DynamicString(manAttributeTypeNameApp.orEmpty()),
    managementAttributeValue = UiText.DynamicString(value.orEmpty()),
    managementAttributeType = ManAttributeType.getAttributeType(manAttributeTypeNameApp)
)

const val DEFAULT_STATUS_COLOR = "#51DEA3"