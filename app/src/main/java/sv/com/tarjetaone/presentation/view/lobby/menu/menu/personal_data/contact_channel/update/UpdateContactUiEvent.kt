package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.contact_channel.update

import sv.com.tarjetaone.domain.entities.response.OtpMethod

sealed class UpdateContactUiEvent {
    data object OnStart: UpdateContactUiEvent()
    object OnTwilioClick : UpdateContactUiEvent()
    data class OnEmailChange(val email: String) : UpdateContactUiEvent()
    data class OnPhoneChange(val phone: String) : UpdateContactUiEvent()
    data class OnSelectOtpMethod(val method: OtpMethod) : UpdateContactUiEvent()
    object OnDismissSelectOtpMethod : UpdateContactUiEvent()
    object OnContinueClick : UpdateContactUiEvent()
}
