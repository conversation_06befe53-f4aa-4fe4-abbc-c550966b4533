package sv.com.tarjetaone.presentation.view.common.selfiepreview

import android.graphics.Bitmap
import com.facephi.fphiwidgetcore.WidgetResult
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler

/***
 * Base class for VMs that handle the selfie preview step
 * to handle common state and facephiResultHandler implemented
 * from compose
 */
abstract class SelfiePreviewViewModel(
    protected val imageUtils: ImageUtils,
    private val facephiResultHandler: FacephiResultHandler
) : BaseViewModel() {

    protected val _uiState: MutableStateFlow<SelfiePreviewUiState> =
        MutableStateFlow(SelfiePreviewUiState())
    val uiState: StateFlow<SelfiePreviewUiState> = _uiState.asStateFlow()

    protected abstract fun onContinueClick()

    /**
     * Override to handle retake selfie action
     */
    protected open fun onTakeNewSelfieClick() {
        // Default implementation, child classes can override.
    }

    protected open fun onStart() {
        updateSelfiePicture()
    }

    protected open fun onCaptureSelfie(result: WidgetResult) {
        val bestImage = facephiResultHandler.onSelfieCaptured(result)
        updateSelfiePicture(bestImage)
    }

    private fun updateSelfiePicture(image: Bitmap? = null) {
        _uiState.update {
            it.copy(
                selfie = image ?: baseSharedPrefs.getSelfieFileName()?.let { selfieName ->
                    imageUtils.getTempImage(selfieName)
                }
            )
        }
    }

    fun onEvent(event: SelfiePreviewUiEvent) {
        when (event) {
            SelfiePreviewUiEvent.OnBack -> sendEvent(UiEvent.NavigateBack)
            SelfiePreviewUiEvent.OnTwilioChat -> sendEvent(UiEvent.TwilioClick)
            SelfiePreviewUiEvent.OnStart -> onStart()
            SelfiePreviewUiEvent.OnContinueClick -> onContinueClick()
            SelfiePreviewUiEvent.OnTakeNewSelfieClick -> onTakeNewSelfieClick()
            is SelfiePreviewUiEvent.OnCaptureSelfie -> onCaptureSelfie(event.result)
        }
    }
}
