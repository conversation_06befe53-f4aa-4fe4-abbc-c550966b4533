package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.animation.animateColorAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.presentation.compose.modifier.animatePlacement
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens

@Composable
fun OneSwitch(
    modifier: Modifier = Modifier,
    checked: Boolean,
    onCheckedChange: (<PERSON><PERSON><PERSON>) -> Unit
) {
    val bgColor by animateColorAsState(
        targetValue = if (checked) MaterialTheme.customColors.successContainer
            else MaterialTheme.customColors.gray300,
        label = "switch_bg_color"
    )
    // Size of the switch fixed at 40x22 based on design system
    Box(
        modifier = modifier
            .size(MaterialTheme.customDimens.dimen40, MaterialTheme.customDimens.dimen22)
            .clip(CircleShape)
            .background(bgColor)
            .clickable { onCheckedChange(!checked) }
    ) {
        Box(
            modifier = Modifier
                .animatePlacement()
                .align(if (checked) Alignment.CenterEnd else Alignment.CenterStart)
                .padding(horizontal = MaterialTheme.customDimens.dimen3)
                .size(MaterialTheme.customDimens.dimen16)
                .clip(CircleShape)
                .background(MaterialTheme.colorScheme.background)
        )
    }
}

@Preview
@Composable
private fun OneSwitchUncheckedPreview() {
    OneAppTheme {
        OneSwitch(
            checked = false,
            onCheckedChange = {}
        )
    }
}

@Preview
@Composable
private fun OneSwitchCheckedPreview() {
    OneAppTheme {
        OneSwitch(
            checked = true,
            onCheckedChange = {}
        )
    }
}
