package sv.com.tarjetaone.presentation.compose.uicomponent.button

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.theme.poppinsFamily
import sv.com.tarjetaone.presentation.compose.util.Spacer8


/**
 * @param modifier modifier for the button dimensions
 * @param colors button container and content colors
 * @param border button border stroke colors
 * @param buttonShape button shape
 * @param content dynamic button content
 * @param onClick button functionality
 * */
@Composable
fun CustomOutlinedButton(
    modifier: Modifier = Modifier
        .fillMaxWidth()
        .sizeIn(
            minHeight = MaterialTheme.customDimens.dimen48
        ),
    colors: ButtonColors = ButtonDefaults.outlinedButtonColors(
        containerColor = Color.Transparent,
        contentColor = MaterialTheme.customColors.defaultSurface
    ),
    border: BorderStroke = BorderStroke(
        MaterialTheme.customDimens.dimen1,
        MaterialTheme.customColors.defaultSurface
    ),
    buttonShape: Shape = MaterialTheme.shapes.extraLarge,
    content: @Composable RowScope.() -> Unit,
    onClick: () -> Unit = {},

    ) {
    OutlinedButton(
        modifier = modifier,
        colors = colors,
        border = border,
        shape = buttonShape,
        content = {
            content()
        },
        onClick = {
            onClick()
        },
    )
}

@Composable
@Preview
fun CustomOutlinedButtonPreview() {
    CustomOutlinedButton(content = {
        Image(
            modifier = Modifier.size(
                MaterialTheme.customDimens.dimen24
            ),
            painter = painterResource(
                id = R.drawable.ic_lock_fill
            ),
            contentDescription = null
        )
        Spacer8()
        Text(
            text = stringResource(
                id = R.string.biometric_password_header
            ),
            modifier = Modifier
                .align(Alignment.CenterVertically)
                .padding(
                    vertical = MaterialTheme.customDimens.dimen8
                ),
            style = MaterialTheme.typography.bodySmall.copy(
                fontWeight = FontWeight.SemiBold,
                fontFamily = poppinsFamily,
                fontSize = MaterialTheme.customDimensSp.sp18
            ),
            textAlign = TextAlign.Center
        )
    }
    )
}