package sv.com.tarjetaone.presentation.compose.util

import androidx.compose.foundation.pager.PagerState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.util.lerp
import kotlin.math.absoluteValue

/**
 * Get the offset for a specific page
 */
fun PagerState.offsetForPage(page: Int) = (currentPage - page) + currentPageOffsetFraction

/**
 * Get the start offset for a specific page from the left
 */
fun PagerState.startOffsetForPage(page: Int): Float {
    return offsetForPage(page).coerceAtLeast(0f)
}

/**
 * Get the start offset for a specific page from the right
 */
fun PagerState.endOffsetForPage(page: Int): Float {
    return offsetForPage(page).coerceAtMost(0f)
}

fun Modifier.flipTransition(page: Int, pagerState: PagerState) = graphicsLayer {
    val pageOffset = pagerState.offsetForPage(page)
    translationX = size.width * pageOffset
    rotationY = if (pageOffset in OFFSET_RANGE_START..OFFSET_RANGE_END) {
        -ROTATION_START_VALUE * pageOffset.absoluteValue
    } else {
        ROTATION_START_VALUE * pageOffset.absoluteValue
    }
    alpha = if (pageOffset > OFFSET_RANGE_END) 0f else if (pageOffset < -OFFSET_RANGE_END) 0f else 1f
    cameraDistance = DEFAULT_CAMERA_DISTANCE
}

fun Modifier.scaleTransition(page: Int, pagerState: PagerState, scaleStart: Float = 0.75f) = graphicsLayer {
    val pageOffset = pagerState.offsetForPage(page)
    val scale = lerp(
        start = scaleStart,
        stop = 1f,
        fraction = 1f - pageOffset.absoluteValue.coerceIn(0f, 1f)
    )
    scaleX = scale
    scaleY = scale
}

private const val DEFAULT_CAMERA_DISTANCE = 30f
private const val ROTATION_START_VALUE = 180f
private const val OFFSET_RANGE_START = 0.0f
private const val OFFSET_RANGE_END = 0.5f