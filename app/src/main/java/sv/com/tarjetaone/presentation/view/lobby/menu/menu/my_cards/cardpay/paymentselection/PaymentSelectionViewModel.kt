package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.cardpay.paymentselection

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.analytics.dynatrace.DynatraceEvent
import sv.com.tarjetaone.analytics.dynatrace.DynatraceManager
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.SUCCESS_RESPONSE_CODE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_CHAR
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.request.CardPaymentExecutePaymentRequestUI
import sv.com.tarjetaone.domain.entities.response.CardPaymentAccountsDebitDataUI
import sv.com.tarjetaone.domain.usecases.requestcard.payment.GetDebitAccountsUseCase
import sv.com.tarjetaone.domain.usecases.requestcard.payment.GetMinAndMaxPaymentUseCase
import javax.inject.Inject

@HiltViewModel
class PaymentSelectionViewModel @Inject constructor(
    private val getMinAndMaxPaymentUseCase: GetMinAndMaxPaymentUseCase,
    private val getDebitAccountsUseCase: GetDebitAccountsUseCase,
    private val dynatraceManager: DynatraceManager,
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {
    private val args = PaymentSelectionFragmentArgs.fromSavedStateHandle(savedStateHandle)
    private val _uiState = MutableStateFlow(PaymentSelectionUiState())
    val uiState = _uiState.asStateFlow()

    private fun onStart() {
        dynatraceManager.sendInAppEvent(DynatraceEvent.CreditCardPayment.ViewPayCard)
        viewModelScope.launch {
            sendEvent(UiEvent.Loading(true))
            getMinAndMaxPaymentUseCase(
                cCardId = baseSharedPrefs.mainCard?.creditCardId.orZero()
            ).executeUseCase {
                sendEvent(UiEvent.Loading(false))
                if (it.statusResponse?.responseStatus?.code == SUCCESS_RESPONSE_CODE) {
                    _uiState.update { state ->
                        state.copy(
                            minAmount = it.data?.minPayment.orZero(),
                            fullAmount = it.data?.fullPayment.orZero(),
                            cardNumber = baseSharedPrefs.mainCard?.cardNumMasked.orEmpty()
                        )
                    }
                } else {
                    showUpsErrorMessage(false) { sendEvent(UiEvent.NavigateBack) }
                }
            }
        }
    }

    private fun onSelectPayment(type: PaymentType) {
        dynatraceManager.sendInAppEvent(
            when (type) {
                PaymentType.FULL -> DynatraceEvent.CreditCardPayment.PayFullAmount
                PaymentType.MIN -> DynatraceEvent.CreditCardPayment.PayMinimumAmount
                PaymentType.OTHER -> DynatraceEvent.CreditCardPayment.PayAnotherAmount
            }
        )
        _uiState.update {
            it.copy(
                selectedPayment = type,
                otherAmount = if (type == PaymentType.OTHER) it.otherAmount else EMPTY_STRING
            )
        }
    }

    private fun onOtherPaymentChange(other: String) {
        val otherFiltered = other
            .filter { it.isDigit() }
            .dropWhile { it == ZERO_CHAR } // remove leading zeros
        _uiState.update { it.copy(otherAmount = otherFiltered) }
    }

    private fun getPaymentAmount(state: PaymentSelectionUiState): Double {
        return when (state.selectedPayment) {
            PaymentType.MIN -> state.minAmount
            PaymentType.FULL -> state.fullAmount
            else -> state.otherAmount.toDouble() / OTHER_AMOUNT_DIVISOR
        }
    }

    private fun onContinueMethodSelectionClick() {
        viewModelScope.launch {
            sendEvent(UiEvent.Loading(true))
            getDebitAccountsUseCase(
                cCardId = baseSharedPrefs.mainCard?.creditCardId.orZero()
            ).executeUseCase { response ->
                sendEvent(UiEvent.Loading(false))
                if (response.statusResponse?.responseStatus?.code == SUCCESS_RESPONSE_CODE) {
                    if (response.data?.isEmpty() == true) {
                        showUpsErrorMessage(false) { sendEvent(UiEvent.NavigateBack) }
                    } else {
                        val accounts = response.data.orEmpty()
                        val accountTypes = accounts
                            .mapNotNull { it.accountType }
                            .distinct()
                        _uiState.update {
                            it.copy(
                                isSelectingAccount = true,
                                accountTypes = accountTypes,
                                accounts = accounts,
                                finalAmount = getPaymentAmount(it)
                            )
                        }
                        onAccountTypeChange(accountTypes.firstOrNull().orEmpty())
                    }
                } else {
                    showUpsErrorMessage(false) { sendEvent(UiEvent.NavigateBack) }
                }
            }
        }
    }

    private fun onAccountTypeChange(accountType: String) {
        _uiState.update { state ->
            val filteredAccounts = state.accounts.filter { it.accountType == accountType }
            state.copy(
                selectedAccountType = accountType,
                filteredAccounts = if (accountType != state.selectedAccountType) {
                    filteredAccounts
                } else {
                    state.filteredAccounts
                },
                selectedAccount = if (accountType != state.selectedAccountType) {
                    filteredAccounts.firstOrNull()
                } else {
                    state.selectedAccount
                }
            )
        }
    }

    private fun onAccountChange(account: CardPaymentAccountsDebitDataUI) {
        _uiState.update { it.copy(selectedAccount = account) }
    }

    private fun onPaymentDescChange(paymentDesc: String) {
        _uiState.update { it.copy(paymentDescription = paymentDesc) }
    }

    private fun onContinuePaymentClick() {
        dynatraceManager.sendInAppEvent(DynatraceEvent.CreditCardPayment.PayCardContinue)

        val state = uiState.value
        val request = CardPaymentExecutePaymentRequestUI(
            cCardId = baseSharedPrefs.mainCard?.creditCardId.orZero(),
            paymentMethodCode = args.paymentMethodCode.orEmpty(),
            accountNumber = state.selectedAccount?.accountNumber.orEmpty(),
            descriptionPayment = state.paymentDescription,
            amount = state.finalAmount.toString(),
        )
        sendEvent(
            UiEvent.Navigate(
                PaymentSelectionFragmentDirections.actionSelectPaymentToAlmostThere(request)
            )
        )
    }

    fun onEvent(event: PaymentSelectionUiEvent) {
        when (event) {
            PaymentSelectionUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            PaymentSelectionUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            PaymentSelectionUiEvent.OnStart -> onStart()
            PaymentSelectionUiEvent.OnContinueMethodSelectionClick -> onContinueMethodSelectionClick()
            PaymentSelectionUiEvent.OnContinuePaymentClick -> onContinuePaymentClick()
            is PaymentSelectionUiEvent.OnOtherPaymentChange -> onOtherPaymentChange(event.other)
            is PaymentSelectionUiEvent.OnPaymentDescChange -> onPaymentDescChange(event.desc)
            is PaymentSelectionUiEvent.OnSelectPayment -> onSelectPayment(event.payment)
            is PaymentSelectionUiEvent.OnAccountChange -> onAccountChange(event.account)
            is PaymentSelectionUiEvent.OnAccountTypeChange -> onAccountTypeChange(event.accountType)
        }
    }

    companion object {
        private const val OTHER_AMOUNT_DIVISOR = 100
    }
}
