package sv.com.tarjetaone.presentation.view.lobby.menu.cardpin.detail

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.common.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.presentation.view.common.management.DEFAULT_STATUS_COLOR
import sv.com.tarjetaone.presentation.view.common.management.ManagementDetailBaseViewModel
import sv.com.tarjetaone.presentation.view.common.management.toAttributeUiModel
import sv.com.tarjetaone.presentation.view.utils.DAY_OF_WEEK_MONTH_TIME_FORMAT
import sv.com.tarjetaone.presentation.view.utils.YEAR_MONTH_DAY_TIME_FORMAT
import javax.inject.Inject

@HiltViewModel
class CardPinDetailViewModel @Inject constructor(
    imageUtils: ImageUtils,
    savedStateHandle: SavedStateHandle
) : ManagementDetailBaseViewModel(imageUtils) {
    private val args = CardPinDetailFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onStart() {
        args.data?.let {
            _uiState.update { state ->
                state.copy(
                    isBackButtonVisible = false,
                    managementNumber = it.manNumberApp.orEmpty(),
                    managementStatus = it.mrStatusNameApp.orEmpty(),
                    managementStatusColor = it.mrStatusTextColor ?: DEFAULT_STATUS_COLOR,
                    managementName = it.manTypeNameApp.orEmpty(),
                    customerName = it.clientName.orEmpty(),
                    creditCardNumber = it.cardNumMasked.orEmpty(),
                    creditCardType = it.typeCardText.orEmpty(),
                    requestStartDate = it.processDate?.getFormattedDateFromTo(
                        YEAR_MONTH_DAY_TIME_FORMAT,
                        DAY_OF_WEEK_MONTH_TIME_FORMAT
                    ).orEmpty(),
                    requestEndDate = it.closeDate?.getFormattedDateFromTo(
                        YEAR_MONTH_DAY_TIME_FORMAT,
                        DAY_OF_WEEK_MONTH_TIME_FORMAT
                    ).orEmpty(),
                    managementAttributes = it.manAttributes.map { attribute ->
                        attribute.toAttributeUiModel()
                    }
                )
            }
        }
    }

    override fun onPrimaryButtonClick() {
        sendEvent(UiEvent.Navigate(CardPinDetailFragmentDirections.actionHome()))
    }
}
