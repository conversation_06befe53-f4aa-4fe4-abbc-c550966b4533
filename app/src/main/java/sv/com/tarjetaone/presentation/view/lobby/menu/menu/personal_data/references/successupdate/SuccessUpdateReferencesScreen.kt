package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.references.successupdate

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.OneBackHandler
import sv.com.tarjetaone.presentation.view.common.confirmation_gradient.SuccessGradientScreen

@Composable
fun SuccessUpdateReferencesScreen(viewModel: SuccessUpdateReferencesViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LaunchedEffect(Unit) {
        viewModel.onEvent(SuccessUpdateReferencesUiEvent.OnStart)
    }
    OneBackHandler()
    SuccessGradientScreen(
        message = stringResource(
            if (uiState.isReferenceAdded) {
                R.string.reference_was_successfully_added
            } else {
                R.string.reference_was_successfully_updated
            }
        ),
        buttonText = stringResource(id = R.string.done_button),
        onButtonClick = { viewModel.onEvent(SuccessUpdateReferencesUiEvent.OnDoneClick) }
    )
}
