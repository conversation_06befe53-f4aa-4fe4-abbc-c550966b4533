package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.pay_with_points.success

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import javax.inject.Inject

@HiltViewModel
class PayWithPointsSuccessViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {
    private val args = PayWithPointsSuccessFragmentArgs.fromSavedStateHandle(savedStateHandle)

    private fun onContinueClick() {
        sendEvent(
            UiEvent.Navigate(
                PayWithPointsSuccessFragmentDirections
                    .actionPayWithPointsSuccessFragmentToPayWithPointsDetailsFragment(
                        response = args.response
                    )
            )
        )
    }

    fun onEvent(event: PayWithPointsSuccessUiEvent) {
        when (event) {
            PayWithPointsSuccessUiEvent.OnContinueClick -> onContinueClick()
        }
    }
}
