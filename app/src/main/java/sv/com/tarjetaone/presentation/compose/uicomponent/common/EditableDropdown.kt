package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusManager
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.PopupProperties
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.FieldDecorationType
import sv.com.tarjetaone.presentation.compose.util.Spacer4
import sv.com.tarjetaone.presentation.compose.util.Spacer8

/**
 * Simple dropdown with editable text. This dropdown DOES NOT handle filtering of items.
 * Any filtering logic is meant to be hoisted to either the parent composable or the ViewModel with
 * the provided callbacks.
 * @param modifier Modifier for the dropdown.
 * @param items List of items to be displayed in the dropdown.
 * @param itemLabel Function to map an item to a string to be displayed as option in the dropdown or
 * as display string for the selected [value].
 * @param value Currently selected item.
 * @param onValueChange Callback for when the selected item changes.
 * @param query String to be shown when [value] is null. Useful when the user is actively typing
 * @param onQueryChange Callback for when the user types in the text field.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Suppress("kotlin:S107")
@Composable
fun <T> EditableDropdown(
    modifier: Modifier = Modifier,
    label: String? = null,
    labelIcon: Int? = null,
    labelIconColor: Color = MaterialTheme.customColors.disclaimer,
    labelIconClick: () -> Unit = {},
    placeholder: String? = null,
    items: List<T>,
    itemLabel: (T) -> String,
    value: T?,
    onValueChange: (T) -> Unit,
    query: String,
    onQueryChange: (String) -> Unit,
    decorationType: FieldDecorationType = FieldDecorationType.ELEVATED,
    focusManager: FocusManager = LocalFocusManager.current
) {
    val dimen48 = MaterialTheme.customDimens.dimen48
    var expanded by remember { mutableStateOf(false) }

    var size by remember { mutableStateOf(IntSize.Zero) }
    val sizeOfOneItem by remember { mutableStateOf(dimen48) }
    val configuration = LocalConfiguration.current
    val screenHeight50 by remember {
        val screenHeight = configuration.screenHeightDp.dp
        // Assuming the DropDown menu anchor is in middle of the screen. This is the maximum height that popup menu can take.
        mutableStateOf(screenHeight / TWO_VALUE)
    }
    Column(modifier = modifier) {
        label?.let {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = it,
                    style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Medium),
                    color = MaterialTheme.customColors.gray700,
                )
                labelIcon?.let { icon ->
                    Spacer8()
                    Icon(
                        imageVector = ImageVector.vectorResource(id = icon),
                        tint = labelIconColor,
                        contentDescription = null,
                        modifier = Modifier.clickable(onClick = labelIconClick)
                    )
                }
            }
            Spacer4()
        }
        ExposedDropdownMenuBox(
            expanded = expanded,
            onExpandedChange = { expanded = !expanded },
            modifier = Modifier.onSizeChanged { size = it },
        ) {
            SimpleElevatedTextField(
                value = value?.let { itemLabel(it) } ?: query,
                onValueChange = {
                    expanded = true
                    onQueryChange(it)
                },
                modifier = Modifier.menuAnchor(),
                decorationType = decorationType,
                trailingIcon = {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_arrow_down_filled),
                        contentDescription = null,
                        tint = Color.Gray,
                        modifier = Modifier.padding(start = MaterialTheme.customDimens.dimen8)
                    )
                },
                placeholder = placeholder,
                textStyle = MaterialTheme.typography.bodyMedium
            )
            val height by remember(items.size) {
                val itemsSize = sizeOfOneItem * items.size
                mutableStateOf(minOf(itemsSize, screenHeight50))
            }
            DropdownMenu(
                modifier = Modifier
                    .background(Color.White)
                    .exposedDropdownSize(true),
                expanded = expanded,
                properties = PopupProperties(focusable = false),
                onDismissRequest = { expanded = false },
            ) {
                Box(
                    modifier = Modifier.then(
                        with(LocalDensity.current) {
                            Modifier.size(
                                width = size.width.toDp(),
                                height = height
                            )
                        }
                    )
                ) {
                    LazyColumn {
                        items(items) { item ->
                            DropdownMenuItem(
                                text = {
                                    Text(
                                        text = itemLabel(item),
                                        style = MaterialTheme.typography.labelLarge.copy(
                                            fontWeight = FontWeight.Normal
                                        ),
                                    )
                                },
                                onClick = {
                                    focusManager.clearFocus()
                                    onValueChange(item)
                                    expanded = false
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun EditableDropdownPreview() {
    OneAppTheme {
        EditableDropdown(
            items = listOf(EMPTY_STRING),
            onQueryChange = {},
            onValueChange = {},
            itemLabel = { it },
            value = EMPTY_STRING,
            query = EMPTY_STRING,
            modifier = Modifier.fillMaxWidth()
        )
    }
}
