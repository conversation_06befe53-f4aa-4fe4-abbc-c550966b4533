package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.address_location.success

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import javax.inject.Inject

@HiltViewModel
class SuccessUpdateAddressViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {
    private val args = SuccessUpdateAddressFragmentArgs.fromSavedStateHandle(savedStateHandle)

    private val _uiState = MutableStateFlow(SuccessUpdateAddressUiState(action = args.action))
    val uiState: StateFlow<SuccessUpdateAddressUiState> = _uiState.asStateFlow()

    private fun onDoneClick() {
        sendEvent(UiEvent.NavigateBackTo(destinationId = R.id.addresLocation))
    }

    fun onEvent(event: SuccessUpdateAddressUiEvent) {
        when (event) {
            SuccessUpdateAddressUiEvent.OnDoneClick -> onDoneClick()
        }
    }
}
