package sv.com.tarjetaone.presentation.compose.uicomponent.card

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.sp
import sv.com.tarjetaone.presentation.compose.modifier.animatePlacement
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.seaweedScriptFamily
import sv.com.tarjetaone.presentation.compose.uicomponent.common.RadioButtonComponent
import sv.com.tarjetaone.presentation.compose.util.Spacer8

/**
 *  Reusable composable Row component
 *  @param modifier to apply modifications from the caller function.
 *  @param isSelected the state of the radio button.
 *  @param onClick the action to be performed when the radio button is clicked.
 *  @param content the content to be displayed inside the card.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BaesSelectableCard(
    modifier: Modifier = Modifier,
    cardElevation: Dp = MaterialTheme.customDimens.dimen4,
    verticalAlignment: Alignment.Vertical = Alignment.CenterVertically,
    isSelected: Boolean,
    onClick: () -> Unit,
    content: @Composable () -> Unit
) {
    Card(
        elevation = CardDefaults.cardElevation(defaultElevation = cardElevation),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) MaterialTheme.customColors.successLightContainer
                else MaterialTheme.colorScheme.background,
        ),
        onClick = onClick,
        modifier = modifier
    ) {
        Row(
            verticalAlignment = verticalAlignment,
            modifier = Modifier
                .fillMaxWidth()
                .sizeIn(
                    minHeight = MaterialTheme.customDimens.dimen74,
                    minWidth = MaterialTheme.customDimens.dimen330
                )
                .padding(MaterialTheme.customDimens.dimen8)
        ) {
            RadioButtonComponent(
                selected = isSelected,
                onClick = onClick,
                modifier = Modifier.animatePlacement()
            )
            Spacer8()
            content()
        }
    }
}

@Preview
@Composable
private fun BaesSelectableCardPreview() {
    OneAppTheme {
        BaesSelectableCard(
            isSelected = false,
            onClick = {}
        ) {
            Text(
                text = "Juan Perez",
                fontSize = 37.sp,
                fontFamily = seaweedScriptFamily,
                fontWeight = FontWeight.Normal
            )
        }
    }
}

@Preview
@Composable
private fun BaesSelectableCardSelectedPreview() {
    OneAppTheme {
        BaesSelectableCard(
            isSelected = true,
            onClick = {}
        ) {
            Text(
                text = "Juan Perez",
                fontSize = 37.sp,
                fontFamily = seaweedScriptFamily,
                fontWeight = FontWeight.Normal
            )
        }
    }
}
