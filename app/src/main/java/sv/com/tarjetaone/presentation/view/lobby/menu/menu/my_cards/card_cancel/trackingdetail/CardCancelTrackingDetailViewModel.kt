package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_cancel.trackingdetail

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.presentation.view.common.management.ManagementDetailBaseViewModel
import sv.com.tarjetaone.presentation.view.utils.DAY_OF_WEEK_MONTH_TIME_FORMAT
import sv.com.tarjetaone.presentation.view.utils.YEAR_MONTH_DAY_TIME_FORMAT
import javax.inject.Inject

@HiltViewModel
class CardCancelTrackingDetailViewModel @Inject constructor(
    imageUtils: ImageUtils,
    savedStateHandle: SavedStateHandle
) : ManagementDetailBaseViewModel(imageUtils) {

    private val args = CardCancelTrackingDetailFragmentArgs
        .fromSavedStateHandle(savedStateHandle)

    override fun onStart() {
        setDetailData()
    }

    override fun onPrimaryButtonClick() {
        sendEvent(
            UiEvent.Navigate(
                CardCancelTrackingDetailFragmentDirections
                    .actionNavigationMainCancelCardsToCancelCardSurveyFragment()
            )
        )
    }

    override fun onExitButtonClick() {
        sendEvent(
            UiEvent.Navigate(
                CardCancelTrackingDetailFragmentDirections.actionHome()
            )
        )
    }

    private fun setDetailData() {
        with(args.data) {
            _uiState.update {
                it.copy(
                    primaryButtonText = R.string.cancel_card_tracking_detail_take_survey_button,
                    managementNumber = this?.manNumberApp.orEmpty(),
                    managementStatus = this?.mrStatusNameApp.orEmpty(),
                    managementStatusColor = this?.mrStatusTextColor.orEmpty(),
                    managementName = this?.manTypeNameApp.orEmpty(),
                    customerName = this?.clientName.orEmpty(),
                    creditCardNumber = this?.cardNumMasked.orEmpty(),
                    creditCardType = this?.typeCardText.orEmpty(),
                    requestStartDate = this?.processDate?.getFormattedDateFromTo(
                        YEAR_MONTH_DAY_TIME_FORMAT,
                        DAY_OF_WEEK_MONTH_TIME_FORMAT
                    ).orEmpty(),
                    requestEndDate = this?.closeDate?.getFormattedDateFromTo(
                        YEAR_MONTH_DAY_TIME_FORMAT,
                        DAY_OF_WEEK_MONTH_TIME_FORMAT
                    ).orEmpty(),
                    resolutionDays = this?.availableDay,
                    descriptionResource = R.string.cancel_card_tracking_detail_on_process_text,
                    showFeedbackMessage = true,
                    feedbackMessageIcon = R.drawable.ic_sad_face,
                    feedbackMessageTitle = R.string.cancel_card_tracking_detail_sorry_text,
                    feedbackMessageDescription = R.string.footermanagement,
                    showExitButton = true
                )
            }
        }
    }
}