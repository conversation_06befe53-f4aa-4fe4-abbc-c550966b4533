package sv.com.tarjetaone.presentation.view.lobby.menu.menu.account_deletion.delete_account_menu

import android.os.Bundle
import android.view.View
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.getValue
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withLink
import androidx.fragment.app.viewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import dagger.hilt.android.AndroidEntryPoint
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.base.BaseFragment
import sv.com.tarjetaone.databinding.FragmentComposeBinding
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.account_deletion.common.DeleteAccountScreen
import sv.com.tarjetaone.presentation.view.utils.requireLobbyActivity

@AndroidEntryPoint
class DeleteAccountMenuFragment : BaseFragment<FragmentComposeBinding>(
    R.layout.fragment_compose
) {
    override val viewModel: DeleteAccountMenuViewModel by viewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requireLobbyActivity().hideNavigationBottom()
        binding.composeView.setContentScreen {
            val privacyPolicy by viewModel.privacyPolicy.collectAsStateWithLifecycle()
            val annotatedString = buildAnnotatedString {
                append(stringResource(id = R.string.delete_account_description_menu))
                withLink(
                    LinkAnnotation.Url(
                        url = privacyPolicy,
                        styles = TextLinkStyles(
                            style = SpanStyle(
                                color = MaterialTheme.colorScheme.primary,
                                textDecoration = TextDecoration.Underline
                            )
                        )
                    )
                ) {
                    append(stringResource(id = R.string.delete_account_description_continuation))
                }
            }
            DeleteAccountScreen(
                description = annotatedString,
                primaryButtonText = stringResource(id = R.string.understood_label),
                hyperLinkButtonText = stringResource(id = R.string.cancel_my_one_product),
                onEvent = viewModel::onEvent
            )
        }
    }
}
