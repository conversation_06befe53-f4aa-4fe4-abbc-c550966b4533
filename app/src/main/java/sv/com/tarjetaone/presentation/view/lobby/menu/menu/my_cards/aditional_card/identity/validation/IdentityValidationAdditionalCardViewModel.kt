package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.identity.validation

import android.graphics.Bitmap
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.BITMAP_COMPRESS_QUALITY_80
import sv.com.tarjetaone.common.utils.AppConstants.SUCCESS_RESPONSE_CODE
import sv.com.tarjetaone.common.utils.helpers.CryptoHelper
import sv.com.tarjetaone.core.utils.bitmap.BitmapUtils
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.core.utils.extensions.removeLineBreak
import sv.com.tarjetaone.data.api.models.OCRCode
import sv.com.tarjetaone.data.api.models.toDataCollection
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.CustomerDocumentInfoUI
import sv.com.tarjetaone.domain.entities.response.DataCardReplacementResponseUI
import sv.com.tarjetaone.domain.usecases.auth.FaceAuthenticationUseCase
import sv.com.tarjetaone.domain.usecases.requestcard.additionalcard.AdditionalCardUseCase
import sv.com.tarjetaone.domain.usecases.requestcard.uploaddocument.SendDocumentUseCase
import sv.com.tarjetaone.presentation.view.common.identity_validation.IdentityValidationBaseViewModel
import javax.inject.Inject

@HiltViewModel
class IdentityValidationAdditionalCardViewModel @Inject constructor(
    private val additionalCardUseCase: AdditionalCardUseCase,
    private val sendDocuments: SendDocumentUseCase,
    private val imgUtils: ImageUtils,
    private val bitmapUtils: BitmapUtils,
    private val cryptoHelper: CryptoHelper,
    faceAuthenticationUseCase: FaceAuthenticationUseCase,
    private val sharedPrefsRepo: SecureSharedPreferencesRepository
) : IdentityValidationBaseViewModel(
    faceAuthenticationUseCase = faceAuthenticationUseCase,
    sharedPrefs = sharedPrefsRepo
) {
    override val onBlockedAction = {
        sendEvent(UiEvent.NavigateBackTo(R.id.navigation_home))
    }

    override fun operation(biometryId: Int) {
        sendAdditionalCardApplication(biometryId)
    }

    private fun sendAdditionalCardApplication(biometryProcessID: Int) {
        viewModelScope.launch {
            sharedPrefsRepo.userAdditionalCardData?.let { user ->
                additionalCardUseCase(user, buildDataCollection(sharedPrefsRepo.getOcr()))
                    .executeUseCase {
                        when (it.statusResponse?.responseStatus?.code) {
                            SUCCESS_RESPONSE_CODE -> {
                                sharedPrefsRepo.userAdditionalCardData = null
                                it.data?.let { data ->
                                    data.management?.let { management ->
                                        sendDocuments(
                                            biometryProcessID,
                                            data.addCustomerId.orZero(),
                                            management
                                        )
                                    }
                                }
                            }

                            ROLLBACK_ERROR_CODE, EMBOSSER_ERROR_CODE -> {
                                showUpsErrorMessage(false) {
                                    sendEvent(
                                        UiEvent.Navigate(
                                            IdentityValidationAdditionalCardFragmentDirections
                                                .navigateToCardAditionalFragment()
                                        )
                                    )
                                }
                            }

                            else -> {
                                showUpsErrorMessage(false) { sendEvent(UiEvent.NavigateBack) }
                            }
                        }
                    }
            }
        }
    }

    private fun sendDocuments(
        biometryProcessID: Int,
        addCustomerId: Int,
        managementData: DataCardReplacementResponseUI
    ) {
        viewModelScope.launch {
            val documentBackFileName = sharedPrefsRepo.getDocBackFileName().orEmpty()
            val documentFrontFileName = sharedPrefsRepo.getDocFrontFileName().orEmpty()

            val documentBackImageBase64 =
                async { encodeDocumentImageToBase64(documentBackFileName) }
            val documentFrontImageBase64 =
                async { encodeDocumentImageToBase64(documentFrontFileName) }

            val customerInfo = CustomerDocumentInfoUI(
                documentBackImageBase64 = documentBackImageBase64.await(),
                documentFrontImageBase64 = documentFrontImageBase64.await(),
                customerId = sharedPrefsRepo.getCustomerId().orZero(),
                addCustomerId = addCustomerId,
                biometryProcessID = biometryProcessID
            )
            sendDocuments(customerInfo).executeUseCase {
                sendEvent(
                    UiEvent.Navigate(
                        IdentityValidationAdditionalCardFragmentDirections
                            .actionIdentityValidationACFragmentToAdditionalCardFinishDetailsFragment(
                                data = managementData
                            )
                    )
                )
            }
        }
    }

    private suspend fun encodeDocumentImageToBase64(fileName: String): String {
        val bitmap = imgUtils.getTempImageAsync(fileName)
        val byteArray = bitmap?.let {
            bitmapUtils.toByteArray(
                it,
                Bitmap.CompressFormat.JPEG,
                BITMAP_COMPRESS_QUALITY_80
            )
        }
        return byteArray?.let {
            cryptoHelper.encryptBASE64Async(it)
                .removeLineBreak()
        }.orEmpty()
    }

    private fun buildDataCollection(ocrCode: OCRCode?) = ocrCode?.toDataCollection().orEmpty()

    override fun onFailedIdentityValidation(resultCode: String?) =
        sendEvent(UiEvent.NavigateBack)

    companion object {
        const val ROLLBACK_ERROR_CODE = 110
        const val EMBOSSER_ERROR_CODE = 111
    }
}
