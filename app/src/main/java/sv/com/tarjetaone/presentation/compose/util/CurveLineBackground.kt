package sv.com.tarjetaone.presentation.compose.util

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.FOUR_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens

@Composable
fun CurveLineBackgroundComponent(
    modifier: Modifier = Modifier,
    curveColor: Color = MaterialTheme.customColors.tertiaryLight,
    strokeWith: Int = FOUR_VALUE,
    height: Dp = MaterialTheme.customDimens.dimen200,
    width: Int = LocalConfiguration.current.screenWidthDp,
    content: @Composable () -> Unit
) {

    Box(
        modifier = Modifier.wrapContentHeight().fillMaxWidth(),
        contentAlignment = Alignment.BottomCenter
    ) {
        Canvas(
            modifier = modifier
        ) {
            drawPath(
                path = Path().apply {
                    moveTo(
                        x = ZERO_VALUE.dp.toPx(),
                        y = height.toPx()
                    )
                    quadraticTo(
                        x1 = (width / TWO_VALUE).dp.toPx(),
                        y1 = (height / TWO_VALUE).toPx(),
                        x2 = width.dp.toPx(),
                        y2 = height.toPx()
                    )
                },
                color = curveColor,
                style = Stroke(strokeWith.dp.value)
            )
        }
        content()
    }
}

@Preview
@Composable
fun CurvedLinePreview() {
    CurveLineBackgroundComponent(
        content = {
            Image(
                painter = painterResource(
                    id = R.drawable.empty_card
                ),
                contentDescription = null
            )
        }
    )
}