package sv.com.tarjetaone.presentation.compose.util.barcodescanner

import android.graphics.Rect
import androidx.camera.core.ImageAnalysis
import androidx.camera.mlkit.vision.MlKitAnalyzer
import androidx.camera.view.LifecycleCameraController
import androidx.camera.view.PreviewView
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.toComposeRect
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.google.mlkit.vision.barcode.BarcodeScanner
import com.google.mlkit.vision.barcode.BarcodeScannerOptions
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.barcode.common.Barcode
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.onEach
import sv.com.tarjetaone.presentation.compose.theme.customDimens

@OptIn(FlowPreview::class)
@Composable
fun BarcodeScannerPreview(
    modifier: Modifier = Modifier,
    onBarcodeScanned: (String) -> Unit
) {
    var barcode by remember { mutableStateOf<String?>(null) }
    var boundingRect by remember { mutableStateOf<Rect?>(null) }
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val cameraController = remember {
        LifecycleCameraController(context)
    }
    // To give the effect of the barcode being scanned (as it is almost instant)
    LaunchedEffect(barcode) {
        snapshotFlow { barcode }
            .filterNotNull()
            .debounce(SCAN_DEBOUNCE)
            .onEach {
                onBarcodeScanned(it)
            }
            .collect()
    }
    Box(
        modifier = Modifier
            .clipToBounds()
            .then(modifier)
    ) {
        AndroidView(
            modifier = Modifier.matchParentSize(),
            factory = { ctx ->
                PreviewView(ctx).apply {
                    // Configure barcode scanning options for supported formats
                    val options = BarcodeScannerOptions.Builder()
                        .setBarcodeFormats(
                            Barcode.FORMAT_CODE_39,
                            Barcode.FORMAT_CODE_93,
                            Barcode.FORMAT_CODE_128,
                            Barcode.FORMAT_EAN_8,
                            Barcode.FORMAT_EAN_13,
                            Barcode.FORMAT_UPC_A,
                            Barcode.FORMAT_UPC_E,
                            Barcode.FORMAT_ITF
                        )
                        .build()

                    val barcodeScanner: BarcodeScanner = BarcodeScanning.getClient(options)

                    // Set up the image analysis analyzer for barcode detection
                    cameraController.setImageAnalysisAnalyzer(
                        ContextCompat.getMainExecutor(ctx),
                        MlKitAnalyzer(
                            listOf(barcodeScanner),
                            ImageAnalysis.COORDINATE_SYSTEM_VIEW_REFERENCED,
                            ContextCompat.getMainExecutor(ctx)
                        ) { result: MlKitAnalyzer.Result? ->
                            val barcodeResult = result?.getValue(barcodeScanner)?.firstOrNull()
                            barcode = barcodeResult?.rawValue
                            boundingRect = barcodeResult?.boundingBox
                        }
                    )

                    // Bind the camera controller to the lifecycle owner
                    cameraController.bindToLifecycle(lifecycleOwner)

                    // Set the camera controller for the PreviewView
                    this.controller = cameraController
                }
            }
        )
        boundingRect?.toComposeRect()?.let {
            val borderWidth = MaterialTheme.customDimens.dimen1
            Canvas(modifier = Modifier.matchParentSize()) {
                drawRect(
                    color = Color.Red,
                    topLeft = Offset(it.left, it.top),
                    size = Size(it.width, it.height),
                    style = Stroke(width = borderWidth.toPx())
                )
            }
        }
    }
}

private const val SCAN_DEBOUNCE = 200L
