package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.THREE_VALUE
import sv.com.tarjetaone.common.utils.extensions.configCurrencyWithFractions
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.response.PointsTUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.MegaPointsStatus
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.formatDate
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.formatPoints
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.getPointsColor

@Composable
fun MegaPointsTransactionItem(
    modifier: Modifier = Modifier,
    pointTransactionData: PointsTUI,
    pointsStatus: MegaPointsStatus
) {
    val descTextStyle = MaterialTheme.typography.bodyMedium.copy(
        color = MaterialTheme.colorScheme.secondary,
        fontWeight = FontWeight.SemiBold
    )
    val detailStyle = MaterialTheme.typography.labelLarge.copy(
        color = MaterialTheme.customColors.secondaryLight,
        fontWeight = FontWeight.Medium
    )

    ElevatedCard(
        shape = MaterialTheme.shapes.medium,
        modifier = modifier,
        colors = CardDefaults.elevatedCardColors(containerColor = MaterialTheme.colorScheme.background),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.customDimens.dimen16),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Column(
                modifier = Modifier.weight(0.5f),
                horizontalAlignment = Alignment.Start,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = pointTransactionData.description.orEmpty(),
                    maxLines = THREE_VALUE,
                    textAlign = TextAlign.Start,
                    overflow = TextOverflow.Ellipsis,
                    style = descTextStyle,
                    modifier = Modifier.padding(end = MaterialTheme.customDimens.dimen8)
                )
                Text(
                    text = pointTransactionData.formatDate(),
                    style = detailStyle
                )
            }
            Column(
                modifier = Modifier.weight(0.25f),
                horizontalAlignment = Alignment.End,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = stringResource(
                        id = if (pointsStatus == MegaPointsStatus.Accumulated)
                            R.string.megaPoints_positive_points_label else R.string.megaPoints_negative_points_label,
                        pointTransactionData.formatPoints()
                    ),
                    textAlign = TextAlign.End,
                    style = descTextStyle,
                    color = pointsStatus.getPointsColor()
                )
                Text(
                    text = pointTransactionData.paymentAmount.orZero().configCurrencyWithFractions(),
                    style = detailStyle
                )
            }
        }
    }
}

@Preview
@Composable
private fun MegaPointsTransactionAccumulatedItemPreview() {
    OneAppTheme {
        MegaPointsTransactionItem(
            pointTransactionData = PointsTUI(
                type = "",
                pointsAmount = 550,
                valueDate = "09/05/2024",
                description = "Pago Burger King",
                paymentAmount = 8.50
            ),
            pointsStatus = MegaPointsStatus.Accumulated
        )
    }
}

@Preview
@Composable
private fun MegaPointsTransactionRedeemedItemPreview() {
    OneAppTheme {
        MegaPointsTransactionItem(
            pointTransactionData = PointsTUI(
                type = "",
                pointsAmount = 1000,
                valueDate = "09/05/2024",
                description = "Lorem ipsum dolor sit amet, consectetur adipiscing elit," +
                        " sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
                paymentAmount = 34.50
            ),
            pointsStatus = MegaPointsStatus.Redeemed
        )
    }
}
