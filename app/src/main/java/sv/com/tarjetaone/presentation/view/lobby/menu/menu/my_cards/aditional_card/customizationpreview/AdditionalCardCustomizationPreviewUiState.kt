package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.customizationpreview

import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.CardColor

data class AdditionalCardCustomizationPreviewUiState(
    val cardColors: CardColor = CardColor(),
    val nameOnCard: String = EMPTY_STRING,
    val deliveryDate: String = EMPTY_STRING
)