package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_activation.identity.preview

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.analytics.amplitude.AmplitudeEvents
import sv.com.tarjetaone.analytics.amplitude.AmplitudeManager
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.presentation.view.common.selfiepreview.SelfiePreviewViewModel
import javax.inject.Inject

@HiltViewModel
class CardActivationSelfiePreviewViewModel @Inject constructor(
    private val amplitudeManager: AmplitudeManager,
    imageUtils: ImageUtils,
    facephiResultHandler: FacephiResultHandler,
    savedStateHandle: SavedStateHandle
) : SelfiePreviewViewModel(imageUtils, facephiResultHandler) {
    private val args = CardActivationSelfiePreviewFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onTakeNewSelfieClick() {
        amplitudeManager.track(AmplitudeEvents.ON_TAKE_PICTURE_ACTIVATION_EVENT)
        super.onTakeNewSelfieClick()
    }

    override fun onContinueClick() {
        sendEvent(
            UiEvent.Navigate(
                CardActivationSelfiePreviewFragmentDirections
                    .actionSelfieConfirmationCardActivationToIdentityValidationCardActivationFragment(args.cardId)
            )
        )
    }
}
