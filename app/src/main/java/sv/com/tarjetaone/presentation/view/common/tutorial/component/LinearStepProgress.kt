package sv.com.tarjetaone.presentation.view.common.tutorial.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens

@Composable
fun LinearStepProgress(
    modifier: Modifier = Modifier,
    currentStep: Int,
    totalSteps: Int,
    filledColor: Color = MaterialTheme.colorScheme.primary,
    unfilledColor: Color = MaterialTheme.customColors.secondarySoft,
    fillPreviousSteps: Boolean = true,
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen8),
    ) {
        repeat(totalSteps) { index ->
            Box(
                modifier = Modifier
                    .height(MaterialTheme.customDimens.dimen6)
                    .clip(MaterialTheme.shapes.large)
                    .weight(ONE_FLOAT_VALUE)
                    .background(
                        if ((index < currentStep.minus(ONE_VALUE) && fillPreviousSteps)
                            || index == currentStep.minus(ONE_VALUE)
                        ) {
                            filledColor
                        } else {
                            unfilledColor
                        }
                    )
            )
        }
    }
}

@Preview
@Composable
private fun LinearStepProgressPreview() {
    OneAppTheme {
        LinearStepProgress(
            modifier = Modifier.fillMaxWidth(),
            currentStep = 2,
            totalSteps = 5,
        )
    }
}