package sv.com.tarjetaone.presentation.view.lobby.menu.menu.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.domain.entities.alerts.AlertBodyUI
import sv.com.tarjetaone.domain.entities.alerts.AlertCloseConfigUI
import sv.com.tarjetaone.domain.entities.alerts.AlertStyleUI
import sv.com.tarjetaone.domain.entities.alerts.AlertTypeUI
import sv.com.tarjetaone.domain.entities.alerts.AlertUI
import sv.com.tarjetaone.presentation.compose.modifier.shimmerEffect
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.util.ShimmerSize
import sv.com.tarjetaone.presentation.compose.util.ShimmerStyle
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer4
import sv.com.tarjetaone.presentation.compose.util.toColor
import sv.com.tarjetaone.presentation.helpers.drawableResFromString

@Composable
fun AlertBanner(
    modifier: Modifier = Modifier,
    alert: AlertUI,
    onClick: () -> Unit,
    onCloseClick: (() -> Unit)? = null
) {
    with(alert) {
        val (icon, title, description) = body ?: AlertBodyUI()
        val (backgroundColor, contentColor) = style ?: AlertStyleUI()
        Column(
            modifier = modifier
                .height(MaterialTheme.customDimens.dimen74)
                .clip(MaterialTheme.shapes.large)
                .background(
                    color = backgroundColor
                        ?.toColor(MaterialTheme.customColors.trackingBanner)
                        ?: MaterialTheme.customColors.trackingBanner
                )
                .clickable { onClick() }
        ) {
            Row(
                modifier = Modifier.padding(
                    horizontal = MaterialTheme.customDimens.dimen16,
                    vertical = MaterialTheme.customDimens.dimen16
                ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    modifier = Modifier.size(
                        height = MaterialTheme.customDimens.dimen40,
                        width = MaterialTheme.customDimens.dimen30
                    ),
                    imageVector = ImageVector.vectorResource(
                        id = drawableResFromString(icon.orEmpty())
                    ),
                    contentDescription = null
                )
                Spacer16()
                Column(
                    modifier = Modifier.weight(ONE_FLOAT_VALUE)
                ) {
                    Text(
                        text = title.orEmpty(),
                        style = MaterialTheme.typography.bodySmall.copy(
                            lineHeight = MaterialTheme.customDimensSp.sp14,
                            fontWeight = FontWeight.SemiBold,
                            color = contentColor
                                ?.toColor(MaterialTheme.customColors.bannerText)
                                ?: MaterialTheme.customColors.bannerText
                        )
                    )
                    Spacer4()
                    Text(
                        text = description.orEmpty(),
                        style = MaterialTheme.typography.labelMedium.copy(
                            lineHeight = MaterialTheme.customDimensSp.sp12,
                            color = contentColor
                                ?.toColor(MaterialTheme.customColors.bannerText)
                                ?: MaterialTheme.customColors.bannerText
                        )
                    )
                }
                Spacer16()
                if (closeConfig?.showButton ?: false) {
                    Icon(
                        modifier = Modifier
                            .size(MaterialTheme.customDimens.dimen16)
                            .clip(CircleShape)
                            .clickable { onCloseClick?.invoke() },
                        imageVector = ImageVector.vectorResource(id = R.drawable.ic_close),
                        tint = MaterialTheme.colorScheme.secondary,
                        contentDescription = null
                    )
                    Spacer4()
                }
            }
        }
    }
}

@Composable
fun CustomAlertBannerSkeleton(
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(MaterialTheme.customDimens.dimen74)
            .padding(horizontal = MaterialTheme.customDimens.dimen24)
            .clip(shape = MaterialTheme.shapes.large)
            .background(
                color = MaterialTheme.customColors.gray300,
            )
            .shimmerEffect(
                shape = MaterialTheme.shapes.large,
                shimmerSize = ShimmerSize.MEDIUM,
                shimmerStyle = ShimmerStyle.LIGHT_BACKGROUND
            )
    )
}

@Preview
@Composable
private fun AlertBannerPreview() {
    OneAppTheme {
        AlertBanner(
            modifier = Modifier,
            alert = AlertUI(
                id = "alert_12345",
                code = "TRACKING",
                type = AlertTypeUI.BANNER,
                cCardId = 1,
                customerId = 2,
                isAdditionalCard = false,
                body = AlertBodyUI(
                    icon = "tarjeta-visa",
                    title = "Sample Banner",
                    description = "This is a sample banner description."
                ),
                closeConfig = AlertCloseConfigUI(
                    allowGesture = true,
                    showButton = true,
                    canRemove = false
                ),
                style = AlertStyleUI(
                    backgroundColor = "#FFEBEE",
                    contentColor = "#B71C1C"
                )
            ),
            onClick = { }
        )
    }
}
