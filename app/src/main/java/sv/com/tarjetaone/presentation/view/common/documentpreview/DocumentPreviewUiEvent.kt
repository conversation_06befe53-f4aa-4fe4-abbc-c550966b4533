package sv.com.tarjetaone.presentation.view.common.documentpreview

import com.facephi.fphiselphidwidgetcore.WidgetSelphIDResult

sealed class DocumentPreviewUiEvent {
    data object OnStart : DocumentPreviewUiEvent()
    data object OnBackClick : DocumentPreviewUiEvent()
    data object OnTwilioClick : DocumentPreviewUiEvent()
    data object OnContinueClick : DocumentPreviewUiEvent()
    data object OnRetakeClick : DocumentPreviewUiEvent()
    data class OnDocumentCaptured(val result: WidgetSelphIDResult) : DocumentPreviewUiEvent()
    data class OnDocumentCaptureFailed(val result: WidgetSelphIDResult) : DocumentPreviewUiEvent()
}
