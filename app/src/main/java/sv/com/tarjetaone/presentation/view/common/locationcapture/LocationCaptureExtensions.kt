package sv.com.tarjetaone.presentation.view.common.locationcapture

import android.location.Address
import sv.com.tarjetaone.common.utils.AppConstants.COMMA_WITH_SPACE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.core.utils.extensions.takeIfNotEmpty

fun Address.getAddressString(): String? {
    return buildString {
        thoroughfare?.let { append(it).append(COMMA_WITH_SPACE) }
        subLocality?.let { append(it).append(COMMA_WITH_SPACE) }
        locality?.let { append(it) }
    }.takeIfNotEmpty() ?: getAddressLine(ZERO_VALUE)
}
