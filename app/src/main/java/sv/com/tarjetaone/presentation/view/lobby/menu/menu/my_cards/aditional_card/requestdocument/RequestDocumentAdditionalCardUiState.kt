package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.requestdocument

import androidx.annotation.StringRes
import sv.com.tarjetaone.R

data class RequestDocumentAdditionalCardPreviewUiState(
    val isContinueButtonEnabled: Boolean = false,
    val isDialogVisible: Boolean = false,
    val selectedOption: AnswerOption? = null
)

enum class AnswerOption(@StringRes val label: Int) {
    YES(R.string.yes),
    NO(R.string.no)
}