package sv.com.tarjetaone.presentation.view.common.survey.base

import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.request.SaveSurveyRequestUI
import sv.com.tarjetaone.domain.entities.request.SurveyAnswerUI
import sv.com.tarjetaone.domain.entities.request.SurveyType
import sv.com.tarjetaone.domain.entities.response.SurveyResponseUI
import sv.com.tarjetaone.domain.usecases.survey.GetSurveyByNameUseCase
import sv.com.tarjetaone.domain.usecases.survey.SaveSurveyUseCase

/**
 * This ViewModel serves as the parent for all child survey ViewModels. Its purpose is to centralize
 * shared logic, such as saving survey options or fetching them by type. This approach eliminates the
 * need to duplicate the same logic in each child ViewModel. Each survey screen corresponds to a specific
 * type, triggering consistent API endpoints and expecting a uniform data object in response. This design
 * choice streamlines the codebase by preventing redundancy and promoting maintainability.
 */
abstract class BaseSurveyViewModel(
    private val getSurveyByNameUseCase: GetSurveyByNameUseCase,
    private val saveSurveyUseCase: SaveSurveyUseCase
) : BaseViewModel() {
    private var surveyId: Int = ZERO_VALUE

    /**
     * get the survey options by type on each survey screen.
     * @param surveyType to determine from which screen we need to fetch the right survey options for each screen.
     * @param onSurveyFetched to delegate the success response to the child ViewModel.
     */
    protected fun getSurveyByType(
        surveyType: SurveyType,
        onSurveyFetched: (SurveyResponseUI) -> Unit
    ) {
        viewModelScope.launch {
            sendEvent(SideEffect.Loading(true))
            getSurveyByNameUseCase(surveyType).executeUseCase { response ->
                surveyId = response.data.id.orZero()
                onSurveyFetched(response)
                sendEvent(SideEffect.Loading(false))
            }
        }
    }

    /**
     * Make the send survey request on each survey screen.
     * @param surveyType to determine from which screen we need to send the survey options to the API.
     */
    protected fun sendSurveyByType(
        surveyType: SurveyType?,
        surveyAnswers: List<SurveyAnswerUI>,
        onSurveySent: () -> Unit
    ) {
        sendEvent(SideEffect.Loading(true))
        val request = SaveSurveyRequestUI(
            answers = surveyAnswers.filter { it.surveyType == surveyType }
                .map {
                    SurveyAnswerUI(
                        optionId = it.optionId.orZero(),
                        other = it.other
                    )
                }
        )
        viewModelScope.launch {
            saveSurveyUseCase(
                surveyId,
                request
            ).executeUseCase {
                sendEvent(SideEffect.Loading(false))
                onSurveySent()
            }
        }
    }
}
