package sv.com.tarjetaone.presentation.view.lobby.menu.menu.account_deletion.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withLink
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.HyperLinkTextButton
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer24

@Composable
fun DeleteAccountScreen(
    description: AnnotatedString,
    primaryButtonText: String,
    hyperLinkButtonText: String,
    onEvent: (DeleteAccountUiEvent) -> Unit
) {
    ScreenWithTopAppBar(
        isRightButtonVisible = false,
        title = stringResource(R.string.cancel_account),
        onLeftButtonClick = {
            onEvent(DeleteAccountUiEvent.OnBackClick)
        },
        onRightButtonClick = {}
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = MaterialTheme.customDimens.dimen32)
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer24()
            Image(
                imageVector = ImageVector.vectorResource(id = R.drawable.ic_account_deletion),
                contentDescription = stringResource(id = R.string.cancel_account)
            )
            Spacer16()
            Text(
                text = stringResource(id = R.string.delete_account_confirmation_title),
                style = MaterialTheme.typography.bodyLarge.copy(
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center
                )
            )
            Spacer16()
            Text(
                text = description,
                style = MaterialTheme.typography.bodyMedium.copy(
                    color = MaterialTheme.customColors.gray700
                )
            )
            Spacer16()
            Spacer1f()
            OneButton(
                modifier = Modifier.fillMaxWidth(),
                text = primaryButtonText,
                onClick = {
                    onEvent(DeleteAccountUiEvent.OnPrimaryButtonClick)
                }
            )
            HyperLinkTextButton(
                text = hyperLinkButtonText
            ) {
                onEvent(DeleteAccountUiEvent.OnSecondaryButtonClick)
            }
        }
    }
}

@Preview(
    showBackground = true,
    showSystemUi = true
)
@Composable
fun DeleteAccountInformationScreenPreview() {
    OneAppTheme {
        val annotatedString = buildAnnotatedString {
            append(stringResource(id = R.string.delete_account_description_menu))
            withLink(
                LinkAnnotation.Url(
                    url = "",
                    styles = TextLinkStyles(
                        style = SpanStyle(
                            color = MaterialTheme.colorScheme.primary,
                            textDecoration = TextDecoration.Underline
                        )
                    )
                )
            ) {
                append(stringResource(id = R.string.delete_account_description_continuation))
            }
        }
        DeleteAccountScreen(
            description = annotatedString,
            primaryButtonText = stringResource(id = R.string.understood_label),
            hyperLinkButtonText = stringResource(id = R.string.cancel_my_one_product),
            onEvent = { }
        )
    }
}
