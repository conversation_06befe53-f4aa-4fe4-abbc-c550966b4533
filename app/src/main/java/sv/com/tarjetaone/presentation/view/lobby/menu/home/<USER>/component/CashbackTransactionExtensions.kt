package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.component

import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.domain.entities.response.CashbackTUI

fun CashbackTUI.getDateFormatted(): String {
    return valueDate?.getFormattedDateFromTo(
        AppConstants.DAY_MONTH_YEAR_WITH_SLASH,
        AppConstants.DAY_OF_FULL_MONTH_NAME_OF_YEAR_WITH_SPACES
    ).orEmpty()
}
