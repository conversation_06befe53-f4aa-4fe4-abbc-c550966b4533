package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.report.report_purchase

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.DAY_OF_WEEK_DAY_MONTH_YEAR_WITH_SPACES
import sv.com.tarjetaone.domain.entities.response.TransactionsUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.button.HyperLinkTextButton
import sv.com.tarjetaone.presentation.compose.uicomponent.button.SolidLargeButton
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.MultiOptionDialog
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.Spacer12
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.helpers.capitalizeAllWords
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component.getAmountFormatted
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component.getCardNumberHidden
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component.getDateFormatted

@Composable
fun ReportPurchaseScreen(
    uiState: ReportPurchaseUIState,
    onEvent: (ReportPurchaseUiEvent) -> Unit
) {
    if (uiState.dynamicDialogVisibility) {
        MultiOptionDialog(
            title = stringResource(R.string.report_dialog_title),
            subTitle = stringResource(R.string.report_dialog_subtitle),
            dynamicButtons = uiState.reportDialogList
        ) { id ->
            onEvent(ReportPurchaseUiEvent.OnReportUnrecognizedPurchaseClick(id))
        }
    }
    ReportPurchaseContent(
        uiState = uiState,
        onEvent = onEvent
    )
}

@Composable
fun ReportPurchaseContent(
    uiState: ReportPurchaseUIState,
    onEvent: (ReportPurchaseUiEvent) -> Unit
) {
    LaunchedEffect(Unit) {
        onEvent(ReportPurchaseUiEvent.OnStart)
    }

    ScreenWithTopAppBar(
        onLeftButtonClick = { onEvent(ReportPurchaseUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(ReportPurchaseUiEvent.OnSupportClick) },
        title = stringResource(id = R.string.unrecognized_purchase)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .background(MaterialTheme.colorScheme.surface)
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            Text(
                textAlign = TextAlign.Center,
                text = stringResource(
                    id = R.string.authorization_number,
                    uiState.transaction.authorizationCode.orEmpty()
                ),
                style = MaterialTheme.typography.titleSmall,
                modifier = Modifier.fillMaxWidth()
            )
            Card(
                shape = MaterialTheme.shapes.extraLarge,
                colors = CardDefaults.cardColors(containerColor = Color.White),
                modifier = Modifier.padding(vertical = MaterialTheme.customDimens.dimen25)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = MaterialTheme.customDimens.dimen24),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        textAlign = TextAlign.Center,
                        text = uiState.transaction.description.orEmpty().uppercase(),
                        style = MaterialTheme.typography.labelMedium.copy(
                            color = MaterialTheme.colorScheme.secondary,
                            fontWeight = FontWeight.SemiBold
                        )
                    )
                    Text(
                        textAlign = TextAlign.Center,
                        text = uiState.transaction
                            .getDateFormatted(DAY_OF_WEEK_DAY_MONTH_YEAR_WITH_SPACES)
                            .uppercase(),
                        style = MaterialTheme.typography.labelMedium.copy(
                            color = MaterialTheme.colorScheme.secondary,
                            fontWeight = FontWeight.SemiBold
                        ),
                        modifier = Modifier.padding(bottom = MaterialTheme.customDimens.dimen15)
                    )
                    Divider(
                        modifier = Modifier.padding(horizontal = MaterialTheme.customDimens.dimen40),
                        thickness = MaterialTheme.customDimens.dimen1,
                        color = MaterialTheme.customColors.gray200
                    )
                    Spacer16()
                    Text(
                        textAlign = TextAlign.Center,
                        text = uiState.transaction.getAmountFormatted().capitalizeAllWords(),
                        style = MaterialTheme.typography.displayLarge.copy(
                            color = MaterialTheme.colorScheme.secondary
                        )
                    )
                }
            }
            Spacer12()
            Text(
                textAlign = TextAlign.Center,
                text = stringResource(id = R.string.credit_card_used_for_this_purchase),
                style = MaterialTheme.typography.titleMedium.copy(
                    fontSize = MaterialTheme.customDimensSp.sp14
                ),
                modifier = Modifier.fillMaxWidth()
            )
            Card(
                shape = MaterialTheme.shapes.medium,
                modifier = Modifier
                    .padding(
                        horizontal = MaterialTheme.customDimens.dimen30,
                        vertical = MaterialTheme.customDimens.dimen2
                    )
                    .shadow(MaterialTheme.customDimens.dimen2, MaterialTheme.shapes.medium),
                colors = CardDefaults.cardColors(containerColor = Color.White)
            ) {
                Row(
                    modifier = Modifier
                        .height(MaterialTheme.customDimens.dimen100)
                        .fillMaxWidth()
                        .padding(
                            horizontal = MaterialTheme.customDimens.dimen15,
                            vertical = MaterialTheme.customDimens.dimen8
                        ),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_visa_one),
                        modifier = Modifier.size(MaterialTheme.customDimens.dimen56),
                        contentDescription = stringResource(id = R.string.unrecognized_purchase_visa),
                    )
                    Spacer16()
                    Column {
                        Text(
                            text = stringResource(id = R.string.unrecognized_purchase_visa),
                            style = MaterialTheme.typography.bodySmall
                        )
                        Text(
                            text = uiState.transaction.getCardNumberHidden(),
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
            }
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.customDimens.dimen30)
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_info_2),
                    contentDescription = null
                )
                Spacer8()
                Text(
                    text = stringResource(id = R.string.unrecognized_purchase_warning),
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.customColors.gray700
                )
            }
            Spacer1f()
            Text(
                textAlign = TextAlign.Center,
                text = stringResource(id = R.string.unrecognized_purchase_report),
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = MaterialTheme.customDimens.dimen64)
            )
            Spacer8()
            SolidLargeButton(
                modifier = Modifier.padding(horizontal = MaterialTheme.customDimens.dimen24),
                text = stringResource(id = R.string.report_purchase_and_block),
                onClick = {
                    onEvent(ReportPurchaseUiEvent.OnContinueClick)
                },
            )
            Spacer8()
            HyperLinkTextButton(
                modifier = Modifier
                    .padding(horizontal = MaterialTheme.customDimens.dimen24)
                    .fillMaxWidth(),
                text = stringResource(id = R.string.dont_report_purchase_and_block),
                textStyle = MaterialTheme.typography.labelSmall.copy(
                    fontSize = MaterialTheme.customDimensSp.sp13,
                    lineHeight = MaterialTheme.customDimensSp.sp14,
                    textDecoration = TextDecoration.Underline
                ),
                onClick = {
                    onEvent(ReportPurchaseUiEvent.OnNotReportClick)
                }
            )
        }
    }
}

@Composable
@Preview
fun ReportPurchaseScreenPreview() {
    OneAppTheme {
        ReportPurchaseContent(
            ReportPurchaseUIState(
                transaction = TransactionsUI(
                    authorizationCode = "placeholder",
                    description = "pago uber",
                    valueDate = "07/24/2024",
                    amount = 15.00,
                    panMasked = "1234 **** **** 1234",
                    installmentTerms = listOf()
                )
            ), onEvent = {}
        )
    }
}
