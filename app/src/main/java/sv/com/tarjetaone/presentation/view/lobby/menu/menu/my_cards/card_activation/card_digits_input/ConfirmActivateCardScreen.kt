package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_activation.card_digits_input

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.platform.SoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.common.utils.CardColor
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.personalization.CARD_FRONT_PAGE
import sv.com.tarjetaone.presentation.compose.uicomponent.personalization.CreditCardComponent
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.compose.util.Spacer4
import sv.com.tarjetaone.presentation.compose.util.Spacer64
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_activation.card_digits_input.CardDigitsInputViewModel.Companion.DIGITS_LENGTH

@Composable
fun ConfirmActivateCardScreen(
    viewModel: CardDigitsInputViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    ConfirmActivateCardContent(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@Composable
fun ConfirmActivateCardContent(
    uiState: ConfirmActivateCardUiState,
    onEvent: (ConfirmActivateCardEvent) -> Unit
) {
    val pagerState = rememberPagerState(
        initialPage = CARD_FRONT_PAGE,
        pageCount = { TWO_VALUE }
    )
    val keyboardController = LocalSoftwareKeyboardController.current
    LaunchedEffect(Unit) {
        onEvent(ConfirmActivateCardEvent.OnStart)
    }
    ScreenWithTopAppBar(
        isLeftButtonVisible = false,
        title = stringResource(id = R.string.card_verification),
        onLeftButtonClick = { },
        onRightButtonClick = { onEvent(ConfirmActivateCardEvent.OnTwilioIconClicked) },
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = MaterialTheme.customDimens.dimen32)
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer32()
            uiState.cardColor?.let {
                CreditCardComponent(
                    pagerState = pagerState,
                    cardColors = uiState.cardColor,
                    nameOnCard = uiState.nameOnCard,
                    isClickable = true
                )
            }
            Spacer64()
            CreditCardDigitsInput(
                uiState = uiState,
                onEvent = onEvent,
                keyboardController = keyboardController,
            )
            Spacer16()
            OneButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = MaterialTheme.customDimens.dimen32),
                text = stringResource(id = R.string.continue_button_label),
                enabled = uiState.areDigitsValid,
                onClick = { onEvent(ConfirmActivateCardEvent.OnContinueButtonClicked) }
            )
        }
    }
}

@Composable
fun CreditCardDigitsInput(
    uiState: ConfirmActivateCardUiState,
    onEvent: (ConfirmActivateCardEvent) -> Unit,
    keyboardController: SoftwareKeyboardController? = null
) {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(id = R.string.enter_last_card_digits),
            style = MaterialTheme.typography.bodyMedium.copy(
                color = MaterialTheme.customColors.bodyLightGray,
                textAlign = TextAlign.Center
            )
        )
        Spacer4()
        SimpleElevatedTextField(
            modifier = Modifier.fillMaxWidth(),
            textFieldAlignment = Alignment.Center,
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Number,
                imeAction = ImeAction.Done
            ),
            keyboardActions = KeyboardActions(
                onDone = { keyboardController?.hide() }
            ),
            placeholder = stringResource(id = R.string.card_digits_placeholder),
            value = uiState.cardDigits,
            onValueChange = { digits ->
                if (digits.length <= DIGITS_LENGTH) {
                    onEvent(ConfirmActivateCardEvent.OnDigitsChanged(digits))
                }
            }
        )
    }
}

@Composable
@Preview(
    showSystemUi = true,
    showBackground = true
)
fun ConfirmActivateCardScreenPreview() {
    OneAppTheme {
        ConfirmActivateCardContent(
            uiState = ConfirmActivateCardUiState(
                cardColor = CardColor(
                    colors = listOf("#000000"),
                    textColor = "#FFFFFF",
                    iconColor = "#FFFFFF"
                ),
                nameOnCard = "John Doe",
            ),
            onEvent = { }
        )
    }
}
