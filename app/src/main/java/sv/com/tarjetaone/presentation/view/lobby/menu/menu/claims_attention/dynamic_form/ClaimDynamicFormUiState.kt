package sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.dynamic_form

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.dynamic_form.util.VariableFieldUiState

data class ClaimDynamicFormUiState(
    val claimOptionName: String = "",
    val variableFields: SnapshotStateList<VariableFieldUiState> = mutableStateListOf(),
    val claimDescription: String = "",
    val usageInfo: String? = null,
    val canSend: Boolean = false
)
