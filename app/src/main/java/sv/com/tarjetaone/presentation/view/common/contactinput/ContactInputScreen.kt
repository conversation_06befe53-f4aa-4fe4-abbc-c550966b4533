package sv.com.tarjetaone.presentation.view.common.contactinput

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.response.ContactType
import sv.com.tarjetaone.domain.entities.response.OtpMethod
import sv.com.tarjetaone.domain.entities.response.OtpType
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.InfoLabel
import sv.com.tarjetaone.presentation.compose.uicomponent.common.PhoneTextField
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f

@Composable
fun ContactInputScreen(
    contactType: ContactType,
    viewModel: ContactInputBaseViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    SideEffectHandler(viewModel.sideEffects) {
        ContactInputScreen(
            contactType = contactType,
            uiState = uiState,
            onEvent = viewModel::onEvent
        )
    }
}

@Composable
fun ContactInputScreen(
    contactType: ContactType,
    uiState: ContactInputUiState,
    onEvent: (ContactInputUiEvent) -> Unit
) {
    ScreenWithTopAppBar(
        onLeftButtonClick = { onEvent(ContactInputUiEvent.OnBackClick(contactType)) },
        onRightButtonClick = { onEvent(ContactInputUiEvent.OnSupportClick) },
        contentPadding = PaddingValues(MaterialTheme.customDimens.dimen24),
        isProgressbarVisible = true,
        progress = 0.5f,
        title = stringResource(
            id = if (uiState.contactType == ContactType.Phone) {
                R.string.phone_number
            } else {
                R.string.email_address
            }
        ),
    ) {
        ContactInputContent(
            contactType = contactType,
            uiState = uiState,
            onEvent = onEvent,
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = MaterialTheme.customDimens.dimen24)
        )
    }
}

@Composable
fun ContactInputContent(
    modifier: Modifier = Modifier,
    contactType: ContactType,
    uiState: ContactInputUiState,
    onEvent: (ContactInputUiEvent) -> Unit
) {
    LaunchedEffect(Unit) {
        onEvent(ContactInputUiEvent.OnStart(contactType))
    }
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Image(
            imageVector = ImageVector.vectorResource(
                id = if (uiState.contactType == ContactType.Phone) {
                    R.drawable.ic_cellphone
                } else {
                    R.drawable.ic_envelope
                }
            ),
            contentDescription = null
        )
        Spacer16()
        if (uiState.contactType == ContactType.Phone) {
            PhoneTextField(
                modifier = Modifier.fillMaxWidth(),
                value = uiState.contact,
                onValueChange = { onEvent(ContactInputUiEvent.OnPhoneChange(it)) },
                placeholder = stringResource(id = R.string.phone_number_placeholder),
                label = stringResource(id = R.string.type_phone_number_label),
                hasError = !uiState.formIsValid,
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Phone,
                    imeAction = ImeAction.Done
                )
            )
        } else {
            SimpleElevatedTextField(
                modifier = Modifier.fillMaxWidth(),
                value = uiState.contact,
                onValueChange = { onEvent(ContactInputUiEvent.OnEmailChange(it)) },
                placeholder = stringResource(id = R.string.hint_email_address),
                label = stringResource(id = R.string.type_your_email_address),
                hasError = !uiState.formIsValid,
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Email,
                    imeAction = ImeAction.Done
                )
            )
        }
        uiState.validationError.message?.let { message ->
            Spacer16()
            InfoLabel(
                text = message.asString(),
                textColor = MaterialTheme.colorScheme.error,
                iconResId = R.drawable.ic_exclamation_outline,
                modifier = Modifier.align(Alignment.Start)
            )
        }
        Spacer1f()
        if (uiState.contactType == ContactType.Phone) {
            if (uiState.showPhoneActions) {
                Text(
                    text = stringResource(id = R.string.choose_otp_method),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                Spacer16()
                OneButton(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(id = R.string.otp_whatsapp),
                    onClick = {
                        onEvent(
                            ContactInputUiEvent.OnSelectOtpMethod(
                                OtpType.PHONE,
                                OtpMethod.WHATSAPP
                            )
                        )
                    },
                    enabled = uiState.formIsValid
                )
                Spacer16()
                OneButton(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(id = R.string.otp_sms),
                    buttonVariant = ButtonVariant.SECONDARY_VARIANT,
                    onClick = {
                        onEvent(ContactInputUiEvent.OnSelectOtpMethod(OtpType.PHONE, OtpMethod.SMS))
                    },
                    enabled = uiState.formIsValid
                )
            }
        } else {
            OneButton(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(id = R.string.continue_button_label),
                onClick = {
                    onEvent(ContactInputUiEvent.OnSelectOtpMethod(OtpType.EMAIL, OtpMethod.EMAIL))
                },
                enabled = uiState.formIsValid
            )
        }
        Spacer16()
    }
}

@Preview(showBackground = true)
@Composable
private fun ContactInputContentPreview() {
    OneAppTheme {
        ContactInputScreen(
            contactType = ContactType.Phone,
            uiState = ContactInputUiState(),
            onEvent = {}
        )
    }
}

@Preview
@Composable
private fun ContactInputScreenPreview() {
    OneAppTheme {
        ContactInputScreen(
            contactType = ContactType.Phone,
            uiState = ContactInputUiState(
                validationError = ContactValidationError.InvalidFormat(ContactType.Phone)
            ),
            onEvent = {}
        )
    }
}
