package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.cardblock

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI
import sv.com.tarjetaone.domain.entities.response.CreditCardStatus.Active
import sv.com.tarjetaone.domain.entities.response.CustomerCCardType.MAIN
import sv.com.tarjetaone.domain.entities.response.CustomerCCardUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.poppinsFamily
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.card.EmptyStateCardVariant
import sv.com.tarjetaone.presentation.compose.uicomponent.card.OneAppSmallCardAlert
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedDropdown
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.EmptyStateScreen
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ActionNotAllowedDescription
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant.TERTIARY_VARIANT
import sv.com.tarjetaone.presentation.compose.util.Spacer32

@Composable
fun CardBlockScreen(
    viewModel: CardBlockViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    SideEffectHandler(viewModel.sideEffects) {
        CardBlockScreenContent(
            uiState = uiState,
            onUiEvent = viewModel::onScreenUiEvent
        )
    }
}

@Composable
fun CardBlockScreenContent(
    uiState: CardBlockUiState,
    onUiEvent: (CardBlockUiEvent) -> Unit
) {
    LaunchedEffect(Unit) {
        onUiEvent(CardBlockUiEvent.OnStart)
    }
    ScreenWithTopAppBar(
        onLeftButtonClick = { onUiEvent(CardBlockUiEvent.OnBackClick) },
        onRightButtonClick = { onUiEvent(CardBlockUiEvent.OnTwilioClick) },
        title = uiState.screenTitle?.let { stringResource(id = it) },
    ) {
        if (uiState.canBlockCards) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.SpaceBetween
            ) {
                ActiveCardsContent(
                    modifier = Modifier
                        .padding(horizontal = MaterialTheme.customDimens.dimen32),
                    uiState = uiState,
                    onUiEvent = onUiEvent
                )
            }
        } else {
            EmptyStateScreen(
                primaryActionText = stringResource(id = R.string.go_back),
                onPrimaryAction = { onUiEvent(CardBlockUiEvent.OnBackClick) },
                variant = EmptyStateCardVariant.SECONDARY,
                contentDescription = { ActionNotAllowedDescription() }
            )
        }
    }
}

@Composable
fun CardBlockHeader(
    uiState: CardBlockUiState,
    onUiEvent: (CardBlockUiEvent) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth()
    ) {
        OneAppSmallCardAlert(
            modifier = Modifier
                .width(MaterialTheme.customDimens.dimen70)
                .height(MaterialTheme.customDimens.dimen92)
        )
        Column {
            SimpleElevatedDropdown(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        start = MaterialTheme.customDimens.dimen12,
                        top = MaterialTheme.customDimens.dimen4
                    ),
                items = uiState.cardList,
                itemLabel = {
                    stringResource(
                        id = R.string.card_num_masked,
                        it.creditCardMask
                    )
                },
                value = uiState.selectedCard,
                onValueChange = {
                    onUiEvent(CardBlockUiEvent.OnCardChange(it))
                },
            )
            Text(
                modifier = Modifier.padding(
                    start = MaterialTheme.customDimens.dimen25,
                    top = MaterialTheme.customDimens.dimen8
                ),
                style = MaterialTheme.typography.bodyMedium.copy(
                    color = MaterialTheme.customColors.bodyLightGray
                ),
                fontFamily = poppinsFamily,
                text = buildAnnotatedString {
                    append(stringResource(id = R.string.expiration_date_label))
                    withStyle(
                        style = SpanStyle(
                            fontWeight = FontWeight.SemiBold,
                            color = MaterialTheme.customColors.onHeadingTextDark
                        )
                    ) {
                        append(
                            uiState.selectedCard?.expirationDate
                                ?: stringResource(id = R.string.not_applicable)
                        )
                    }
                },
            )
        }
    }
}

@Composable
fun ActiveCardsContent(
    modifier: Modifier = Modifier,
    uiState: CardBlockUiState,
    onUiEvent: (CardBlockUiEvent) -> Unit
) {
    Column(modifier = modifier) {
        CardBlockHeader(
            uiState = uiState,
            onUiEvent = onUiEvent
        )
        LazyColumn(
            horizontalAlignment = Alignment.CenterHorizontally,
            contentPadding = PaddingValues(
                top = MaterialTheme.customDimens.dimen40,
                bottom = MaterialTheme.customDimens.dimen24
            ),
        ) {
            item {
                Image(
                    modifier = Modifier.width(MaterialTheme.customDimens.dimen175),
                    imageVector = ImageVector.vectorResource(id = R.drawable.cardlockimg),
                    contentDescription = null
                )
            }
            item {
                Spacer32()
            }
            items(uiState.blockReasonList) { cardBlockReason ->
                OneButton(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = MaterialTheme.customDimens.dimen16),
                    text = cardBlockReason.name.orEmpty(),
                    buttonVariant = TERTIARY_VARIANT,
                    onClick = {
                        onUiEvent(CardBlockUiEvent.OnSelectedBlockReason(cardBlockReason))
                    }
                )
            }
        }
    }
}

@Preview(showSystemUi = true)
@Composable
private fun CardBlockScreenContentPreview() {
    OneAppTheme {
        CardBlockScreenContent(
            CardBlockUiState(
                selectedCard = CustomerCCardUI(
                    creditCardMask = "5462 **** **** 5433",
                    creditCardId = 0,
                    type = "",
                    creditCardStatus = "",
                    creditCardLabelDetail = "Mario Perez - Activa",
                    expirationDate = "12/24",
                    cCardType = MAIN,
                    cCardStatus = Active
                ),
                blockReasonList = listOf(
                    CatalogItemsCollectionUI(
                        id = "0",
                        isActive = false,
                        name = "Fraude",
                        parentId = "",
                        behaviourInfo = ""
                    ),
                    CatalogItemsCollectionUI(
                        id = "0",
                        isActive = false,
                        name = "Robo",
                        parentId = "",
                        behaviourInfo = ""
                    ),
                    CatalogItemsCollectionUI(
                        id = "0",
                        isActive = false,
                        name = "Pérdida",
                        parentId = "",
                        behaviourInfo = ""
                    ),
                    CatalogItemsCollectionUI(
                        id = "0",
                        isActive = false,
                        name = "Daño en tarjeta",
                        parentId = "",
                        behaviourInfo = ""
                    )
                )
            ),
            onUiEvent = { }
        )
    }
}
