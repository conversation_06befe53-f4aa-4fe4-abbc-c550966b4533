package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform.affiliate

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.entities.request.automaticpayments.paymentaffiliation.contract.ContractAffiliationAction
import sv.com.tarjetaone.domain.usecases.automaticpayments.paymentaffiliation.affiliationform.NotificationChannelsUseCase
import sv.com.tarjetaone.domain.usecases.automaticpayments.servicespayment.invoiceselection.infoproducts.InfoProductsUseCase
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform.AffiliationFormViewModel
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.invoiceselection.util.InvoicePXFieldUiState
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.invoiceselection.util.getInitialValue

@HiltViewModel
class AffiliateServiceViewModel @Inject constructor(
    infoProductsUseCase: InfoProductsUseCase,
    notificationChannelsUseCase: NotificationChannelsUseCase,
    savedStateHandle: SavedStateHandle
) : AffiliationFormViewModel(
    infoProductsUseCase,
    notificationChannelsUseCase
) {

    //Stateless
    private val args = AffiliateServiceFragmentArgs.fromSavedStateHandle(savedStateHandle)
    private val balance = args.balance

    override fun updateUiStateOnInit() {
        _uiState.update { state ->
            state.copy(
                serviceName = args.serviceName.orEmpty(),
                serviceCategoryName = args.serviceCategoryName,
                catalogServiceCode = args.catalogServiceCode.orEmpty(),
                totalPayment = balance.totalPayment.toInt(),
                identifierType = args.identifierCode.orEmpty(),
                referenceNumber = args.referenceNumber.orEmpty(),
                formFields = balance.queryForm.fields.map { field ->
                    InvoicePXFieldUiState(
                        fieldKey = field.key,
                        pxField = field.value,
                        value = field.value.getInitialValue()
                    )
                }
            )
        }
    }

    override fun navigateToAffiliationTerms() {
        sendEvent(
            UiEvent.Navigate(
                AffiliateServiceFragmentDirections
                    .navigateToPaymentAffiliationTermsFragment()
            )
        )
    }

    override fun onContinueClick() {
        sendEvent(
            UiEvent.Navigate(
                AffiliateServiceFragmentDirections.navigateToPaymentAffPrepareForPictureFragment(
                    selectedPaymentMethod = _uiState.value.getSelectedPaymentMethod(),
                    contractAction = ContractAffiliationAction.Create(
                        request = _uiState.value.toCreateAffiliationContractRequestUI(
                            dui = baseSharedPrefs.dui()
                        )
                    )
                )
            )
        )
    }
}
