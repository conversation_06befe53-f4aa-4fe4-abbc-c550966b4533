package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.foundation.layout.Box
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import sv.com.tarjetaone.presentation.compose.theme.LocalCustomColors

@Composable
fun SimpleLoadingIndicator(
    modifier: Modifier = Modifier,
    color: Color = LocalCustomColors.current.successContainer
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
    ) {
        CircularProgressIndicator(
            color = color
        )
    }
}
