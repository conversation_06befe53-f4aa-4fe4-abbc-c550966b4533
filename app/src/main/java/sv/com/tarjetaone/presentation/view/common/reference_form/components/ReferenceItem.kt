package sv.com.tarjetaone.presentation.view.common.reference_form.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.HyperLinkTextButton
import sv.com.tarjetaone.presentation.compose.uicomponent.card.SimpleCardComponent
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.TextButtonVariant

@Composable
fun ReferenceItem(
    fullName: String,
    phoneNumber: String,
    referenceTypeName: String,
    showDeleteTextButton: Boolean,
    onModifyReference: () -> Unit,
    onDeleteReference: () -> Unit = { }
) {
    SimpleCardComponent(
        modifier = Modifier.fillMaxWidth(),
        contentPadding = PaddingValues(MaterialTheme.customDimens.dimen16),
        elevation = CardDefaults.cardElevation(
            defaultElevation = MaterialTheme.customDimens.dimen2
        )
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_person_fill_frame),
                contentDescription = null,
                modifier = Modifier.size(MaterialTheme.customDimens.dimen38),
                tint = MaterialTheme.colorScheme.primary
            )
            Spacer16()
            Column {
                Text(
                    text = fullName,
                    style = MaterialTheme.typography.headlineSmall.copy(
                        color = MaterialTheme.colorScheme.secondary
                    )
                )
                Text(
                    text = referenceTypeName,
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = MaterialTheme.customColors.disabledPlaceholder,
                    )
                )
                Text(
                    text = phoneNumber,
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = MaterialTheme.colorScheme.secondary
                    )
                )
                ReferenceItemActions(
                    showDeleteTextButton = showDeleteTextButton,
                    onModifyReference = onModifyReference,
                    onDeleteReference = onDeleteReference
                )
            }
        }
    }
}

@Composable
fun ReferenceItemActions(
    showDeleteTextButton: Boolean,
    onModifyReference: () -> Unit,
    onDeleteReference: () -> Unit
) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen16)
    ) {
        HyperLinkTextButton(
            textButtonVariant = TextButtonVariant.SMALL_VARIANT,
            text = stringResource(id = R.string.modify),
        ) {
            onModifyReference()
        }
        if (showDeleteTextButton) {
            HyperLinkTextButton(
                textButtonVariant = TextButtonVariant.SMALL_VARIANT,
                text = stringResource(id = R.string.delete_label),
            ) {
                onDeleteReference()
            }
        }
    }
}

@Preview
@Composable
private fun ReferenceItemPreview() {
    OneAppTheme {
        ReferenceItem(
            fullName = "John Doe",
            phoneNumber = "7766-8899",
            referenceTypeName = "Padre",
            showDeleteTextButton = false,
            onModifyReference = { }
        )
    }
}