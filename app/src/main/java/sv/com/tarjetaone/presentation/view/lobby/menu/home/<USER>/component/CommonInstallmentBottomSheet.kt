package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.SheetState
import androidx.compose.material3.Text
import androidx.compose.material3.rememberStandardBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.datasource.LoremIpsum
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.TWENTY_ONE_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.card.HorizontalIconWithText
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SheetDragHandle
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.helpers.UiText

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CommonInstallmentBottomSheet(
    modifier: Modifier = Modifier,
    bottomSheetState: SheetState,
    onDismissRequest: () -> Unit,
    content: @Composable () -> Unit,
) {
    ModalBottomSheet(
        modifier = modifier,
        onDismissRequest = onDismissRequest,
        sheetState = bottomSheetState,
        containerColor = MaterialTheme.colorScheme.background,
        dragHandle = {
            SheetDragHandle(
                color = MaterialTheme.customColors.gray500
            )
        }
    ) {
        Column {
            InstallmentBottomSheetHeader()
            Spacer24()
            content()
        }
    }
}

@Composable
private fun InstallmentBottomSheetHeader() {
    Column(modifier = Modifier.fillMaxWidth()) {
        HorizontalIconWithText(
            modifier = Modifier
                .padding(
                    vertical = MaterialTheme.customDimens.dimen8,
                    horizontal = MaterialTheme.customDimens.dimen24
                )
                .align(Alignment.CenterHorizontally),
            leadingIcon = R.drawable.ic_shopping_bag,
            iconTextSpacing = MaterialTheme.customDimens.dimen8,
            text = UiText.StringResource(res = R.string.installment_payment_title),
            textColor = MaterialTheme.colorScheme.onSurfaceVariant,
            style = MaterialTheme.typography.bodyLarge.copy(
                fontWeight = FontWeight.SemiBold,
            )
        )
        HorizontalDivider(color = MaterialTheme.customColors.gray200)
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Preview
@Composable
private fun PreviewCommonInstallmentBottomSheet() {
    OneAppTheme {
        CommonInstallmentBottomSheet(
            bottomSheetState = rememberStandardBottomSheetState(),
            onDismissRequest = { }
        ) {
            Column(
                modifier = Modifier.padding(horizontal = MaterialTheme.customDimens.dimen16)
            ) {
                Text(
                    text = LoremIpsum(TWENTY_ONE_VALUE).values.first(),
                    color = MaterialTheme.colorScheme.secondary
                )
            }
        }
    }
}
