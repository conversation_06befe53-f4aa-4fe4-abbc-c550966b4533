package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.contact_channel.component

import sv.com.tarjetaone.domain.entities.request.ContactNotificationType
import sv.com.tarjetaone.domain.entities.request.CustomerContactsUI
import sv.com.tarjetaone.domain.entities.response.ContactType

data class ContactItemUiState(
    val contactType: ContactType?,
    val contact: CustomerContactsUI
)

fun CustomerContactsUI.updateFlags(
    isActive: <PERSON>olean,
    notificationType: ContactNotificationType
): CustomerContactsUI {
    return when (notificationType) {
        ContactNotificationType.Transactions -> copy(notificationTran = isActive)
        ContactNotificationType.Advertising -> copy(notificationAdvertising = isActive)
    }
}
