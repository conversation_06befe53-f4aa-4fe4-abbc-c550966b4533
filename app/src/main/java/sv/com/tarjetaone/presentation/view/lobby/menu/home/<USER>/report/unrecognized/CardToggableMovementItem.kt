package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.report.unrecognized

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.core.utils.extensions.substringAfter
import sv.com.tarjetaone.domain.entities.response.UnrecognizedTransactionUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.helpers.removeExtraSpaces
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component.getAmountFormatted
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component.getDateFormatted
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component.unrecognizedTransaction

@Composable
fun CardSwitchableTransactionItem(
    maskedCard: String,
    modifier: Modifier = Modifier,
    isSelected: Boolean?,
    transaction: UnrecognizedTransactionUI,
    onTransactionAction: (UnrecognizedTransactionUI) -> Unit,
    transactionAvailability: Color = Color.White
) {

    Card(
        shape = MaterialTheme.shapes.medium,
        border = BorderStroke(
            width = MaterialTheme.customDimens.dimen1,
            color = getColor(
                isSelected = isSelected,
                selectedColor = MaterialTheme.colorScheme.primary,
                unselectedColor = MaterialTheme.customColors.gray300,
                defaultColor = MaterialTheme.customColors.defaultSurface
            )
        ),
        modifier = modifier.shadow(
            MaterialTheme.customDimens.dimen2,
            MaterialTheme.shapes.medium
        ),
        colors = CardDefaults.cardColors(containerColor = transactionAvailability),
        onClick = { onTransactionAction(transaction) }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    getColor(
                        isSelected = isSelected,
                        selectedColor = MaterialTheme.customColors.backgroundError,
                        unselectedColor = MaterialTheme.customColors.gray300,
                        defaultColor = MaterialTheme.customColors.defaultSurface,
                    )
                )
                .padding(MaterialTheme.customDimens.dimen16)
        ) {
            TransactionToggleInfoSection(maskedCard, transaction, isSelected)
        }
    }
}

@Composable
fun TransactionToggleInfoSection(
    maskedCard: String,
    transaction: UnrecognizedTransactionUI,
    isSelected: Boolean? = null
) {
    Column(modifier = Modifier.fillMaxWidth()) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            val descTextStyle = MaterialTheme.typography.bodySmall.copy(
                color = getColor(
                    isSelected = isSelected,
                    selectedColor = Color.White,
                    unselectedColor = MaterialTheme.customColors.gray600,
                    defaultColor = MaterialTheme.colorScheme.secondary
                ),
                fontWeight = FontWeight.SemiBold,
                fontSize = MaterialTheme.customDimensSp.sp16
            )
            val detailStyle = MaterialTheme.typography.labelSmall.copy(
                color = getColor(
                    isSelected = isSelected,
                    selectedColor = MaterialTheme.customColors.gray600,
                    unselectedColor = Color.White,
                    defaultColor = MaterialTheme.colorScheme.secondary
                ),
                fontWeight = FontWeight.Normal,
                fontSize = MaterialTheme.customDimensSp.sp13
            )
            Column(
                verticalArrangement = Arrangement.Center,
                modifier = Modifier.weight(ONE_FLOAT_VALUE),
            ) {
                Text(
                    text = transaction.description.removeExtraSpaces(),
                    maxLines = TWO_VALUE,
                    overflow = TextOverflow.Ellipsis,
                    style = descTextStyle
                )
                Text(
                    text = maskedCard.substringAfter(
                        index = maskedCard.length.orZero() / TWO_VALUE
                    ).trim(),
                    maxLines = ONE_VALUE,
                    style = descTextStyle.copy(
                        color = getColor(
                            isSelected = isSelected,
                            selectedColor = Color.White,
                            unselectedColor = MaterialTheme.customColors.gray600,
                            defaultColor = MaterialTheme.customColors.gray600
                        )
                    )
                )
            }
            Spacer(modifier = Modifier.width(MaterialTheme.customDimens.dimen4))
            Column(horizontalAlignment = Alignment.End) {
                Text(
                    text = stringResource(
                        id = R.string.negative_transaction,
                        transaction.getAmountFormatted()
                    ),
                    style = descTextStyle.copy(
                        color = getColor(
                            isSelected = isSelected,
                            selectedColor = MaterialTheme.colorScheme.primary,
                            unselectedColor = MaterialTheme.customColors.gray600,
                            defaultColor = MaterialTheme.customColors.gray600
                        )
                    )
                )
                Text(
                    text = transaction.getDateFormatted(),
                    style = detailStyle.copy(
                        color = getColor(
                            isSelected = isSelected,
                            selectedColor = Color.White,
                            unselectedColor = MaterialTheme.customColors.gray600,
                            defaultColor = MaterialTheme.customColors.gray600
                        )
                    )
                )
            }
        }
    }
}

/**
 * Returns a color based on the selection state.
 * - If `isSelected` is `true`, returns `selectedColor`.
 * - If `isSelected` is `null`, returns `unselectedColor`.
 * - If `isSelected` is `false`, returns `defaultColor`.
 */
@Composable
private fun getColor(
    isSelected: Boolean?,
    selectedColor: Color = MaterialTheme.colorScheme.primary,
    unselectedColor: Color = MaterialTheme.customColors.gray300,
    defaultColor: Color = MaterialTheme.customColors.defaultSurface,
): Color {
    return when (isSelected) {
        true -> selectedColor
        null -> unselectedColor
        else -> defaultColor
    }
}

@Preview
@Composable
private fun CollapsedTransactionItemPreview() {
    OneAppTheme {
        CardSwitchableTransactionItem(
            transaction = unrecognizedTransaction,
            onTransactionAction = { _ -> },
            isSelected = null,
            modifier = Modifier.fillMaxWidth(),
            maskedCard = "*** 1234"
        )
    }
}

@Preview
@Composable
private fun CollapsedTransactionSelectedItemPreview() {
    OneAppTheme {
        CardSwitchableTransactionItem(
            transaction = unrecognizedTransaction.copy(isFavorite = false),
            onTransactionAction = { _ -> },
            isSelected = true,
            modifier = Modifier.fillMaxWidth(),
            maskedCard = "*** 4567"
        )
    }
}

@Preview
@Composable
private fun CollapsedTransactionUnselectedItemPreview() {
    OneAppTheme {
        CardSwitchableTransactionItem(
            transaction = unrecognizedTransaction.copy(isFavorite = true),
            onTransactionAction = { _ -> },
            isSelected = false,
            modifier = Modifier.fillMaxWidth(),
            maskedCard = "*** 0127"
        )
    }
}
