package sv.com.tarjetaone.presentation.compose.uicomponent.button

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.shape.CornerSize
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import sv.com.tarjetaone.presentation.compose.theme.LocalCustomColors
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant

/**
 * Button to be used as primary/secondary squared button.
 *
 */
@Composable
fun SquaredButton(
    modifier: Modifier = Modifier,
    text: String,
    enabled: Boolean = true,
    buttonVariant: ButtonVariant = ButtonVariant.PRIMARY_VARIANT,
    onClick: () -> Unit,
) {

    var border: BorderStroke? = null
    val colors = when (buttonVariant) {
        ButtonVariant.PRIMARY_VARIANT -> {
            ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.errorContainer,
                contentColor = MaterialTheme.colorScheme.onError,
                disabledContainerColor = LocalCustomColors.current.gray300,
                disabledContentColor = LocalCustomColors.current.gray600,
            )
        }

        else -> {
            border = BorderStroke(width = 1.dp, color = MaterialTheme.colorScheme.tertiary)
            ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.background,
                contentColor = MaterialTheme.colorScheme.onSurfaceVariant,
                disabledContainerColor = LocalCustomColors.current.gray300,
                disabledContentColor = LocalCustomColors.current.gray600,
            )
        }
    }

    Button(
        modifier = modifier
            .fillMaxWidth()
            .sizeIn(minHeight = 55.dp),
        enabled = enabled,
        colors = colors,
        contentPadding = PaddingValues(vertical = 8.dp, horizontal = 24.dp),
        shape = MaterialTheme.shapes.extraSmall.copy(all = CornerSize(6.dp)),
        border = border,
        onClick = onClick,
    ) {
        Text(
            text = text,
            modifier = Modifier
                .align(Alignment.CenterVertically),
            style = MaterialTheme.typography.labelMedium
        )
    }
}

@Preview
@Composable
fun SquaredButtonPreview() {
    OneAppTheme {
        SquaredButton(
            modifier = Modifier,
            text = "Primary Squared Button",
            enabled = true,
            buttonVariant = ButtonVariant.PRIMARY_VARIANT,
            onClick = { }
        )
    }
}
