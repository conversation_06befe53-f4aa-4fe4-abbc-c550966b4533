package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_cancel.carousel

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.CommonDialogWithIconParams
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.configCurrencyWithFractions
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.response.CancelAdditionalCardUI
import sv.com.tarjetaone.domain.usecases.card.GetCardCloseUseCase
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_cancel.carousel.component.toMainCardUI
import javax.inject.Inject

@HiltViewModel
class CardCancelCarouselViewModel @Inject constructor(
    private val getCardCloseUseCase: GetCardCloseUseCase,
    private val sharedPrefsRepo: SecureSharedPreferencesRepository
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(CardCancelCarouselUiState())
    val uiState: StateFlow<CardCancelCarouselUiState> = _uiState.asStateFlow()

    init {
        start()
    }

    private fun start() {
        viewModelScope.launch {
            sendEvent(UiEvent.Loading(true))
            val customerId = sharedPrefsRepo.getCustomerId().orZero()
            getCardCloseUseCase(customerId).executeUseCase { response ->
                sendEvent(UiEvent.Loading(false))
                getCardsData(response)
            }
        }
    }

    private fun onCardSelected(cardIndex: Int) {
        val cardSelected = _uiState.value.cardsToCancel[cardIndex]
        _uiState.update { it.copy(cardSelected = cardSelected) }
    }

    private fun onCardCancellationClick() {
        val isCardCancellable = _uiState.value.isMainCardCancellable
        val selectedCard = _uiState.value.cardSelected
        if (selectedCard?.mainCard == true && !isCardCancellable) {
            showPendingBalanceDialog(
                selectedCard.currentBalance.orZero().configCurrencyWithFractions()
            )
        } else {
            sendEvent(
                UiEvent.Navigate(
                    CardCancelCarouselFragmentDirections
                        .actionNavigationMenuCancelCardsToNavigationReasonCancelCards(
                            card = selectedCard
                        )
                )
            )
        }
    }

    private fun getCardsData(card: CancelAdditionalCardUI) {
        val mainCard = card.mainCard.toMainCardUI()
        val additionalCards = card.additionalCards.map { it.toMainCardUI() }
        val cardList = buildList {
            add(mainCard)
            addAll(additionalCards)
        }
        _uiState.update { state ->
            state.copy(
                cardSelected = cardList.firstOrNull(),
                cardsToCancel = cardList,
                isMainCardCancellable = card.mainCard.canClose
            )
        }
    }

    private fun showPendingBalanceDialog(dueAmount: String) {
        sendEvent(
            UiEvent.ShowCommonDialog(
                params = CommonDialogWithIconParams(
                    isDismissible = false,
                    icon = R.drawable.ic_error_yellow,
                    title = UiText.StringResource(R.string.cancel_account_dialog_title),
                    message = UiText.StringResource(
                        R.string.cancel_account_dialog_message,
                        dueAmount
                    ),
                    buttonText = UiText.StringResource(R.string.cancel_account_dialog_button),
                    buttonColor = R.color.yellow_alert_button,
                )
            )
        )
    }

    fun onEvent(event: CardCancelCarouselUiEvent) {
        when (event) {
            CardCancelCarouselUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            CardCancelCarouselUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            is CardCancelCarouselUiEvent.OnCardSelected -> onCardSelected(event.cardIndex)
            CardCancelCarouselUiEvent.OnCardCancellationClick -> onCardCancellationClick()
        }
    }
}