package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldType
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldValueType
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedDropdown
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.helpers.capitalizeAllWords
import sv.com.tarjetaone.presentation.helpers.dropLeadingZeros
import sv.com.tarjetaone.presentation.helpers.filterDigits
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.invoiceselection.util.InvoicePXFieldUiState
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.queryform.component.keyboardType
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.queryform.component.visualTransformation

@Composable
fun PXFormField(
    modifier: Modifier = Modifier,
    labelValue: String,
    fieldState: InvoicePXFieldUiState,
    readOnly: Boolean = false,
    formFieldStyle: PXFormFieldDisposition = PXFormFieldDisposition.DOUBLE_LINE,
    onValueChanged: (String) -> Unit,
) {
    val labelStyle = MaterialTheme.typography.bodySmall.copy(
        fontWeight = when (fieldState.pxField.type) {
            PXFieldType.Input, PXFieldType.SelectBox -> FontWeight.Medium
            else -> FontWeight.SemiBold
        },
        color = when (fieldState.pxField.type) {
            PXFieldType.Input, PXFieldType.SelectBox -> MaterialTheme.customColors.textBodyLight
            else -> MaterialTheme.customColors.gray5
        },
    )
    when (formFieldStyle) {
        PXFormFieldDisposition.SINGLE_LINE -> {
            Row(
                modifier = modifier.padding(
                    vertical = MaterialTheme.customDimens.dimen8,
                ),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    modifier = Modifier.weight(ONE_FLOAT_VALUE),
                    text = labelValue,
                    style = labelStyle
                )
                PxFormFieldContent(
                    modifier = Modifier.weight(ONE_FLOAT_VALUE),
                    fieldState = fieldState,
                    formFieldStyle = formFieldStyle,
                    onValueChanged = { value -> onValueChanged(value) }
                )
            }
        }

        PXFormFieldDisposition.DOUBLE_LINE -> {
            Column(modifier = modifier) {
                Text(
                    text = labelValue,
                    style = labelStyle
                )
                PxFormFieldContent(
                    modifier = Modifier.then(
                        if (fieldState.pxField.type == PXFieldType.SelectBox ||
                            fieldState.pxField.type == PXFieldType.Input
                        ) Modifier.padding(top = MaterialTheme.customDimens.dimen8) else Modifier
                    ),
                    readOnly = readOnly,
                    fieldState = fieldState,
                    formFieldStyle = formFieldStyle,
                    onValueChanged = { value ->
                        onValueChanged(value)
                    }
                )
            }
        }
    }
}

@Composable
private fun PxFormFieldContent(
    modifier: Modifier = Modifier,
    readOnly: Boolean = false,
    fieldState: InvoicePXFieldUiState,
    formFieldStyle: PXFormFieldDisposition = PXFormFieldDisposition.DOUBLE_LINE,
    onValueChanged: (String) -> Unit,
) {
    when (fieldState.pxField.type) {
        PXFieldType.Output, PXFieldType.Selectable -> {
            Text(
                modifier = modifier,
                text = fieldState.getFormattedValue().capitalizeAllWords(),
                style = MaterialTheme.typography.bodySmall.copy(
                    textAlign = if (formFieldStyle == PXFormFieldDisposition.SINGLE_LINE) {
                        TextAlign.End
                    } else {
                        TextAlign.Start
                    },
                )
            )
        }

        PXFieldType.Input -> {
            SimpleElevatedTextField(
                modifier = modifier,
                value = fieldState.value.capitalizeAllWords(),
                onValueChange = {
                    val filteredValue = when (fieldState.pxField.valueString?.type) {
                        PXFieldValueType.Decimal,
                        PXFieldValueType.Double,
                        PXFieldValueType.Money,
                        PXFieldValueType.Integer,
                        PXFieldValueType.Long -> it.filterDigits().dropLeadingZeros()

                        else -> it
                    }
                    onValueChanged(filteredValue)
                },
                enabled = !readOnly && (fieldState.pxField.valueString?.editable ?: false),
                hasError = fieldState.hasError,
                error = stringResource(id = R.string.field_is_mandatory),
                visualTransformation = fieldState.pxField.valueString?.type.visualTransformation(),
                keyboardOptions = KeyboardOptions(
                    keyboardType = fieldState.pxField.valueString?.type.keyboardType()
                ),
            )
        }

        PXFieldType.SelectBox -> {
            val items = fieldState.pxField.valueList?.value?.toList().orEmpty()
            SimpleElevatedDropdown(
                modifier = modifier,
                items = items,
                itemLabel = { item -> item.second.label.orEmpty().capitalizeAllWords() },
                onValueChange = { onValueChanged(it.second.name.orEmpty()) },
                value = items.firstOrNull { item ->
                    item.first == fieldState.value
                },
                enabled = !readOnly
            )
        }
        else -> Unit
    }
}

enum class PXFormFieldDisposition {
    SINGLE_LINE,
    DOUBLE_LINE
}
