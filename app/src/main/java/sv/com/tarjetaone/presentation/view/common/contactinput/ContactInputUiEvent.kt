package sv.com.tarjetaone.presentation.view.common.contactinput

import sv.com.tarjetaone.domain.entities.response.ContactType
import sv.com.tarjetaone.domain.entities.response.OtpMethod
import sv.com.tarjetaone.domain.entities.response.OtpType

sealed class ContactInputUiEvent {
    data class OnBackClick(val contactType: ContactType) : ContactInputUiEvent()
    data object OnSupportClick : ContactInputUiEvent()
    data class OnStart(val contactType: ContactType) : ContactInputUiEvent()
    data class OnEmailChange(val email: String) : ContactInputUiEvent()
    data class OnPhoneChange(val phone: String) : ContactInputUiEvent()
    data class OnSelectOtpMethod(val type: OtpType, val method: OtpMethod) : ContactInputUiEvent()
}
