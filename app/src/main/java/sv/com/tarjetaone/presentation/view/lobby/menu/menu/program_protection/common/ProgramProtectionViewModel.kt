package sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.common

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.analytics.amplitude.AmplitudeEvents.ON_START_CONTRACTING_PROTECTION_PROGRAM_EVENT
import sv.com.tarjetaone.analytics.amplitude.AmplitudeManager
import sv.com.tarjetaone.common.utils.AppConstants.THIRTY_ONE_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.THIRTY_VALUE
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.usecases.alert.GetFraudProtectionStatusUseCase
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.canActivateFraudProtection
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.canCheckActiveFraudProtection
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.canDisableFraudProtection
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.canGetFraudProtection
import javax.inject.Inject

@HiltViewModel
class ProgramProtectionViewModel @Inject constructor(
    private val getFraudProtectionStatusUseCase: GetFraudProtectionStatusUseCase,
    private val amplitudeManager: AmplitudeManager,
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(ProgramProtectionUiState())
    val uiState = _uiState.asStateFlow()

    private val hasPaymentPenalty: Boolean
        get() = baseSharedPrefs.mainCard?.hasPaymentPenalty ?: false

    private fun onStart() {
        if (hasPaymentPenalty) {
            _uiState.update { it.copy(hasPaymentPenalty = hasPaymentPenalty) }
            return
        }
        sendEvent(SideEffect.Loading(true))
        viewModelScope.launch {
            getFraudProtectionStatusUseCase(
                creditCardId = baseSharedPrefs.mainCard?.creditCardId.orZero()
            ).executeUseCase(
                onApiErrorAction = { _, error, _, _ ->
                    if (error?.code?.toIntOrNull() in FISERV_PROTECTION_PROGRAM_ERROR_CODES) {
                        _uiState.update {
                            it.copy(protectionProgramState = ProgramProtectionState.Invalid)
                        }
                    } else {
                        showUpsErrorMessage(isDismissible = false) {
                            sendEvent(UiEvent.NavigateBack)
                        }
                    }
                    sendEvent(SideEffect.Loading(false))
                }
            ) { response ->
                val protectionProgramState = when {
                    response.canGetFraudProtection -> ProgramProtectionState.Enroll
                    response.canActivateFraudProtection -> ProgramProtectionState.Activate
                    response.canCheckActiveFraudProtection -> ProgramProtectionState.Deactivate
                    else -> null
                }

                if (protectionProgramState == null) {
                    showUpsErrorMessage(isDismissible = false) {
                        sendEvent(UiEvent.NavigateBack)
                    }
                    return@executeUseCase
                }

                _uiState.update { state ->
                    state.copy(
                        screenTitle = UiText.StringResource(
                            R.string.protection_plan
                        ).takeIf { protectionProgramState == ProgramProtectionState.Deactivate },
                        fraudProtectionDetails = response,
                        isActionButtonEnabled = true,
                        canDeactivate = response.canDisableFraudProtection,
                        protectionProgramState = protectionProgramState
                    )
                }
                sendEvent(SideEffect.Loading(false))
            }
        }
    }

    private fun onEnrollClick() {
        amplitudeManager.track(ON_START_CONTRACTING_PROTECTION_PROGRAM_EVENT)
        sendEvent(
            UiEvent.Navigate(
                ProgramProtectionFragmentDirections
                    .actionNavigateToProgramProtectionEnrollGraph(
                        protectionTypeId = _uiState.value.getProtectionTypeId()
                    )
            )
        )
    }

    private fun onActivateClick() {
        sendEvent(
            UiEvent.Navigate(
                ProgramProtectionFragmentDirections
                    .actionNavigateToReactivateProgramProtectionGraph(
                        fraud = _uiState.value.fraudProtectionDetails
                    )
            )
        )
    }

    private fun onDeactivateClick() {
        sendEvent(
            UiEvent.Navigate(
                ProgramProtectionFragmentDirections
                    .actionNavigateToDeactivateProgramProtectionGraph(
                        fraud = _uiState.value.fraudProtectionDetails
                    )
            )
        )
    }

    private fun onActionButtonClick() {
        when (uiState.value.protectionProgramState) {
            ProgramProtectionState.Enroll -> onEnrollClick()
            ProgramProtectionState.Activate -> onActivateClick()
            ProgramProtectionState.Deactivate -> onDeactivateClick()
            else -> Unit
        }
    }

    fun onEvent(event: ProgramProtectionUiEvent) {
        when (event) {
            ProgramProtectionUiEvent.OnStart -> onStart()
            ProgramProtectionUiEvent.OnNavigateBack -> sendEvent(UiEvent.NavigateBack)
            ProgramProtectionUiEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
            ProgramProtectionUiEvent.OnActionButtonClick -> onActionButtonClick()
        }
    }

    companion object {
        private val FISERV_PROTECTION_PROGRAM_ERROR_CODES = listOf(THIRTY_VALUE, THIRTY_ONE_VALUE)
    }

}
