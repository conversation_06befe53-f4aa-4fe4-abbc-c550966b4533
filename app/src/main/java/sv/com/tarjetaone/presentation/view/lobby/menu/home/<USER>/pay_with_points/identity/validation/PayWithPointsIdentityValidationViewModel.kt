package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.pay_with_points.identity.validation

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.usecases.auth.FaceAuthenticationUseCase
import sv.com.tarjetaone.domain.usecases.card.pointspayment.PayWithPointsUseCase
import sv.com.tarjetaone.presentation.view.common.identity_validation.IdentityValidationBaseViewModel
import javax.inject.Inject

@HiltViewModel
class PayWithPointsIdentityValidationViewModel @Inject constructor(
    faceAuthenticationUseCase: FaceAuthenticationUseCase,
    sharedPrefs: SecureSharedPreferencesRepository,
    private val payWithPointsUseCase: PayWithPointsUseCase,
    savedStateHandle: SavedStateHandle
) : IdentityValidationBaseViewModel(faceAuthenticationUseCase, sharedPrefs) {
    private val args =
        PayWithPointsIdentityValidationFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override val onBlockedAction = {
        sendEvent(UiEvent.NavigateBackTo(R.id.navigation_home))
    }

    override fun operation(biometryId: Int) {
        viewModelScope.launch {
            payWithPointsUseCase(args.request).executeUseCase(
                onApiErrorAction = { _, _, _, _ ->
                    showUpsErrorMessage(false) { sendEvent(UiEvent.NavigateBack) }
                },
                onSuccessAction = {
                    sendEvent(
                        UiEvent.Navigate(
                            PayWithPointsIdentityValidationFragmentDirections
                                .actionPayWithPointsIdentityValidationFragmentToPayWithPointsSuccessFragment(
                                    it
                                )
                        )
                    )
                }
            )
        }
    }

    override fun onFailedIdentityValidation(resultCode: String?) {
        sendEvent(UiEvent.NavigateBack)
    }
}
