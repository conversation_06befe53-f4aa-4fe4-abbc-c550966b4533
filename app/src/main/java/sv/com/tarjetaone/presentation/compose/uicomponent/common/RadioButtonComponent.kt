package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonColors
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.presentation.compose.theme.LocalCustomColors
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme

@Composable
fun RadioButtonComponent(
    modifier: Modifier = Modifier,
    radioButtonColors: RadioButtonColors = RadioButtonDefaults.colors(
        selectedColor = Color.White,
        unselectedColor = LocalCustomColors.current.disabledGray
    ),
    selected: Boolean,
    onClick: () -> Unit
) {
    RadioButton(
        modifier = modifier,
        selected = selected,
        colors = radioButtonColors,
        onClick = onClick
    )
}

@Preview
@Composable
fun RadioButtonComponentPreview() {
    OneAppTheme {
        RadioButtonComponent(
            selected = true,
            onClick = {}
        )
    }
}
