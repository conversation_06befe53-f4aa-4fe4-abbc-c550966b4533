package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.address_location.identity.validation

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.SUCCESS_RESPONSE_CODE
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.request.CustomerAddressesRequestUI
import sv.com.tarjetaone.domain.entities.request.CustomerAddressesUpdateRequestUI
import sv.com.tarjetaone.domain.entities.request.HomeAddressesRequestUI
import sv.com.tarjetaone.domain.entities.request.HomeAddressesUpdateRequestUI
import sv.com.tarjetaone.domain.usecases.auth.FaceAuthenticationUseCase
import sv.com.tarjetaone.domain.usecases.personalData.address.SaveCustomerAddressUseCase
import sv.com.tarjetaone.domain.usecases.personalData.address.UpdateCustomerAddressUseCase
import sv.com.tarjetaone.domain.utils.onApiError
import sv.com.tarjetaone.domain.utils.onNetworkError
import sv.com.tarjetaone.domain.utils.onSuccess
import sv.com.tarjetaone.presentation.view.common.identity_validation.IdentityValidationBaseViewModel
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.address_location.UserHomeAddressAction
import javax.inject.Inject

@HiltViewModel
class HomeAddressIdentityValidationViewModel @Inject constructor(
    private val saveCustomerAddressUseCase: SaveCustomerAddressUseCase,
    private val updateCustomerAddressUseCase: UpdateCustomerAddressUseCase,
    private val sharedPreferences: SecureSharedPreferencesRepository,
    faceAuthenticationUseCase: FaceAuthenticationUseCase,
    savedStateHandle: SavedStateHandle
) : IdentityValidationBaseViewModel(faceAuthenticationUseCase, sharedPreferences) {

    private val navArgs = HomeAddressIdentityValidationFragmentArgs.fromSavedStateHandle(savedStateHandle)
    override val onBlockedAction = {
        sendEvent(UiEvent.NavigateBackTo(R.id.navigation_home))
    }

    override fun operation(biometryId: Int) {
        when (navArgs?.action) {
            UserHomeAddressAction.SAVE -> saveCustomerAddress(biometryId)
            UserHomeAddressAction.UPDATE -> updateCustomerAddress(biometryId)
            null -> showErrorMessage()
        }
    }

    private fun saveCustomerAddress(biometryId: Int) {
        val address = navArgs?.userLocation?.let {
            HomeAddressesRequestUI(
                addressTypeCode = it.addressType,
                municipalityId = it.idMunicipio?.toInt().orZero(),
                residential = it.residential.orEmpty(),
                streetAvenue = it.street,
                houseApartment = it.houseNumber,
                addInfo = it.reference,
                latitude = it.latitude,
                longitude = it.longitude,
                isMainAddress = false,
                alias = it.alias,
                isDeliveryAvailable = it.isAvailityDelivery,
            )
        }
        val request = CustomerAddressesRequestUI(
            customerId = sharedPreferences.getCustomerId().orZero(),
            addresses = address?.let { listOf(it) } ?: emptyList(),
            biometryId = biometryId
        )

        viewModelScope.launch {
            saveCustomerAddressUseCase(request).onSuccess {
                val response = it.statusResponse?.responseStatus
                if (response?.code != SUCCESS_RESPONSE_CODE) {
                    showSimpleError(response?.message.orEmpty())
                    return@onSuccess
                }

                navigateToSuccessUpdateAddress()
            }.onApiError { _, _, _, _ ->
                showUpsErrorMessage()
            }.onNetworkError {
                showUpsErrorMessage()
            }
        }
    }

    private fun updateCustomerAddress(biometryId: Int) {
        val address = navArgs?.userLocation?.let {
            HomeAddressesUpdateRequestUI(
                addressId = it.addressId,
                addressTypeCode = it.addressType,
                municipalityId = it.idMunicipio?.toInt().orZero(),
                residential = it.residential.orEmpty(),
                streetAvenue = it.street,
                houseApartment = it.houseNumber,
                addInfo = it.reference,
                latitude = it.latitude,
                longitude = it.longitude,
                isMainAddress = it.isMainAddress,
                alias = it.alias,
                isDeliveryAvailable = it.isAvailityDelivery,
            )
        }

        val request = CustomerAddressesUpdateRequestUI(
            customerId = sharedPreferences.getCustomerId().orZero(),
            addresses = address?.let { listOf(it) } ?: emptyList(),
            biometryId = biometryId
        )

        viewModelScope.launch {
            updateCustomerAddressUseCase(request).onSuccess {
                val response = it.statusResponse?.responseStatus
                if (response?.code != SUCCESS_RESPONSE_CODE) {
                    showSimpleError(response?.message.orEmpty())
                    return@onSuccess
                }

                navigateToSuccessUpdateAddress()
            }.onApiError { _, _, _, _ ->
                showUpsErrorMessage()
            }.onNetworkError {
                showUpsErrorMessage()
            }
        }
    }

    private fun navigateToSuccessUpdateAddress() {
        sendEvent(
            UiEvent.Navigate(
                HomeAddressIdentityValidationFragmentDirections
                    .actionHomeAddressValidationToSuccessUpdateAddressFragment(
                        action = navArgs?.action
                            ?: UserHomeAddressAction.SAVE // Set action by default as Save
                    )
            )
        )
    }

    override fun onFailedIdentityValidation(resultCode: String?) {
        showErrorMessage()
    }

    private fun showErrorMessage() {
        showUpsErrorMessage(
            isDismissible = false,
            onButtonClick = {
                sendEvent(UiEvent.NavigateBackTo(R.id.prepareForPicturePersonalDataAddressFragment))
            }
        )
    }
}