package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.paymentdetail

import android.view.View
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.DAY_OF_WEEK_DAY_MONTH
import sv.com.tarjetaone.common.utils.AppConstants.MONTH_DAY_YEAR_WITH_SLASH
import sv.com.tarjetaone.common.utils.extensions.capitalizeAllWords
import sv.com.tarjetaone.core.utils.extensions.configCurrencyWithFractions
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.paymentdetail.PxPaymentServiceDetailUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.CaptureAndroidViewWrapper
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.button.ShareVoucherButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.NonEmptyValueWrapper
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.Spacer12
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.handleShareVoucherCallback
import sv.com.tarjetaone.presentation.compose.util.handleVoucherSharingPermission
import sv.com.tarjetaone.presentation.view.common.management.component.DetailManagementCardItem
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.common.paymentdetail.PaymentServiceDetailHeader

@Composable
fun PxPaymentServiceDetailScreen(viewModel: PxPaymentServiceDetailViewModel) {

    LaunchedEffect(Unit) {
        viewModel.onEvent(PxPaymentServiceDetailUiEvent.OnStart)
    }

    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    PaymentServiceDetailContent(uiState, viewModel::onEvent)
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun PaymentServiceDetailContent(
    uiState: PxPaymentServiceDetailUiState,
    onEvent: (PxPaymentServiceDetailUiEvent) -> Unit
) {
    ScreenWithTopAppBar(
        contentPadding = PaddingValues(
            start = MaterialTheme.customDimens.dimen24,
            end = MaterialTheme.customDimens.dimen24,
            top = MaterialTheme.customDimens.dimen24,
            bottom = MaterialTheme.customDimens.dimen4
        ),
        isLeftButtonVisible = false,
        onLeftButtonClick = { },
        onRightButtonClick = { onEvent(PxPaymentServiceDetailUiEvent.OnTwilioClick) },
    ) {

        // Voucher view
        var voucherView by remember { mutableStateOf<View?>(null) }
        val context = LocalContext.current

        val storagePermissionState = handleVoucherSharingPermission(
            voucherView = voucherView,
            onHideComponentBeforeCapture = {
                PxPaymentServiceDetailUiEvent.OnHideComponentBeforeCapture
            },
            onPermissionGranted = { bitmap ->
                PxPaymentServiceDetailUiEvent.OnStoragePermissionGranted(bitmap)
            },
            onPermissionDenied = { showRationale ->
                PxPaymentServiceDetailUiEvent.OnStoragePermissionDenied(showRationale)
            },
            onEvent = onEvent
        )

        val shareVoucherCallback = handleShareVoucherCallback(
            context = context,
            voucherView = voucherView,
            storagePermissionState = storagePermissionState,
            onEvent = onEvent,
            hideBeforeCaptureEvent = PxPaymentServiceDetailUiEvent.OnHideComponentBeforeCapture,
            onGrantedEvent = { bitmap ->
                PxPaymentServiceDetailUiEvent.OnStoragePermissionGranted(bitmap)
            },
            onShareVoucherClickEvent = { requestPermissionCallback ->
                PxPaymentServiceDetailUiEvent.OnShareVoucherClick(requestPermissionCallback)
            }
        )

        Column(modifier = Modifier.fillMaxSize()) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .weight(0.9f)
                    .verticalScroll(rememberScrollState())
            ) {
                CaptureAndroidViewWrapper(view = { voucherView = it }) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(MaterialTheme.colorScheme.surface)
                    ) {
                        Spacer12()
                        PaymentServiceDetailHeader(
                            modifier = Modifier.align(Alignment.CenterHorizontally),
                            referenceNumber = uiState.pxPaymentServiceDetail.referenceNumberCore.orEmpty()
                        )
                        Spacer16()
                        PaymentServiceDetailCard(
                            uiState = uiState,
                            onShareVoucherClick = shareVoucherCallback
                        )
                    }
                }
            }

            OneButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.customDimens.dimen32),
                text = stringResource(id = R.string.back_to_my_account),
                onClick = { onEvent(PxPaymentServiceDetailUiEvent.OnContinueClick) },
            )
            Spacer16()
        }
    }
}

@Composable
fun PaymentServiceDetailCard(
    modifier: Modifier = Modifier,
    uiState: PxPaymentServiceDetailUiState,
    onShareVoucherClick: () -> Unit,
) {
    Card(
        modifier = modifier
            .fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.onPrimaryContainer
        ),
        shape = RoundedCornerShape(MaterialTheme.customDimens.dimen30)
    ) {
        Column(
            modifier = modifier
                .padding(
                    top = MaterialTheme.customDimens.dimen32,
                    start = MaterialTheme.customDimens.dimen32,
                    end = MaterialTheme.customDimens.dimen32,
                    bottom = MaterialTheme.customDimens.dimen16
                ),
            verticalArrangement = Arrangement.spacedBy(
                space = MaterialTheme.customDimens.dimen8
            )
        ) {

            NonEmptyValueWrapper(uiState.pxPaymentServiceDetail.subject) { value ->
                DetailManagementCardItem(
                    title = stringResource(id = R.string.name_of_request),
                    description = value,
                    showDivider = true
                )
            }

            NonEmptyValueWrapper(
                uiState.pxPaymentServiceDetail.customerName,
                uiState.pxPaymentServiceDetail.name
            ) { value ->
                DetailManagementCardItem(
                    title = stringResource(id = R.string.client_name),
                    description = value.capitalizeAllWords(),
                    showDivider = true
                )
            }

            val paymentMethod = if (uiState.isCardPaymentMethod) {
                stringResource(id = R.string.payment_service_tc_label)
            } else {
                uiState.selectedPaymentMethod
            }

            NonEmptyValueWrapper(paymentMethod) { value ->
                DetailManagementCardItem(
                    title = stringResource(id = R.string.payment_method_title),
                    description = value,
                    showDivider = true
                )
            }

            NonEmptyValueWrapper(uiState.pxPaymentServiceDetail.date) { value ->
                DetailManagementCardItem(
                    title = stringResource(id = R.string.payment_date_label),
                    description = value.getFormattedDateFromTo(
                        MONTH_DAY_YEAR_WITH_SLASH,
                        DAY_OF_WEEK_DAY_MONTH
                    ),
                    showDivider = true
                )
            }

            NonEmptyValueWrapper(
                uiState.pxPaymentServiceDetail.collectorName,
                uiState.pxPaymentServiceDetail.collector
            ) { value ->
                DetailManagementCardItem(
                    title = stringResource(id = R.string.payment_method_service_label),
                    description = value,
                    showDivider = true
                )
            }

            if (uiState.pxPaymentServiceDetail.digits.isNullOrBlank().not()) {
                NonEmptyValueWrapper(uiState.pxPaymentServiceDetail.digits) { value ->
                    DetailManagementCardItem(
                        title = stringResource(id = R.string.payment_method_account_label),
                        description = value,
                        showDivider = true
                    )
                }
            }

            NonEmptyValueWrapper(
                uiState.pxPaymentServiceDetail.totalPayment,
                uiState.pxPaymentServiceDetail.amount
            ) { value ->
                DetailManagementCardItem(
                    modifier = Modifier.padding(top = MaterialTheme.customDimens.dimen8),
                    title = stringResource(id = R.string.total_payment_label),
                    description = value.configCurrencyWithFractions(),
                    showDivider = uiState.isCapturingVoucherView.not()
                )
            }

            if (uiState.isCapturingVoucherView.not()) {
                ShareVoucherButton(
                    modifier = Modifier
                        .align(Alignment.CenterHorizontally)
                        .padding(vertical = MaterialTheme.customDimens.dimen8),
                    onClick = onShareVoucherClick
                )
            }
        }
    }
}

@Preview
@Composable
private fun PaymentServiceDetailScreenPreview() {
    OneAppTheme {
        PaymentServiceDetailContent(
            uiState = PxPaymentServiceDetailUiState(
                pxPaymentServiceDetail = PxPaymentServiceDetailUI(
                    subject = "Pago de servicios",
                    referenceNumberCore = "234.543.556",
                    customerName = "JUAN PEREZ",
                    name = "JUAN HERNANDEZ",
                    date = "2025/03/25 16:15:11",
                    collectorName = "ANDA",
                    collector = "CAESS",
                    digits = "25896314",
                    totalPayment = 54.00,
                    amount = 89.00
                ),
                selectedPaymentMethod = "Cuentas de Ahorro PN - 5801...",
                isCapturingVoucherView = false,
                isCardPaymentMethod = false,
                isFromScanning = false
            ),
            onEvent = {}
        )
    }
}
