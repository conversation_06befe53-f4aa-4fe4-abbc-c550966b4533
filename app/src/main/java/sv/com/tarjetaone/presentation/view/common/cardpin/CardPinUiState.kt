package sv.com.tarjetaone.presentation.view.common.cardpin

import androidx.annotation.StringRes
import androidx.compose.runtime.Stable
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.FOUR_VALUE

@Stable
data class CardPinUiState(
    val canModifyPin: Boolean = false,
    val pin: String = EMPTY_STRING,
    val hasError: Boolean = false,
    @StringRes val continueButtonLabel: Int? = null,
    @StringRes val descriptionLabel: Int? = null
) {
    val canEnableContinueButton = pin.length == FOUR_VALUE && !hasError
}