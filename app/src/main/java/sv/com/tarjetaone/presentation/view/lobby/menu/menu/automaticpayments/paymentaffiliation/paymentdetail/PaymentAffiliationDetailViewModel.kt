package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.paymentdetail

import android.graphics.Bitmap
import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.interfaces.TextFromResourcesUtil
import sv.com.tarjetaone.common.utils.AppConstants.DASH_STRING
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.CommonDialogWithIconParams
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.files.ContentType
import sv.com.tarjetaone.core.utils.openAppSettings
import sv.com.tarjetaone.domain.utils.automaticpayments.servicespayment.invoiceselection.ProductTypeCode
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.utils.getTimestamp

@HiltViewModel
class PaymentAffiliationDetailViewModel @Inject constructor(
    private val imageUtils: ImageUtils,
    private val textFromResourcesUtil: TextFromResourcesUtil,
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {

    private val args = PaymentAffiliationDetailFragmentArgs.fromSavedStateHandle(savedStateHandle)

    // Stateful
    private val _uiState = MutableStateFlow(PaymentAffiliationDetailUiState())
    val uiState = _uiState.asStateFlow()

    private fun onStart() {
        _uiState.update {
            it.copy(
                paymentAffiliationDetailUI = args.paymentAffiliationDetailUI,
                isCardPaymentMethod = isCardPaymentMethod()
            )
        }
    }

    /**
     * Check if the payment method is a card payment method.
     */
    private fun isCardPaymentMethod(): Boolean {
        return args.paymentAffiliationDetailUI.contractProductType == ProductTypeCode.CreditCards.value
    }

    /**
     * Return a callback to the UI, to launch `requestPermissionLauncher` with the required permission.
     */
    private fun onShareVoucherClick(requestPermissionCallback: () -> Unit) {
        requestPermissionCallback()
    }

    private fun onHideComponentBeforeCapture() {
        _uiState.update { it.copy(isCapturingVoucherView = true) }
    }

    private fun onContinueClick() {
        sendEvent(UiEvent.Navigate(PaymentAffiliationDetailFragmentDirections.actionHome()))
    }

    /**
     * Share Voucher after saving image in the device's storage.
     */
    private fun onShareVoucher(bitmap: Bitmap?) {
        bitmap?.let { bitmapImage ->
            imageUtils.saveImage(fileCapturedName(), bitmapImage) { uri ->
                sendEvent(UiEvent.ShareContent(uri, ContentType.ANY_IMAGE))
                _uiState.update { it.copy(isCapturingVoucherView = false) }
            }
        }
    }

    private fun fileCapturedName(): String {
        return "${textFromResourcesUtil.getString(R.string.app_name)} $DASH_STRING ${getTimestamp()}"
    }

    private fun onStoragePermissionGranted(bitmap: Bitmap?) {
        onShareVoucher(bitmap)
    }

    fun onPermissionDenied(showRationale: Boolean) {
        sendEvent(
            UiEvent.ShowCommonDialog(
                CommonDialogWithIconParams(
                    title = UiText.StringResource(R.string.multimedia_access),
                    message = UiText.StringResource(R.string.permission_external_storage),
                    buttonText = UiText.StringResource(if (showRationale) R.string.accept_label else R.string.settings),
                    onButtonClick = {
                        if (!showRationale) sendEvent(UiEvent.StartIntent(openAppSettings()))
                    }
                )
            )
        )
    }

    fun onEvent(event: PaymentAffiliationDetailUiEvent) {
        when (event) {
            PaymentAffiliationDetailUiEvent.OnStart -> onStart()
            PaymentAffiliationDetailUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            PaymentAffiliationDetailUiEvent.OnContinueClick -> onContinueClick()
            is PaymentAffiliationDetailUiEvent.OnHideComponentBeforeCapture -> onHideComponentBeforeCapture()
            is PaymentAffiliationDetailUiEvent.OnShareVoucherClick -> onShareVoucherClick(event.requestPermissionCallback)
            is PaymentAffiliationDetailUiEvent.OnStoragePermissionDenied -> onPermissionDenied(event.showRationale)
            is PaymentAffiliationDetailUiEvent.OnStoragePermissionGranted -> {
                onStoragePermissionGranted(event.bitmap)
            }
        }
    }
}
