package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.employment_info

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import java.text.DateFormatSymbols
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.SUCCESS_RESPONSE_CODE
import sv.com.tarjetaone.common.utils.extensions.SV_LOCALE
import sv.com.tarjetaone.common.utils.extensions.capitalize
import sv.com.tarjetaone.common.utils.extensions.configCurrencyWithFractions
import sv.com.tarjetaone.common.utils.extensions.formatPhoneNumber
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.response.JobsIncomesUI
import sv.com.tarjetaone.domain.usecases.personalData.employment.GetCustomerIncomesUseCase

@HiltViewModel
class EmploymentInfoOverviewViewModel @Inject constructor(
    private val getCustomerIncomesUseCase: GetCustomerIncomesUseCase,
    private val sharedPref: SecureSharedPreferencesRepository
) : BaseViewModel() {

    private val _uiState: MutableStateFlow<EmploymentInfoOverviewUiState> =
        MutableStateFlow(EmploymentInfoOverviewUiState())
    val uiState: StateFlow<EmploymentInfoOverviewUiState> = _uiState.asStateFlow()

    private fun fetchData() {
        sendEvent(UiEvent.Loading(true))
        viewModelScope.launch {
            getCustomerIncomesUseCase(sharedPref.getCustomerId().orZero()).executeUseCase {
                val response = it.statusResponse?.responseStatus
                if (response?.code != SUCCESS_RESPONSE_CODE) {
                    showSimpleError(response?.message.orEmpty())
                    return@executeUseCase
                }

                loadEmploymentInfo(it.data?.jobs.orEmpty())
            }
        }
    }

    private fun loadEmploymentInfo(incomeList: List<JobsIncomesUI>) {
        val employments = incomeList.map {
            EmploymentInfo(
                id = it.jobId.orZero(),
                contactId = it.customerContacts.firstOrNull()?.contactId.orZero(),
                contactTypeCode = it.customerContacts.firstOrNull()?.contactTypeCode.orEmpty(),
                incomeType = it.fIncomeTypeName.orEmpty(),
                fixedIncome = it.fixedIncome?.configCurrencyWithFractions().orEmpty(),
                variableIncome = it.variableIncome?.configCurrencyWithFractions().orEmpty(),
                companyName = it.companyName.orEmpty(),
                sinceDate = formatMonthAndYear(it.startMonth, it.startYear).orEmpty(),
                hiringType = it.hiringTypeName.orEmpty(),
                occupation = it.occupationName.orEmpty(),
                address = it.addresses.firstOrNull()?.fullAddress.orEmpty(),
                phoneNumber = it.customerContacts.firstOrNull()?.contactValue?.formatPhoneNumber().orEmpty()
            )
        }

        _uiState.value.employmentInfoList.clear()
        _uiState.value.employmentInfoList.addAll(employments)

        sendEvent(UiEvent.Loading(false))
    }

    private fun formatMonthAndYear(starMonth: Int?, startYear: Int?): String? {
        return starMonth?.let {
            val month = DateFormatSymbols(SV_LOCALE).months[it - ONE_VALUE]
            "${month.capitalize()} $startYear"
        }
    }

    private fun onModifyClick(jobId: Int, contactId: Int, contactTypeCode: String) {
        sendEvent(
            UiEvent.Navigate(
                EmploymentInfoOverviewFragmentDirections
                    .actionEmploymentInfoOverviewFragmentToPrepareForPictureEmploymentInfoFragment(
                        jobId = jobId,
                        contactId = contactId,
                        contactTypeCode = contactTypeCode
                    )
            )
        )
    }

    fun onEvent(event: EmploymentInfoOverviewUiEvent) {
        when (event) {
            EmploymentInfoOverviewUiEvent.OnStart -> fetchData()
            EmploymentInfoOverviewUiEvent.OnBackClick -> sendEvent(
                UiEvent.NavigateBackTo(R.id.personalDataFragment, false)
            )
            EmploymentInfoOverviewUiEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
            is EmploymentInfoOverviewUiEvent.OnModifyEmploymentInfo -> onModifyClick(
                jobId = event.jobId,
                contactId = event.contactId,
                contactTypeCode = event.contactTypeCode
            )
        }
    }
}
