package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.paging.LoadState
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.items
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.response.PointsCTUI
import sv.com.tarjetaone.domain.entities.response.TransactionsSearchMode
import sv.com.tarjetaone.domain.entities.response.TransactionsUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleLoadingIndicator
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer4
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.TransactionItemAction

@Composable
fun CardTransactionsList(
    userPoints: PointsCTUI,
    searchMode: TransactionsSearchMode,
    selectedTransaction: TransactionsUI?,
    transactionItems: LazyPagingItems<TransactionsUI>?,
    onTransactionAction: (TransactionItemAction, TransactionsUI) -> Unit,
    onAppendError: (() -> Unit) -> Unit
) {
    if (transactionItems == null || transactionItems.itemCount == 0) {
        EmptyTransactionsItem(
            modifier = Modifier.fillMaxSize(),
            image = if (searchMode is TransactionsSearchMode.ByCurrentPeriod) {
                R.drawable.ic_movement_default
            } else {
                R.drawable.ic_empty_movements
            },
            subtitle = stringResource(
                id = if (searchMode is TransactionsSearchMode.ByCurrentPeriod) {
                    R.string.my_movements_no_movements
                } else {
                    R.string.my_movements_no_result
                }
            )
        )
    } else {
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(4.dp),
            contentPadding = PaddingValues(horizontal = 35.dp, vertical = 10.dp),
            modifier = Modifier.fillMaxSize()
        ) {
            items(items = transactionItems) { transaction ->
                transaction?.let {
                    CardTransactionItem(
                        userPoints = userPoints,
                        transaction = it,
                        onTransactionAction = onTransactionAction,
                        isExpanded = selectedTransaction == it,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
            when (transactionItems.loadState.append) {
                is LoadState.Loading -> item {
                    SimpleLoadingIndicator(modifier = Modifier.fillMaxWidth())
                }

                is LoadState.Error -> {
                    onAppendError { transactionItems.retry() }
                }

                else -> Unit
            }
        }
    }
}

@Composable
fun EmptyTransactionsItem(
    modifier: Modifier = Modifier,
    @DrawableRes image: Int,
    imageSize: Dp? = null,
    title: String? = null,
    subtitle: String
) {
    Column(
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Image(
            modifier = Modifier.then(
                imageSize?.let { Modifier.size(it) } ?: Modifier
            ),
            painter = painterResource(id = image),
            contentDescription = null
        )
        Spacer24()
        title?.let {
            Text(
                text = it,
                style = MaterialTheme.typography.headlineLarge.copy(
                    color = MaterialTheme.customColors.gray700
                ),
                textAlign = TextAlign.Center
            )
            Spacer4()
        }
        Text(
            text = subtitle,
            style = MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.customColors.gray700
            ),
            textAlign = TextAlign.Center
        )
    }
}

@Preview
@Composable
private fun EmptyTransactionsItemPreview() {
    OneAppTheme {
        Surface {
            EmptyTransactionsItem(
                modifier = Modifier.fillMaxSize(),
                image = R.drawable.ic_movement_default,
                subtitle = stringResource(id = R.string.my_movements_no_movements)
            )
        }
    }
}
