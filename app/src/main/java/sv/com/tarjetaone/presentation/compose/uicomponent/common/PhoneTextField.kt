package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.FieldDecorationType
import sv.com.tarjetaone.presentation.compose.util.Spacer4
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.compose.util.input.MaskVisualTransformation
import sv.com.tarjetaone.presentation.helpers.filterDigits

/**
 * Composable that combines an area code picker and a phone number input field.
 * Note that the area code picker is not functional as it only displays the code for SV numbers.
 *
 * @param value The phone number value.
 * @param onValueChange The callback when the value changes. The value is always filtered to only
 * allow digits and a maximum of 8 characters.
 * @param visualTransformation By default the mask ####-#### is applied.
 * @param keyboardOptions By default the object is initialized with a phone keyboard type.
 *
 */
@Suppress("kotlin:S107")
@Composable
fun PhoneTextField(
    modifier: Modifier = Modifier,
    value: String,
    onValueChange: (String) -> Unit,
    readOnly: Boolean = false,
    placeholder: String? = null,
    label: String? = null,
    labelStyle: TextStyle = MaterialTheme.typography.bodySmall.copy(
        fontWeight = FontWeight.Medium
    ),
    hasError: Boolean = false,
    error: String? = null,
    errorStyle: TextStyle = MaterialTheme.typography.labelMedium.copy(
        color = MaterialTheme.colorScheme.error
    ),
    visualTransformation: VisualTransformation = MaskVisualTransformation(
        mask = MaskVisualTransformation.PHONE_WITH_DASH_MASK
    ),
    trailingIcon: (@Composable () -> Unit)? = null,
    decorationType: FieldDecorationType = FieldDecorationType.ELEVATED,
    keyboardOptions: KeyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone),
    keyboardActions: KeyboardActions = KeyboardActions.Default,
) {
    Column(
        modifier = modifier
    ) {
        label?.let {
            Text(
                text = it,
                style = labelStyle,
                color = MaterialTheme.customColors.gray700
            )
            Spacer4()
        }
        Row {
            SimpleElevatedTextField(
                value = stringResource(id = R.string.area_code),
                onValueChange = {},
                modifier = Modifier.width(MaterialTheme.customDimens.dimen100),
                readOnly = true,
                trailingIcon = {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_arrow_down_filled),
                        contentDescription = null,
                        tint = MaterialTheme.customColors.gray600,
                        modifier = Modifier.align(Alignment.CenterVertically)
                    )
                },
                hasError = hasError,
                decorationType = decorationType
            )
            Spacer8()
            SimpleElevatedTextField(
                value = value,
                onValueChange = {
                    onValueChange(it.filterDigits().take(AppConstants.PHONE_MAX_LENGTH))
                },
                modifier = Modifier.weight(1f),
                keyboardOptions = keyboardOptions,
                keyboardActions = keyboardActions,
                visualTransformation = visualTransformation,
                hasError = hasError,
                decorationType = decorationType,
                readOnly = readOnly,
                placeholder = placeholder,
                trailingIcon = trailingIcon
            )
        }
        if (hasError && error.isNullOrEmpty().not()) {
            Spacer4()
            Text(text = error.orEmpty(), style = errorStyle)
        }
    }
}

@Preview
@Composable
private fun PhoneTextFieldPreview() {
    OneAppTheme {
        PhoneTextField(
            value = "12345678",
            onValueChange = {},
            label = "Phone number"
        )
    }
}
