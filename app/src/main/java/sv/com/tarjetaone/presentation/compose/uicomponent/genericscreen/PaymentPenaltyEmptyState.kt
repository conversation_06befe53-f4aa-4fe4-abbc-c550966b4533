package sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.Spacer8

@Composable
fun PaymentPenaltyEmptyState(
    modifier: Modifier = Modifier,
    onPrimaryButtonClick: () -> Unit = { }
) {
    EmptyStateScreen(
        modifier = modifier,
        title = stringResource(id = R.string.charge_off_action_not_allowed),
        primaryActionText = stringResource(id = R.string.go_back),
        onPrimaryAction = onPrimaryButtonClick,
        contentDescription = {
            Column(
                modifier = Modifier
                    .padding(horizontal = MaterialTheme.customDimens.dimen24)
                    .fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(
                    text = stringResource(id = R.string.charge_off_payment_instructions),
                    style = MaterialTheme.typography.headlineSmall.copy(
                        color = MaterialTheme.colorScheme.secondary,
                        fontWeight = FontWeight.Normal,
                        textAlign = TextAlign.Center
                    )
                )
                Spacer8()
                Text(
                    text = stringResource(id = R.string.charge_off_contact_help),
                    style = MaterialTheme.typography.headlineSmall.copy(
                        color = MaterialTheme.colorScheme.secondary,
                        fontWeight = FontWeight.Normal,
                        textAlign = TextAlign.Center
                    )
                )
            }
        }
    )
}

@Preview(showBackground = true)
@Composable
private fun PaymentPenaltyEmptyStatePreview() {
    OneAppTheme {
        PaymentPenaltyEmptyState()
    }
}
