package sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.deactivate_program_protection.deactivated.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.card.HorizontalIconWithText
import sv.com.tarjetaone.presentation.helpers.UiText

@Composable
fun ProgramProtectionDeactivatedInfoCard(
    deactivationDate: String,
    chargeAmount: String
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.onPrimaryContainer
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = MaterialTheme.customDimens.dimen20
        ),
        modifier = Modifier.width(MaterialTheme.customDimens.dimen330)
    ) {
        CompositionLocalProvider(
            LocalContentColor provides MaterialTheme.colorScheme.onSurface
        ) {
            Column(
                modifier = Modifier.padding(MaterialTheme.customDimens.dimen32),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen32)
            ) {
                HorizontalIconWithText(
                    leadingImageIcon = R.drawable.ic_shield_blocked,
                    text = UiText.StringResource(
                        R.string.after_this_day_your_card_will_be_no_longer_protected,
                        deactivationDate
                    ),
                    style = MaterialTheme.typography.labelMedium.copy(
                        lineHeight = MaterialTheme.customDimensSp.sp14
                    )
                )
                HorizontalIconWithText(
                    leadingImageIcon = R.drawable.ic_shield_minus,
                    text = UiText.StringResource(
                        R.string.after_this_day_your_card_will_be_no_longer_protected_2,
                        chargeAmount
                    ),
                    style = MaterialTheme.typography.labelMedium.copy(
                        lineHeight = MaterialTheme.customDimensSp.sp14
                    )
                )
            }
        }
    }
}

@Preview
@Composable
private fun ProgramProtectionDeactivatedInfoCardPreview() {
    OneAppTheme {
        ProgramProtectionDeactivatedInfoCard(
            deactivationDate = "19 de septiembre",
            chargeAmount = "$1.99"
        )
    }
}
