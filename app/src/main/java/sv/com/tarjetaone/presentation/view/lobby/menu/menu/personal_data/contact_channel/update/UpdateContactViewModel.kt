package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.contact_channel.update

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.SUCCESS_OTP_SENT_CODE
import sv.com.tarjetaone.common.utils.AppConstants.SUCCESS_RESPONSE_CODE
import sv.com.tarjetaone.core.interfaces.PatternsValidator
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.request.CustomerContactValidationRequestUI
import sv.com.tarjetaone.domain.entities.request.CustomerContactValidationType
import sv.com.tarjetaone.domain.entities.response.ContactType
import sv.com.tarjetaone.domain.entities.response.OtpMethod
import sv.com.tarjetaone.domain.entities.response.OtpType
import sv.com.tarjetaone.domain.usecases.requestcard.otpvalidation.SendOtpCodeUseCase
import sv.com.tarjetaone.domain.usecases.requestcard.validatecustomercontact.ValidateCustomerContactUseCase
import sv.com.tarjetaone.domain.utils.onApiError
import sv.com.tarjetaone.domain.utils.onNetworkError
import sv.com.tarjetaone.domain.utils.onSuccess
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.helpers.hasInvalidPhoneNumber
import sv.com.tarjetaone.presentation.helpers.removeDash
import javax.inject.Inject

@HiltViewModel
class UpdateContactViewModel @Inject constructor(
    private val validateCustomerContactUseCase: ValidateCustomerContactUseCase,
    private val sendOtpCode: SendOtpCodeUseCase,
    private val patternsValidator: PatternsValidator,
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {
    private var contactValidationJob: Job? = null
    private val args = UpdateContactFragmentArgs.fromSavedStateHandle(savedStateHandle)
    private val _uiState = MutableStateFlow(UpdateContactUiState())
    val uiState = _uiState.asStateFlow()

    private fun onStart() {
        args.contactType?.let { type -> _uiState.update { it.copy(contactType = type) } }
    }

    private fun onEmailChange(email: String) {
        _uiState.update {
            it.copy(
                contact = email,
                isValidContact = patternsValidator.isEmailAddressValid(email),
            )
        }
    }

    private fun onPhoneChange(phone: String) {
        val isValidPhone = phone.hasInvalidPhoneNumber().not()
        _uiState.update {
            it.copy(
                contact = phone,
                isValidContact = isValidPhone,
                isValidatingContact = isValidPhone,
                contactError = if (isValidPhone) null else UiText.StringResource(R.string.invalid_phone_number),
            )
        }
        if (isValidPhone) validateCustomerContact(phone) else contactValidationJob?.cancel()
    }

    private fun validateCustomerContact(phoneNumber: String) {
        contactValidationJob?.cancel()
        _uiState.update {
            it.copy(
                isValidatingContact = true,
                contactError = null
            )
        }
        val request = CustomerContactValidationRequestUI(
            customerId = baseSharedPrefs.getCustomerId().orZero(),
            contact = phoneNumber.removeDash(),
            type = CustomerContactValidationType.PHONE
        )
        contactValidationJob = viewModelScope.launch {
            validateCustomerContactUseCase(request).executeUseCase(
                onSuccessAction = {
                    val isValidPhone = it.data
                    _uiState.update { state ->
                        state.copy(
                            isValidContact = isValidPhone,
                            contactError = if (isValidPhone) {
                                null
                            } else {
                                UiText.DynamicString(it.statusResponse?.responseStatus?.message.orEmpty())
                            },
                            isValidatingContact = false
                        )
                    }
                },
                onApiErrorAction = { _, _, _, _ ->
                    _uiState.update {
                        it.copy(
                            isValidContact = false,
                            isValidatingContact = false
                        )
                    }
                    onDefaultApiError()
                }
            )
        }
    }

    private fun onContinueClick() {
        if (_uiState.value.contactType == ContactType.Email) {
            sendOtpCode(OtpType.EMAIL, OtpMethod.EMAIL)
        } else {
            _uiState.update { it.copy(showOtpMethodDialog = true) }
        }
    }

    private fun onSelectOtpMethod(method: OtpMethod) {
        _uiState.update { it.copy(showOtpMethodDialog = false) }
        sendOtpCode(OtpType.PHONE, method)
    }

    private fun onDismissSelectOtpMethod() {
        _uiState.update { it.copy(showOtpMethodDialog = false) }
    }

    private fun sendOtpCode(otpType: OtpType, otpMethod: OtpMethod) {
        sendEvent(UiEvent.Loading(true))
        viewModelScope.launch {
            sendOtpCode(otpType, _uiState.value.contact, otpMethod).onSuccess {
                sendEvent(UiEvent.Loading(false))
                if (it.code == SUCCESS_RESPONSE_CODE || it.code == SUCCESS_OTP_SENT_CODE) {
                    sendEvent(
                        UiEvent.Navigate(
                            UpdateContactFragmentDirections
                                .actionUpdateContactFragmentToUpdateContactOtpValidationFragment(
                                    sharedKey = it.sharedKey,
                                    contact = _uiState.value.contact,
                                    contactType = args.contactType,
                                    contactId = args.contactId.orZero(),
                                    biometryId = args.biometryId.orZero()
                                )
                        )
                    )
                } else {
                    it.result?.let { message ->
                        _uiState.update { state ->
                            state.copy(errorMessage = UiText.DynamicString(message))
                        }
                    }
                }
            }.onApiError { _, error, _, _ ->
                sendEvent(UiEvent.Loading(false))
                error?.result?.let { message ->
                    _uiState.update { it.copy(errorMessage = UiText.DynamicString(message)) }
                }
            }.onNetworkError {
                showUpsErrorMessage()
            }
        }
    }

    fun onEvent(event: UpdateContactUiEvent) {
        when (event) {
            UpdateContactUiEvent.OnStart -> onStart()
            UpdateContactUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            UpdateContactUiEvent.OnContinueClick -> onContinueClick()
            is UpdateContactUiEvent.OnEmailChange -> onEmailChange(event.email)
            is UpdateContactUiEvent.OnPhoneChange -> onPhoneChange(event.phone)
            is UpdateContactUiEvent.OnSelectOtpMethod -> onSelectOtpMethod(event.method)
            UpdateContactUiEvent.OnDismissSelectOtpMethod -> onDismissSelectOtpMethod()
        }
    }
}
