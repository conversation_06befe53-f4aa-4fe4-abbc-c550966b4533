package sv.com.tarjetaone.presentation.view.common.cardpin.component

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpSize
import sv.com.tarjetaone.common.utils.AppConstants.ASTERISK
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.helpers.filterDigits

@Composable
@Suppress("LongParameterList")
fun PinTextField(
    modifier: Modifier = Modifier,
    length: Int,
    value: String,
    digitFieldSize: DpSize? = null,
    onValueChange: (String) -> Unit,
    hasError: Boolean = false
) {
    val focusRequester = remember { FocusRequester() }

    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }
    BasicTextField(
        modifier = Modifier
            .focusRequester(focusRequester),
        value = value,
        onValueChange = {
            onValueChange(it.filterDigits().take(length))
        },
        keyboardOptions = KeyboardOptions(
            keyboardType = KeyboardType.NumberPassword
        ),
        decorationBox = {
            Row(
                modifier = modifier,
                horizontalArrangement = Arrangement.spacedBy(
                    space = MaterialTheme.customDimens.dimen14,
                    alignment = Alignment.CenterHorizontally
                )
            ) {
                repeat(length) { index ->
                    val digit = value.getOrNull(index)?.toString().orEmpty()
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .then(
                                digitFieldSize?.let { Modifier.size(it) } ?: Modifier
                            )
                            .padding(MaterialTheme.customDimens.dimen1)
                            .border(
                                width = MaterialTheme.customDimens.dimen1,
                                color = when {
                                    hasError -> MaterialTheme.colorScheme.error
                                    !hasError && digit.isNotEmpty() -> {
                                        MaterialTheme.customColors.onDefaultSurface
                                    }

                                    else -> {
                                        MaterialTheme.colorScheme.tertiary
                                    }
                                },
                                shape = MaterialTheme.shapes.small
                            )
                            .clip(MaterialTheme.shapes.small)
                            .background(MaterialTheme.customColors.defaultSurface)
                    ) {
                        Text(
                            text = if (digit.isNotEmpty()) ASTERISK else digit,
                            style = MaterialTheme.typography.displayMedium
                        )
                    }
                }
            }
        }
    )
}

@Preview(showBackground = true)
@Composable
private fun PinTextFieldPreview() {
    OneAppTheme {
        PinTextField(
            modifier = Modifier.fillMaxWidth(),
            length = 4,
            value = "1234",
            onValueChange = {},
            digitFieldSize = DpSize(
                width = MaterialTheme.customDimens.dimen60,
                height = MaterialTheme.customDimens.dimen64
            )
        )
    }
}