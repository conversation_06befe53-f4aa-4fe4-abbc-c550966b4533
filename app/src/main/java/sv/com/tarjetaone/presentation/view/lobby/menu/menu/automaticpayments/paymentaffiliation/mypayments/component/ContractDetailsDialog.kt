package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.mypayments.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.window.Dialog
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.extensions.configCurrencyWithFractions
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.affiliationform.NotificationChannelType
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.contractslist.RecurringChargeContractUI
import sv.com.tarjetaone.domain.utils.automaticpayments.servicespayment.invoiceselection.ProductTypeCode
import sv.com.tarjetaone.presentation.compose.shape.BottomPerforatedShape
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.ButtonSize
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.NonEmptyValueWrapper
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.component.getLabel
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.mypayments.MyPaymentsDummyData

@Composable
fun ContractDetailsDialog(
    modifier: Modifier = Modifier,
    contract: RecurringChargeContractUI,
    notificationChannelLabel: UiText? = null,
    onEditRecurringCharge: () -> Unit,
    onViewPaymentHistory: () -> Unit,
    onDismissRequest: () -> Unit,
) {
    Dialog(
        onDismissRequest = onDismissRequest
    ) {
        Column(
            modifier = modifier
                .clip(
                    RoundedCornerShape(
                        topStart = MaterialTheme.customDimens.dimen32,
                        topEnd = MaterialTheme.customDimens.dimen32
                    )
                )
                .clip(BottomPerforatedShape(MaterialTheme.customDimens.dimen10))
                .background(MaterialTheme.colorScheme.surface)
                .padding(MaterialTheme.customDimens.dimen16)
        ) {
            DialogHeader(
                serviceName = contract.serviceName,
                onCloseAction = onDismissRequest,
            )
            NonEmptyValueWrapper(contract.maxAmount.toDoubleOrNull().orZero()) { value ->
                Spacer8()
                InfoLine(
                    label = stringResource(R.string.my_payments_max_amount),
                    value = value.configCurrencyWithFractions(),
                    modifier = Modifier.fillMaxWidth()
                )
            }
            Spacer8()
            InfoLine(
                label = stringResource(R.string.my_payments_last_charge),
                value = stringResource(R.string.money_string_format, contract.applicationAmount),
                modifier = Modifier.fillMaxWidth()
            )
            Spacer8()
            InfoLine(
                label = stringResource(R.string.my_payments_date_last_charge),
                value = contract.formattedLastApplicationDate(),
                modifier = Modifier.fillMaxWidth()
            )
            HorizontalDivider(
                modifier = Modifier.padding(vertical = MaterialTheme.customDimens.dimen8),
                thickness = MaterialTheme.customDimens.dimen1,
                color = MaterialTheme.customColors.gray200
            )
            InfoColumn(
                label = stringResource(id = R.string.my_payments_charge_account),
                value = "${
                    ProductTypeCode.fromString(contract.contractProductType)
                        .getLabel(appendOneLabel = true)?.asString()
                } ${contract.contractProduct}",
            )
            HorizontalDivider(
                modifier = Modifier.padding(vertical = MaterialTheme.customDimens.dimen4),
                thickness = MaterialTheme.customDimens.dimen1,
                color = MaterialTheme.customColors.gray200
            )
            InfoColumn(
                label = stringResource(id = R.string.my_payments_notification_channel),
                value = notificationChannelLabel?.asString().orEmpty(),
            )
            contract.identifiers.firstOrNull()?.let { identifier ->
                HorizontalDivider(
                    modifier = Modifier.padding(vertical = MaterialTheme.customDimens.dimen4),
                    thickness = MaterialTheme.customDimens.dimen1,
                    color = MaterialTheme.customColors.gray200
                )
                InfoColumn(
                    label = stringResource(id = R.string.account_number),
                    value = identifier.identifier,
                )
            }
            Spacer32()
            BottomSection(
                modifier = Modifier.fillMaxWidth(),
                onEditRecurringCharge = onEditRecurringCharge,
                onViewPaymentHistory = onViewPaymentHistory,
            )
        }
    }
}

@Composable
private fun DialogHeader(
    onCloseAction: () -> Unit,
    serviceName: String,
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f),
        ) {
            Text(
                modifier = Modifier.align(Alignment.CenterHorizontally),
                text = serviceName,
                style = MaterialTheme.typography.bodyLarge.copy(
                    fontWeight = FontWeight.SemiBold
                ),
            )
        }
        CloseDialogButton(onCloseAction = onCloseAction)
    }
}

@Composable
private fun BottomSection(
    modifier: Modifier = Modifier,
    onEditRecurringCharge: () -> Unit,
    onViewPaymentHistory: () -> Unit,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        OneButton(
            modifier = Modifier.widthIn(
                min = MaterialTheme.customDimens.dimen190
            ),
            text = stringResource(id = R.string.my_payments_edit_service),
            buttonVariant = ButtonVariant.SECONDARY_VARIANT,
            size = ButtonSize.MEDIUM,
            onClick = onEditRecurringCharge,
        )
        Spacer8()
        OneButton(
            modifier = Modifier.widthIn(
                min = MaterialTheme.customDimens.dimen190
            ),
            text = stringResource(id = R.string.my_payments_see_payment_history),
            size = ButtonSize.MEDIUM,
            onClick = onViewPaymentHistory,
        )
        Spacer24()
    }
}

@Composable
private fun CloseDialogButton(
    onCloseAction: () -> Unit,
) {
    IconButton(
        modifier = Modifier
            .size(MaterialTheme.customDimens.dimen26)
            .clip(MaterialTheme.shapes.extraLarge),
        onClick = onCloseAction
    ) {
        Icon(
            modifier = Modifier.fillMaxSize(),
            imageVector = ImageVector.vectorResource(id = R.drawable.ic_x_circle_red),
            tint = MaterialTheme.colorScheme.primary,
            contentDescription = null,
        )
    }
}

@Preview
@Composable
private fun ContractDetailsDialogPreview() {
    OneAppTheme {
        ContractDetailsDialog(
            notificationChannelLabel = NotificationChannelType.WhatsApp.getLabel(),
            onEditRecurringCharge = { },
            onViewPaymentHistory = { },
            onDismissRequest = { },
            contract = MyPaymentsDummyData.recurringChargeContract,
        )
    }
}
