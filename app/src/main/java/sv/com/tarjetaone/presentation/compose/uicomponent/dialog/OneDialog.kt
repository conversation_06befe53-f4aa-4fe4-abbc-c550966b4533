package sv.com.tarjetaone.presentation.compose.uicomponent.dialog

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.text.BulletText
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.helpers.UiText.BulletPointString

@Composable
fun OneDialog(
    params: OneDialogParams,
    onDismissRequest: () -> Unit,
) {
    Dialog(
        onDismissRequest = {
            onDismissRequest()
            params.onDismiss()
        },
        properties = DialogProperties(
            dismissOnBackPress = params.isDismissible,
            dismissOnClickOutside = params.isDismissible,
        )
    ) {
        Card(
            colors = CardDefaults.cardColors().copy(
                containerColor = MaterialTheme.colorScheme.background,
            ),
            modifier = Modifier.fillMaxWidth()
        ) {
            Box(modifier = Modifier.wrapContentSize()) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .fillMaxWidth()
                        .verticalScroll(rememberScrollState())
                        .padding(MaterialTheme.customDimens.dimen24),
                ) {
                    params.icon?.let {
                        Image(
                            imageVector = ImageVector.vectorResource(it),
                            contentDescription = null,
                            modifier = Modifier
                                .size(MaterialTheme.customDimens.dimen46)
                                .padding(bottom = MaterialTheme.customDimens.dimen8)
                        )
                    }
                    params.title?.let {
                        Text(
                            text = it.asString(),
                            style = MaterialTheme.typography.headlineSmall,
                            color = MaterialTheme.customColors.onHeadingTextDark,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.padding(bottom = MaterialTheme.customDimens.dimen8)
                        )
                    }
                    params.message?.let {
                        if (it.text is BulletPointString) {
                            Text(
                                modifier = Modifier.fillMaxWidth(),
                                text = stringResource(it.text.titleRes),
                                style = MaterialTheme.typography.bodySmall.copy(
                                    fontSize = it.size.sp,
                                    textAlign = it.alignment.textAlign,
                                    lineHeight = it.size.lineHeight
                                ),
                                color = MaterialTheme.customColors.bodyLightGray,
                            )
                            it.text.itemResList.forEach { bulletText ->
                                BulletText(
                                    modifier = Modifier.fillMaxWidth(),
                                    text = stringResource(bulletText),
                                    style = MaterialTheme.typography.bodySmall.copy(
                                        fontSize = it.size.sp,
                                        textAlign = it.alignment.textAlign,
                                        color = MaterialTheme.customColors.bodyLightGray,
                                        lineHeight = it.size.lineHeight
                                    )
                                )
                            }
                        } else {
                            Text(
                                text = it.text.asAnnotatedString(),
                                style = MaterialTheme.typography.bodySmall.copy(
                                    fontSize = it.size.sp,
                                    textAlign = it.alignment.textAlign
                                ),
                                color = MaterialTheme.customColors.bodyLightGray,
                            )
                        }
                    }
                    Spacer24()
                    OneDialogButton(
                        text = params.primaryAction.text.asString(),
                        onClick = {
                            onDismissRequest()
                            params.primaryAction.onClick()
                            params.onDismiss()
                        },
                        buttonColor = params.primaryAction.actionType.color,
                    )
                    params.secondaryAction?.let {
                        Spacer16()
                        Text(
                            text = it.text.asString(),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.primary,
                            textAlign = TextAlign.Center,
                            textDecoration = TextDecoration.Underline,
                            modifier = Modifier.clickable {
                                onDismissRequest()
                                it.onClick()
                                params.onDismiss()
                            }
                        )
                    }
                }
                if (params.showCloseButton) {
                    Icon(
                        imageVector = ImageVector.vectorResource(R.drawable.ic_close_sign),
                        contentDescription = null,
                        modifier = Modifier
                            .padding(MaterialTheme.customDimens.dimen16)
                            .size(MaterialTheme.customDimens.dimen12)
                            .align(Alignment.TopEnd)
                            .clickable {
                                onDismissRequest()
                                params.onDismiss()
                            }
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun OneDialogPreview() {
    OneAppTheme {
        OneDialog(
            params = OneDialogParams(
                message = MessageParams(
                    text = BulletPointString(
                        titleRes = R.string.facephi_selfie_suggestions_title,
                        itemResList = listOf(
                            R.string.facephi_selfie_suggestions_item1,
                            R.string.facephi_selfie_suggestions_item2,
                            R.string.facephi_selfie_suggestions_item3,
                            R.string.facephi_selfie_suggestions_item4
                        )
                    ),
                    alignment = MessageParams.MessageAlign.START
                )
            ),
            onDismissRequest = {}
        )
    }
}
