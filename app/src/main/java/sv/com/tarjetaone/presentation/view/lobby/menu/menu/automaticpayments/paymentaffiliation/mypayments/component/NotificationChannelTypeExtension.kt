package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.mypayments.component

import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.affiliationform.NotificationChannelType
import sv.com.tarjetaone.presentation.helpers.UiText

/**
 * Extension function to get the label of the NotificationChannelType.
 * If it's "NOTEMAIL", return the string resource for email.
 * If it's "NOTWHATS", return the string resource for WhatsApp.
 */
fun NotificationChannelType.getLabel(): UiText =
    when (this) {
        NotificationChannelType.Email -> UiText.StringResource(R.string.my_payments_notification_channel_email)
        NotificationChannelType.WhatsApp -> UiText.StringResource(R.string.my_payments_notification_channel_whatsapp)
        NotificationChannelType.Unknown -> UiText.DynamicString(EMPTY_STRING)
    }
