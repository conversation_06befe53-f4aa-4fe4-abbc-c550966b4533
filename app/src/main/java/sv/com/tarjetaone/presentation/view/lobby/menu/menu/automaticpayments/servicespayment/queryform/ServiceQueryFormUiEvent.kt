package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.queryform

import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldUI

sealed class ServiceQueryFormUiEvent {
    data object OnStart : ServiceQueryFormUiEvent()
    data object OnBackClick : ServiceQueryFormUiEvent()
    data object OnTwilioClick : ServiceQueryFormUiEvent()
    data class OnPXFieldChange(
        val index: Int,
        val typedValue: String,
        val selectedValue: Pair<String, PXFieldUI>?
    ) : ServiceQueryFormUiEvent()
    data class OnQueryOptionChange(
        val selectedQueryOption: Pair<String, PXFieldUI>
    ) : ServiceQueryFormUiEvent()
    data class OnReferenceChange(
        val reference: String
    ) : ServiceQueryFormUiEvent()
    data object OnContinueClick : ServiceQueryFormUiEvent()
}
