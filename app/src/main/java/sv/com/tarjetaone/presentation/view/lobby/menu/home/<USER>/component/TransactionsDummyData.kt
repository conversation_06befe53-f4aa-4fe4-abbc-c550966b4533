package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component

import sv.com.tarjetaone.domain.entities.response.TransactionsUI
import sv.com.tarjetaone.domain.entities.response.UnrecognizedTransactionUI

val itemAllActionsAllowed = TransactionsUI(
    panMasked = "4513 **** **** 4676",
    referenceNumber = "01234567890122000920065",
    authorizationCode = "000765",
    valueDate = "1/22/2024",
    type = "D",
    amount = 350.00,
    pointsNeeded = 70000,
    description = "Tienda Lorem ipsum dolor sit amet, consectetur adipiscing elit",
    isPosted = true,
    notRecognizedAllowed = true,
    installmentsAllowed = true,
    pointsPaymentAllowed = true,
    pointsPaymentMessage = true,
    managementText = "Compra no reconocida",
    installmentTerms = null
)

val itemNoActionsAllowed = itemAllActionsAllowed.copy(
    notRecognizedAllowed = false,
    installmentsAllowed = false,
    pointsPaymentAllowed = false,
    managementText = "Compra no reconocida",
    isPosted = false
)

val itemNoPaymentOptions = itemAllActionsAllowed.copy(
    notRecognizedAllowed = true,
    installmentsAllowed = false,
    pointsPaymentAllowed = false
)

val itemCredit = itemAllActionsAllowed.copy(
    type = "C",
    description = "Deposito en efectivo",
    notRecognizedAllowed = false,
    installmentsAllowed = false,
    pointsPaymentAllowed = false
)


val unrecognizedTransaction = UnrecognizedTransactionUI(
    referenceNumber = "Numero referencia",
    authorizationNumber = "123123",
    valueDate = "28 jul 2020",
    amount = 20.0F,
    description = "Lorem ipsum dolor sit amet, consectetur adipiscing elit"
)
