package sv.com.tarjetaone.presentation.compose.uicomponent.checkbox

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxColors
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.LocalMinimumInteractiveComponentEnforcement
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.presentation.compose.theme.LocalCustomColors
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.text.ClickableHyperLinkText
import sv.com.tarjetaone.presentation.compose.uicomponent.text.ClickableHyperLinkTextProps
import sv.com.tarjetaone.presentation.compose.util.Spacer4

@OptIn(ExperimentalMaterial3Api::class)
@Suppress("kotlin:S107")
@Composable
fun CheckBoxWithText(
    modifier: Modifier = Modifier,
    checkBoxColors: CheckboxColors = CheckboxDefaults.colors(
        uncheckedColor = LocalCustomColors.current.gray500,
        checkedColor = LocalCustomColors.current.successContainer,
        checkmarkColor = Color.White
    ),
    text: String? = null,
    textStyle: TextStyle = MaterialTheme.typography.labelMedium,
    clickableHyperLinkTextProps: ClickableHyperLinkTextProps? = null,
    color: Color = MaterialTheme.colorScheme.onSurface,
    isChecked: Boolean,
    onCheckedChanged: ((Boolean) -> Unit)?,
    decreaseCheckboxPadding: Boolean = false,
    increaseCheckboxTouchTarget: Boolean = true,
    verticalAlignment: Alignment.Vertical = Alignment.CenterVertically
) {
    Row(
        modifier = modifier
            .then(
                if (increaseCheckboxTouchTarget) {
                    Modifier.padding(end = MaterialTheme.customDimens.dimen16)
                } else Modifier
            ),
        verticalAlignment = verticalAlignment
    ) {
        CompositionLocalProvider(
            LocalMinimumInteractiveComponentEnforcement provides increaseCheckboxTouchTarget
        ) {
            Checkbox(
                modifier = if (decreaseCheckboxPadding)
                    Modifier
                        .padding(end = MaterialTheme.customDimens.dimen8)
                        .width(MaterialTheme.customDimens.dimen24)
                else Modifier,
                checked = isChecked,
                onCheckedChange = onCheckedChanged,
                colors = checkBoxColors
            )
        }
        if (!increaseCheckboxTouchTarget) Spacer4()
        text?.let {
            Text(
                text = it,
                color = color,
                style = textStyle
            )
        } ?: clickableHyperLinkTextProps?.let {
            ClickableHyperLinkText(hyperLinkTextProps = it)
        }
    }
}

@Preview
@Composable
fun CheckBoxWithTextPreview() {
    OneAppTheme {
        CheckBoxWithText(
            modifier = Modifier,
            text = "CheckBox text",
            color = Color.White,
            isChecked = true,
            onCheckedChanged = {},
        )
    }
}
