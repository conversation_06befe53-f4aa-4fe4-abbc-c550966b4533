package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.dataoverview

import androidx.lifecycle.SavedStateHandle
import com.facephi.fphiselphidwidgetcore.WidgetSelphIDResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.CommonDialogWithIconParams
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.core.utils.extensions.setGender
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.core.utils.openAppSettings
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.response.CustomerDataUI
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.helpers.addDashDui
import sv.com.tarjetaone.presentation.helpers.capitalizeAllWords
import javax.inject.Inject

@HiltViewModel
class DataOverviewAdditionalCardViewModel @Inject constructor(
    private val sharedPrefsRepo: SecureSharedPreferencesRepository,
    private val facephiResultHandler: FacephiResultHandler,
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {

    private val args = DataOverviewAdditionalCardFragmentArgs
        .fromSavedStateHandle(savedStateHandle)

    private val _uiState = MutableStateFlow(DataOverviewAdditionalCardUiState())
    val uiState = _uiState.asStateFlow()

    fun onEvent(event: DataOverviewAdditionalCardUiEvent) {
        when (event) {
            DataOverviewAdditionalCardUiEvent.OnStart -> onStart()
            DataOverviewAdditionalCardUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            DataOverviewAdditionalCardUiEvent.OnTwilioButtonClick -> sendEvent(UiEvent.TwilioClick)
            DataOverviewAdditionalCardUiEvent.OnEditClick -> showEditDialog()
            DataOverviewAdditionalCardUiEvent.OnConfirmButtonClick -> onConfirmAction()
            is DataOverviewAdditionalCardUiEvent.OnWrongDataButtonClick -> onWrongDataClick {
                event.requestPermission()
            }

            is DataOverviewAdditionalCardUiEvent.OnDocumentCapture -> onDocumentCaptured(event.result)
            is DataOverviewAdditionalCardUiEvent.OnCameraPermissionDenied -> onCameraPermissionDenied(
                event.showRationale
            )
        }
    }

    private fun onStart() {
        setUserDocumentData()
    }

    private fun setUserDocumentData() {
        sharedPrefsRepo.userAdditionalCardData?.let { userAdditionalCard ->
            val names = userAdditionalCard.customer.name.capitalizeAllWords()
            val lastNames = userAdditionalCard.customer.lastName.capitalizeAllWords()

            _uiState.update {
                it.copy(
                    customerData = CustomerDataUI(
                        fullName = "$names $lastNames",
                        genderCode = userAdditionalCard.customer.genderCode.setGender(),
                        dui = userAdditionalCard.customer.documents.document.addDashDui(),
                        duiExpirationDate = userAdditionalCard.customer.documents.expirationDate
                            ?.getFormattedDateFromTo(
                                AppConstants.YEAR_MONTH_DAY_NO_SPACES,
                                AppConstants.DAY_MONTH_YEAR_WITH_SPACES
                            ).orEmpty(),
                        birthDate = userAdditionalCard.customer.birthDate
                            .getFormattedDateFromTo(
                                AppConstants.YEAR_MONTH_DAY_NO_SPACES,
                                AppConstants.DAY_FULL_MONTH_NAME_YEAR_WITH_SPACES
                            )
                    )
                )
            }
        }
    }

    private fun onConfirmAction() {
        val direction = if (args.shouldSkipAdditionalInformation) {
            DataOverviewAdditionalCardFragmentDirections
                .actionDataOverviewAdditionalCardToPersonalizationAditionalCardFragment()
        } else {
            DataOverviewAdditionalCardFragmentDirections
                .actionDataOverviewAdditionalCardAdditionalInformation()
        }
        sendEvent(UiEvent.Navigate(direction))
    }

    private fun showEditDialog() {
        sendEvent(
            UiEvent.ShowCommonDialog(
                CommonDialogWithIconParams(
                    icon = R.drawable.ic_check_yellow,
                    title = UiText.StringResource(
                        R.string.document_dialog_title
                    ),
                    message = UiText.StringResource(
                        R.string.document_dialog_subtitle
                    ),
                    buttonColor = R.color.yellow_alert_button,
                    buttonText = UiText.StringResource(R.string.continue_button_label),
                    onButtonClick = {
                        sendEvent(
                            UiEvent.Navigate(
                                DataOverviewAdditionalCardFragmentDirections
                                    .actionDataOverviewAdditionalCardToPersonalDataEditFragment()
                            )
                        )
                    }
                )
            )
        )
    }

    private fun onWrongDataClick(requestPermission: () -> Unit) {
        requestPermission()
    }

    private fun onCameraPermissionDenied(showRationale: Boolean) {
        sendEvent(
            UiEvent.ShowCommonDialog(
                CommonDialogWithIconParams(
                    title = UiText.StringResource(R.string.camera_access),
                    message = UiText.StringResource(R.string.camera_rationale_document),
                    buttonText = UiText.StringResource(if (showRationale) R.string.accept_label else R.string.settings),
                    onButtonClick = {
                        if (!showRationale) {
                            sendEvent(UiEvent.StartIntent(openAppSettings()))
                        }
                    }
                )
            )
        )
    }

    private fun onDocumentCaptured(result: WidgetSelphIDResult) {
        val event = facephiResultHandler.onDocumentCaptured(result)?.let {
            UiEvent.NavigateBack
        } ?: UiEvent.ShowCommonDialog(
            CommonDialogWithIconParams(
                isDismissible = false,
                icon = R.drawable.ic_error_yellow,
                buttonColor = R.color.yellow_alert_button,
                title = UiText.StringResource(R.string.error_doc_picture_title),
                message = UiText.StringResource(R.string.error_doc_picture_desc),
                buttonText = UiText.StringResource(R.string.take_picture)
            )
        )
        sendEvent(event)
    }
}