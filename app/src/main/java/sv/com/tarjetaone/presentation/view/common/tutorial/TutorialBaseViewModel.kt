package sv.com.tarjetaone.presentation.view.common.tutorial

import androidx.navigation.NavDirections
import com.facephi.fphiselphidwidgetcore.WidgetExceptionType
import com.facephi.fphiselphidwidgetcore.WidgetSelphIDResult
import com.facephi.fphiwidgetcore.WidgetResult
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.R
import sv.com.tarjetaone.analytics.dynatrace.DynatraceEvent
import sv.com.tarjetaone.analytics.dynatrace.DynatraceManager
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.core.utils.openAppSettings
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.DialogAction
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.MessageParams
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.OneDialogParams
import sv.com.tarjetaone.presentation.helpers.UiText

abstract class TutorialBaseViewModel(
    private val dynatraceManager: DynatraceManager,
    private val facephiResultHandler: FacephiResultHandler,
) : BaseViewModel() {

    private val _uiState: MutableStateFlow<TutorialUiState> =
        MutableStateFlow(TutorialUiState())
    val uiState: StateFlow<TutorialUiState> = _uiState.asStateFlow()

    abstract fun getTutorialType(): TutorialType

    abstract fun getNextDestination(): NavDirections

    protected open fun onContinueClick() = Unit

    protected open fun onStart() {
        val type = getTutorialType()
        _uiState.update {
            it.copy(
                tutorialType = type,
                isCloseButtonVisible = isTutorialDisplayed(type),
                items = type.items
            )
        }
    }

    private fun updateTutorialState() {
        with(baseSharedPrefs) {
            when (_uiState.value.tutorialType) {
                TutorialType.Document -> hasDisplayedDocumentTutorial = true
                is TutorialType.Selfie -> hasDisplayedSelfieTutorial = true
            }
        }
    }

    fun isTutorialDisplayed(tutorialType: TutorialType): Boolean {
        return when (tutorialType) {
            TutorialType.Document -> baseSharedPrefs.hasDisplayedDocumentTutorial
            is TutorialType.Selfie -> baseSharedPrefs.hasDisplayedSelfieTutorial
                    && tutorialType.type == SelfieTutorialType.DEFAULT
        }
    }

    private fun onDocumentCapture(result: WidgetSelphIDResult) {
        facephiResultHandler.onDocumentCaptured(result)?.let {
            sendEvent(
                UiEvent.Navigate(
                    direction = getNextDestination()
                )
            )
        } ?: sendEvent(
            SideEffect.ShowOneDialog(
                params = OneDialogParams(
                    icon = R.drawable.ic_error_yellow,
                    title = UiText.StringResource(R.string.error_doc_picture_title),
                    message = MessageParams(
                        text = UiText.StringResource(R.string.error_doc_picture_desc)
                    ),
                    primaryAction = DialogAction(
                        text = UiText.StringResource(R.string.facephi_dialog_agree),
                        actionType = DialogAction.ActionType.WARNING
                    ),
                    isDismissible = false,
                )
            )
        )
    }

    private fun onCameraPermissionDenied(showRationale: Boolean) {
        sendEvent(
            event = SideEffect.ShowOneDialog(
                params = OneDialogParams(
                    icon = R.drawable.ic_match_error,
                    title = UiText.StringResource(R.string.camera_access),
                    message = MessageParams(
                        text = UiText.StringResource(R.string.camera_rationale_document)
                    ),
                    primaryAction = DialogAction(
                        text = UiText.StringResource(if (showRationale) R.string.accept_label else R.string.settings),
                        onClick = {
                            if (!showRationale) {
                                sendEvent(SideEffect.StartIntent(openAppSettings()))
                            }
                        }
                    ),
                )
            )
        )
    }

    private fun onCaptureFailed(result: Any) {
        if (getTutorialType() == TutorialType.Document) {
            onDocumentCaptureFailed(result as WidgetSelphIDResult)
        } else {
            onSelfieCaptureFailed()
        }
    }

    private fun onCaptureResult(result: Any) {
        if (getTutorialType() == TutorialType.Document) {
            onDocumentCapture(result as WidgetSelphIDResult)
        } else {
            onSelfieCaptureResult(result as WidgetResult)
        }
    }

    private fun onDocumentCaptureFailed(result: WidgetSelphIDResult) {
        facephiResultHandler.onDocumentCaptureFailed(result) {
            val title = when (it) {
                WidgetExceptionType.Timeout -> R.string.facephi_timeout_title
                else -> R.string.error_doc_picture_title
            }
            sendEvent(
                SideEffect.ShowOneDialog(
                    params = OneDialogParams(
                        isDismissible = false,
                        icon = R.drawable.ic_error_yellow,
                        title = UiText.StringResource(title),
                        message = MessageParams(
                            text = UiText.StringResource(R.string.error_doc_picture_desc)
                        ),
                        primaryAction = DialogAction(
                            text = UiText.StringResource(R.string.facephi_dialog_agree),
                            onClick = { sendEvent(UiEvent.NavigateBack) },
                            actionType = DialogAction.ActionType.WARNING
                        )
                    )
                )
            )
        }
    }

    private fun onSelfieCaptureFailed() {
        if (uiState.value.isCloseButtonVisible) {
            sendEvent(UiEvent.NavigateBack)
        }
    }

    private fun onSelfieCaptureResult(result: WidgetResult) {
        facephiResultHandler.onSelfieCaptured(result)
        sendEvent(
            UiEvent.Navigate(getNextDestination())
        )
    }

    private fun onCloseButtonClick(requestPermission: () -> Unit) {
        dynatraceManager.sendInAppEvent(
            event = if (getTutorialType() is TutorialType.Selfie) {
                DynatraceEvent.ShortCapture.ClosePortraitTutorial1
            } else {
                DynatraceEvent.ShortCapture.CloseDocumentTutorial
            }
        )

        if (isTutorialDisplayed(getTutorialType())) {
            sendEvent(UiEvent.NavigateBack)
        } else {
            updateTutorialState()
            requestPermission()
        }
    }

    private fun onCaptureClick(requestPermission: () -> Unit) {
        if (getTutorialType() == TutorialType.Document) {
            dynatraceManager.sendInAppEvent(
                event = DynatraceEvent.ShortCapture.DocumentTutorial3
            )
        }
        updateTutorialState()
        requestPermission()
    }

    fun onEvent(event: TutorialUiEvent) {
        when (event) {
            is TutorialUiEvent.OnStart -> onStart()
            is TutorialUiEvent.OnCameraPermissionDenied -> onCameraPermissionDenied(event.showRationale)
            is TutorialUiEvent.OnCaptureClick -> onCaptureClick(event.requestPermission)
            is TutorialUiEvent.OnCloseButtonClick -> onCloseButtonClick(event.requestPermission)
            is TutorialUiEvent.OnFacephiCaptureFailed -> onCaptureFailed(event.result)
            is TutorialUiEvent.OnFacephiCaptureResult -> onCaptureResult(event.result)
            TutorialUiEvent.OnContinueClick -> onContinueClick()
        }
    }
}