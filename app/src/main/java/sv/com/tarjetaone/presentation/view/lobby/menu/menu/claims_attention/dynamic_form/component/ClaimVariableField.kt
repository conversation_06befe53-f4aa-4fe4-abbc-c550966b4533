package sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.dynamic_form.component

import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.domain.entities.response.CatalogsUI
import sv.com.tarjetaone.domain.entities.response.FieldType
import sv.com.tarjetaone.domain.entities.response.VariableFieldUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedDropdown
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.compose.util.FieldDecorationType
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.dynamic_form.util.VariableFieldDummyUiStates
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.dynamic_form.util.VariableFieldUiState
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.dynamic_form.util.keyboardType

/**
 * Component that represents a variable field in a claim form.
 * Can be a text field or a dropdown. Determined by [VariableFieldUI.fieldType].
 */
@Composable
fun ClaimVariableField(
    modifier: Modifier = Modifier,
    fieldState: VariableFieldUiState,
    onTypedValueChange: (String) -> Unit,
    onSelectedValueChange: (CatalogsUI) -> Unit
) {
    if (fieldState.variableField.fieldType == FieldType.SelectedValue) {
        SimpleElevatedDropdown(
            modifier = modifier,
            items = fieldState.variableField.catalogs,
            itemLabel = { it.fieldTypeCatalogName.orEmpty() },
            value = fieldState.selectedValue,
            onValueChange = onSelectedValueChange,
            label = fieldState.variableField.manAttributeTypeNameApp,
            decorationType = FieldDecorationType.OUTLINED
        )
    } else {
        val focusManager = LocalFocusManager.current
        SimpleElevatedTextField(
            modifier = modifier,
            value = fieldState.typedValue,
            onValueChange = onTypedValueChange,
            placeholder = fieldState.variableField.placeholder,
            label = fieldState.variableField.manAttributeTypeNameApp,
            decorationType = FieldDecorationType.OUTLINED,
            keyboardOptions = KeyboardOptions(
                imeAction = ImeAction.Next,
                keyboardType = fieldState.variableField.keyboardType()
            ),
            keyboardActions = KeyboardActions(
                onNext = { focusManager.moveFocus(FocusDirection.Down) }
            )
        )
    }
}

@Preview
@Composable
private fun ClaimVariableFieldTypedValuePreview() {
    OneAppTheme {
        ClaimVariableField(
            fieldState = VariableFieldDummyUiStates.typedEmail,
            onTypedValueChange = {},
            onSelectedValueChange = {}
        )
    }
}

@Preview
@Composable
private fun ClaimVariableFieldSelectedValuePreview() {
    OneAppTheme {
        ClaimVariableField(
            fieldState = VariableFieldDummyUiStates.selectableMonth,
            onTypedValueChange = {},
            onSelectedValueChange = {}
        )
    }
}
