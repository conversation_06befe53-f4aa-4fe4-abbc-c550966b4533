package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.report.identity.validation

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.AppConstants.CHA_APP
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.usecases.auth.FaceAuthenticationUseCase
import sv.com.tarjetaone.domain.usecases.report.ReportUnrecognizedPurchaseUseCase
import sv.com.tarjetaone.presentation.view.common.identity_validation.IdentityValidationBaseViewModel
import javax.inject.Inject

@HiltViewModel
class ReportValidationViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    faceAuthenticationUseCase: FaceAuthenticationUseCase,
    sharedPreferencesRepository: SecureSharedPreferencesRepository,
    private val reportUnrecognizedPurchaseUseCase: ReportUnrecognizedPurchaseUseCase
) : IdentityValidationBaseViewModel(faceAuthenticationUseCase, sharedPreferencesRepository) {
    private val args = ReportValidationFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override val onBlockedAction = {
        sendEvent(UiEvent.Navigate(ReportValidationFragmentDirections.actionHome()))
    }

    private fun navigateBack() = sendEvent(UiEvent.NavigateBack)

    override fun operation(biometryId: Int) {
        viewModelScope.launch {
            reportUnrecognizedPurchaseUseCase(
                cardStatusId = args.cardStatusId.toIntOrNull().orZero(),
                cCardId = args.cardId,
                channelCode = CHA_APP,
                biometryId = biometryId,
                transactionUI = args.reportableTransactions?.toList().orEmpty()
            ).executeUseCase(
                onApiErrorAction = { _, _, _, _ ->
                    showUpsErrorMessage(false) {
                        navigateBack()
                    }
                },
                onSuccessAction = {
                    sendEvent(
                        UiEvent.Navigate(
                            ReportValidationFragmentDirections
                                .actionReportValidationFragmentToReportPurchaseSuccessFragment(
                                    cardStatusId = args.cardStatusId,
                                    cardId = args.cardId,
                                    cardBlockId = it.cardBlockManNumber.orEmpty(),
                                    managementId = it.management?.manNumberApp.orEmpty()
                                )
                        )
                    )
                }
            )
        }
    }

    override fun onFailedIdentityValidation(resultCode: String?) {
        navigateBack()
    }
}