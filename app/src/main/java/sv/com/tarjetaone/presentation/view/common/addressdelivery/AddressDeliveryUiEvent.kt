package sv.com.tarjetaone.presentation.view.common.addressdelivery

import sv.com.tarjetaone.domain.entities.response.AddressDeliveryUI

sealed class AddressDeliveryUiEvent {
    data object OnStart : AddressDeliveryUiEvent()
    data object OnBackClick : AddressDeliveryUiEvent()
    data object OnContinueClick : AddressDeliveryUiEvent()
    data class OnSelectAddress(val address: AddressDeliveryUI) : AddressDeliveryUiEvent()
    data object OnLocationPermissionGranted : AddressDeliveryUiEvent()
    data class OnLocationPermissionDenied(val showRationale: Boolean) : AddressDeliveryUiEvent()
    data object OnDismissSecurityAlert : AddressDeliveryUiEvent()
    data object OnTwilioClick: AddressDeliveryUiEvent()
}
