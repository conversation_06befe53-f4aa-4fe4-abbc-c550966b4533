package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.pay_with_points

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.DAY_OF_WEEK_DAY_MONTH_YEAR_WITH_SPACES
import sv.com.tarjetaone.core.utils.extensions.formatAsString
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.response.TransactionsUI
import sv.com.tarjetaone.presentation.compose.modifier.diagonalGradientBackground
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.SolidLargeButton
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Corner
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.compose.util.Spacer64
import sv.com.tarjetaone.presentation.helpers.capitalizeAllWords
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component.getAmountFormatted
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component.getDateFormatted

@Composable
fun PayWithPointsScreen(
    viewModel: PayWithPointsViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    LaunchedEffect(Unit) {
        viewModel.onEvent(PayWithPointsUiEvent.OnStart)
    }
    PayWithPointsContent(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@Composable
fun PayWithPointsContent(
    uiState: PayWithPointsUiState,
    onEvent: (PayWithPointsUiEvent) -> Unit
) {
    ScreenWithTopAppBar(
        onLeftButtonClick = { onEvent(PayWithPointsUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(PayWithPointsUiEvent.OnTwilioClick) },
        title = stringResource(id = R.string.pay_with_points_label)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            Text(
                textAlign = TextAlign.Center,
                text = stringResource(
                    id = R.string.authorization_number,
                    uiState.transaction.authorizationCode.orEmpty()
                ),
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.fillMaxWidth()
            )
            Spacer32()
            Card(
                shape = MaterialTheme.shapes.extraLarge,
                colors = CardDefaults.cardColors(containerColor = Color.White)
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            vertical = MaterialTheme.customDimens.dimen24,
                            horizontal = MaterialTheme.customDimens.dimen44
                        )
                ) {
                    Text(
                        textAlign = TextAlign.Center,
                        text = uiState.transaction.description.orEmpty().uppercase(),
                        style = MaterialTheme.typography.labelMedium.copy(
                            color = MaterialTheme.colorScheme.secondary,
                            fontWeight = FontWeight.SemiBold
                        )
                    )
                    Text(
                        text = uiState.transaction
                            .getDateFormatted(DAY_OF_WEEK_DAY_MONTH_YEAR_WITH_SPACES)
                            .uppercase(),
                        style = MaterialTheme.typography.labelMedium.copy(
                            color = MaterialTheme.colorScheme.secondary,
                            fontWeight = FontWeight.SemiBold
                        ),
                        modifier = Modifier.padding(bottom = MaterialTheme.customDimens.dimen15)
                    )
                    Divider(color = MaterialTheme.customColors.gray200)
                    Spacer16()
                    Text(
                        textAlign = TextAlign.Center,
                        text = uiState.transaction.getAmountFormatted().capitalizeAllWords(),
                        style = MaterialTheme.typography.displayLarge,
                        color = MaterialTheme.colorScheme.secondary
                    )
                    Spacer16()
                    Divider(color = MaterialTheme.customColors.gray200)
                    Spacer16()
                    Text(
                        text = stringResource(
                            id = R.string.necessary_points_to_pay,
                            uiState.transaction.pointsNeeded.orZero().formatAsString()
                        ),
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.labelMedium,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
            Spacer16()
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.customDimens.dimen32)
                    .clip(MaterialTheme.shapes.medium)
                    .diagonalGradientBackground(
                        colors = MaterialTheme.customColors.primaryGradient,
                        startingPoint = Corner.TOP_RIGHT
                    )
            ) {
                Column(
                    modifier = Modifier.padding(MaterialTheme.customDimens.dimen8),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = stringResource(id = R.string.available_points),
                        style = MaterialTheme.typography.headlineSmall,
                        color = MaterialTheme.colorScheme.onPrimary,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        textAlign = TextAlign.Center,
                        text = uiState.availablePoints.formatAsString(),
                        style = MaterialTheme.typography.displayLarge,
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                }
            }
            Spacer64()
            Spacer1f()
            Text(
                textAlign = TextAlign.Center,
                text = stringResource(
                    id = R.string.want_to_use_points_to_pay,
                    uiState.transaction.pointsNeeded.orZero().formatAsString()
                ),
                style = MaterialTheme.typography.titleMedium
            )
            Spacer16()
            SolidLargeButton(
                modifier = Modifier.padding(horizontal = MaterialTheme.customDimens.dimen32),
                text = stringResource(id = R.string.yes_pay_with_my_points),
                onClick = { onEvent(PayWithPointsUiEvent.OnContinueClick) },
            )
            Spacer16()
            SolidLargeButton(
                modifier = Modifier.padding(horizontal = MaterialTheme.customDimens.dimen32),
                text = stringResource(id = R.string.no_dont_pay_with_my_points),
                onClick = { onEvent(PayWithPointsUiEvent.OnDoNotPayClick) },
                buttonVariant = ButtonVariant.SECONDARY_VARIANT
            )
            Spacer24()
        }
    }
}

@Composable
@Preview
private fun PayWithPointsScreenPreview() {
    OneAppTheme {
        PayWithPointsContent(
            uiState = PayWithPointsUiState(
                transaction = TransactionsUI(
                    authorizationCode = "000765",
                    description = "pago uber",
                    valueDate = "07/24/2024",
                    amount = 15.00,
                    pointsNeeded = 1000,
                    panMasked = "1234 **** **** 1234",
                    installmentTerms = listOf()
                ),
                availablePoints = 10000
            ),
            onEvent = {}
        )
    }
}
