package sv.com.tarjetaone.presentation.compose.util

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.navigation.NavDirections

/**
 * Data class to hold the information of a menu item not created dynamically.
 * Useful when creating a static menu with MenuList for a compose screen.
 */
data class StaticMenuItem(
    @DrawableRes val icon: Int,
    @StringRes val label: Int,
    val labelColor: @Composable () -> Color = { Color.Unspecified },
    val displayArrow: Boolean = true,
    val destination: NavDirections? = null,
    val otherAction: (() -> Unit)? = null
)
