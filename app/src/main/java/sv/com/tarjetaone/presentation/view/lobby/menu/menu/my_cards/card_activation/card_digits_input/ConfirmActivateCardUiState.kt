package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_activation.card_digits_input

import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.CardColor

data class ConfirmActivateCardUiState(
    val cardDigits: String = EMPTY_STRING,
    val cardColor: CardColor? = null,
    val nameOnCard: String = EMPTY_STRING,
    val areDigitsValid: Boolean = false
)
