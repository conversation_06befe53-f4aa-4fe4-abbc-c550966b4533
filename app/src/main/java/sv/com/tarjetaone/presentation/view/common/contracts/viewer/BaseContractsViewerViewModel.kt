package sv.com.tarjetaone.presentation.view.common.contracts.viewer

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent

abstract class BaseContractsViewerViewModel: BaseViewModel() {

    protected val _uiState = MutableStateFlow(ContractViewerUiState())
    val uiState: StateFlow<ContractViewerUiState> = _uiState.asStateFlow()

    protected abstract fun onStart()

    fun onEvent(event: ContractViewerUiEvent) {
        when (event) {
            ContractViewerUiEvent.OnStart -> onStart()
            ContractViewerUiEvent.OnBackAction -> sendEvent(UiEvent.NavigateBack)
            ContractViewerUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
        }
    }
}