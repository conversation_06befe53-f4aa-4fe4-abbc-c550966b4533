package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.generaldata.updategeneraldataresult

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import javax.inject.Inject

@HiltViewModel
class UpdateDataResultViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {
    private val args = UpdateDataResultFragmentArgs.fromSavedStateHandle(savedStateHandle)

    private val _uiState = MutableStateFlow(
        UpdateDataResultUiState(
            isSuccessResult = args.isSuccessResult
        )
    )
    val uiState: StateFlow<UpdateDataResultUiState> = _uiState.asStateFlow()

    private fun onDoneClick() {
        sendEvent(UiEvent.NavigateBackTo(destinationId = R.id.personalDataOverviewFragment))
    }

    fun onEvent(event: UpdateDataResultUiEvent) {
        when (event) {
            UpdateDataResultUiEvent.OnDoneClick -> onDoneClick()
        }
    }
}
