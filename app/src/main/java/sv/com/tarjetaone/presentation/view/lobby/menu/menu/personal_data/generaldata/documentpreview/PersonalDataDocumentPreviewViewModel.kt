package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.generaldata.documentpreview

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.core.interfaces.DuiValidation
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.presentation.view.common.documentpreview.DocumentPreviewViewModel
import javax.inject.Inject

@HiltViewModel
class PersonalDataDocumentPreviewViewModel @Inject constructor(
    duiValidation: DuiValidation,
    imgUtils: ImageUtils,
    facephiResultHandler: FacephiResultHandler,
    savedStateHandle: SavedStateHandle
) : DocumentPreviewViewModel(duiValidation, imgUtils, facephiResultHandler) {
    private val args = PersonalDataDocumentPreviewFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onValidDocument() {
        sendEvent(
            UiEvent.Navigate(
                PersonalDataDocumentPreviewFragmentDirections
                    .actionReadabilityConfirmationPersonalDataFragmentToValidatePersonalDataOverviewFragment(
                        biometryProcessId = args.biometryProcessId
                    )
            )
        )
    }

    override fun onInvalidDocument() {
        sendEvent(UiEvent.NavigateBack)
    }
}
