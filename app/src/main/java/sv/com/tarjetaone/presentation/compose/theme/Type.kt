package sv.com.tarjetaone.presentation.compose.theme

import androidx.compose.material3.Typography
import androidx.compose.ui.text.PlatformTextStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.LineHeightStyle
import androidx.compose.ui.unit.sp
import sv.com.tarjetaone.R

/**
 * Font to be used across the entire project.
 */
val poppinsFamily = FontFamily(
    Font(R.font.poppins_light, FontWeight.Light),
    Font(R.font.poppins_regular, FontWeight.Normal),
    Font(R.font.poppins_italic, FontWeight.Normal, FontStyle.Italic),
    Font(R.font.poppins_medium, FontWeight.Medium),
    Font(R.font.poppins_semi_bold, FontWeight.SemiBold),
    Font(R.font.poppins_bold, FontWeight.Bold)
)

val montserratFamily = FontFamily(
    Font(R.font.montserrat_light, FontWeight.Light),
    Font(R.font.montserrat_regular, FontWeight.Normal),
    Font(R.font.montserrat_italic, FontWeight.Normal, FontStyle.Italic),
    Font(R.font.montserrat_medium, FontWeight.Medium),
    Font(R.font.montserrat_semi_bold, FontWeight.SemiBold),
    Font(R.font.montserrat_bold, FontWeight.Bold)
)

/**
 * Custom Font to be used for sign style.
 */
val seaweedScriptFamily = FontFamily(
    Font(R.font.seaweed_script, FontWeight.Normal)
)

/**
 * The current Design System provided seems to be using the M2 typography system.
 * We are using the M3 Compose guidelines, to know the right typography to use on your UI, please see the below link.
 * @see (https://developer.android.com/jetpack/compose/designsystems/material2-material3#typography)
 */

val Typography = Typography(
    displayLarge = TextStyle( // Headings/H1
        fontWeight = FontWeight.Bold,
        fontSize = 36.sp,
        lineHeight = 48.97.sp,
        letterSpacing = 0.sp,
        fontFamily = poppinsFamily,
        lineHeightStyle = LineHeightStyle(
            alignment = LineHeightStyle.Alignment.Center,
            trim = LineHeightStyle.Trim.Both
        ),
        platformStyle = PlatformTextStyle(
            includeFontPadding = false
        ),
    ),
    displayMedium = TextStyle( // Headings/H2
        fontWeight = FontWeight.SemiBold,
        fontSize = 28.sp,
        lineHeight = 37.3.sp,
        letterSpacing = 0.sp,
        fontFamily = poppinsFamily
    ),
    displaySmall = TextStyle( // Headings/H3
        fontWeight = FontWeight.Bold,
        fontSize = 20.sp,
        lineHeight = 27.3.sp,
        letterSpacing = 0.sp,
        fontFamily = poppinsFamily
    ),
    headlineLarge = TextStyle( // Headings/H4
        fontWeight = FontWeight.SemiBold,
        fontSize = 18.sp,
        lineHeight = 32.sp,
        letterSpacing = 0.sp,
        fontFamily = poppinsFamily
    ),
    headlineSmall = TextStyle( // Headings/H5
        fontWeight = FontWeight.SemiBold,
        fontSize = 16.sp,
        lineHeight = 25.sp,
        letterSpacing = 0.sp,
        fontFamily = poppinsFamily
    ),
    bodyLarge = TextStyle(
        fontWeight = FontWeight.Normal,
        fontSize = 18.sp,
        lineHeight = 30.sp,
        letterSpacing = 0.sp,
        fontFamily = poppinsFamily
    ),
    bodySmall = TextStyle( // Small-Paragraph Regular
        fontWeight = FontWeight.Normal,
        fontSize = 14.sp,
        lineHeight = 23.sp,
        letterSpacing = 0.sp,
        fontFamily = poppinsFamily
    ),
    bodyMedium = TextStyle( // Base text/Paragraph Regular
        fontWeight = FontWeight.Normal,
        fontSize = 16.sp,
        lineHeight = 23.sp,
        letterSpacing = 0.sp,
        fontFamily = poppinsFamily
    ),
    labelLarge = TextStyle( // Caption InformativeText Bold
        fontWeight = FontWeight.Bold,
        fontSize = 13.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.sp,
        fontFamily = poppinsFamily
    ),
    labelMedium = TextStyle( // Caption InformativeText Regular
        fontWeight = FontWeight.Normal,
        fontSize = 12.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.sp,
        fontFamily = poppinsFamily
    ),
    labelSmall = TextStyle(
        // Extra Small Text Regular
        fontWeight = FontWeight.Normal,
        fontSize = 10.sp,
        lineHeight = 16.sp,
        fontFamily = poppinsFamily
    ),
    titleMedium = TextStyle(
        fontWeight = FontWeight.SemiBold,
        fontSize = 20.sp,
        lineHeight = 22.sp,
        letterSpacing = 0.sp,
        fontFamily = poppinsFamily
    ),
)
