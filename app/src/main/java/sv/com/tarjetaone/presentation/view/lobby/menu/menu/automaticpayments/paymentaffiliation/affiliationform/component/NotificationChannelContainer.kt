package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.affiliationform.NotificationChannelType
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.affiliationform.NotificationChannelUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.contractslist.PXStatus
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedDropdown

@Composable
fun NotificationChannelContainer(
    modifier: Modifier = Modifier,
    notificationChannels: List<NotificationChannelUI>,
    selectedNotificationChannel: NotificationChannelUI?,
    onNotificationChannelChange: (NotificationChannelUI) -> Unit = { },
) {
    Column(modifier = modifier) {
        SimpleElevatedDropdown(
            modifier = Modifier.fillMaxWidth(),
            items = notificationChannels,
            itemLabel = { item -> item.name },
            value = selectedNotificationChannel,
            onValueChange = { onNotificationChannelChange(it) },
            label = stringResource(id = R.string.notification_channel_label),
            labelStyle = MaterialTheme.typography.bodySmall.copy(
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.customColors.textBodyLight
            ),
            hint = stringResource(id = R.string.notification_channel_hint),
            hintStyle = MaterialTheme.typography.labelMedium.copy(
                color = MaterialTheme.customColors.gray600,
            )
        )
    }
}

@Preview(
    showBackground = true,
)
@Composable
private fun NotificationChannelContainerPreview() {
    OneAppTheme {
        NotificationChannelContainer(
            notificationChannels = listOf(
                NotificationChannelUI(
                    code = "",
                    name = "Email",
                    datum = "",
                    order = 0,
                    status = PXStatus.Active,
                    type = NotificationChannelType.Email
                ),
                NotificationChannelUI(
                    code = "",
                    name = "WhatsApp",
                    datum = "",
                    order = 0,
                    status = PXStatus.Active,
                    type = NotificationChannelType.WhatsApp
                ),
            ),
            selectedNotificationChannel = NotificationChannelUI(
                code = "",
                name = "Email",
                datum = "",
                order = 0,
                status = PXStatus.Active,
                type = NotificationChannelType.Email
            ),
            onNotificationChannelChange = { }
        )
    }
}
