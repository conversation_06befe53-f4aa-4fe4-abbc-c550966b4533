package sv.com.tarjetaone.presentation.compose.uicomponent.dialog

import android.os.Parcelable
import androidx.annotation.DrawableRes
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.helpers.UiText

/**
 * Data class representing the parameters for a OneDialog.
 */
@Parcelize
data class OneDialogParams(
    @DrawableRes val icon: Int? = R.drawable.ic_error_yellow,
    val title: UiText? = UiText.StringResource(R.string.ups_label),
    val message: MessageParams? = MessageParams(
        text = UiText.StringResource(R.string.general_technical_issues_message)
    ),
    val primaryAction: DialogAction = DialogAction(
        text = UiText.StringResource(R.string.try_again_label),
        actionType = DialogAction.ActionType.WARNING
    ),
    val secondaryAction: DialogAction? = null,
    val isDismissible: Boolean = true,
    @IgnoredOnParcel val onDismiss: () -> Unit = {},
    val showCloseButton: Boolean = false,
) : Parcelable

@Parcelize
data class MessageParams(
    val text: UiText,
    val size: MessageSize = MessageSize.Small,
    val alignment: MessageAlign = MessageAlign.CENTER,
) : Parcelable {
    /**
     * Sealed class representing the size of the message text.
     * Each child represents a specific text size in SP.
     */
    sealed class MessageSize : Parcelable {
        /** 10 sp */
        @Parcelize
        data object ExtraSmall : MessageSize()

        /** 12 sp */
        @Parcelize
        data object Small : MessageSize()

        /** 14 sp */
        @Parcelize
        data object Medium : MessageSize()

        /** 16 sp */
        @Parcelize
        data object Large : MessageSize()

        /** Custom size */
        @Parcelize
        data class Custom(val size: Int) : MessageSize()

        val sp: TextUnit
            @Composable
            @ReadOnlyComposable
            get() = when (this) {
                ExtraSmall -> MaterialTheme.customDimensSp.sp10
                Small -> MaterialTheme.customDimensSp.sp12
                Medium -> MaterialTheme.customDimensSp.sp14
                Large -> MaterialTheme.customDimensSp.sp16
                is Custom -> size.sp
            }

        val lineHeight: TextUnit
            @Composable
            @ReadOnlyComposable
            get() = when (this) {
                ExtraSmall -> MaterialTheme.customDimensSp.sp12
                Small -> MaterialTheme.customDimensSp.sp14
                Medium -> MaterialTheme.customDimensSp.sp16
                Large -> MaterialTheme.customDimensSp.sp18
                is Custom -> size.sp
            }
    }

    /**
     * Enum that works as a wrapper for the TextAlign value class to support kotlin parcelize.
     */
    enum class MessageAlign {
        START,
        END,
        CENTER,
        JUSTIFY;

        val textAlign: TextAlign
            get() = when (this) {
                START -> TextAlign.Start
                END -> TextAlign.End
                CENTER -> TextAlign.Center
                JUSTIFY -> TextAlign.Justify
            }
    }
}

@Parcelize
data class DialogAction(
    val text: UiText,
    @IgnoredOnParcel val onClick: () -> Unit = {},
    val actionType: ActionType = ActionType.DEFAULT,
) : Parcelable {
    /**
     * Enum representing the color of the primary action button.
     * Each entry maps to a specific compose color defined in `Color.kt`
     */
    enum class ActionType {
        /** #51DEA3 (`successContainer`) */
        SUCCESS,

        /** #FFC700 (`alertVariant`) */
        WARNING,

        /** #FF3D00 (`error`) */
        ERROR,

        /** #1F1E36 (`secondary`) */
        INFO,

        /** #C8B3EA (`cardBackground`) */
        INFO_SECONDARY,

        /** #FF2A4A (`primary`) */
        DEFAULT;

        val color: Color
            @Composable
            @ReadOnlyComposable
            get() = when (this) {
                SUCCESS -> MaterialTheme.customColors.successContainer
                WARNING -> MaterialTheme.customColors.alertVariant
                ERROR -> MaterialTheme.colorScheme.error
                INFO -> MaterialTheme.colorScheme.secondary
                INFO_SECONDARY -> MaterialTheme.customColors.cardLightBackground
                DEFAULT -> MaterialTheme.colorScheme.primary
            }
    }
}
