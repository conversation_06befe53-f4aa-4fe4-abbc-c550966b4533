package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.DAY_OF_MONTH
import sv.com.tarjetaone.common.utils.AppConstants.YEAR_MONTH_DAY_WITH_DASH
import sv.com.tarjetaone.core.utils.extensions.configCurrencyWithFractions
import sv.com.tarjetaone.core.utils.extensions.formatAsString
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.response.AvailableCashbackUI
import sv.com.tarjetaone.domain.entities.response.AvailablePointsUI
import sv.com.tarjetaone.domain.entities.response.DataAvailablePointsUI
import sv.com.tarjetaone.presentation.compose.modifier.diagonalGradientBackground
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.Corner
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.helpers.BenefitType

@Composable
fun PointsOverviewCard(
    modifier: Modifier = Modifier,
    benefitType: String?,
    pointsData: DataAvailablePointsUI?,
    secondaryText: String,
    secondaryTextDecoration: TextDecoration? = null,
    onClick: (() -> Unit)? = null
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(MaterialTheme.customDimens.dimen16))
            .diagonalGradientBackground(
                colors = MaterialTheme.customColors.primaryGradient,
                startingPoint = Corner.TOP_RIGHT
            )
            .then(onClick?.let { Modifier.clickable(onClick = it) } ?: Modifier)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    vertical = MaterialTheme.customDimens.dimen16,
                    horizontal = MaterialTheme.customDimens.dimen20
                ),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_group_coins),
                contentDescription = null
            )
            Spacer16()
            Column {
                Text(
                    text = if (benefitType == BenefitType.CASHBACK_BENEFIT.type) {
                        pointsData?.cashback?.periodLabel ?:
                        stringResource(id = R.string.accumulated_cashback_to_date)
                    } else {
                        stringResource(
                            id = R.string.accumulated_points_to_date,
                            pointsData?.valueDate
                                ?.getFormattedDateFromTo(
                                    YEAR_MONTH_DAY_WITH_DASH,
                                    DAY_OF_MONTH
                                ).orEmpty()
                        )
                    },
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.onPrimary
                )
                Text(
                    text = if (benefitType == BenefitType.CASHBACK_BENEFIT.type) {
                        pointsData?.cashback?.cashback.orZero().configCurrencyWithFractions()
                    } else {
                        pointsData?.points?.amount?.toInt().orZero().formatAsString()
                    },
                    style = MaterialTheme.typography.displayMedium,
                    color = MaterialTheme.colorScheme.onPrimary
                )
                Text(
                    text = secondaryText,
                    style = MaterialTheme.typography.labelLarge.copy(
                        fontWeight = FontWeight.Medium
                    ),
                    color = MaterialTheme.colorScheme.onPrimary,
                    textDecoration = secondaryTextDecoration
                )
            }
        }
    }
}

@Preview
@Composable
private fun PointsOverviewCardPreview() {
    OneAppTheme {
        PointsOverviewCard(
            benefitType = BenefitType.SHOPPING_BENEFIT.type,
            pointsData = DataAvailablePointsUI(
                valueDate = "2021-07-31",
                points = AvailablePointsUI(
                    type = "COMPRAS",
                    amount = 1000000.0
                ),
                cashback = AvailableCashbackUI(53.78)
            ),
            secondaryText = "Ver MegaPuntos",
            secondaryTextDecoration = TextDecoration.Underline
        )
    }
}
