package sv.com.tarjetaone.presentation.compose.modifier

import android.os.Build
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.BlurredEdgeTreatment
import androidx.compose.ui.draw.blur
import androidx.compose.ui.unit.dp
import com.skydoves.cloudy.cloudy

/**
 * Applies a blur effect to a composable. When the device is running on Android 12 or higher, the
 * function will apply a native blur effect. Otherwise, it will apply blur with a third-party lib.
 */
fun Modifier.blur(radius: Int): Modifier = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
    Modifier.blur(radius.dp, BlurredEdgeTreatment.Unbounded)
} else {
    composed { Modifier.cloudy(radius * CLOUDY_BLUR_MULTIPLIER) }
}

private const val CLOUDY_BLUR_MULTIPLIER = 5
