package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.cardpay.paymentselection.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.platform.SoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.core.utils.extensions.configCurrencyWithFractions
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.card.BaesSelectableCard
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.compose.util.FieldDecorationType
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer4
import sv.com.tarjetaone.presentation.compose.util.input.CurrencyVisualTransformation
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.cardpay.paymentselection.PaymentType

@Composable
fun PaymentTypeForm(
    modifier: Modifier = Modifier,
    minAmount: Double,
    fullAmount: Double,
    otherAmount: String,
    onOtherAmountChange: (String) -> Unit,
    selectedPayment: PaymentType?,
    onPaymentTypeChange: (PaymentType) -> Unit
) {
    val keyboardController = LocalSoftwareKeyboardController.current

    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.Bottom,
    ) {
        PaymentCard(
            paymentType = PaymentType.FULL,
            isSelected = selectedPayment == PaymentType.FULL,
            amount = fullAmount,
            titleResId = R.string.full_payment,
            onPaymentTypeChange = onPaymentTypeChange,
            onSuccessColor = MaterialTheme.customColors.onSuccess,
            onDefaultSurfaceColor = MaterialTheme.customColors.onDefaultSurface,
            errorColor = MaterialTheme.colorScheme.error
        )
        Spacer16()
        PaymentCard(
            paymentType = PaymentType.MIN,
            isSelected = selectedPayment == PaymentType.MIN,
            amount = minAmount,
            titleResId = R.string.minimum_payment,
            onPaymentTypeChange = onPaymentTypeChange,
            onSuccessColor = MaterialTheme.customColors.onSuccess,
            onDefaultSurfaceColor = MaterialTheme.customColors.onDefaultSurface,
            errorColor = MaterialTheme.colorScheme.error
        )
        Spacer16()
        OtherPaymentSection(
            selectedPayment = selectedPayment,
            otherAmount = otherAmount,
            onOtherAmountChange = onOtherAmountChange,
            onPaymentTypeChange = onPaymentTypeChange,
            keyboardController = keyboardController
        )
    }
}

@Suppress("kotlin:S107")
@Composable
private fun PaymentCard(
    paymentType: PaymentType,
    isSelected: Boolean,
    amount: Double,
    titleResId: Int,
    onPaymentTypeChange: (PaymentType) -> Unit,
    onSuccessColor: Color,
    onDefaultSurfaceColor: Color,
    errorColor: Color
) {
    BaesSelectableCard(
        isSelected = isSelected,
        onClick = { onPaymentTypeChange(paymentType) }
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(
                modifier = Modifier.weight(ONE_FLOAT_VALUE, false),
                maxLines = TWO_VALUE,
                text = stringResource(id = titleResId),
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.SemiBold,
                color = if (isSelected) onSuccessColor else onDefaultSurfaceColor
            )
            Spacer4()
            Text(
                modifier = Modifier.weight(ONE_FLOAT_VALUE, false),
                text = amount.configCurrencyWithFractions(),
                maxLines = ONE_VALUE,
                textAlign = TextAlign.Start,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.SemiBold,
                color = if (isSelected) onSuccessColor else errorColor
            )
        }
    }
}

@Composable
private fun OtherPaymentSection(
    selectedPayment: PaymentType?,
    otherAmount: String,
    onOtherAmountChange: (String) -> Unit,
    onPaymentTypeChange: (PaymentType) -> Unit,
    keyboardController: SoftwareKeyboardController?
) {
    val isOther = selectedPayment == PaymentType.OTHER
    BaesSelectableCard(
        isSelected = isOther,
        onClick = { onPaymentTypeChange(PaymentType.OTHER) },
        verticalAlignment = if (isOther) Alignment.Top else Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.padding(vertical = MaterialTheme.customDimens.dimen8)) {
            Text(
                text = stringResource(id = R.string.other_amount),
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.SemiBold,
                color = if (isOther) MaterialTheme.customColors.onSuccess
                else MaterialTheme.customColors.onDefaultSurface
            )
            AnimatedVisibility(visible = isOther) {
                Column {
                    Text(
                        text = stringResource(id = R.string.amount_to_pay),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.customColors.onSuccess
                    )
                    SimpleElevatedTextField(
                        value = otherAmount,
                        onValueChange = onOtherAmountChange,
                        decorationType = FieldDecorationType.OUTLINED,
                        modifier = Modifier.width(MaterialTheme.customDimens.dimen160),
                        placeholder = stringResource(id = R.string.hint_zero_dollars),
                        visualTransformation = CurrencyVisualTransformation(
                            displayIfEmpty = false,
                            currencySymbol = AppConstants.DOLLAR_STRING
                        ),
                        keyboardOptions = KeyboardOptions(
                            keyboardType = KeyboardType.Number,
                            imeAction = ImeAction.Done
                        ),
                        keyboardActions = KeyboardActions(
                            onDone = { keyboardController?.hide() }
                        )
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PaymentTypeFormPreview() {
    OneAppTheme {
        PaymentTypeForm(
            minAmount = 100.97,
            fullAmount = 2000.03,
            otherAmount = "",
            onOtherAmountChange = {},
            selectedPayment = PaymentType.OTHER,
            onPaymentTypeChange = {},
            modifier = Modifier.padding(MaterialTheme.customDimens.dimen16)
        )
    }
}
