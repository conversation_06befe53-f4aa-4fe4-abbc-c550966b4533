package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.annotation.DrawableRes
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Snackbar
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens

@Composable
fun OneAppSnackBar(
    modifier: Modifier = Modifier,
    @DrawableRes icon: Int? = null,
    message: String,
    messageStyle: TextStyle = MaterialTheme.typography.bodySmall,
    containerColor: Color = MaterialTheme.customColors.successContainer,
    isDismissible: Boolean = true,
    onCancelClick: () -> Unit = {}
) {
    Snackbar(
        modifier = modifier.heightIn(min = MaterialTheme.customDimens.dimen64),
        shape = MaterialTheme.shapes.medium,
        containerColor = containerColor,
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.customDimens.dimen8),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen16)
        ) {
            icon?.let {
                Icon(
                    painter = painterResource(id = it),
                    contentDescription = null
                )
            }
            Text(
                text = message,
                modifier = Modifier.weight(ONE_FLOAT_VALUE),
                style = messageStyle,
            )
            if (isDismissible) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_baseline_close),
                    contentDescription = null,
                    modifier = Modifier
                        .size(MaterialTheme.customDimens.dimen16)
                        .clickable(onClick = onCancelClick)
                )
            }
        }
    }
}

@Preview
@Composable
fun OneAppSnackBarPreview() {
    OneAppTheme {
        OneAppSnackBar(
            icon = R.drawable.ic_check_circle_white,
            message = "Tu dirección fue eliminada con éxito",
        )
    }
}
