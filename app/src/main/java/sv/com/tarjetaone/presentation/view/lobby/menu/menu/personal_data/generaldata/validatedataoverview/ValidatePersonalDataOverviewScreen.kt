package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.generaldata.validatedataoverview

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.core.utils.extensions.setGender
import sv.com.tarjetaone.domain.entities.response.CustomerDataUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer12
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.generaldata.dataoverview.PersonalDataOverviewSharedContent

@Composable
fun ValidatePersonalDataOverviewScreen(
    viewModel: ValidatePersonalDataOverviewViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    ValidatePersonalDataOverviewContent(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@Composable
fun ValidatePersonalDataOverviewContent(
    uiState: ValidatePersonalDataOverviewUiState,
    onEvent: (ValidatePersonalDataOverviewUiEvent) -> Unit
) {
    LaunchedEffect(Unit) {
        onEvent(ValidatePersonalDataOverviewUiEvent.OnGetCustomerOcrData)
    }
    ScreenWithTopAppBar(
        title = stringResource(id = R.string.general_data),
        onLeftButtonClick = { onEvent(ValidatePersonalDataOverviewUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(ValidatePersonalDataOverviewUiEvent.OnSupportClick) },
    ) {
        Column(
            Modifier
                .fillMaxSize()
                .padding(horizontal = MaterialTheme.customDimens.dimen32),
        ) {
            Text(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(id = R.string.validate_personal_data_overview_subtitle),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodySmall
            )
            Column(
                modifier = Modifier
                    .verticalScroll(rememberScrollState())
                    .weight(ONE_FLOAT_VALUE)
            ) {
                PersonalDataOverviewSharedContent(
                    customerData = uiState.customerData,
                    addressLabel = stringResource(R.string.address_label),
                    breakdownAddress = true
                )
                Spacer8()
            }
            Column {
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(id = R.string.validate_personal_data_overview_confirm_title),
                    style = MaterialTheme.typography.displaySmall.copy(
                        fontWeight = FontWeight.SemiBold
                    ),
                    textAlign = TextAlign.Center
                )
                Spacer12()
                OneButton(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(id = R.string.validate_personal_data_overview_confirm_button),
                    onClick = {
                        onEvent(ValidatePersonalDataOverviewUiEvent.OnConfirmPersonalDataClick)
                    }
                )
                Spacer8()
                OneButton(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(id = R.string.validate_personal_data_overview_capture_data_again),
                    buttonVariant = ButtonVariant.SECONDARY_VARIANT,
                    textStyle = MaterialTheme.typography.bodySmall.copy(
                        fontWeight = FontWeight.SemiBold,
                    ),
                    onClick = {
                        onEvent(ValidatePersonalDataOverviewUiEvent.OnCapturePersonalDataAgainClick)
                    }
                )
            }
            Spacer32()
        }
    }
}

@Preview(showSystemUi = true)
@Composable
fun ValidatePersonalDataOverviewScreenPreview() {
    OneAppTheme {
        ValidatePersonalDataOverviewContent(
            uiState = ValidatePersonalDataOverviewUiState(
                customerData = CustomerDataUI(
                    customerId = 0,
                    fullName = "Maria Dolores Linares De Marciano",
                    genderCode = "F".setGender(),
                    dui = "0000000-0",
                    duiExpirationDate = "01 jun 2024",
                    birthDate = "01 julio 1990",
                    address = "Col. Escalón, Calle Francisco Gavidia Casa #4"
                )
            ),
            onEvent = {}
        )
    }
}
