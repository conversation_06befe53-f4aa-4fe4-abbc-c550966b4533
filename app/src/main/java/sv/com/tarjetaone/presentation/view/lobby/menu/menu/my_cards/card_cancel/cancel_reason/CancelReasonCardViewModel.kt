package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_cancel.cancel_reason

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.SUCCESS_RESPONSE_CODE
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.CommonDialogWithIconParams
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI
import sv.com.tarjetaone.domain.entities.response.CatalogType
import sv.com.tarjetaone.domain.usecases.catalog.GetCatalogUseCase
import sv.com.tarjetaone.presentation.helpers.UiText
import javax.inject.Inject

@HiltViewModel
class CancelReasonCardViewModel @Inject constructor(
    private val getCatalogUseCase: GetCatalogUseCase,
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(CancelReasonCardUiState())
    val uiState = _uiState.asStateFlow()
    private val args = CancelReasonCardFragmentArgs.fromSavedStateHandle(savedStateHandle)

    private fun onStart() {
        if (uiState.value.reasonOptions.isEmpty()) {
            sendEvent(UiEvent.Loading(true))
            viewModelScope.launch {
                getCatalogUseCase(CatalogType.GC_CLOSE_REASON).executeUseCase { response ->
                    sendEvent(UiEvent.Loading(false))
                    if (response.statusCatalog?.responseStatus?.code != SUCCESS_RESPONSE_CODE) {
                        showUpsErrorMessage(false) { sendEvent(UiEvent.NavigateBack) }
                        return@executeUseCase
                    }
                    _uiState.update {
                        it.copy(
                            reasonOptions = response.dataCatalog?.catalogItemsCollection.orEmpty()
                        )
                    }
                }
            }
        }
    }

    private fun onSelectReason(reason: CatalogItemsCollectionUI) {
        val isOther = reason.behaviourInfo == OTHERS_OPTION
        _uiState.update {
            it.copy(
                selectedReason = reason,
                isOtherSelected = isOther,
                otherComments = if (!isOther) EMPTY_STRING else it.otherComments
            )
        }
    }

    private fun onCommentsChange(comments: String) {
        _uiState.update { it.copy(otherComments = comments) }
    }

    private fun onContinueClick() {
        fun goToConfirmCancel() {
            sendEvent(
                UiEvent.Navigate(
                    CancelReasonCardFragmentDirections
                        .actionNavigationReasonCancelCardsToNavigationConfirmCancelCards(
                            card = args.card,
                            reason = uiState.value.selectedReason,
                            comments = uiState.value.otherComments
                        )
                )
            )
        }

        if (args.card?.mainCard == true) {
            sendEvent(
                UiEvent.ShowCommonDialog(
                    CommonDialogWithIconParams(
                        icon = R.drawable.ic_check_yellow,
                        title = UiText.StringResource(R.string.cancel_card_label),
                        message = UiText.StringResource(R.string.cancel_reason_dialog_message),
                        isDismissible = false,
                        onButtonClick = { goToConfirmCancel() },
                        buttonColor = R.color.yellow_alert_button,
                        messageTextSize = 16f
                    )
                )
            )
        } else {
            goToConfirmCancel()
        }
    }

    fun onEvent(event: CancelReasonCardUiEvent) {
        when (event) {
            CancelReasonCardUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            CancelReasonCardUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            is CancelReasonCardUiEvent.OnStart -> onStart()
            is CancelReasonCardUiEvent.OnSelectReason -> onSelectReason(event.reason)
            is CancelReasonCardUiEvent.OnCommentsChange -> onCommentsChange(event.comments)
            CancelReasonCardUiEvent.OnContinueClick -> onContinueClick()
        }
    }

    companion object {
        private const val OTHERS_OPTION = "True"
    }
}
