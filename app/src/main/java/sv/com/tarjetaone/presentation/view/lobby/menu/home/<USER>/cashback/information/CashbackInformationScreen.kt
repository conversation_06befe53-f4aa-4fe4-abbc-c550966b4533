package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.cashback.information

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer4
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.cashback.description.CashbackDescriptionScreen

@Composable
fun CashbackInformationScreen(viewModel: CashbackInformationViewModel) {
    CashbackInformationContent(viewModel::onEvent)
}

@Composable
fun CashbackInformationContent(onEvent: (CashbackInformationUiEvent) -> Unit) {
    ScreenWithTopAppBar(
        contentPadding = PaddingValues(
            top = MaterialTheme.customDimens.dimen32,
            start = MaterialTheme.customDimens.dimen32,
            end = MaterialTheme.customDimens.dimen32,
            bottom = MaterialTheme.customDimens.dimen16
        ),
        onLeftButtonClick = {
            onEvent(CashbackInformationUiEvent.OnBackClick)
        }, onRightButtonClick = {
            onEvent(CashbackInformationUiEvent.OnSupportClick)
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_cashback_header),
                contentDescription = null,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier.fillMaxWidth()
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(MaterialTheme.customColors.textSurfaceVariant)
                    .padding(
                        horizontal = MaterialTheme.customDimens.dimen16
                    )
            ) {
                Spacer16()
                Text(
                    text = stringResource(R.string.cashback_information_title),
                    style = MaterialTheme.typography.displayMedium.copy(
                        color = MaterialTheme.colorScheme.secondary
                    )
                )
                Spacer4()
                Text(
                    text = stringResource(R.string.cashback_information_subtitle),
                    style = MaterialTheme.typography.bodyMedium.copy(
                        color = MaterialTheme.colorScheme.secondary
                    )
                )
                Spacer24()
                CashbackDescriptionScreen(
                    buttonAction = {
                        onEvent(CashbackInformationUiEvent.OnContinueClick)
                    }
                )
            }
        }
    }
}

@Composable
@Preview
fun CashbackInformationScreenPreview() {
    OneAppTheme {
        CashbackInformationContent(onEvent = {})
    }
}