package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.identity.validation

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.request.automaticpayments.paymentaffiliation.contract.AffiliationContractActionType
import sv.com.tarjetaone.domain.entities.request.automaticpayments.paymentaffiliation.contract.AffiliationContractRequestUI
import sv.com.tarjetaone.domain.entities.request.automaticpayments.paymentaffiliation.contract.ContractAffiliationAction
import sv.com.tarjetaone.domain.entities.request.automaticpayments.paymentaffiliation.contract.DeleteContractRequestUI
import sv.com.tarjetaone.domain.entities.request.automaticpayments.paymentaffiliation.contractslist.RecurringChargeListRequestUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.contractslist.RecurringChargeContractUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.detail.PaymentAffiliationDetailUI
import sv.com.tarjetaone.domain.usecases.auth.FaceAuthenticationUseCase
import sv.com.tarjetaone.domain.usecases.automaticpayments.paymentaffiliation.cancel.CancelRecurringContractUseCase
import sv.com.tarjetaone.domain.usecases.automaticpayments.paymentaffiliation.contract.create.CreateAffiliationContractUseCase
import sv.com.tarjetaone.domain.usecases.automaticpayments.paymentaffiliation.contract.update.UpdateAffiliationContractUseCase
import sv.com.tarjetaone.domain.usecases.automaticpayments.paymentaffiliation.contracts.GetRecurringChargeContractsUseCase
import sv.com.tarjetaone.presentation.view.common.identity_validation.IdentityValidationBaseViewModel
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform.PaymentAffiliationConstants

@HiltViewModel
class PaymentAffIdentityValidationViewModel @Inject constructor(
    faceAuthenticationUseCase: FaceAuthenticationUseCase,
    sharedPrefs: SecureSharedPreferencesRepository,
    savedStateHandle: SavedStateHandle,
    private val createAffiliationContractUseCase: CreateAffiliationContractUseCase,
    private val updateAffiliationContractUseCase: UpdateAffiliationContractUseCase,
    private val getRecurringChargeContracts: GetRecurringChargeContractsUseCase,
    private val cancelRecurringContractUseCase: CancelRecurringContractUseCase
) : IdentityValidationBaseViewModel(faceAuthenticationUseCase, sharedPrefs) {

    private val args = PaymentAffIdentityValidationFragmentArgs.fromSavedStateHandle(
        savedStateHandle = savedStateHandle
    )

    /**
     * execute contract creation after successful biometric validation.
     * Depending on the action, execute the corresponding use case.
     */
    override fun operation(biometryId: Int) {
        when (val action = args.contractAction) {
            is ContractAffiliationAction.Create -> createRecurringContract(
                biometryId = biometryId,
                affiliationContractRequestUI = action.request
            )

            is ContractAffiliationAction.Update -> updateRecurringContract(
                biometryId = biometryId,
                affiliationContractRequestUI = action.request
            )

            is ContractAffiliationAction.Cancel -> cancelRecurringContract(
                biometryId = biometryId,
                deleteContractRequestUI = action.request,
                contract = action.contract
            )
        }
    }

    /**
     * Create a recurring contract with the parameters from previous screen and the appended biometryId.
     */
    private fun createRecurringContract(
        biometryId: Int,
        affiliationContractRequestUI: AffiliationContractRequestUI
    ) {
        val request = affiliationContractRequestUI.copy(biometryId = biometryId)

        viewModelScope.launch {
            createAffiliationContractUseCase(request)
                .executeUseCase(
                    onApiErrorAction = { _, _, _, _ ->
                        goBackToAffiliationServiceScreen()
                    },
                    onSuccessAction = { contract ->
                        contract?.contractNumber?.let {
                            getContracts(contractNumber = it.toString())
                        } ?: goBackToAffiliationServiceScreen()
                    }
                )
        }
    }

    /**
     * Update a recurring contract with the parameters from previous screen and the appended biometryId.
     */
    private fun updateRecurringContract(
        biometryId: Int,
        affiliationContractRequestUI: AffiliationContractRequestUI
    ) {
        val request = affiliationContractRequestUI.copy(biometryId = biometryId)
        viewModelScope.launch {
            updateAffiliationContractUseCase(request)
                .executeUseCase(
                    onApiErrorAction = { _, _, _, _ ->
                        goBackToAffiliationServiceScreen()
                    },
                    onSuccessAction = { contract ->
                        contract?.contractNumber?.let {
                            getContracts(contractNumber = it.toString())
                        } ?: goBackToAffiliationServiceScreen()
                    }
                )
        }
    }

    private fun cancelRecurringContract(
        biometryId: Int,
        deleteContractRequestUI: DeleteContractRequestUI,
        contract: RecurringChargeContractUI
    ) {
        val request = deleteContractRequestUI.copy(biometryId = biometryId.toString())
        viewModelScope.launch {
            cancelRecurringContractUseCase(request)
                .executeUseCase(
                    onApiErrorAction = { _, _, _, _ ->
                        goBackToMyPaymentsScreen()
                    },
                    onSuccessAction = {
                        val contractToDelete = contract.copy(
                            maxAmount = EMPTY_STRING,
                            contractProductType = EMPTY_STRING
                        )
                        navigateToAffiliationDetails(contractToDelete)
                    }
                )
        }
    }

    /**
     * Get the created contract by its `contractNumber` from the list of contracts.
     */
    private suspend fun getContracts(contractNumber: String?) {
        getRecurringChargeContracts(
            RecurringChargeListRequestUI(
                action = AffiliationContractActionType.Contract.value,
                contractNumber = contractNumber,
                companyCode = PaymentAffiliationConstants.COMPANY_CODE,
                clientCode = sharedPrefs.dui(),
                contractStatus = null,
            )
        ).executeUseCase(
            onApiErrorAction = { _, _, _, _ ->
                goBackToAffiliationServiceScreen()
            },
            onSuccessAction = { contracts ->
                val contract = contracts.firstOrNull { it.contractNumber == contractNumber }
                contract?.let {
                    navigateToAffiliationDetails(contract = contract)
                } ?: goBackToAffiliationServiceScreen()
            }
        )
    }

    /**
     * Get the affiliation management name based on the action type.
     */
    private fun getContractManagementName(): Int {
        return when (args.contractAction) {
            is ContractAffiliationAction.Create -> R.string.payment_affiliation_detail_create_automatic_payment
            is ContractAffiliationAction.Update -> R.string.payment_affiliation_detail_update_automatic_payment
            is ContractAffiliationAction.Cancel -> R.string.payment_affiliation_detail_cancel_automatic_payment
        }
    }

    /**
     * Navigate to Payment Affiliation Detail screen with the contract information.
     */
    private fun navigateToAffiliationDetails(contract: RecurringChargeContractUI?) {
        sendEvent(
            UiEvent.Navigate(
                PaymentAffIdentityValidationFragmentDirections
                    .navigateToPaymentAffiliationDetailFragment(
                        paymentAffiliationDetailUI = PaymentAffiliationDetailUI(
                            contractNumber = contract?.contractNumber,
                            contractManagementName = getContractManagementName(),
                            customerName = sharedPrefs.getUserFullName(),
                            contractProductType = contract?.contractProductType,
                            serviceName = contract?.serviceName,
                            identifier = contract?.identifiers?.firstOrNull()?.identifier,
                            contractMaxAmount = contract?.maxAmount?.toDoubleOrNull(),
                            selectedPaymentMethod = args.selectedPaymentMethod,
                        )
                    )
            )
        )
    }

    /**
     * Show error message and navigate back to Affiliation Service screen.
     */
    private fun goBackToAffiliationServiceScreen() {
        showUpsErrorMessage(isDismissible = false) {
            sendEvent(
                UiEvent.NavigateBackTo(
                    destinationId = R.id.affiliateServiceFragment,
                    inclusive = false
                )
            )
        }
    }

    private fun goBackToMyPaymentsScreen() {
        showUpsErrorMessage(isDismissible = false) {
            sendEvent(
                UiEvent.NavigateBackTo(
                    destinationId = R.id.myAutomaticPaymentsFragments,
                    inclusive = false
                )
            )
        }
    }

    override fun onFailedIdentityValidation(resultCode: String?) {
        sendEvent(UiEvent.NavigateBack)
    }
}
