package sv.com.tarjetaone.presentation.view.common.survey.row.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.domain.entities.response.OptionsSurveyUI
import sv.com.tarjetaone.presentation.compose.theme.customDimens

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun SurveyDynamicOptions(
    modifier: Modifier = Modifier,
    options: List<OptionsSurveyUI>,
    maxItemsInEachRow: Int = TWO_VALUE,
    selectedOption: OptionsSurveyUI?,
    onOptionSelected: (OptionsSurveyUI) -> Unit
) {
    val itemVerticalPadding = MaterialTheme.customDimens.dimen16
    val lasItem by remember { mutableStateOf(options.lastOrNull()) }
    Column {
        FlowRow(
            modifier = modifier
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen25),
            verticalArrangement = Arrangement.spacedBy(itemVerticalPadding),
            maxItemsInEachRow = maxItemsInEachRow
        ) {
            options.dropLast(ONE_VALUE).forEach {
                TextualSurveyOptionItem(
                    text = it.value.orEmpty(),
                    selected = selectedOption?.id == it.id,
                    onItemSelected = { onOptionSelected(it) }
                )
            }
        }
        lasItem?.let {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = itemVerticalPadding)
            ) {
                TextualSurveyOptionItem(
                    text = it.value.orEmpty(),
                    selected = selectedOption?.id == it.id,
                    onItemSelected = { onOptionSelected(it) }
                )
            }
        }
    }
}