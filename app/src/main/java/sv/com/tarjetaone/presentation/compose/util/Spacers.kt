package sv.com.tarjetaone.presentation.compose.util

import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import sv.com.tarjetaone.presentation.compose.theme.customDimens

@Composable
fun ColumnScope.Spacer(length: Dp) {
    Spacer(modifier = Modifier.height(length))
}

@Composable
fun RowScope.Spacer(length: Dp) {
    Spacer(modifier = Modifier.width(length))
}

@Composable
fun LazyItemScope.Spacer(length: Dp) {
    Spacer(modifier = Modifier.size(length))
}

/**
 * ColumnScope Spacer Scopes.
 */
@Composable
fun ColumnScope.Spacer1f() = Spacer(modifier = Modifier.weight(1f))

@Composable
fun ColumnScope.Spacer4() = Spacer(MaterialTheme.customDimens.dimen4)

@Composable
fun ColumnScope.Spacer8() = Spacer(MaterialTheme.customDimens.dimen8)

@Composable
fun ColumnScope.Spacer12() = Spacer(MaterialTheme.customDimens.dimen12)

@Composable
fun ColumnScope.Spacer16() = Spacer(MaterialTheme.customDimens.dimen16)

@Composable
fun ColumnScope.Spacer20() = Spacer(MaterialTheme.customDimens.dimen20)

@Composable
fun ColumnScope.Spacer24() = Spacer(MaterialTheme.customDimens.dimen24)

@Composable
fun ColumnScope.Spacer32() = Spacer(MaterialTheme.customDimens.dimen32)

@Composable
fun ColumnScope.Spacer40() = Spacer(MaterialTheme.customDimens.dimen40)

@Composable
fun ColumnScope.Spacer44() = Spacer(MaterialTheme.customDimens.dimen44)

@Composable
fun ColumnScope.Spacer48() = Spacer(MaterialTheme.customDimens.dimen48)

@Composable
fun ColumnScope.Spacer56() = Spacer(MaterialTheme.customDimens.dimen56)

@Composable
fun ColumnScope.Spacer64() = Spacer(MaterialTheme.customDimens.dimen64)

@Composable
fun ColumnScope.Spacer88() = Spacer(MaterialTheme.customDimens.dimen88)

/**
 * Row Spacer Scopes.
 */
@Composable
fun RowScope.Spacer1f() = Spacer(modifier = Modifier.weight(1f))

@Composable
fun RowScope.Spacer2() = Spacer(MaterialTheme.customDimens.dimen2)

@Composable
fun RowScope.Spacer4() = Spacer(MaterialTheme.customDimens.dimen4)

@Composable
fun RowScope.Spacer8() = Spacer(MaterialTheme.customDimens.dimen8)

@Composable
fun RowScope.Spacer12() = Spacer(MaterialTheme.customDimens.dimen12)

@Composable
fun RowScope.Spacer14() = Spacer(MaterialTheme.customDimens.dimen16)

@Composable
fun RowScope.Spacer16() = Spacer(MaterialTheme.customDimens.dimen16)

@Composable
fun RowScope.Spacer24() = Spacer(MaterialTheme.customDimens.dimen24)

@Composable
fun RowScope.Spacer32() = Spacer(MaterialTheme.customDimens.dimen32)

@Composable
fun RowScope.Spacer48() = Spacer(MaterialTheme.customDimens.dimen48)

/**
 * LazyItemScope Spacer Scopes.
 */
@Composable
fun LazyItemScope.Spacer8() = Spacer(MaterialTheme.customDimens.dimen8)

@Composable
fun LazyItemScope.Spacer16() = Spacer(MaterialTheme.customDimens.dimen16)

@Composable
fun LazyItemScope.Spacer24() = Spacer(MaterialTheme.customDimens.dimen24)

@Composable
fun LazyItemScope.Spacer48() = Spacer(MaterialTheme.customDimens.dimen48)
