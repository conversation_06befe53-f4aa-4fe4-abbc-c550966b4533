package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.generaldata.prepareforduicapture

import com.facephi.fphiselphidwidgetcore.WidgetSelphIDResult

sealed class PersonalDataCaptureDuiUiEvent {
    data object OnSupportClick : PersonalDataCaptureDuiUiEvent()
    data class OnCameraPermissionDenied(val showRationale: Boolean) : PersonalDataCaptureDuiUiEvent()
    data class OnDocumentCaptured(val result: WidgetSelphIDResult) : PersonalDataCaptureDuiUiEvent()
    data class OnDocumentCaptureFailed(val result: WidgetSelphIDResult) : PersonalDataCaptureDuiUiEvent()
}
