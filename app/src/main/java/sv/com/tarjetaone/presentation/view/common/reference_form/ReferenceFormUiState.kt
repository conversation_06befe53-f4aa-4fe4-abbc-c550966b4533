package sv.com.tarjetaone.presentation.view.common.reference_form

import androidx.annotation.StringRes
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI
import sv.com.tarjetaone.domain.entities.response.CatalogType
import sv.com.tarjetaone.presentation.helpers.ReferenceType
import sv.com.tarjetaone.presentation.helpers.UiText

data class ReferenceFormUiState(
    @StringRes
    val referenceTypeLabel: Int = R.string.family_parent,
    val referencesList: List<CatalogItemsCollectionUI> = emptyList(),
    val referenceValueSelected: CatalogItemsCollectionUI? = null,
    val referenceName: String = EMPTY_STRING,
    val referenceNameError: Boolean = false,
    val referencePhoneNumber: String = EMPTY_STRING,
    val referencePhoneNumberError: Boolean = false,
    val phoneNumberErrorMessage: String = EMPTY_STRING,
    val isDropdownEnabled: Boolean = true,
    val isValidatingPhone: Boolean = false,
)

fun ReferenceFormUiState.isContinueButtonEnabled(): Boolean =
    ((!referenceNameError && !referencePhoneNumberError) &&
            (referencePhoneNumber.isNotBlank() && referenceName.isNotBlank()))
            && !isValidatingPhone

fun ReferenceFormUiState.getPhoneErrorMessage(): UiText =
    if (phoneNumberErrorMessage.isNotBlank()) UiText.DynamicString(phoneNumberErrorMessage)
    else if (referencePhoneNumber.isBlank()) UiText.StringResource(R.string.field_is_mandatory)
    else UiText.StringResource(R.string.invalid_phone_number)

fun ReferenceType.getReferenceTypeForCatalog(): CatalogType = when (this) {
    ReferenceType.FAMILY -> CatalogType.FAMILY_REFERENCES_TYPES
    ReferenceType.PERSONAL -> CatalogType.PERSONAL_REFERENCE_TYPES
    ReferenceType.CASH_BACK -> CatalogType.CASHBACK_RELATION_TYPES
}
