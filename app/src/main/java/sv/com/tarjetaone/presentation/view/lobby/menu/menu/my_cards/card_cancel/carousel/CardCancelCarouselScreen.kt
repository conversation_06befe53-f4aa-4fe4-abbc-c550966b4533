package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_cancel.carousel

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.domain.entities.response.MainCardUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.card.CardCarousel
import sv.com.tarjetaone.presentation.compose.uicomponent.card.HomeCreditCard
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.personalization.CardPersonalizationDummyData
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer44
import sv.com.tarjetaone.presentation.compose.util.scaleTransition

@Composable
fun CardCancelCarouselScreen(viewModel: CardCancelCarouselViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    CardCancelCarouselScreen(uiState = uiState, onEvent = viewModel::onEvent)
}

@Composable
fun CardCancelCarouselScreen(
    uiState: CardCancelCarouselUiState,
    onEvent: (CardCancelCarouselUiEvent) -> Unit = {}
) {
    ScreenWithTopAppBar(
        title = stringResource(R.string.cancel_account_title),
        onLeftButtonClick = {
            onEvent(CardCancelCarouselUiEvent.OnBackClick)
        },
        onRightButtonClick = {
            onEvent(CardCancelCarouselUiEvent.OnTwilioClick)
        }
    ) {
        if (uiState.cardsToCancel.isNotEmpty()) {
            CardCancelCarouselScreenContent(uiState = uiState, onEvent = onEvent)
        }
    }
}

@Composable
fun CardCancelCarouselScreenContent(
    uiState: CardCancelCarouselUiState,
    onEvent: (CardCancelCarouselUiEvent) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        val cardPagerState = rememberPagerState(pageCount = { uiState.cardsToCancel.size })
        Spacer44()
        CardCarousel(
            cards = uiState.cardsToCancel,
            cardWidth = MaterialTheme.customDimens.dimen230,
            onCardChange = { onEvent(CardCancelCarouselUiEvent.OnCardSelected(it)) },
            pagerState = cardPagerState,
            preloadCardCount = TWO_VALUE,
        ) { page ->
            val currentCard = uiState.cardsToCancel[page]
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                HomeCreditCard(
                    isCardBlurEnabled = false,
                    availableCredit = currentCard.getFormattedBalance(),
                    usedCredit = currentCard.getFormattedCurrentBalance(),
                    isMainCard = currentCard.mainCard ?: false,
                    cardColors = currentCard.colors,
                    cardNumMasked = currentCard.takeUnless { it.mainCard == true }
                        ?.let {
                            stringResource(R.string.cancel_card_mask, it.cardNumMasked.orEmpty())
                        },
                    modifier = Modifier.scaleTransition(page, cardPagerState)
                )
                Spacer16()
                Text(
                    text = stringResource(R.string.cancel_account_active_card),
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.W500
                    )
                )
            }
        }
        Spacer1f()
        OneButton(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.customDimens.dimen16),
            enabled = uiState.cardsToCancel.isNotEmpty(),
            text = if (uiState.cardSelected?.mainCard == true) stringResource(R.string.cancel_main_card_label)
            else stringResource(R.string.cancel_additional_card_button_text),
            onClick = {
                onEvent(CardCancelCarouselUiEvent.OnCardCancellationClick)
            }
        )
        Spacer16()
    }
}

@Preview
@Composable
fun CardCancelCarouselScreenPreview() {
    OneAppTheme {
        val uiState = CardCancelCarouselUiState(
            cardsToCancel = listOf(
                MainCardUI(
                    balanceAvailable = 2000.0,
                    currentBalance = 500.0,
                    fullName = "John Doe",
                    mainCard = true,
                    colors = CardPersonalizationDummyData.colors.first()
                ),
                MainCardUI(
                    balanceAvailable = 500.0,
                    currentBalance = 500.0,
                    fullName = "John Doe",
                    mainCard = false,
                    cardNumMasked = "xxxx xxxx xxxx 1234",
                    colors = CardPersonalizationDummyData.colors.last(),
                )
            )
        )
        CardCancelCarouselScreen(uiState = uiState)
    }
}