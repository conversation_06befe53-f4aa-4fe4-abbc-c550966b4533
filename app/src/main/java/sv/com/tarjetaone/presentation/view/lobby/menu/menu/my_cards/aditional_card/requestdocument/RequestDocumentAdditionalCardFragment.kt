package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.requestdocument

import android.os.Bundle
import android.view.View
import androidx.fragment.app.viewModels
import androidx.navigation.findNavController
import dagger.hilt.android.AndroidEntryPoint
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.base.BaseFragment
import sv.com.tarjetaone.databinding.FragmentComposeBinding
import sv.com.tarjetaone.presentation.view.utils.requireLobbyActivity

@AndroidEntryPoint
class RequestDocumentAdditionalCardFragment : BaseFragment<FragmentComposeBinding>(
    R.layout.fragment_compose
) {
    override val viewModel: RequestDocumentAdditionalCardViewModel by viewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requireLobbyActivity().hideNavigationBottom()

        val prevScreen =
            view.findNavController().previousBackStackEntry?.destination?.id
        binding.composeView.setContentScreen {
            RequestDocumentAdditionalCardScreen(viewModel, prevScreen)
        }
    }
}
