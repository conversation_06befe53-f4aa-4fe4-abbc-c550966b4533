package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.references

import sv.com.tarjetaone.domain.entities.response.ReferenceUI
import sv.com.tarjetaone.presentation.helpers.ReferenceType

sealed class ReferencesEvent {
    data object OnStart : ReferencesEvent()
    data object OnBackClick : ReferencesEvent()
    data object OnSupportClick : ReferencesEvent()
    data class OnModifyReference(val referenceType: ReferenceType, val reference: ReferenceUI) : ReferencesEvent()
    data object OnDeleteReferenceConfirmed : ReferencesEvent()
    data class OnDeleteReference(val reference: ReferenceUI) : ReferencesEvent()
    data class OnAddNewReference(val referenceType: ReferenceType) : ReferencesEvent()
    data object OnDismissDeleteDialog : ReferencesEvent()
    data object OnHideSnackBar : ReferencesEvent()
}
