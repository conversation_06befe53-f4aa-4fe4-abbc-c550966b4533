package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import sv.com.tarjetaone.presentation.compose.theme.LocalCustomColors
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens

@Composable
fun CustomDotsIndicator(
    modifier: Modifier = Modifier,
    totalDots: Int,
    activeIndex: Int,
    activeColor: Color,
    inactiveColor: Color,
    dotSize: Dp,
    dotsSpacing: Arrangement.Horizontal = Arrangement.spacedBy(MaterialTheme.customDimens.dimen8)
) {
    LazyRow(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = dotsSpacing
    ) {
        items(totalDots) { iteration ->
            val activeDot = iteration == activeIndex
            Box(
                modifier = Modifier
                    .size(dotSize)
                    .background(
                        color = if (activeDot) activeColor else inactiveColor,
                        shape = CircleShape
                    )
            )
        }
    }
}

@Preview
@Composable
fun CustomDotsIndicatorPreview() {
    val customColors = LocalCustomColors.current
    OneAppTheme {
        CustomDotsIndicator(
            totalDots = 5,
            activeIndex = 2,
            activeColor = customColors.gray500,
            inactiveColor = customColors.gray300,
            dotSize = 8.dp
        )
    }
}
