package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldType
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.checkbox.CheckBoxWithText
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.text.ClickableHyperLinkTextProps
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.helpers.appendClickableText
import sv.com.tarjetaone.presentation.helpers.capitalize
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.component.PaymentProductsContainer
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.component.PXFormField
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform.AffiliationFormViewModel.Companion.NAVIGATE_TO_TERMS_TAG
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform.component.MaxAffiliationAmountContainer
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform.component.NotificationChannelContainer

@Composable
fun AffiliationFormScreen(
    viewModel: AffiliationFormViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    AffiliationFormScreen(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@Composable
private fun AffiliationFormScreen(
    uiState: AffiliationFormUiState,
    onEvent: (AffiliationFormUiEvent) -> Unit
) {
    LaunchedEffect(Unit) {
        onEvent(AffiliationFormUiEvent.OnStart)
    }
    ScreenWithTopAppBar(
        title = stringResource(id = R.string.automatic_payments_title),
        onLeftButtonClick = { onEvent(AffiliationFormUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(AffiliationFormUiEvent.OnTwilioClick) }
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
                .padding(
                    horizontal = MaterialTheme.customDimens.dimen24,
                ),
        ) {
            Box(modifier = Modifier.weight(1f)) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .verticalScroll(rememberScrollState())
                ) {
                    Spacer1f()
                    AffiliationForm(
                        modifier = Modifier.fillMaxWidth(),
                        uiState = uiState,
                        onEvent = onEvent
                    )
                    Spacer1f()
                }
            }
            Spacer16()
            OneButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        top = MaterialTheme.customDimens.dimen32,
                        bottom = MaterialTheme.customDimens.dimen24
                    ),
                text = stringResource(id = R.string.continue_button_label),
                enabled = uiState.isValidForm,
                onClick = { onEvent(AffiliationFormUiEvent.OnContinueClick) }
            )
        }
    }
}

@Composable
private fun AffiliationForm(
    modifier: Modifier = Modifier,
    uiState: AffiliationFormUiState,
    onEvent: (AffiliationFormUiEvent) -> Unit
) {
    Column(
        modifier = modifier
            .background(
                color = MaterialTheme.colorScheme.secondaryContainer,
                shape = MaterialTheme.shapes.large
            )
            .padding(
                vertical = MaterialTheme.customDimens.dimen16
            )
    ) {
        if (uiState.serviceName.isNotEmpty()) {
            AffiliationFormHeader(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.CenterHorizontally)
                    .padding(horizontal = MaterialTheme.customDimens.dimen16),
                serviceName = uiState.serviceName,
                serviceCategoryName = uiState.serviceCategoryName
            )
            Spacer8()
        }
        AffiliationFormBody(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.customDimens.dimen16),
            uiState = uiState,
            onEvent = onEvent
        )
    }
}

@Composable
private fun AffiliationFormHeader(
    modifier: Modifier = Modifier,
    serviceName: String,
    serviceCategoryName: String? = null,
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Text(
            text = serviceName,
            style = MaterialTheme.typography.bodyLarge.copy(
                fontWeight = FontWeight.Bold,
            ),
            maxLines = TWO_VALUE,
            overflow = TextOverflow.Ellipsis,
            textAlign = TextAlign.Center
        )
        serviceCategoryName?.let {
            Text(
                text = it,
                style = MaterialTheme.typography.labelMedium.copy(
                    color = MaterialTheme.customColors.gray600
                ),
                maxLines = TWO_VALUE,
                overflow = TextOverflow.Ellipsis,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun AffiliationFormBody(
    modifier: Modifier = Modifier,
    uiState: AffiliationFormUiState,
    onEvent: (AffiliationFormUiEvent) -> Unit
) {
    val focusManager = LocalFocusManager.current
    Column(modifier = modifier) {
        uiState.formFields?.forEach { field ->
            if (field.pxField.type != PXFieldType.Hidden) {
                PXFormField(
                    modifier = Modifier.fillMaxWidth(),
                    labelValue = field.pxField.label?.lowercase()?.capitalize().orEmpty(),
                    fieldState = field,
                    readOnly = true,
                    onValueChanged = { /* No implementation */}
                )
                Spacer16()
            }
        }
        NotificationChannelContainer(
            modifier = Modifier.fillMaxWidth(),
            notificationChannels = uiState.notificationChannels,
            selectedNotificationChannel = uiState.selectedNotificationChannel,
            onNotificationChannelChange = { notificationChannel ->
                onEvent(AffiliationFormUiEvent.OnSelectNotificationChannel(notificationChannel))
            }
        )
        Spacer16()
        PaymentProductsContainer(
            userProductsTypes = uiState.userProductsTypes,
            productsByTypeFiltered = uiState.userProductsFiltered,
            selectedProduct = uiState.selectedProduct,
            selectedProductType = uiState.selectedProductType,
            onSelectProductType = { productType ->
                onEvent(AffiliationFormUiEvent.OnSelectProductType(productType))
            },
            onSelectProduct = { product ->
                onEvent(AffiliationFormUiEvent.OnSelectedProduct(product))
            }
        )
        Spacer16()
        MaxAffiliationAmountContainer(
            modifier = Modifier.fillMaxWidth(),
            focusManager = focusManager,
            maxAmountEnabled = uiState.isMaxAmountEnabled,
            maxChargeAmount = uiState.maxChargeAmount,
            onMaxAmountChange = { chargeAmount ->
                onEvent(AffiliationFormUiEvent.OnChargeAmountChange(chargeAmount))
            },
            onMaxAmountOptionChange = {
                onEvent(AffiliationFormUiEvent.OnMaxChargeOptionChange(it))
            }
        )
        if (uiState.showTermsAndConditions) {
            Spacer16()
            TermsAndConditionsSection(
                modifier = Modifier.fillMaxWidth(),
                acceptedTermsAndConditions = uiState.acceptedTermsAndConditions,
                onCheckedChange = {
                    onEvent(AffiliationFormUiEvent.OnTermsCheckChange(it))
                },
                onTermsAndConditionsClick = {
                    onEvent(AffiliationFormUiEvent.NavigateToAffiliationTerms)
                }
            )
        }
    }
}

@Composable
private fun TermsAndConditionsSection(
    modifier: Modifier = Modifier,
    acceptedTermsAndConditions: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    onTermsAndConditionsClick: () -> Unit,
) {
    Column(modifier = modifier) {
        val annotatedString = buildAnnotatedString {
            append(stringResource(id = R.string.affiliation_terms_and_conditions))
            withStyle(
                style = SpanStyle(
                    color = MaterialTheme.colorScheme.primary,
                    textDecoration = TextDecoration.Underline
                )
            ) {
                appendClickableText(
                    tag = NAVIGATE_TO_TERMS_TAG,
                    text = stringResource(id = R.string.affiliation_terms_and_conditions_link),
                )
            }
        }
        CheckBoxWithText(
            clickableHyperLinkTextProps = ClickableHyperLinkTextProps(
                annotatedString = annotatedString,
                textStyle = MaterialTheme.typography.labelMedium.copy(
                    color = MaterialTheme.colorScheme.onBackground,
                ),
            ) { annotationTag ->
                if (annotationTag == NAVIGATE_TO_TERMS_TAG) {
                    onTermsAndConditionsClick()
                }
            },
            color = MaterialTheme.colorScheme.onBackground,
            decreaseCheckboxPadding = true,
            increaseCheckboxTouchTarget = false,
            isChecked = acceptedTermsAndConditions,
            onCheckedChanged = onCheckedChange
        )
    }
}

@Preview
@Composable
private fun AffiliationFormScreenPreview() {
    OneAppTheme {
        AffiliationFormScreen(
            uiState = AffiliationFormUiState(
                serviceName = "Service name",
                serviceCategoryName = "Service category name",
                isMaxAmountEnabled = true,
            ),
            onEvent = { }
        )
    }
}
