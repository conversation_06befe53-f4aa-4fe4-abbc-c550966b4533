package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.documentpreview

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.IMAGE_CODE_DUI
import sv.com.tarjetaone.core.interfaces.DuiValidation
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.request.CustomerData
import sv.com.tarjetaone.domain.entities.request.DocumentsData
import sv.com.tarjetaone.presentation.view.common.documentpreview.DocumentPreviewViewModel
import javax.inject.Inject

@HiltViewModel
class AdditionalCardDocumentPreviewViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    duiValidation: DuiValidation,
    imageUtils: ImageUtils,
    facephiResultHandler: FacephiResultHandler,
    private val sharedPrefsRepo: SecureSharedPreferencesRepository
) : DocumentPreviewViewModel(duiValidation, imageUtils, facephiResultHandler) {
    private val args =
        AdditionalCardDocumentPreviewFragmentArgs.fromSavedStateHandle(savedStateHandle)

    private fun setScanningResult() {
        sharedPrefsRepo.getOcr()?.let {
            sharedPrefsRepo.userAdditionalCardData = sharedPrefsRepo.userAdditionalCardData?.copy(
                matchingSidesScore = it.matchingSidesScore?.toDouble().orZero(),
                customer = CustomerData(
                    name = it.frontMLFirstName.orEmpty(),
                    birthDate = it.frontMLDateOfBirth?.getFormattedDateFromTo(
                        AppConstants.DAY_MONTH_YEAR_WITH_SLASH,
                        AppConstants.YEAR_MONTH_DAY_NO_SPACES
                    ).orEmpty(),
                    knownBy = it.frontMLFirstName.orEmpty(),
                    lastName = it.frontMLLastName.orEmpty(),
                    genderCode = it.gender.orEmpty(),
                    marriedName = it.marriedName.orEmpty(),
                    phoneNumber = sharedPrefsRepo.userAdditionalCardData?.customer?.phoneNumber.orEmpty(),
                    referenceTypeId = sharedPrefsRepo.userAdditionalCardData?.customer?.referenceTypeId.orZero(),
                    isNotificationTran = sharedPrefsRepo.userAdditionalCardData?.customer?.isNotificationTran
                        ?: false,
                    addressId = sharedPrefsRepo.userAdditionalCardData?.customer?.addressId,
                    address = sharedPrefsRepo.userAdditionalCardData?.customer?.address?.copy(),
                    documents = DocumentsData(
                        idTypeCode = IMAGE_CODE_DUI,
                        document = it.frontMLDocumentNumber?.replace(
                            AppConstants.DASH_STRING,
                            EMPTY_STRING
                        ).orEmpty(),
                        issuedDate = it.frontMLDateOfIssue?.getFormattedDateFromTo(
                            AppConstants.DAY_MONTH_YEAR_WITH_SLASH,
                            AppConstants.YEAR_MONTH_DAY_NO_SPACES
                        ).orEmpty(),
                        expirationDate = it.frontMLDateOfExpiry?.getFormattedDateFromTo(
                            AppConstants.DAY_MONTH_YEAR_WITH_SLASH,
                            AppConstants.YEAR_MONTH_DAY_NO_SPACES
                        ).orEmpty()
                    )
                )
            )

            sendEvent(
                UiEvent.Navigate(
                    AdditionalCardDocumentPreviewFragmentDirections
                        .actionAdditionalCardDocumentPreviewFragmentToDataOverviewAdditionalCard(
                            isAdditionalCard = args.isAdditionalCard,
                            shouldSkipAdditionalInformation = args.shouldSkipAdditionalInformation
                        )
                )
            )
        }
    }

    override fun onValidDocument() {
        setScanningResult()
    }

    override fun onInvalidDocument() {
        sendEvent(UiEvent.NavigateBack)
    }
}
