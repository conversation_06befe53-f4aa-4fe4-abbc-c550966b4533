package sv.com.tarjetaone.presentation.helpers.extensions

import sv.com.tarjetaone.common.utils.AppConstants.DAY_OF_WEEK_DAY_MONTH_TIME
import sv.com.tarjetaone.common.utils.AppConstants.YEAR_MONTH_DAY_WITH_DASH_AND_TIME
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.domain.entities.response.ManagementUI

fun ManagementUI.formatProcessDate(format: String = DAY_OF_WEEK_DAY_MONTH_TIME): String? {
    return processDate?.getFormattedDateFromTo(YEAR_MONTH_DAY_WITH_DASH_AND_TIME, format)
}

fun ManagementUI.formatCloseDate(format: String = DAY_OF_WEEK_DAY_MONTH_TIME): String? {
    return closeDate?.getFormattedDateFromTo(YEAR_MONTH_DAY_WITH_DASH_AND_TIME, format)
}
