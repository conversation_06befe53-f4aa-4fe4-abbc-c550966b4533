package sv.com.tarjetaone.presentation.compose.util

import android.net.Uri
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.net.toFile
import com.github.barteksc.pdfviewer.PDFView
import com.github.barteksc.pdfviewer.scroll.DefaultScrollHandle

/**
 * Composable wrapper for the PDFView library.
 *
 * @param uri Must be a Uri with a file scheme (file://).
 */
@Composable
fun PdfViewer(
    modifier: Modifier = Modifier,
    uri: Uri
) {
    AndroidView(
        modifier = modifier,
        factory = { context ->
            PDFView(context, null).apply {
                fromFile(uri.toFile())
                    .scrollHandle(DefaultScrollHandle(context))
                    .enableAnnotationRendering(true)
                    .defaultPage(0)
                    .load()
            }
        }
    )
}
