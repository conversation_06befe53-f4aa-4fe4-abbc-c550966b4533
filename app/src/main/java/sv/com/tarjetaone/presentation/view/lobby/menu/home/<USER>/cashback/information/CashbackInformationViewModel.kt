package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.cashback.information

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import javax.inject.Inject

@HiltViewModel
class CashbackInformationViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
) : BaseViewModel() {
    private val args = CashbackInformationFragmentArgs.fromSavedStateHandle(savedStateHandle)

    private fun onContinueClick() {
        baseSharedPrefs.hasSeenCashbackInfo = true
        sendEvent(
            UiEvent.Navigate(
                CashbackInformationFragmentDirections.actionMyDocumentsMenuFragmentToMyCashbackFragment(
                    args.pointsData
                )
            )
        )
    }

    fun onEvent(event: CashbackInformationUiEvent) {
        when (event) {
            CashbackInformationUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            CashbackInformationUiEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
            CashbackInformationUiEvent.OnContinueClick -> onContinueClick()
        }
    }
}