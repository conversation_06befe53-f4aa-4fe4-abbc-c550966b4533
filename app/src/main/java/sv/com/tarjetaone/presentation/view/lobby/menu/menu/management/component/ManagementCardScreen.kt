package sv.com.tarjetaone.presentation.view.lobby.menu.menu.management.component

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.card.SimpleCardComponent
import sv.com.tarjetaone.presentation.compose.util.Spacer16

@Composable
fun ManagementCardScreen(
    title: String,
    date: String,
    state: String,
    managementNumber: String,
    stateColor: Color,
    onClick: () -> Unit
) {
    SimpleCardComponent(
        modifier = Modifier
            .clickable { onClick() }
            .padding(horizontal = MaterialTheme.customDimens.dimen20),
        content = {
            Column(
                Modifier
                    .wrapContentHeight()
                    .fillMaxWidth(),
                verticalArrangement = Arrangement.Center
            ) {
                Row(
                    modifier = Modifier
                        .wrapContentSize()
                ) {
                    Column(
                        modifier = Modifier.weight(ONE_FLOAT_VALUE),
                        horizontalAlignment = Alignment.Start
                    ) {
                        Text(
                            textAlign = TextAlign.Start,
                            text = title,
                            style = MaterialTheme.typography.labelLarge.copy(
                                fontSize = MaterialTheme.customDimensSp.sp14,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            ),
                            modifier = Modifier.fillMaxWidth()
                        )
                        Text(
                            textAlign = TextAlign.Start,
                            text = date,
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.fillMaxWidth()
                        )
                        Spacer16()
                        Text(
                            textAlign = TextAlign.Start,
                            text = state,
                            style = MaterialTheme.typography.bodySmall.copy(color = stateColor),
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                    Column(
                        modifier = Modifier.weight(ONE_FLOAT_VALUE),
                        horizontalAlignment = Alignment.End
                    ) {
                        Text(
                            textAlign = TextAlign.End,
                            text = stringResource(id = R.string.request_number),
                            style = MaterialTheme.typography.labelMedium.copy(
                                fontWeight = FontWeight.Normal,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            ),
                            modifier = Modifier.fillMaxWidth()
                        )
                        Text(
                            textAlign = TextAlign.End,
                            text = managementNumber,
                            style = MaterialTheme.typography.labelMedium.copy(
                                fontWeight = FontWeight.Normal,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            ),
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            }
        }
    )
}

@Composable
@Preview
private fun ManagementCardPreview() {
    OneAppTheme {
        ManagementCardScreen(
            "Cambio de Pin",
            "20 de mayo",
            "Favorable",
            "1234532123123",
            Color.Red
        ) {}
    }
}
