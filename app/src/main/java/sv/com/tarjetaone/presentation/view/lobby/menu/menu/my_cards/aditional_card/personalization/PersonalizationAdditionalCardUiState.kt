package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.personalization

import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE_DOUBLE
import sv.com.tarjetaone.common.utils.CardColor
import sv.com.tarjetaone.presentation.view.utils.NamesCard
import sv.com.tarjetaone.presentation.helpers.UiText

data class PersonalizationAdditionalCardUiState(
    val sliderProgress: Int = ZERO_VALUE,
    val minLimit: Double = ZERO_VALUE_DOUBLE,
    val maxLimit: Double = ZERO_VALUE_DOUBLE,
    val stepSize: Int = ONE_VALUE,
    val cardLimit: String = EMPTY_STRING,
    val isValidAmount: Boolean = false,
    val errorMessage: UiText? = null,
    val nameOnCard: String = EMPTY_STRING,
    val colors: List<CardColor> = emptyList(),
    val selectedColor: CardColor? = null,
    val animateToBack: Boolean = false,
    val customerNames: List<NamesCard> = emptyList(),
    val customerFirstNames: List<String> = emptyList(),
    val customerLastNames: List<String> = emptyList(),
    val isValidNameSelection: Boolean = false
) {
    val isFormValid = isValidAmount && isValidNameSelection
}
