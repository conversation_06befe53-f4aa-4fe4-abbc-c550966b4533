package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.LAYOUT_WEIGHT_1
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.MovementStatus

@Composable
fun <T> TransactionsStateFilter(
    status: T,
    firstStatus: T,
    @StringRes firstStatusName: Int,
    secondStatus: T,
    @StringRes secondStatusName: Int,
    onStatusChange: (T) -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(MaterialTheme.customDimens.dimen48)
            .clip(MaterialTheme.shapes.small)
            .background(MaterialTheme.colorScheme.tertiary)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.matchParentSize()
        ) {
            FirstStateFilter(
                modifier = Modifier.weight(LAYOUT_WEIGHT_1),
                status = status,
                currentStatus = firstStatus,
                statusName = firstStatusName,
                onStatusChange = onStatusChange
            )
            FirstStateFilter(
                modifier = Modifier.weight(LAYOUT_WEIGHT_1),
                status = status,
                currentStatus = secondStatus,
                statusName = secondStatusName,
                onStatusChange = onStatusChange
            )
        }
    }
}

@Composable
private fun <T> FirstStateFilter(
    modifier: Modifier = Modifier,
    status: T,
    currentStatus: T,
    @StringRes statusName: Int,
    onStatusChange: (T) -> Unit
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
            .fillMaxHeight()
            .then(
                if (status == currentStatus) {
                    Modifier.background(
                        color = MaterialTheme.colorScheme.primary,
                        shape = MaterialTheme.shapes.small
                    )
                } else Modifier
            )
            .clip(MaterialTheme.shapes.small)
            .clickable { onStatusChange(currentStatus) }
    ) {
        Text(
            text = stringResource(id = statusName),
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.bodySmall.copy(
                color = if (status == currentStatus) Color.White else MaterialTheme.customColors.gray600,
                fontWeight = FontWeight.Bold,
                fontSize = MaterialTheme.customDimensSp.sp16
            )
        )
    }
}

@Preview
@Composable
private fun TransactionsStateFilterPreview() {
    OneAppTheme {
        TransactionsStateFilter(
            status = MovementStatus.Confirmed,
            firstStatus = MovementStatus.Confirmed,
            firstStatusName = R.string.my_movements_confirmed,
            secondStatus = MovementStatus.Pending,
            secondStatusName = R.string.my_movements_pending,
            onStatusChange = {}
        )
    }
}
