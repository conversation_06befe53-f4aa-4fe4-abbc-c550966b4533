package sv.com.tarjetaone.presentation.view.common.locationcapture

import android.location.Address
import androidx.lifecycle.viewModelScope
import com.google.android.gms.maps.model.LatLng
import com.google.maps.android.PolyUtil
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.AppConstants.DEPARTMENT
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.FIFTY_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.FOUR_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.MAX_SEARCH_SUGGESTIONS
import sv.com.tarjetaone.common.utils.AppConstants.POLYGON_TYPE_EXCLUDED
import sv.com.tarjetaone.common.utils.AppConstants.POLYGON_TYPE_MAP_SV
import sv.com.tarjetaone.common.utils.AppConstants.SV_DEFAULT_LOCATION_LAT
import sv.com.tarjetaone.common.utils.AppConstants.SV_DEFAULT_LOCATION_LNG
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.GeocoderUtil
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.request.UserLocationUI
import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI
import sv.com.tarjetaone.domain.entities.response.CatalogType
import sv.com.tarjetaone.domain.entities.response.SuggestionsItemUi
import sv.com.tarjetaone.domain.usecases.catalog.GetCatalogByParentUseCase
import sv.com.tarjetaone.domain.usecases.catalog.GetCatalogUseCase
import sv.com.tarjetaone.domain.usecases.requestcard.locationcapture.GetGeoZonesUseCase
import sv.com.tarjetaone.presentation.helpers.hasInvalidCharsAndMinLength
import sv.com.tarjetaone.presentation.helpers.hasInvalidCharsOrEmojis
import sv.com.tarjetaone.presentation.view.common.locationcapture.helper.AddressAliasSuggestion
import javax.inject.Inject

abstract class LocationCaptureBaseViewModel(
    private val getGeoZonesUseCase: GetGeoZonesUseCase,
    private val getCatalogUseCase: GetCatalogUseCase,
    private val getCatalogByParentUseCase: GetCatalogByParentUseCase
) : BaseViewModel() {
    protected val _uiState = MutableStateFlow(LocationCaptureUiState())
    val uiState = _uiState.asStateFlow()

    @Inject
    lateinit var geocoderUtil: GeocoderUtil

    /**
     * Method that should be implemented by the child class to handle navigation to the next screen
     * of the individual flow of address capture.
     * @param address object with the data entered by the user.
     */
    protected abstract fun goToNextScreen(address: UserLocationUI)

    /**
     * Methods that should be implemented by the child class to execute additional functionalities
     * like track events, calls to API, etc. These are optionals so there's not require to override
     * in the child class
     * */
    protected open fun onScreenStart() = Unit
    protected open fun onSaveMap() = Unit
    protected open fun onSaveAddress() = Unit
    protected open fun onSearchMyAddress() = Unit

    private suspend fun getAddressFromUserAddress(userAddress: UserLocationUI): Address? {
        return if (userAddress.latitude != null && userAddress.longitude != null) {
            geocoderUtil.getAddressByLocation(
                Pair(
                    userAddress.latitude?.toDouble().orZero(),
                    userAddress.longitude?.toDouble().orZero()
                )
            )
        } else {
            val address = userAddress.address ?: userAddress.municipio
            geocoderUtil.getAddressByLocationName(address.orEmpty())
        }
    }

    private fun onStart(showExcludedZones: Boolean) {
        onScreenStart()
        if (uiState.value.excludedZones.isNotEmpty()) {
            // If the excluded zones are already loaded, keep the current state
            _uiState.update { it.copy(isAddingAddressInfo = false) }
            return
        }
        sendEvent(SideEffect.Loading(true))
        viewModelScope.launch {
            getGeoZonesUseCase().executeUseCase { response ->
                val address = getAddressFromUserAddress(uiState.value.userAddress)
                val location = address?.let {
                    LatLng(it.latitude, it.longitude)
                } ?: LatLng(SV_DEFAULT_LOCATION_LAT, SV_DEFAULT_LOCATION_LNG)
                val polygons = response.data.flatMap { geoData ->
                    geoData.Zones.flatMap { zone -> zone.polygons.toList() }
                }
                val excludedZones = polygons
                    .filter { it.type == POLYGON_TYPE_EXCLUDED }
                    .map { polygon ->
                        polygon.coordinates.map {
                            LatLng(it.latitude.orZero(), it.longitude.orZero())
                        }
                    }
                val svZone = polygons
                    .find { it.type == POLYGON_TYPE_MAP_SV }
                    ?.coordinates
                    ?.map { LatLng(it.latitude.orZero(), it.longitude.orZero()) }
                sendEvent(SideEffect.Loading(false))
                _uiState.update { state ->
                    state.copy(
                        showExcludedZones = showExcludedZones,
                        location = location,
                        excludedZones = excludedZones,
                        svZone = svZone,
                        displayAddress = address?.getAddressString().orEmpty(),
                        mapAddress = address,
                        validLocation = validateLocation(
                            location,
                            !showExcludedZones,
                            svZone,
                            excludedZones
                        )
                    )
                }
            }
        }
    }

    protected fun updateInitialAlias(addressAlias: String?) {
        val addressAliasSuggestion = getAddressAliasSuggestion(addressAlias)
        _uiState.update {
            it.copy(
                addressAliasSelected = addressAliasSuggestion,
                addressAlias = addressAlias,
            )
        }
    }

    private fun getAddressAliasSuggestion(addressAlias: String?): AddressAliasSuggestion {
        return addressAlias?.let {
            AddressAliasSuggestion.fromName(it)
        } ?: AddressAliasSuggestion.Home
    }

    private fun validateLocation(
        location: LatLng,
        ignoreExcludedZones: Boolean = false,
        svZone: List<LatLng>? = uiState.value.svZone,
        excludedZones: List<List<LatLng>> = uiState.value.excludedZones
    ): Boolean {
        val isInsideSVZone = svZone?.let {
            PolyUtil.containsLocation(location, it, true)
        } ?: false
        val isInsideExcludedZone = !ignoreExcludedZones && excludedZones.any {
            PolyUtil.containsLocation(location, it, true)
        }
        return isInsideSVZone && !isInsideExcludedZone
    }

    private fun onMapClick(latLng: LatLng) {
        if (uiState.value.isAddingAddressInfo) return // Do not update location if address form is visible
        viewModelScope.launch {
            val address = geocoderUtil.getAddressByLocation(Pair(latLng.latitude, latLng.longitude))
            _uiState.update {
                it.copy(
                    location = latLng,
                    validLocation = validateLocation(latLng, !it.showExcludedZones),
                    displayAddress = address?.getAddressString().orEmpty(),
                    mapAddress = address,
                    searchQuery = EMPTY_STRING,
                    searchSuggestions = emptyList()
                )
            }
        }
    }

    private fun onSearchQueryChange(query: String) {
        _uiState.update {
            it.copy(searchQuery = query)
        }
    }

    private fun onPerformSearch(query: String) {
        viewModelScope.launch {
            geocoderUtil.getPlacesByQuery(query, MAX_SEARCH_SUGGESTIONS)?.let { suggestions ->
                _uiState.update { state ->
                    state.copy(
                        searchSuggestions = suggestions,
                        showSnackBarError = suggestions.isEmpty()
                    )
                }
            }
        }
    }

    private fun onSuggestionClick(suggestion: SuggestionsItemUi) {
        viewModelScope.launch {
            val coordinates = geocoderUtil.getCoordinatesById(suggestion.id)
            coordinates?.let { latLng ->
                val address =
                    geocoderUtil.getAddressByLocation(Pair(latLng.latitude, latLng.longitude))
                _uiState.update { state ->
                    state.copy(
                        location = latLng,
                        validLocation = validateLocation(latLng, !state.showExcludedZones),
                        displayAddress = address?.getAddressString().orEmpty(),
                        mapAddress = address,
                        searchQuery = EMPTY_STRING,
                        searchSuggestions = emptyList()
                    )
                }
            }
        }
    }

    /**
     * Method that builds the new user location object with the location selected in the map and the
     * data entered in the address form (only if adding a new address).
     */
    private fun updateUserAddress(): UserLocationUI {
        return with(uiState.value) {
            if (shouldShowForm) {
                // If adding a new address, update the user address with the form data
                userAddress.copy(
                    alias = retrieveAddressAlias(),
                    address = buildFullAddress(),
                    residential = neighborhood,
                    street = street,
                    houseNumber = homeNumber,
                    reference = additionalInfo,
                    departmentId = selectedState?.id,
                    departmentName = selectedState?.name,
                    department = selectedState?.name,
                    idMunicipio = selectedMunicipality?.id,
                    municipalityName = selectedMunicipality?.name,
                    municipio = selectedMunicipality?.name,
                    latitude = location.latitude.toString(),
                    longitude = location.longitude.toString(),
                    isAvailityDelivery = validateLocation(location)
                )
            } else {
                // If DUI address is correct, update the latitude and longitude
                userAddress.copy(
                    latitude = location.latitude.toString(),
                    longitude = location.longitude.toString(),
                    isAvailityDelivery = validateLocation(location)
                )
            }
        }
    }

    private fun onSaveClick() {
        onSaveMap()

        with(uiState.value) {
            if (shouldShowForm) {
                _uiState.update {
                    it.copy(
                        isAddingAddressInfo = true,
                        addressAlias = it.addressAlias.takeIf { alias ->
                            getAddressAliasSuggestion(alias) == AddressAliasSuggestion.Other
                        },
                        neighborhood = EMPTY_STRING,
                        street = EMPTY_STRING,
                        homeNumber = EMPTY_STRING,
                        additionalInfo = EMPTY_STRING,
                        selectedState = null,
                        selectedMunicipality = null,
                        validAlias = it.addressAlias?.isNotBlank() == true,
                        validAliasLength = it.addressAlias?.isNotBlank() == true,
                        validNeighborhood = false,
                        validStreet = false,
                        validHomeNumber = false,
                        validAdditionalInfo = false
                    )
                }
                populateDropdowns(
                    mapAddress?.adminArea?.replace(DEPARTMENT, EMPTY_STRING, true)?.trim(),
                    mapAddress?.locality
                )
            } else {
                goToNextScreen(updateUserAddress())
            }
        }
    }

    private fun onSearchMyAddressClick() {
        onSearchMyAddress()
        _uiState.update { it.copy(isAddingAddressInfo = false) }
    }

    private fun onAddressAliasSuggestionChange(suggestion: AddressAliasSuggestion) {
        _uiState.update {
            it.copy(
                addressAliasSelected = suggestion,
            )
        }
    }

    private fun onAliasChange(alias: String) {
        _uiState.update {
            it.copy(
                addressAlias = alias,
                validAlias = !alias.trim().hasInvalidCharsAndMinLength(FOUR_VALUE),
                validAliasLength = alias.length <= FIFTY_VALUE
            )
        }
    }

    private fun onNeighborhoodChange(neighborhood: String) {
        _uiState.update {
            it.copy(
                neighborhood = neighborhood,
                validNeighborhood = !neighborhood.hasInvalidCharsAndMinLength()
            )
        }
    }

    private fun onStreetChange(street: String) {
        _uiState.update {
            it.copy(
                street = street,
                validStreet = !street.hasInvalidCharsAndMinLength()
            )
        }
    }

    private fun onHomeNumberChange(homeNumber: String) {
        _uiState.update {
            it.copy(
                homeNumber = homeNumber,
                validHomeNumber = !homeNumber.hasInvalidCharsOrEmojis()
            )
        }
    }

    private fun onAdditionalInfoChange(additionalInfo: String) {
        _uiState.update {
            it.copy(
                additionalInfo = additionalInfo,
                validAdditionalInfo = !additionalInfo.hasInvalidCharsAndMinLength()
            )
        }
    }

    private fun populateDropdowns(stateName: String?, municipalityName: String?) {
        if (uiState.value.states.isNotEmpty()) { // States catalog already loaded
            val selectedState = uiState.value.states.firstOrNull { it.name.equals(stateName, true) }
            selectedState?.let { onStateChange(it, municipalityName) }
            return
        }
        sendEvent(SideEffect.Loading(true))
        viewModelScope.launch {
            getCatalogUseCase(CatalogType.DEPARTMENTS).executeUseCase { value ->
                val states = value.dataCatalog?.catalogItemsCollection.orEmpty()
                val selectedState = states.firstOrNull { it.name.equals(stateName, true) }

                _uiState.update { it.copy(states = states) }
                sendEvent(SideEffect.Loading(false))
                selectedState?.let { onStateChange(it, municipalityName) }
            }
        }
    }

    private fun onStateChange(state: CatalogItemsCollectionUI, municipalityName: String? = null) {
        _uiState.update {
            val municipality = if (it.selectedState == state) {
                it.selectedMunicipality
            } else {
                // repopulate when state changes
                populateMunicipalities(state, municipalityName)
                null
            }
            it.copy(
                selectedState = state,
                selectedMunicipality = municipality
            )
        }
    }

    private fun populateMunicipalities(state: CatalogItemsCollectionUI, municipalityName: String?) {
        sendEvent(SideEffect.Loading(true))
        viewModelScope.launch {
            getCatalogByParentUseCase(
                CatalogType.MUNICIPALITIES,
                state.id.toInt()
            ).executeUseCase { value ->
                val municipalities = value.dataCatalog?.catalogItemsCollection.orEmpty()
                val selectedMunicipality = municipalityName?.let { name ->
                    municipalities.firstOrNull { it.name.equals(name, true) }
                }

                _uiState.update {
                    it.copy(
                        municipalities = municipalities,
                        selectedMunicipality = selectedMunicipality
                    )
                }
                sendEvent(SideEffect.Loading(false))
            }
        }
    }

    private fun onMunicipalityChange(municipality: CatalogItemsCollectionUI) {
        _uiState.update { it.copy(selectedMunicipality = municipality) }
    }

    private fun onContinueClick() {
        onSaveAddress()
        goToNextScreen(updateUserAddress())
    }

    private fun onSnackBarVisibilityChange(visibility: Boolean) {
        _uiState.update { it.copy(showSearchSnackBar = visibility) }
    }

    fun onEvent(event: LocationCaptureUiEvent) {
        when (event) {
            is LocationCaptureUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            is LocationCaptureUiEvent.OnStart -> onStart(showExcludedZones = event.showExcludedZones)

            is LocationCaptureUiEvent.OnMapClick -> onMapClick(event.latLng)
            is LocationCaptureUiEvent.OnSearchQueryChange -> onSearchQueryChange(event.query)
            is LocationCaptureUiEvent.OnPerformSearch -> onPerformSearch(event.query)
            is LocationCaptureUiEvent.OnSuggestionClick -> onSuggestionClick(event.suggestion)
            is LocationCaptureUiEvent.OnSaveClick -> onSaveClick()
            is LocationCaptureUiEvent.OnSearchMyAddressClick -> onSearchMyAddressClick()
            is LocationCaptureUiEvent.OnAddressAliasSuggestionChange -> onAddressAliasSuggestionChange(
                event.suggestion
            )

            is LocationCaptureUiEvent.OnAddressAliasChange -> onAliasChange(event.alias)
            is LocationCaptureUiEvent.OnNeighborhoodChange -> onNeighborhoodChange(event.neighborhood)
            is LocationCaptureUiEvent.OnStreetChange -> onStreetChange(event.street)
            is LocationCaptureUiEvent.OnHomeNumberChange -> onHomeNumberChange(event.homeNumber)
            is LocationCaptureUiEvent.OnAdditionalInfoChange -> onAdditionalInfoChange(event.additionalInfo)
            is LocationCaptureUiEvent.OnMunicipalityChange -> onMunicipalityChange(event.municipality)
            is LocationCaptureUiEvent.OnStateChange -> onStateChange(event.state)
            LocationCaptureUiEvent.OnContinueClick -> onContinueClick()
            is LocationCaptureUiEvent.OnSnackBarVisibility -> onSnackBarVisibilityChange(event.visibility)
        }
    }
}
