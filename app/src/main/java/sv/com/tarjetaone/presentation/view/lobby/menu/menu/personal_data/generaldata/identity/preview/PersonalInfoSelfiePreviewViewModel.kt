package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.generaldata.identity.preview

import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.presentation.view.common.selfiepreview.SelfiePreviewViewModel
import javax.inject.Inject

@HiltViewModel
class PersonalInfoSelfiePreviewViewModel @Inject constructor(
    imageUtils: ImageUtils,
    facephiResultHandler: FacephiResultHandler
) : SelfiePreviewViewModel(imageUtils, facephiResultHandler) {
    override fun onContinueClick() {
        sendEvent(
            UiEvent.Navigate(
                PersonalInfoSelfiePreviewFragmentDirections
                    .actionSelfieConfirmationPersonalDataOverviewFragmentToPersonalDataIdentityValidationFragment()
            )
        )
    }
}
