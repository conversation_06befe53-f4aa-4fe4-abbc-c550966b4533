package sv.com.tarjetaone.presentation.view.common.survey.row

import androidx.annotation.StringRes
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import sv.com.tarjetaone.domain.entities.request.SurveyOptionType.OPTION_OPEN
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.view.common.survey.row.component.SurveyDynamicOptions
import sv.com.tarjetaone.presentation.view.common.survey.row.component.SurveyOtherCommentSection

@Composable
fun SurveyRowTypeScreen(
    modifier: Modifier = Modifier,
    uiState: SurveyRowTypeUiState,
    onEvent: (SurveyRowTypeUiEvent) -> Unit,
    isOtherTextVisible: Boolean = false,
    @StringRes actionButtonLabel: Int
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(MaterialTheme.customDimens.dimen24)
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = uiState.description,
            style = MaterialTheme.typography.bodySmall
        )
        Spacer24()
        if (uiState.surveyOptions.isNotEmpty()) {
            SurveyDynamicOptions(
                options = uiState.surveyOptions,
                selectedOption = uiState.selectedOption,
                onOptionSelected = {
                    onEvent(SurveyRowTypeUiEvent.OnOptionSelected(it))
                }
            )
            Spacer16()
            SurveyOtherCommentSection(
                isOtherTextVisible = isOtherTextVisible,
                isSectionVisible = uiState.selectedOption?.type == OPTION_OPEN.type,
                commentValue = uiState.additionalComments,
                onValueChange = {
                    onEvent(SurveyRowTypeUiEvent.OnAdditionalCommentsChange(it))
                }
            )
        }
        Spacer16()
        Spacer1f()
        OneButton(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(id = actionButtonLabel),
            enabled = uiState.isSendButtonEnabled,
            onClick = {
                onEvent(SurveyRowTypeUiEvent.OnSendSurvey)
            }
        )
        Spacer16()
    }
}