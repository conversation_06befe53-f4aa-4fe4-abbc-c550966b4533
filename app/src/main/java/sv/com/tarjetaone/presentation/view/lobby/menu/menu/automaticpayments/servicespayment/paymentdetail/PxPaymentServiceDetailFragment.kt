package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.paymentdetail

import android.os.Bundle
import android.view.View
import androidx.fragment.app.viewModels
import dagger.hilt.android.AndroidEntryPoint
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.base.BaseFragment
import sv.com.tarjetaone.databinding.FragmentComposeBinding

@AndroidEntryPoint
class PxPaymentServiceDetailFragment : BaseFragment<FragmentComposeBinding>(
    R.layout.fragment_compose
) {
    override val viewModel: PxPaymentServiceDetailViewModel by viewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.composeView.setContentScreen {
            PxPaymentServiceDetailScreen(viewModel)
        }
    }
}
