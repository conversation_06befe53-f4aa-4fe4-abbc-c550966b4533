package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.details

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.presentation.view.common.management.ManagementDetailBaseViewModel
import sv.com.tarjetaone.presentation.view.utils.DAY_OF_WEEK_MONTH_TIME_FORMAT
import sv.com.tarjetaone.presentation.view.utils.YEAR_MONTH_DAY_TIME_FORMAT
import javax.inject.Inject

@HiltViewModel
class AdditionalCardFinishDetailsViewModel @Inject constructor(
    imageUtils: ImageUtils,
    savedStateHandle: SavedStateHandle
) : ManagementDetailBaseViewModel(imageUtils) {
    private val args = AdditionalCardFinishDetailsFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onStart() {
        _uiState.update {
            it.copy(
                managementNumber = args.data.manRequestId.toString(),
                managementStatus = args.data.mrStatusNameApp.orEmpty(),
                managementStatusColor = args.data.mrStatusTextColor.orEmpty(),
                managementName = args.data.manTypeNameApp.orEmpty(),
                customerName = args.data.clientName.orEmpty(),
                creditCardNumber = args.data.cardNumMasked.orEmpty(),
                creditCardType = args.data.typeCardText.orEmpty(),
                requestStartDate = args.data.processDate?.getFormattedDateFromTo(
                    YEAR_MONTH_DAY_TIME_FORMAT,
                    DAY_OF_WEEK_MONTH_TIME_FORMAT
                ).orEmpty(),
                description = args.data.description,
                resolutionDays = args.data.availableDay,
                managementAttributes = transformAttributesToUiTextList(args.data.manAttributes)
            )
        }
    }

    override fun onPrimaryButtonClick() {
        sendEvent(UiEvent.Navigate(AdditionalCardFinishDetailsFragmentDirections.actionHome()))
    }
}