package sv.com.tarjetaone.presentation.view.lobby.menu.home

import sv.com.tarjetaone.domain.entities.response.MainCardUI

sealed class HomeUiEvent {
    data class OnStart(val isStrongBiometricAvailable: Boolean) : HomeUiEvent()
    data object OnRetryHome : HomeUiEvent()
    data object OnRetryAvailablePoints : HomeUiEvent()
    data object OnCardDetailsClick : HomeUiEvent()
    data object OnMyMovementsClick : HomeUiEvent()
    data object OnMyStatementClick : HomeUiEvent()
    data object OnMyPointsClick : HomeUiEvent()
    data class OnRequestCardClick(val card: MainCardUI) : HomeUiEvent()
    data object OnCancelDetailsClick : HomeUiEvent()
    data object OnDeleteAccountClick : HomeUiEvent()
    data object OnBiometryAuthenticationSuccess : HomeUiEvent()
    data class OnBiometryAuthenticationFailed(val type: BiometricSheetType) : HomeUiEvent()
    data object OnDismissBiometricLoginSheet : HomeUiEvent()
    data object OnDismissChargeOffBottomSheet : HomeUiEvent()
}
