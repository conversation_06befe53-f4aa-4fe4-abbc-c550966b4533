package sv.com.tarjetaone.presentation.view.lobby.menu.home

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.BottomSheetDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.FORTY_FIVE_VALUE_PERCENT
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.domain.entities.response.CreditCardStatus
import sv.com.tarjetaone.domain.entities.response.MainCardUI
import sv.com.tarjetaone.domain.entities.response.ReplacementStatus
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.SetStatusBarAppearance
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.card.CardCarousel
import sv.com.tarjetaone.presentation.compose.uicomponent.card.HomeCreditCard
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.OneBackHandler
import sv.com.tarjetaone.presentation.compose.uicomponent.personalization.CardPersonalizationDummyData
import sv.com.tarjetaone.presentation.compose.uicomponent.skeleton.SkeletonContainer
import sv.com.tarjetaone.presentation.compose.uicomponent.skeleton.SkeletonState
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.LoadAsyncImage
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.compose.util.scaleTransition
import sv.com.tarjetaone.presentation.helpers.BenefitType
import sv.com.tarjetaone.presentation.view.lobby.menu.home.component.CardDetailsAction
import sv.com.tarjetaone.presentation.view.lobby.menu.home.component.ChargeOffInfoBottomSheet
import sv.com.tarjetaone.presentation.view.lobby.menu.home.component.HomeBottomSheet
import sv.com.tarjetaone.presentation.view.lobby.menu.home.component.HomeCardAction
import sv.com.tarjetaone.presentation.view.lobby.menu.home.inactive_product.InactiveProductScreen
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.component.PointsOverviewCard
import sv.com.tarjetaone.presentation.view.lobby.menu.home.skeleton.CardCarouselSkeleton
import sv.com.tarjetaone.presentation.view.lobby.menu.home.skeleton.HomeNoContentState
import sv.com.tarjetaone.presentation.view.lobby.menu.home.skeleton.PointsContainerErrorState
import sv.com.tarjetaone.presentation.view.utils.BiometricPromptManager

/**
 * Home screen composable. Lambdas are used to handle actions that rely on functionality provided by
 * the activity/legacy code
 */
@Composable
fun HomeScreen(
    viewModel: HomeViewModel,
    onHideNavBar: () -> Unit,
    onShowNavBar: () -> Unit,
    onLogOutClick: () -> Unit,
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val context = LocalContext.current
    val biometricPromptManager = remember { BiometricPromptManager(context) }
    OneBackHandler()
    SetStatusBarAppearance()
    LaunchedEffect(Unit) {
        viewModel.onEvent(HomeUiEvent.OnStart(biometricPromptManager.isStrongBiometricAvailable()))
        onHideNavBar()
    }

    AnimatedContent(
        targetState = uiState.homeStatus,
        label = HOME_SCREEN_STATUS_ANIMATION,
        transitionSpec = { fadeIn() togetherWith fadeOut() },
        modifier = Modifier.fillMaxSize(),
    ) { targetState ->
        when (targetState) {
            HomeStatus.Close -> {
                InactiveProductScreen(
                    informationText = R.string.home_inactive_product_description,
                    primaryButtonText = R.string.logout,
                    secondaryButtonText = R.string.delete_account_definitely,
                    secondaryButtonVariant = ButtonVariant.HYPERLINK_VARIANT,
                    onPrimaryButtonAction = {
                        onLogOutClick()
                    },
                    onSecondaryButtonAction = {
                        viewModel.onEvent(HomeUiEvent.OnDeleteAccountClick)
                    }
                )
            }

            HomeStatus.ThirdFailedDeliveryAttempt -> {
                InactiveProductScreen(
                    informationText = R.string.home_inactive_product_description,
                    showPrimaryButton = false,
                    secondaryButtonText = R.string.logout,
                    secondaryButtonVariant = ButtonVariant.SECONDARY_VARIANT,
                    onSecondaryButtonAction = {
                        onLogOutClick()
                    }
                )
            }

            else -> {
                if (uiState.mainCardStatus == CreditCardStatus.Close) {
                    InactiveProductScreen(
                        informationText = R.string.card_cancellation_in_process_message,
                        primaryButtonText = R.string.show_request_status,
                        secondaryButtonText = R.string.logout,
                        secondaryButtonVariant = ButtonVariant.SECONDARY_VARIANT,
                        onPrimaryButtonAction = {
                            viewModel.onEvent(HomeUiEvent.OnCancelDetailsClick)
                        },
                        onSecondaryButtonAction = {
                            onLogOutClick()
                        }
                    )
                } else {
                    LaunchedEffect(uiState.cardsState) {
                        if (uiState.cardsState == SkeletonState.Loading ||
                            uiState.cardsState == SkeletonState.Failure
                        ) {
                            onHideNavBar()
                        } else {
                            onShowNavBar()
                        }
                    }
                    HomeScreen(
                        uiState = uiState,
                        onEvent = viewModel::onEvent
                    )
                }
            }
        }
    }
    if (uiState.showChargeOffBottomSheet) {
        ChargeOffBottomSheetSection(
            onDismissRequest = { viewModel.onEvent(HomeUiEvent.OnDismissChargeOffBottomSheet) },
            onUnderstoodEvent = { viewModel.onEvent(HomeUiEvent.OnDismissChargeOffBottomSheet) }
        )
    }
    BottomSheetSection(
        uiState = uiState,
        onEvent = viewModel::onEvent,
        biometricPromptManager = biometricPromptManager
    )
}

@Composable
fun HomeScreen(
    uiState: HomeUiState,
    onEvent: (HomeUiEvent) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.customColors.textSurfaceVariant)
    ) {
        GreetingSection(
            name = uiState.name,
            modifier = Modifier.fillMaxWidth(),
        )
        HomeContentBody(
            modifier = Modifier.fillMaxSize(),
            uiState = uiState,
            onEvent = onEvent
        )
    }
}

@Composable
private fun HomeContentBody(
    modifier: Modifier = Modifier,
    uiState: HomeUiState,
    onEvent: (HomeUiEvent) -> Unit,
) {
    AnimatedContent(
        modifier = modifier,
        targetState = uiState.cardsState,
        label = HOME_SCREEN_CONTENT_ANIMATION,
        transitionSpec = { fadeIn() togetherWith fadeOut() }
    ) { targetState ->
        when (targetState) {

            SkeletonState.Failure -> {
                HomeContentFailure(
                    modifier = modifier.fillMaxSize(),
                    onEvent = onEvent
                )
            }

            else -> {
                HomeContentLoaded(
                    uiState = uiState,
                    onEvent = onEvent
                )
            }
        }
    }
}

@Composable
fun HomeContentLoaded(
    modifier: Modifier = Modifier,
    uiState: HomeUiState,
    onEvent: (HomeUiEvent) -> Unit,
) {
    var selectedCard by remember(uiState.cards) {
        mutableStateOf(uiState.cards.firstOrNull())
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
    ) {
        Spacer16()
        Text(
            modifier = Modifier.fillMaxWidth(),
            text = uiState.lastLogin,
            color = MaterialTheme.customColors.gray600,
            style = MaterialTheme.typography.labelMedium,
            fontWeight = FontWeight.SemiBold,
            textAlign = TextAlign.Center,
        )
        CardsSection(
            cards = uiState.cards,
            skeletonState = uiState.cardsState,
            onRequestCardClick = { onEvent(HomeUiEvent.OnRequestCardClick(it)) },
            modifier = Modifier.fillMaxWidth(),
            onCardChange = { selectedCard = uiState.cards.getOrNull(it) }
        )
        Spacer24()
        selectedCard?.let {
            CardActionsSection(
                selectedCard = it,
                skeletonState = uiState.cardsState,
                onCardDetailsClick = { onEvent(HomeUiEvent.OnCardDetailsClick) },
                onMyMovementsClick = { onEvent(HomeUiEvent.OnMyMovementsClick) },
                onMyStatementClick = { onEvent(HomeUiEvent.OnMyStatementClick) },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.customDimens.dimen24),
            )
        }
        Spacer24()
        SkeletonContainer(
            modifier = Modifier.padding(horizontal = MaterialTheme.customDimens.dimen24),
            state = uiState.availablePointsState,
            errorContent = { dpSize ->
                PointsContainerErrorState(
                    modifier = Modifier.size(dpSize),
                    onRetry = { onEvent(HomeUiEvent.OnRetryAvailablePoints) }
                )
            }
        ) {
            PointsOverviewCard(
                benefitType = uiState.benefitType,
                pointsData = uiState.availablePoints,
                secondaryText = stringResource(
                    id = if (uiState.benefitType == BenefitType.CASHBACK_BENEFIT.type) {
                        R.string.see_my_cashback
                    } else {
                        R.string.see_megapoints
                    }
                ),
                secondaryTextDecoration = TextDecoration.Underline,
                onClick = { onEvent(HomeUiEvent.OnMyPointsClick) },
            )
        }
        Spacer32()
    }
}

@Composable
private fun HomeContentFailure(
    modifier: Modifier = Modifier,
    onEvent: (HomeUiEvent) -> Unit,
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.Center,
    ) {
        HomeNoContentState(
            modifier = Modifier.fillMaxWidth(),
            onLoadAgain = { onEvent(HomeUiEvent.OnRetryHome) }
        )
    }
}

@Composable
private fun GreetingSection(
    modifier: Modifier = Modifier,
    name: String,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .background(MaterialTheme.customColors.defaultSurface)
                .statusBarsPadding()
                .padding(MaterialTheme.customDimens.dimen24)
        ) {
            Icon(
                modifier = Modifier.height(MaterialTheme.customDimens.dimen18),
                imageVector = ImageVector.vectorResource(R.drawable.app_one_logo),
                contentDescription = null
            )
            Text(
                text = stringResource(R.string.greeting_home, name),
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.SemiBold,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = MaterialTheme.customDimens.dimen8),
            )
        }
    }
}

@Composable
private fun CardsSection(
    modifier: Modifier = Modifier,
    skeletonState: SkeletonState,
    cards: List<MainCardUI>,
    onCardChange: (Int) -> Unit,
    onRequestCardClick: (MainCardUI) -> Unit
) {
    val cardsPager = rememberPagerState(pageCount = { cards.size })

    SkeletonContainer(
        state = skeletonState,
        customSkeletonContent = { CardCarouselSkeleton() }
    ) {
        CardCarousel(
            modifier = modifier,
            cards = cards,
            cardWidth = MaterialTheme.customDimens.dimen230,
            onCardChange = onCardChange,
            pagerState = cardsPager,
            preloadCardCount = TWO_VALUE,
        ) {
            val currentCard = cards[it]

            HomeCreditCard(
                availableCredit = currentCard.getFormattedBalance(),
                usedCredit = currentCard.getFormattedCurrentBalance(),
                cardNumMasked = null,
                holderName = currentCard.nameOnCard,
                isMainCard = currentCard.mainCard ?: false,
                status = CreditCardStatus.fromName(currentCard.ccStatus.orEmpty()),
                replacementStatus = ReplacementStatus.fromName(currentCard.replacementStatus.orEmpty()),
                cardColors = currentCard.colors,
                onRequestCardClick = { onRequestCardClick(currentCard) },
                modifier = Modifier.scaleTransition(it, cardsPager)
            )
        }
    }
}

@Composable
private fun CardActionsSection(
    modifier: Modifier = Modifier,
    selectedCard: MainCardUI,
    skeletonState: SkeletonState,
    onCardDetailsClick: () -> Unit,
    onMyMovementsClick: () -> Unit,
    onMyStatementClick: () -> Unit,
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen16)
    ) {
        if (selectedCard.mainCard == true) {
            SkeletonContainer(state = skeletonState) {
                CardDetailsAction(
                    card = selectedCard,
                    onClick = onCardDetailsClick
                )
            }
        }
        SkeletonContainer(state = skeletonState) {
            HomeCardAction(
                modifier = Modifier,
                label = stringResource(R.string.my_movements),
                onClick = onMyMovementsClick
            )
        }
        if (selectedCard.mainCard == true) {
            SkeletonContainer(
                state = skeletonState,
                shape = MaterialTheme.shapes.medium,
            ) {
                HomeCardAction(
                    label = stringResource(R.string.account_status),
                    onClick = onMyStatementClick
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ChargeOffBottomSheetSection(
    onDismissRequest: () -> Unit,
    onUnderstoodEvent: () -> Unit
) {
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    ChargeOffInfoBottomSheet(
        sheetState = sheetState,
        onDismissRequest = onDismissRequest,
        onUnderstoodClick = onUnderstoodEvent
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun BottomSheetSection(
    uiState: HomeUiState,
    onEvent: (HomeUiEvent) -> Unit,
    biometricPromptManager: BiometricPromptManager
) {
    val sheetState = rememberModalBottomSheetState()

    if (uiState.isStrongBiometricAvailable && uiState.showBiometricDialog) {
        ModalBottomSheet(
            onDismissRequest = { onEvent(HomeUiEvent.OnDismissBiometricLoginSheet) },
            sheetState = sheetState,
            shape = MaterialTheme.shapes.large,
            dragHandle = null,
            containerColor = if (uiState.isLoadingBiometric) {
                MaterialTheme.colorScheme.inverseSurface
            } else {
                BottomSheetDefaults.ContainerColor
            }
        ) {
            if (uiState.isLoadingBiometric) {
                Box(
                    modifier = Modifier
                        .fillMaxHeight(FORTY_FIVE_VALUE_PERCENT)
                        .fillMaxWidth()
                        .align(Alignment.CenterHorizontally)
                ) {
                    LoadAsyncImage(
                        modifier = Modifier
                            .width(MaterialTheme.customDimens.dimen92)
                            .height(MaterialTheme.customDimens.dimen92)
                            .align(Alignment.Center),
                        url = R.raw.loading_bars,
                        placeholderPreview = R.raw.loading_bars
                    )
                }
            } else {
                BottomSheetContent(
                    type = uiState.biometricPromptType,
                    biometricPromptManager = biometricPromptManager,
                    onEvent = onEvent
                )
            }
        }
    }
}

@Composable
private fun BottomSheetContent(
    type: BiometricSheetType,
    biometricPromptManager: BiometricPromptManager,
    onEvent: (HomeUiEvent) -> Unit
) {
    val promptTitle = stringResource(id = R.string.finger_print)
    val negativeText = stringResource(id = R.string.decline_activation)

    HomeBottomSheet(
        type = type,
        onButtonClick = { sheetType ->
            when (sheetType) {
                BiometricSheetType.ACTIVATE, BiometricSheetType.FAILED -> {
                    biometricPromptManager.showBiometricPrompt(
                        title = promptTitle,
                        negativeButtonText = negativeText,
                        onAuthenticationFailed = {
                            onEvent(HomeUiEvent.OnBiometryAuthenticationFailed(sheetType))
                        },
                        onAuthenticationSuccess = {
                            onEvent(HomeUiEvent.OnBiometryAuthenticationSuccess)
                        }
                    )
                }

                BiometricSheetType.SUCCESS -> onEvent(HomeUiEvent.OnDismissBiometricLoginSheet)
            }
        },
        onSecondaryClick = {
            onEvent(HomeUiEvent.OnDismissBiometricLoginSheet)
        }
    )
}

@Preview
@Composable
private fun HomeScreenPreview() {
    OneAppTheme {
        HomeScreen(
            uiState = HomeUiState(
                name = "Juan Hernandez",
                lastLogin = "Última sesión 11/10/2024 10:39 a.m.",
                cardsState = SkeletonState.Success,
                availablePointsState = SkeletonState.Success,
                cards = listOf(
                    MainCardUI(
                        balanceAvailable = 2000.0,
                        currentBalance = 500.0,
                        fullName = "John Doe",
                        mainCard = true,
                        colors = CardPersonalizationDummyData.colors.first()
                    ),
                    MainCardUI(
                        balanceAvailable = 2000.0,
                        currentBalance = 500.0,
                        fullName = "John Doe",
                        mainCard = false,
                        colors = CardPersonalizationDummyData.colors.first()
                    )
                ),
            ),
            onEvent = {}
        )
    }
}

const val HOME_SCREEN_CONTENT_ANIMATION = "HomeScreenContentAnimation"
const val HOME_SCREEN_STATUS_ANIMATION = "HomeScreenStatusAnimation"
