package sv.com.tarjetaone.presentation.view.lobby.menu.home

import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.analytics.dynatrace.DynatraceEvent
import sv.com.tarjetaone.analytics.dynatrace.DynatraceManager
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.SUCCESS_RESPONSE_CODE
import sv.com.tarjetaone.common.utils.AppConstants.SUCCESS_STATUS_CODE
import sv.com.tarjetaone.common.utils.CardColor
import sv.com.tarjetaone.common.utils.helpers.CryptoHelper
import sv.com.tarjetaone.common.utils.helpers.CryptoHelper.encryptKeyBASE64
import sv.com.tarjetaone.common.utils.helpers.CryptoHelper.encryptKeyWhitHeaderAndFooterBASE64
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.CommonDialogWithIconParams
import sv.com.tarjetaone.core.utils.MenuAlertType
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.alert.AlertManager
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.core.utils.extensions.removeLineBreak
import sv.com.tarjetaone.data.api.models.ArrayColor
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.alerts.AlertTypeUI
import sv.com.tarjetaone.domain.entities.alerts.AlertUI
import sv.com.tarjetaone.domain.entities.request.NoAlertRequestUI
import sv.com.tarjetaone.domain.entities.request.SaveBiometricPublicKeyRequestUI
import sv.com.tarjetaone.domain.entities.response.AdditionalCardsUI
import sv.com.tarjetaone.domain.entities.response.CreditCardStatus
import sv.com.tarjetaone.domain.entities.response.DataHomeCardUI
import sv.com.tarjetaone.domain.entities.response.LoginUserProfileResponseUI
import sv.com.tarjetaone.domain.entities.response.MainCardUI
import sv.com.tarjetaone.domain.entities.response.ManagementUI
import sv.com.tarjetaone.domain.entities.response.ReplacementStatus
import sv.com.tarjetaone.domain.usecases.alert.CancelAlertUseCase
import sv.com.tarjetaone.domain.usecases.auth.SaveBiometricPublicKeyUseCase
import sv.com.tarjetaone.domain.usecases.home.GetHomeCreditCardUseCase
import sv.com.tarjetaone.domain.usecases.lifemiles.AvailablePointsUseCase
import sv.com.tarjetaone.domain.usecases.managments.details.GetLogManagementsDetailUseCase
import sv.com.tarjetaone.domain.utils.onApiError
import sv.com.tarjetaone.domain.utils.onNetworkError
import sv.com.tarjetaone.domain.utils.onSuccess
import sv.com.tarjetaone.presentation.compose.uicomponent.skeleton.SkeletonState
import sv.com.tarjetaone.presentation.helpers.BenefitType
import sv.com.tarjetaone.presentation.helpers.DeleteAccountDetailType
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.helpers.getAlertButtonText
import sv.com.tarjetaone.presentation.helpers.getAlertColorButton
import sv.com.tarjetaone.presentation.helpers.getMenuAlertIcon
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.locationreceive.LocationReceiveCardAction
import javax.inject.Inject

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val getHomeCreditCardUseCase: GetHomeCreditCardUseCase,
    private val availablePointsUseCase: AvailablePointsUseCase,
    private val cancelAlertUseCase: CancelAlertUseCase,
    private val getLogManagementsDetailUseCase: GetLogManagementsDetailUseCase,
    private val saveBiometricPublicKey: SaveBiometricPublicKeyUseCase,
    private val sharedPrefsRepo: SecureSharedPreferencesRepository,
    private val alertManager: AlertManager,
    private val dynatraceManager: DynatraceManager
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState = _uiState.asStateFlow()

    private fun getAlerts() {
        // Gets alerts from memory using manager
        val alert = alertManager.getNextAlert(type = AlertTypeUI.ALERT) ?: return
        when (alert.code) {
            MenuAlertType.PROTECTION.type -> {
                showAlert(
                    alert = alert,
                    onButtonClick = {
                        sendEvent(
                            UiEvent.Navigate(
                                HomeFragmentDirections
                                    .actionNavigateToProgramProtectionFragment()
                            )
                        )
                    },
                    showSecondaryAction = true,
                    secondaryActionText = R.string.dont_protect_card,
                )
            }

            MenuAlertType.THIRD_DELIVERY_ATTEMPT.type -> {
                showAlert(
                    alert = alert,
                    onButtonClick = {
                        sendEvent(
                            UiEvent.Navigate(
                                HomeFragmentDirections.actionNavigationHomeToMenuMyCardsLocation(
                                    cardAction = LocationReceiveCardAction.ThirdAttempt(
                                        cardId = alert.cCardId.orZero()
                                    )
                                )
                            )
                        )
                    }
                )
            }

            MenuAlertType.THIRD_FAILED_DELIVERY_ATTEMPT.type -> {
                showAlert(
                    alert = alert,
                    onButtonClick = {
                        // Navigate to request card additional
                        sendEvent(
                            UiEvent.Navigate(
                                HomeFragmentDirections.actionNavigationHomeToRequestAdditionalCard()
                            )
                        )
                    },
                )
            }

            MenuAlertType.ACTIVATION.type -> activateCard(alert.cCardId.orZero(), alert)
            else -> getAlerts() // If alert is not handled, it will be skipped and removed from queue
        }
    }

    private fun activateCard(cardId: Int, alert: AlertUI? = null) {
        showAlert(
            alert = alert,
            onButtonClick = {
                sendEvent(
                    UiEvent.Navigate(
                        HomeFragmentDirections.actionNavigationHomeToCardActivation(cardId)
                    )
                )
            }
        )
    }

    /**
     * Show pop-up on screen
     * @param alert: AlertUI object to show on screen if is null show alert for activate card
     * @param onButtonClick: Callback to execute when the button is clicked
     * @param onCloseButtonClick: Callback to execute when the close button is clicked
     * @param onDismiss: Callback to execute when the alert is dismissed
     * @param showSecondaryAction: Boolean to show or hide secondary action
     * @param secondaryActionText: Int to set the text of the secondary action
     * @param onSecondaryActionClick: Callback to execute when the secondary action is clicked
     **/
    private fun showAlert(
        alert: AlertUI? = null,
        onButtonClick: () -> Unit = {},
        onCloseButtonClick: () -> Unit = {},
        onDismiss: () -> Unit = {},
        showSecondaryAction: Boolean = false,
        secondaryActionText: Int? = null,
        onSecondaryActionClick: () -> Unit = {}
    ) {
        val (body, closeConfig) = alert?.body to alert?.closeConfig
        sendEvent(
            UiEvent.ShowCommonDialog(
                CommonDialogWithIconParams(
                    icon = body?.icon?.let { getMenuAlertIcon(it) } ?: R.drawable.ic_visa2,
                    title = body?.title?.let { UiText.DynamicString(it) } ?: UiText.StringResource(
                        R.string.we_are_ready
                    ),
                    message = body?.description?.let { UiText.DynamicString(it) }
                        ?: UiText.StringResource(R.string.description_status_card),
                    buttonColor = getAlertColorButton(alert?.code),
                    buttonText = UiText.StringResource(getAlertButtonText(alert?.code)),
                    isDismissible = closeConfig?.allowGesture ?: true,
                    showCloseButton = closeConfig?.showButton ?: false,
                    secondaryActionVisible = showSecondaryAction,
                    secondaryActionText = secondaryActionText?.let { UiText.StringResource(it) }
                        ?: UiText.DynamicString(EMPTY_STRING),
                    onButtonClick = {
                        sendEvent(UiEvent.Loading(false))
                        onButtonClick()
                        getAlerts()
                    },
                    onCloseButtonClick = {
                        if (closeConfig?.canRemove == true) {
                            cancelAlert(alert?.code, alert?.cCardId)
                        }
                        onCloseButtonClick()
                        getAlerts()
                    },
                    onDismiss = {
                        if (closeConfig?.canRemove == true) {
                            cancelAlert(alert?.code, alert?.cCardId)
                        }
                        onDismiss()
                        getAlerts()
                    },
                    onSecondaryActionClick = {
                        if (closeConfig?.canRemove == true) {
                            cancelAlert(alert?.code, alert?.cCardId)
                        }
                        onSecondaryActionClick()
                        getAlerts()
                    }
                )
            ))
    }

    /**
     * Call endpoint 'NoAlert' to cancel alert received from menu alert
     * in order to avoid get the same alert multiple times
     * @param modeName: Mode name received from 'MenuAlert'
     * @param cCardId: Credit card id also could be another value received from 'MenuAlert'
     * @param onCancelAlert: Callback to execute when the alert is canceled
     * */
    private fun cancelAlert(modeName: String?, cCardId: Int?, onCancelAlert: () -> Unit = {}) {

        val request = NoAlertRequestUI(
            mode = modeName.orEmpty(),
            queryString = cCardId.toString()
        )

        viewModelScope.launch {
            cancelAlertUseCase(request).executeUseCase {
                sendEvent(UiEvent.Loading(false))
                onCancelAlert()
            }
        }

    }

    private fun navigateToDeleteAccount() {
        sendEvent(
            UiEvent.Navigate(
                HomeFragmentDirections
                    .actionHomeFragmentToDeleteAccountHomeFragment()
            )
        )
    }

    private fun navigateToCancellationDetails() {
        viewModelScope.launch {
            sendEvent(UiEvent.Loading(true))
            getLogManagementsDetailUseCase(
                manRequests = uiState.value.mainCard?.manRequestId.orZero()
            ).onSuccess { managementDataResponse ->
                sendEvent(UiEvent.Loading(false))
                if (managementDataResponse.statusResponse?.responseStatus?.code != SUCCESS_RESPONSE_CODE) {
                    showUpsErrorMessage()
                    return@launch
                }
                managementDataResponse.data?.manRequests?.apply {
                    val managementData = ManagementUI(
                        manRequestId = manRequestId,
                        clientName = clientName,
                        manNumberApp = manNumberApp,
                        mrStatusTextColor = mrStatusTextColor,
                        mrStatusNameApp = mrStatusNameApp,
                        manTypeNameApp = manTypeNameApp,
                        closeDate = closeDate,
                        applicationDate = applicationDate,
                        processDate = processDate,
                        cardNumMasked = cardNumMasked,
                        typeCardText = typeCardText,
                        description = description,
                        showDescription = showDescription,
                        availableDay = availableDay
                    )
                    sendEvent(
                        UiEvent.Navigate(
                            HomeFragmentDirections
                                .actionHomeFragmentToDeleteAccountDetailFragment(
                                    deleteAccountDetail = managementData,
                                    deleteAccountDetailType = DeleteAccountDetailType.VERIFYING_PROCESS_STATUS
                                )
                        )
                    )
                }
            }.onApiError { _, _, _, _ ->
                showUpsErrorMessage()
            }.onNetworkError { _ ->
                showUpsErrorMessage()
            }
        }
    }

    private fun updateInitialData(profileData: LoginUserProfileResponseUI?) {
        _uiState.update {
            it.copy(
                name = "${profileData?.firstName.orEmpty()} ${profileData?.firstSurname.orEmpty()}",
                lastLogin = profileData?.lastLogin.orEmpty()
            )
        }
    }

    private fun onStart(isStringBiometricAvailable: Boolean) {
        dynatraceManager.sendInAppEvent(DynatraceEvent.Home.ViewHome)
        viewModelScope.launch {
            updateInitialData(sharedPrefsRepo.getUserProfile())
            getHomeData()
            _uiState.update { it.copy(isStrongBiometricAvailable = isStringBiometricAvailable) }
        }
    }

    private fun getHomeData() {
        _uiState.update { it.copy(cardsState = SkeletonState.Loading) }
        viewModelScope.launch {
            getHomeCreditCardUseCase(
                sharedPrefsRepo.getCustomerId().orZero()
            ).executeUseCase(
                onApiErrorAction = { _, _, _, _ ->
                    onHomeDataFetchError()
                },
                onNetworkErrorAction = {
                    onHomeDataFetchError()
                }
            ) { response ->
                val status = response.statusResponse?.responseStatus
                val mainCard = response.data?.mainCard
                val mainCardStatus = CreditCardStatus.fromName(mainCard?.ccStatus.orEmpty())
                when (status?.code) {
                    HomeStatus.Success.code -> {
                        sharedPrefsRepo.apply {
                            putCustomerId(response.data?.customerId)
                            mainCard?.let {
                                this.mainCard = it
                                accountBenefitType = it.logoCode
                                lastStatementDate = it.lastStatementDate.orEmpty()
                            }
                        }
                        _uiState.update {
                            it.copy(
                                cards = buildCardsList(response.data),
                                benefitType = mainCard?.logoCode,
                                mainCard = mainCard,
                                mainCardStatus = mainCardStatus,
                                homeStatus = HomeStatus.fromCode(status.code),
                                cardsState = SkeletonState.Success,
                            )
                        }

                        // If main card is closed, do not proceed to load more data
                        if (mainCardStatus == CreditCardStatus.Close) return@executeUseCase

                        // Decide if charge off bottom sheet should be displayed
                        if (mainCard?.hasPaymentPenalty == true && !sharedPrefsRepo.hasShownChargeOffWarning) {
                            _uiState.update { it.copy(showChargeOffBottomSheet = true) }
                        } else {
                            completeHomeDataLoad()
                        }
                    }

                    HomeStatus.Close.code,
                    HomeStatus.ThirdFailedDeliveryAttempt.code -> {
                        _uiState.update {
                            it.copy(
                                homeStatus = HomeStatus.fromCode(status.code),
                                homeErrorMessage = status.message,
                                cardsState = SkeletonState.Success
                            )
                        }
                    }

                    // Other codes that represent invalid states or errors
                    else -> {
                        _uiState.update {
                            it.copy(
                                homeStatus = HomeStatus.fromCode(status?.code),
                                homeErrorMessage = EMPTY_STRING,
                                cardsState = SkeletonState.Failure,
                            )
                        }
                    }
                }
            }
        }
    }

    private fun completeHomeDataLoad() {
        getAvailablePoints()
        // Decide if biometric prompt should be displayed
        val shouldShowBiometric = sharedPrefsRepo.showBiometricLoginPrompt && uiState.value.isStrongBiometricAvailable
        if (shouldShowBiometric) {
            _uiState.update {
                it.copy(
                    showBiometricDialog = true,
                    biometricPromptType = BiometricSheetType.ACTIVATE
                )
            }
            return
        }

        // When biometric bottom sheet is not displayed load alerts if there are
        getAlerts()
    }

    private fun onHomeDataFetchError() {
        _uiState.update {
            it.copy(
                homeStatus = HomeStatus.Unknown,
                homeErrorMessage = EMPTY_STRING,
                cardsState = SkeletonState.Failure,
            )
        }
    }

    private fun MainCardUI.getColorData(): CardColor = runCatching {
        val colorJson = "{\"Colors\":[${cardColor}]}"
        val colorCardCatalog = Gson().fromJson(colorJson, ArrayColor::class.java)
        CardColor(
            colors = colorCardCatalog.colors.map { it.color },
            iconColor = visaColor.orEmpty(),
            textColor = textColor.orEmpty(),
            id = EMPTY_STRING
        )
    }.getOrElse { CardColor() }

    private fun AdditionalCardsUI.getColorData(): CardColor = runCatching {
        val colorJson = "{\"Colors\":[${cardColor}]}"
        val colorCardCatalog = Gson().fromJson(colorJson, ArrayColor::class.java)
        CardColor(
            colors = colorCardCatalog.colors.map { it.color },
            iconColor = visaColor.orEmpty(),
            textColor = textColor.orEmpty(),
            id = EMPTY_STRING
        )
    }.getOrElse { CardColor() }

    private fun buildCardsList(dataHome: DataHomeCardUI?): List<MainCardUI> {
        if (dataHome == null) return emptyList()
        return buildList {
            dataHome.mainCard?.let { add(it.copy(colors = it.getColorData())) }
            addAll(
                dataHome.additionalCards.map {
                    MainCardUI(
                        nameOnCard = it.nameOnCard,
                        creditCardId = it.creditCardId,
                        ccStatus = it.ccStatus,
                        balanceAvailable = it.cardLimit,
                        currentBalance = it.cardLimit,
                        currentLimit = it.cardLimit,
                        cardColor = it.cardColor,
                        mainCard = false,
                        cardNumMasked = it.cardNumMasked,
                        colors = it.getColorData(),
                        replacement = it.replacement,
                        replacementStatus = it.replacementStatus
                    )
                }
            )
        }
    }

    private fun getAvailablePoints() {
        _uiState.update { it.copy(availablePointsState = SkeletonState.Loading) }
        viewModelScope.launch {
            availablePointsUseCase(
                sharedPrefsRepo.getCustomerId()
            ).executeUseCase(
                onApiErrorAction = { _, _, _, _ ->
                    _uiState.update { it.copy(availablePointsState = SkeletonState.Failure) }
                },
                onNetworkErrorAction = {
                    _uiState.update { it.copy(availablePointsState = SkeletonState.Failure) }
                },
            ) { response ->
                _uiState.update {
                    it.copy(
                        availablePoints = response,
                        availablePointsState = SkeletonState.Success
                    )
                }
            }
        }
    }

    private fun onCardDetailsClick() {
        dynatraceManager.sendInAppEvent(DynatraceEvent.Home.CardDetails)
    }

    private fun onMyMovementsClick() {
        dynatraceManager.sendInAppEvent(DynatraceEvent.Home.CardMovements)
        sendEvent(
            UiEvent.Navigate(HomeFragmentDirections.actionNavigationHomeToMyMovementsFragment())
        )
    }

    private fun onMyStatementClick() {
        dynatraceManager.sendInAppEvent(DynatraceEvent.Home.AccountStatus)
        sendEvent(
            UiEvent.Navigate(HomeFragmentDirections.actionNavigationHomeToAccountStatusFragment())
        )
    }

    private fun onMyPointsClick() {
        val isCashback = uiState.value.benefitType == BenefitType.CASHBACK_BENEFIT.type

        if (isCashback) {
            dynatraceManager.sendInAppEvent(DynatraceEvent.Home.GoCashback)
        }

        sendEvent(
            UiEvent.Navigate(
                when {
                    isCashback && (sharedPrefsRepo.hasSeenCashbackInfo || sharedPrefsRepo.mainCard?.hasPaymentPenalty == true) -> {
                        dynatraceManager.sendInAppEvent(DynatraceEvent.Home.GoCashback)
                        HomeFragmentDirections.actionNavigationHomeToMyCashbackFragment(uiState.value.availablePoints)
                    }

                    isCashback -> {
                        dynatraceManager.sendInAppEvent(DynatraceEvent.Home.GoCashback)
                        HomeFragmentDirections.actionNavigationHomeToCashbackInformationFragment(
                            uiState.value.availablePoints
                        )
                    }

                    else ->
                        HomeFragmentDirections.actionNavigationHomeToMyMegaPointsFragment()
                }
            )
        )
    }

    private fun onRequestCardClick(card: MainCardUI) {
        val nextNavigation = when (ReplacementStatus.fromName(card.replacementStatus.orEmpty())) {
            ReplacementStatus.InProgress -> UiEvent.Navigate(
                HomeFragmentDirections.actionNavigationHomeToCardTracking(
                    cardId = card.creditCardId.orZero()
                )
            )

            ReplacementStatus.Delivered -> UiEvent.Navigate(
                HomeFragmentDirections.actionNavigationHomeToCardActivation(
                    cardId = card.replacement.orZero()
                )
            )

            // If the card is not in progress or delivered (null or other value),
            // it means that user has blocked the card recently so the next step is request a replacement
            else -> UiEvent.Navigate(
                HomeFragmentDirections.actionNavigationHomeToMenuMyCardsLocation(
                    cardAction = LocationReceiveCardAction.CardReplacement(
                        cardId = card.creditCardId.orZero()
                    )
                )
            )
        }
        sendEvent(nextNavigation)
    }

    private fun onBiometryAuthenticationFailed(type: BiometricSheetType) {
        // If the actual bottom sheet displayed is "activate" and failed app should display
        // bottom sheet with error in order to retry biometry prompt until user click
        // "No activate" option. Other options shouldn't do nothing
        when (type) {
            BiometricSheetType.ACTIVATE ->
                _uiState.update { it.copy(biometricPromptType = BiometricSheetType.FAILED) }

            else -> Unit
        }
    }

    private fun onBiometryAuthenticationSuccess() {
        val dynatraceEvent = when (_uiState.value.biometricPromptType) {
            BiometricSheetType.ACTIVATE -> DynatraceEvent.BiometricSheet.Activate
            BiometricSheetType.FAILED -> DynatraceEvent.BiometricSheet.TryAgain
            else -> null
        }
        dynatraceEvent?.let { dynatraceManager.sendInAppEvent(it) }

        val keypair = CryptoHelper.generateKeyPair()
        val publicKeyBase64 =
            keypair.public.encoded.encryptKeyWhitHeaderAndFooterBASE64().removeLineBreak()
        val privateKeyBase64 = keypair.private.encoded.encryptKeyBASE64().removeLineBreak()

        viewModelScope.launch {
            _uiState.update { state -> state.copy(isLoadingBiometric = true) }
            saveBiometricPublicKey(
                request = SaveBiometricPublicKeyRequestUI(
                    biometricPublicKey = publicKeyBase64,
                    deviceId = sharedPrefsRepo.getUniqueDeviceID()
                )
            ).executeUseCase(
                onSuccessAction = { response ->
                    if (response.status == SUCCESS_STATUS_CODE) {
                        _uiState.update { state -> state.copy(isLoadingBiometric = false) }
                        sharedPrefsRepo.apply {
                            savePublicKeyBiometric(publicKeyBase64)
                            savePrivateKeyBiometric(privateKeyBase64)
                            putFingerPrintEnable(true)

                            _uiState.update { state ->
                                state.copy(biometricPromptType = BiometricSheetType.SUCCESS)
                            }
                        }
                    } else {
                        showFailedBiometryPrompt()
                    }
                },
                onApiErrorAction = { _, _, _, _ ->
                    showFailedBiometryPrompt()
                },
                onServerErrorAction = {
                    showFailedBiometryPrompt()
                },
                onAuthenticationErrorAction = {
                    showFailedBiometryPrompt()
                },
                onNetworkErrorAction = {
                    showFailedBiometryPrompt()
                }
            )
        }
    }

    private fun showFailedBiometryPrompt() {
        _uiState.update { state -> state.copy(isLoadingBiometric = false) }
        _uiState.update { state -> state.copy(biometricPromptType = BiometricSheetType.FAILED) }
    }

    private fun onDismissBiometricLoginSheet() {
        val dynatraceEvent = when (uiState.value.biometricPromptType) {
            // send the continue event if the sheet is dismissed after the activation was successful
            BiometricSheetType.SUCCESS -> DynatraceEvent.BiometricSheet.ModalBiometryContinue
            // for any other case the user dissmissed the sheet or chose `Do not activate`
            BiometricSheetType.ACTIVATE,
            BiometricSheetType.FAILED -> DynatraceEvent.BiometricSheet.NoActivate
        }
        dynatraceManager.sendInAppEvent(dynatraceEvent)

        _uiState.update { it.copy(showBiometricDialog = false) }

        sharedPrefsRepo.apply {
            showBiometricLoginPrompt = false
            if (uiState.value.biometricPromptType != BiometricSheetType.SUCCESS) {
                putFingerPrintEnable(false)
            }
        }

        // When biometric bottom sheet is closed load alerts if there are
        getAlerts()
    }

    private fun onDismissChargeOffBottomSheet() {
        _uiState.update { it.copy(showChargeOffBottomSheet = false) }
        sharedPrefsRepo.hasShownChargeOffWarning = true
        completeHomeDataLoad()
    }

    fun onEvent(event: HomeUiEvent) {
        when (event) {
            is HomeUiEvent.OnStart -> onStart(event.isStrongBiometricAvailable)
            is HomeUiEvent.OnRetryHome -> getHomeData()
            is HomeUiEvent.OnRetryAvailablePoints -> getAvailablePoints()
            is HomeUiEvent.OnRequestCardClick -> onRequestCardClick(event.card)
            HomeUiEvent.OnCardDetailsClick -> onCardDetailsClick()
            HomeUiEvent.OnMyMovementsClick -> onMyMovementsClick()
            HomeUiEvent.OnMyStatementClick -> onMyStatementClick()
            HomeUiEvent.OnMyPointsClick -> onMyPointsClick()
            HomeUiEvent.OnCancelDetailsClick -> navigateToCancellationDetails()
            HomeUiEvent.OnDeleteAccountClick -> navigateToDeleteAccount()
            is HomeUiEvent.OnBiometryAuthenticationFailed -> onBiometryAuthenticationFailed(event.type)
            is HomeUiEvent.OnBiometryAuthenticationSuccess -> onBiometryAuthenticationSuccess()
            HomeUiEvent.OnDismissBiometricLoginSheet -> onDismissBiometricLoginSheet()
            HomeUiEvent.OnDismissChargeOffBottomSheet -> onDismissChargeOffBottomSheet()
        }
    }
}
