package sv.com.tarjetaone.presentation.view.lobby.menu.home

/**
 * Represents the status of the home screen.
 *
 * @property code The unique code representing the status.
 */
sealed class HomeStatus(val code: Int) {
    data object Success : HomeStatus(0)
    data object ThirdFailedDeliveryAttempt : HomeStatus(20)
    data object Close : HomeStatus(21)
    data object Unknown : HomeStatus(-1)

    companion object {
        fun fromCode(code: Int?): HomeStatus {
            return when (code) {
                Success.code -> Success
                ThirdFailedDeliveryAttempt.code -> ThirdFailedDeliveryAttempt
                Close.code -> Close
                else -> Unknown
            }
        }
    }
}
