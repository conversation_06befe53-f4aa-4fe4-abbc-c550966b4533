package sv.com.tarjetaone.presentation.view.lobby.menu.home

import androidx.compose.runtime.Stable
import sv.com.tarjetaone.domain.entities.response.CreditCardStatus
import sv.com.tarjetaone.domain.entities.response.DataAvailablePointsUI
import sv.com.tarjetaone.domain.entities.response.MainCardUI
import sv.com.tarjetaone.presentation.compose.uicomponent.skeleton.SkeletonState

@Stable
data class HomeUiState(
    val name: String = "",
    val lastLogin: String = "",
    val cards: List<MainCardUI> = listOf(MainCardUI()),
    val homeStatus: HomeStatus? = null,
    val homeErrorMessage: String? = null,
    val mainCard: MainCardUI? = MainCardUI(),
    val mainCardStatus: CreditCardStatus? = null,
    val availablePoints: DataAvailablePointsUI? = null,
    val benefitType: String? = null,
    val isStrongBiometricAvailable: Boolean = false,
    val showBiometricDialog: Boolean = false,
    val isLoadingBiometric: Boolean = false,
    val biometricPromptType: BiometricSheetType = BiometricSheetType.ACTIVATE,
    val cardsState: SkeletonState = SkeletonState.Loading,
    val availablePointsState: SkeletonState = SkeletonState.Loading,
    val showChargeOffBottomSheet: Boolean = false,
)

enum class BiometricSheetType {
    ACTIVATE,
    FAILED,
    SUCCESS
}
