package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.pay_with_points.identity.capture

import android.graphics.Bitmap
import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.presentation.view.common.selfiecapture.SelfieCaptureBaseViewModel
import javax.inject.Inject

@HiltViewModel
class PayWithPointsSelfieCaptureViewModel @Inject constructor(
    facephiResultHandler: FacephiResultHandler,
    savedStateHandle: SavedStateHandle
) : SelfieCaptureBaseViewModel(facephiResultHandler) {

    private val args = PayWithPointsSelfieCaptureFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onSuccessfulSelfieCapture(selfie: Bitmap) {
        sendEvent(
            UiEvent.Navigate(
                PayWithPointsSelfieCaptureFragmentDirections
                    .actionPayWithPointsPrepareForPictureFragmentToPayWithPointsSelfieConfirmationFragment(
                        request = args.request
                    )
            )
        )
    }
}
