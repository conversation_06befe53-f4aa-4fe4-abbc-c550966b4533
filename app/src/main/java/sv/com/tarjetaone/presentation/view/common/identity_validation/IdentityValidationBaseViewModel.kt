package sv.com.tarjetaone.presentation.view.common.identity_validation

import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ERROR_STATUS_CODE
import sv.com.tarjetaone.common.utils.AppConstants.SUCCESS_RESPONSE_CODE
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.CommonDialogWithIconParams
import sv.com.tarjetaone.core.utils.FacephiConstants
import sv.com.tarjetaone.core.utils.IdentityValidationResult
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.request.FaceAuthenticationRequestUI
import sv.com.tarjetaone.domain.usecases.auth.FaceAuthenticationUseCase
import sv.com.tarjetaone.domain.utils.onApiError
import sv.com.tarjetaone.domain.utils.onAuthenticationError
import sv.com.tarjetaone.domain.utils.onNetworkError
import sv.com.tarjetaone.domain.utils.onServerError
import sv.com.tarjetaone.domain.utils.onSuccess
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.utils.handleFacephiError

abstract class IdentityValidationBaseViewModel(
    private val faceAuthenticationUseCase: FaceAuthenticationUseCase,
    protected val sharedPrefs: SecureSharedPreferencesRepository
) : BaseViewModel() {
    init {
        validateIdentity()
    }

    /**
     * override onBlockedAction() to change the action to do on successful response with errors
     * and result code != IdentityValidationResult.BLOCK
     * */
    open val onBlockedAction: () -> Unit = {
        sendEvent(UiEvent.NavigateBack)
    }

    private fun validateIdentity() {
        viewModelScope.launch {
            val request = FaceAuthenticationRequestUI(
                ccApplicationId = sharedPrefs.getCCApplicationId(),
                method = FacephiConstants.FACIAL_AUTH_METHOD_2,
                imageBuffer = sharedPrefs.getSelfieString()
            )

            faceAuthenticationUseCase(request).onSuccess {
                val responseCode = it.statusResponse?.responseStatus?.code
                if (responseCode == SUCCESS_RESPONSE_CODE) {
                    operation(it.data.biometryId)
                } else {
                    sendEvent(
                        SideEffect.ShowOneDialog(
                            handleFacephiError(
                                resultCode = it.data.resultCode,
                                onPrimaryActionClick = {
                                    onFacephiError(it.data.resultCode)
                                }
                            )
                        )
                    )
                }
            }.onApiError { code, _, _, _ ->
                showAuthenticationErrorMessage(code.orEmpty())
            }.onNetworkError {
                showAuthenticationErrorMessage("$ERROR_STATUS_CODE")
            }.onServerError {
                if (sharedPrefs.userIsLoggedIn) {
                    showUpsErrorMessage(isDismissible = false) {
                        sendEvent(UiEvent.NavigateBack)
                    }
                } else {
                    onDefaultServerError()
                }
            }.onAuthenticationError {
                sharedPrefs.putSessionExpiredStatus(true)
                onAuthorizationExpired(R.id.welcomeFragment)
            }
        }
    }

    private fun showAuthenticationErrorMessage(resultCode: String) {
        sendEvent(
            UiEvent.ShowCommonDialog(
                CommonDialogWithIconParams(
                    icon = R.drawable.ic_error_yellow,
                    title = UiText.StringResource(R.string.not_identified),
                    message = UiText.StringResource(R.string.facephi_technical_issues),
                    buttonText = UiText.StringResource(R.string.take_picture),
                    buttonColor = R.color.color_alert_default,
                    onButtonClick = { onFailedIdentityValidation(resultCode) },
                    isDismissible = false,
                )
            )
        )
    }

    /**
     * Override [onBackAction] to define the behavior that should occur when a response is successful
     * but contains errors, and the result code is different from [IdentityValidationResult.BLOCK]
     * in [onFacephiError].
     */
    protected open fun onBackAction() = sendEvent(UiEvent.NavigateBack)

    /**
     * Action to perform when identity validation succeeds.
     * Ex. Call operation endpoint, navigate to other screen, etc.
     * @param biometryId Id of the successful identity validation. Required in some operations
     */
    protected abstract fun operation(biometryId: Int)

    /**
     * Action to perform when identity validation fails.
     * Ex. Navigate back, navigate to other screen, etc.
     * @param resultCode The result code from the faceAuthentication endpoint
     */
    protected abstract fun onFailedIdentityValidation(resultCode: String?)

    /**
     * Action to perform error handling on errors in successful biometry response
     * @param resultCode The result code from the faceAuthentication endpoint
     * */
    private fun onFacephiError(resultCode: String) {
        if (resultCode == IdentityValidationResult.BLOCK.toString()) {
            onBlockedAction()
        } else {
            onBackAction()
        }
    }
}
