package sv.com.tarjetaone.presentation.helpers

import androidx.compose.ui.text.AnnotatedString
import sv.com.tarjetaone.common.utils.AppConstants.BLANK_SPACE
import sv.com.tarjetaone.common.utils.AppConstants.DASH_STRING
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.FIVE_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.SCREENSHOT_PREFIX
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_CHAR
import sv.com.tarjetaone.common.utils.helpers.CryptoHelper
import sv.com.tarjetaone.presentation.view.utils.getTimestamp
import java.util.Locale

private const val COMMA = ","
private const val DOT = "."
private const val WHITE_SPACE = " "
private const val MONEY_REGEX = "(?=.*?\\d)^\\\$?(([1-9]\\d{0,2}(,\\d{3})*)|\\d+)?(\\.\\d{1,2})?\$"
private const val UPPERCASE_REGEX = "(.*[A-Z].*)"
private const val LOWERCASE_REGEX = "(.*[a-z].*)"
private const val NUMERIC_REGEX = ".*[0-9].*"
private const val SPECIAL_CHAR_REGEX = "[^a-zA-Z0-9]"
private const val ALPHA_NUMERIC_REGEX = "[A-Za-záéíóúÁÉÍÓÚ0-9\\s,]+"

/**
 * Validate that the phone number has 8 digits and that it is not a repeated number like 1111-1111, 2222-2222, 3333-3333, etc.
 * 2, 6 or 7 are allowed if is repeated like 2222-2222, 6666-6666, 7777-7777
 **/
private const val PHONE_NUMBER_REGEX = "^(?:([267])\\1{7}|(?!([0-9])\\2{7})[267][0-9]{7})\$"

fun String.capitalizeStringByComa(): String = this.lowercase()
    .split(COMMA)
    .joinToString(COMMA) { it.capitalizeAllWords() }
    .split(DOT)
    .joinToString(DOT) { it.capitalizeAllWords() }

fun String.capitalizeAllWords(): String = this.lowercase()
    .split(WHITE_SPACE)
    .joinToString(WHITE_SPACE) { it.capitalize() }

fun String.capitalize(): String = this.replaceFirstChar {
    if (it.isLowerCase()) it.titlecase(Locale.getDefault()) else it.toString()
}

fun String.splitAndGetFirst(
    delimiter: String,
    ignoreCase: Boolean = false
): String? = this.split(delimiter, ignoreCase = ignoreCase).firstOrNull()

fun String.splitAndGet(
    delimiter: String,
    index: Int,
    ignoreCase: Boolean = false
): String? = this.split(delimiter, ignoreCase = ignoreCase).getOrNull(index)

fun String.hasInvalidCharsAndMinLength(minChars: Int = FIVE_VALUE): Boolean {
    return if (Regex("^[\\p{L}\\p{N}\\s\\p{P}\\p{S}]{$minChars,1000}$").matches(this.trim())) {
        this.trim().split(WHITE_SPACE).map { string ->
            Regex("[^\\p{L}\\p{N}]").matches(string) && string.none { it.isLetterOrDigit() }
                    || string.containsOnlySpecialCharacters()
                    || string.any { it.isSurrogate() }
        }.contains(true)
    } else {
        true
    }
}

fun String.containsOnlySpecialCharacters(): Boolean {
    return Regex("^[\\p{P}\\p{S}]+\$").matches(this)
}

fun String.hasInvalidCharsOrEmojis(): Boolean {
    // Minimum length of 1
    if (isEmpty()) {
        return true
    }

    // Reject strings with only spaces or emojis
    if (isBlank() || any { it.isSurrogate() }) {
        return true
    }

    val specialCharacterRegex = Regex("[^\\p{L}\\p{N}]")

    // Special characters are allowed only in the presence of numbers or letters
    return specialCharacterRegex.matches(this) && none { it.isLetterOrDigit() }
}

fun String.containsInvalidCharacters(): Boolean {
    return if (this.matches("^[,.ñ a-zA-Z0-9]*$".toRegex())) {
        this.split(WHITE_SPACE).map {
            it.validateCharacters(",.", 1)
        }.contains(false)
    } else {
        true
    }
}

fun String.validateCharacters(listOfCharacters: String, maxSize: Int): Boolean {
    return if (length == 1 && contains("[$listOfCharacters]".toRegex())) {
        false
    } else {
        count { it.toString().contains("[$listOfCharacters]".toRegex()) } <= maxSize
    }
}

fun String.hasInvalidPhoneNumber(): Boolean {
    val phoneNumber = this.removeDash().removeBlankSpace()
    return PHONE_NUMBER_REGEX.toRegex().matches(phoneNumber).not()
}

fun String.removeDash(): String {
    return this.replace(DASH_STRING, EMPTY_STRING)
}

fun String.addDashPhoneNumber(): String {
    return if (this.contains(BLANK_SPACE).not())
        listOf(substring(0, 4), substring(4, 8)).joinToString(DASH_STRING)
    else this.replace(BLANK_SPACE, DASH_STRING)
}

/**
 * Replace any repeated spaces with a single space
 */
fun String.removeExtraSpaces(): String {
    return trim().replace("\\s+".toRegex(), WHITE_SPACE)
}

internal fun String.addDashDui(): String = if (length == 9) {
    listOf(substring(0, 8), substring(8)).joinToString(DASH_STRING)
} else {
    this
}

fun String.removeComma(): String {
    return this.replace(COMMA, EMPTY_STRING)
}

fun String.removeBlankSpace(): String {
    return this.replace(BLANK_SPACE, EMPTY_STRING)
}

fun AnnotatedString.Builder.appendLink(tag: String, linkText: String, linkUrl: String) {
    pushStringAnnotation(tag = tag, annotation = linkUrl)
    append(linkText)
    pop()
}

fun AnnotatedString.onLinkClick(offset: Int, onClick: (String) -> Unit) {
    getStringAnnotations(start = offset, end = offset).firstOrNull()?.let {
        onClick(it.item)
    }
}

fun AnnotatedString.Builder.appendClickableText(tag: String, text: String) {
    pushStringAnnotation(tag = tag, annotation = tag)
    append(text)
    pop()
}

fun AnnotatedString.onClickableTextClick(offset: Int, onClick: (String) -> Unit) {
    getStringAnnotations(start = offset, end = offset).firstOrNull()?.let {
        onClick(it.tag)
    }
}

fun String.hasOnlyLettersAndDigits(): Boolean = matches(ALPHA_NUMERIC_REGEX.toRegex())

fun String.filterDigits(): String = filter { it.isDigit() }

fun String.filterLettersAndDigits(): String = filter { it.isLetterOrDigit() }

fun String.dropLeadingZeros(): String = dropWhile { it == ZERO_CHAR }

fun String.validateUpperCase(): Boolean = this.contains(Regex(UPPERCASE_REGEX))

fun String.validateLowerCase(): Boolean = this.contains(Regex(LOWERCASE_REGEX))

fun String.validateHasOneNumber(): Boolean = this.matches(Regex(NUMERIC_REGEX))

fun String.validateMinimumLength(minLength: Int): Boolean = this.length >= minLength

fun String.validateSpecialChars(): Boolean =
    Regex(SPECIAL_CHAR_REGEX).findAll(this).map { it.value }.toList().size >= ONE_VALUE

fun String.encryptPassword(): String = CryptoHelper.encryptByPublicKey(this.toByteArray())
    ?.let { CryptoHelper.encryptBASE64(it) }.orEmpty()

fun fileCapturedName(): String {
    return "$SCREENSHOT_PREFIX $DASH_STRING ${getTimestamp()}"
}
