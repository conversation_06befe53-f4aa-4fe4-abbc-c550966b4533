package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.phonenotifications

import sv.com.tarjetaone.domain.entities.response.OtpMethod

sealed interface AdditionalCardPhoneNotificationsUiEvent {
    data class OnSelectOtpMethod(val method: OtpMethod): AdditionalCardPhoneNotificationsUiEvent
    data object OnDenyNotificationsButtonClick: AdditionalCardPhoneNotificationsUiEvent
    data object OnBackClick: AdditionalCardPhoneNotificationsUiEvent
    data object OnTwilioClick: AdditionalCardPhoneNotificationsUiEvent
}
