package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform

import sv.com.tarjetaone.domain.entities.response.automaticpayments.paymentaffiliation.affiliationform.NotificationChannelUI
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.invoiceselection.infoproducts.PXPaymentMethodUI
import sv.com.tarjetaone.domain.utils.automaticpayments.servicespayment.invoiceselection.ProductTypeCode

sealed class AffiliationFormUiEvent {
    data object OnStart : AffiliationFormUiEvent()
    data object OnBackClick : AffiliationFormUiEvent()
    data object OnTwilioClick : AffiliationFormUiEvent()
    data class OnSelectNotificationChannel(
        val notificationChannel: NotificationChannelUI
    ) : AffiliationFormUiEvent()
    data class OnSelectProductType(val productType: ProductTypeCode) : AffiliationFormUiEvent()
    data class OnSelectedProduct(val product: PXPaymentMethodUI) : AffiliationFormUiEvent()
    data class OnChargeAmountChange(val chargeAmount: String) : AffiliationFormUiEvent()
    data class OnMaxChargeOptionChange(val value: Boolean) : AffiliationFormUiEvent()
    data class OnTermsCheckChange(val value: Boolean) : AffiliationFormUiEvent()
    data object NavigateToAffiliationTerms : AffiliationFormUiEvent()
    data object OnContinueClick : AffiliationFormUiEvent()
}
