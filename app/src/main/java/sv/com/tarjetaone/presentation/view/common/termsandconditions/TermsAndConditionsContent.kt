package sv.com.tarjetaone.presentation.view.common.termsandconditions

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.datasource.LoremIpsum
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.HyperLinkTextButton
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.TextButtonVariant
import sv.com.tarjetaone.presentation.compose.util.TextFromHtml

@Composable
fun TermsAndConditionsContent(
    modifier: Modifier = Modifier,
    uiState: TermsAndConditionsUiState,
    onEvent: (TermsAndConditionsUiEvent) -> Unit
) {
    Column(
        modifier = modifier
    ) {
        TextFromHtml(
            modifier = Modifier
                .fillMaxWidth()
                .animateContentSize(),
            text = if (uiState.showFullTerms) {
                uiState.fullTermsAndConditions
            } else {
                uiState.partialTermsAndConditions
            },
            style = MaterialTheme.typography.bodySmall
        )
        Spacer16()
        if (!uiState.showFullTerms) {
            HyperLinkTextButton(
                modifier = Modifier.align(Alignment.CenterHorizontally),
                text = stringResource(id = R.string.read_full_terms),
                textButtonVariant = TextButtonVariant.SMALL_VARIANT
            ) {
                onEvent(TermsAndConditionsUiEvent.OnShowFullTerms)
            }
        }
    }
}

@Preview
@Composable
fun PreviewTermsAndConditionsContent() {
    OneAppTheme {
        TermsAndConditionsContent(
            modifier = Modifier
                .background(MaterialTheme.colorScheme.surface)
                .padding(horizontal = MaterialTheme.customDimens.dimen16),
            uiState = TermsAndConditionsUiState(
                partialTermsAndConditions = LoremIpsum(
                    words = 150
                ).values.toList().first().toString(),
                fullTermsAndConditions = LoremIpsum(
                    words = 185
                ).values.toList().first().toString(),
                showFullTerms = false
            ),
            onEvent = {}
        )
    }
}
