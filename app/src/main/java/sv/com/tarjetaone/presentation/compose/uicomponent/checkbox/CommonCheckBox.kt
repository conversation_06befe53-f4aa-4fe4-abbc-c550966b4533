package sv.com.tarjetaone.presentation.compose.uicomponent.checkbox

import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxColors
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import sv.com.tarjetaone.presentation.compose.theme.LocalCustomColors

@Composable
fun CommonCheckBox(
    modifier: Modifier = Modifier,
    colors: CheckboxColors = CheckboxDefaults.colors(
        checkedColor = LocalCustomColors.current.successContainer,
        uncheckedColor = LocalCustomColors.current.gray500,
        checkmarkColor = MaterialTheme.colorScheme.onPrimary
    ),
    isChecked: Boolean,
    onCheck: (Boolean) -> Unit
) {
    Checkbox(
        checked = isChecked,
        colors = colors,
        onCheckedChange = onCheck,
        modifier = modifier
    )
}