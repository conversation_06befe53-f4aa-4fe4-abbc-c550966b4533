package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.employment_info.identity.preview

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.presentation.view.common.selfiepreview.SelfiePreviewViewModel
import javax.inject.Inject

@HiltViewModel
class EmploymentInfoSelfiePreviewViewModel @Inject constructor(
    imageUtils: ImageUtils,
    facephiResultHandler: FacephiResultHandler,
    savedStateHandle: SavedStateHandle
) : SelfiePreviewViewModel(imageUtils, facephiResultHandler) {

    private val args = EmploymentInfoSelfiePreviewFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onContinueClick() {
        sendEvent(
            UiEvent.Navigate(
                EmploymentInfoSelfiePreviewFragmentDirections
                    .actionSelfieConfirmationToEmploymentInfoIdentityValidationFragment(
                        jobId = args.jobId,
                        contactId = args.contactId,
                        contactTypeCode = args.contactTypeCode
                    )
            )
        )
    }
}
