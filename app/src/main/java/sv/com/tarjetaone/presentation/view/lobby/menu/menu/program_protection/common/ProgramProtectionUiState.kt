package sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.common

import androidx.compose.runtime.Stable
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.response.FraudProtectionDataUI
import sv.com.tarjetaone.presentation.helpers.UiText

@Stable
data class ProgramProtectionUiState(
    val screenTitle: UiText? = null,
    val fraudProtectionDetails: FraudProtectionDataUI? = null,
    val isActionButtonEnabled: Boolean = true,
    val canDeactivate: Boolean = false,
    val protectionProgramState: ProgramProtectionState? = null,
    val hasPaymentPenalty: Boolean = false,
) {
    fun getAmountAsString(): String =
        fraudProtectionDetails?.fProtectionTypes?.firstOrNull()?.logoNameApp.orEmpty()

    fun getAmountAsDouble(): Double =
        fraudProtectionDetails?.fProtectionTypes?.firstOrNull()?.amount.orZero()

    fun getProtectionTypeId(): String? =
        fraudProtectionDetails?.fProtectionTypes?.firstOrNull()?.fProtectionTypeId
}
