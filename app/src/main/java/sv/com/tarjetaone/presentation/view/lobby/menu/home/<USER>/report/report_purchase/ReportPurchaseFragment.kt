package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.report.report_purchase

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import dagger.hilt.android.AndroidEntryPoint
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.base.BaseFragment
import sv.com.tarjetaone.databinding.FragmentComposeBinding
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.view.utils.requireLobbyActivity

@AndroidEntryPoint
class ReportPurchaseFragment : BaseFragment<FragmentComposeBinding>(
    R.layout.fragment_compose
) {
    override val viewModel: ReportPurchaseViewModel by viewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requireLobbyActivity().hideNavigationBottom()
        binding.composeView.setContentScreen {
            val uiState by viewModel.uiState.collectAsStateWithLifecycle()
            SideEffectHandler(viewModel.sideEffects) {
                ReportPurchaseScreen(
                    uiState = uiState,
                    onEvent = viewModel::onEvent
                )
            }
        }
    }
}
