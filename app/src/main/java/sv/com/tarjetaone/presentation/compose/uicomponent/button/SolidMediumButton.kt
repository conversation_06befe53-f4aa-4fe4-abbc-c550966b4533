package sv.com.tarjetaone.presentation.compose.uicomponent.button

import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.poppinsFamily
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.getButtonColors

/**
 * Button to be used as primary/secondary/tertiary medium button.
 *
 * Consider modifying it according to the desired needs.
 * E.g. To handle disabled button, or to receive an icon, etc.
 */
@Deprecated(
    message = "Use OneButton component with size param instead",
    replaceWith = ReplaceWith(
        "OneButton()",
        "sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton"
    )
)
@Composable
fun SolidMediumButton(
    modifier: Modifier = Modifier,
    text: String,
    enabled: Boolean = true,
    buttonVariant: ButtonVariant = ButtonVariant.PRIMARY_VARIANT,
    textStyle: TextStyle = TextStyle(
        fontWeight = FontWeight.SemiBold,
        fontFamily = poppinsFamily,
        fontSize = 14.sp
    ),
    onClick: () -> Unit,
) {
    val buttonContent: @Composable RowScope.() -> Unit = {
        Text(
            text = text,
            modifier = Modifier
                .align(Alignment.CenterVertically)
                .padding(vertical = 4.dp),
            style = textStyle,
            textAlign = TextAlign.Center
        )
    }

    if (buttonVariant == ButtonVariant.TERTIARY_VARIANT) {
        OutlinedButton(
            modifier = modifier.sizeIn(minHeight = 38.dp),
            enabled = enabled,
            colors = buttonVariant.getButtonColors(),
            shape = MaterialTheme.shapes.extraLarge,
            onClick = onClick,
            content = buttonContent
        )
    } else {
        Button(
            modifier = modifier.sizeIn(minHeight = 38.dp),
            enabled = enabled,
            colors = buttonVariant.getButtonColors(),
            shape = MaterialTheme.shapes.extraLarge,
            onClick = onClick,
            content = buttonContent
        )
    }
}

@Preview
@Composable
private fun SolidMediumButtonPreview() {
    OneAppTheme {
        SolidMediumButton(
            modifier = Modifier.fillMaxWidth(),
            text = "Botón primario",
            enabled = true,
            onClick = {}
        )
    }
}

@Preview
@Composable
private fun SolidMediumButtonSecondaryVariantPreview() {
    OneAppTheme {
        SolidMediumButton(
            modifier = Modifier.fillMaxWidth(),
            text = "Botón primario",
            enabled = true,
            buttonVariant = ButtonVariant.SECONDARY_VARIANT,
            onClick = {}
        )
    }
}

@Preview
@Composable
private fun SolidMediumButtonTertiaryVariantPreview() {
    OneAppTheme {
        SolidMediumButton(
            modifier = Modifier.fillMaxWidth(),
            text = "Botón Terciario",
            enabled = true,
            buttonVariant = ButtonVariant.TERTIARY_VARIANT,
            onClick = {}
        )
    }
}
