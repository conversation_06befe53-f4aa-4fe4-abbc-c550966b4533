package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.requestdocument

import android.Manifest
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.rememberPermissionState
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.facephi.CaptureType
import sv.com.tarjetaone.core.utils.facephi.rememberFacephiWidgetLauncher
import sv.com.tarjetaone.core.utils.shouldShowRationale
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.card.HorizontalIconWithText
import sv.com.tarjetaone.presentation.compose.uicomponent.card.SimpleCardComponent
import sv.com.tarjetaone.presentation.compose.uicomponent.common.OneAppSnackBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.compose.uicomponent.radiobutton.GradientRadioButton
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.helpers.UiText

@Composable
fun RequestDocumentAdditionalCardScreen(
    viewModel: RequestDocumentAdditionalCardViewModel,
    prevDestId: Int?
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    SideEffectHandler(viewModel.sideEffects) {
        RequestDocumentAdditionalCardScreen(
            uiState = uiState,
            onEvent = viewModel::onEvent,
            prevDestId = prevDestId
        )
    }
}

@Composable
fun RequestDocumentAdditionalCardScreen(
    uiState: RequestDocumentAdditionalCardPreviewUiState,
    onEvent: (event: RequestDocumentAdditionalCardUiEvent) -> Unit = {},
    prevDestId: Int? = null
) {
    ScreenWithTopAppBar(
        title = stringResource(R.string.additional_card_already),
        onLeftButtonClick = {
            onEvent(RequestDocumentAdditionalCardUiEvent.OnBack(prevDestId))
        },
        onRightButtonClick = {
            onEvent(RequestDocumentAdditionalCardUiEvent.OnTwilio)
        }
    ) {
        RequestDocumentAdditionalCardScreenContent(uiState, onEvent)
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun RequestDocumentAdditionalCardScreenContent(
    uiState: RequestDocumentAdditionalCardPreviewUiState,
    onEvent: (event: RequestDocumentAdditionalCardUiEvent) -> Unit = {}
) {
    val radioOptions by remember {
        mutableStateOf(AnswerOption.entries)
    }
    val cameraPermission = Manifest.permission.CAMERA
    val context = LocalContext.current
    val facephiLauncher = rememberFacephiWidgetLauncher(CaptureType.Document()) {
        onEvent(RequestDocumentAdditionalCardUiEvent.OnDocumentCaptured(it))
    }
    val cameraPermissionState = rememberPermissionState(cameraPermission) { isGranted ->
        if (isGranted) {
            facephiLauncher.initWidget(context)
        } else {
            onEvent(
                RequestDocumentAdditionalCardUiEvent.OnCameraPermissionDenied(
                    shouldShowRationale = context.shouldShowRationale(cameraPermission)
                )
            )
        }
    }

    Column(
        Modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.customDimens.dimen32)
    ) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = stringResource(id = R.string.now_need_general_label),
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.Medium
                ),
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
            )
            Spacer16()
            SimpleCardComponent(
                elevation = CardDefaults.cardElevation(
                    defaultElevation = MaterialTheme.customDimens.dimen2
                )
            ) {
                HorizontalIconWithText(
                    textColor = MaterialTheme.colorScheme.onSurface,
                    leadingIcon = R.drawable.ic_card_aditional_card,
                    text = UiText.StringResource(R.string.additional_card_verify_identity),
                    style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Medium),
                    leadingIconAlignment = Alignment.Top
                )
            }
            Column(
                modifier = Modifier.padding(vertical = MaterialTheme.customDimens.dimen10)
            ) {
                Text(
                    text = stringResource(id = R.string.additional_card_valid_dui),
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                )
                LazyRow(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(
                        MaterialTheme.customDimens.dimen40
                    )
                ) {
                    items(radioOptions) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(
                                MaterialTheme.customDimens.dimen14
                            )
                        ) {
                            GradientRadioButton(
                                selected = it == uiState.selectedOption,
                                onClick = {
                                    onEvent(
                                        RequestDocumentAdditionalCardUiEvent.OnOptionSelected(it)
                                    )
                                }
                            )
                            Text(stringResource(it.label))
                        }
                    }
                }
            }
        }
        Spacer1f()
        AnimatedVisibility(uiState.isDialogVisible) {
            OneAppSnackBar(
                message = stringResource(id = R.string.additional_card_valid_person),
                containerColor = MaterialTheme.customColors.cardLightBackground,
                icon = R.drawable.ic_check_circle_white,
                onCancelClick = { onEvent(RequestDocumentAdditionalCardUiEvent.OnDismiss) }
            )
        }
        Spacer32()
        OneButton(
            modifier = Modifier.fillMaxWidth(),
            enabled = uiState.isContinueButtonEnabled,
            text = stringResource(id = R.string.continue_button_label),
            onClick = {
                onEvent(
                    RequestDocumentAdditionalCardUiEvent
                        .OnConfirm(cameraPermissionState::launchPermissionRequest)
                )
            }
        )
        Spacer16()
    }
}

@Preview
@Composable
fun RequestDocumentAdditionalCardScreenPreview() {
    OneAppTheme {
        RequestDocumentAdditionalCardScreen(
            RequestDocumentAdditionalCardPreviewUiState(
                selectedOption = AnswerOption.YES,
                isContinueButtonEnabled = true,
                isDialogVisible = true
            )
        )
    }
}