package sv.com.tarjetaone.presentation.view.common.reference_overview

import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent

abstract class ReferenceOverviewBaseViewModel : BaseViewModel() {

    /**
     * Action to be executed when the user clicks on the continue button
     * */
    abstract fun onContinueClick()

    /**
     * Action to be executed when the user clicks on the modify button
     * */
    abstract fun onModifyClick()

    fun onEvent(event: ReferenceOverviewEvent) {
        when (event) {
            is ReferenceOverviewEvent.OnContinueClick -> onContinueClick()
            is ReferenceOverviewEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            is ReferenceOverviewEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            is ReferenceOverviewEvent.OnModifyReference -> {
                onModifyClick()
                sendEvent(UiEvent.NavigateBack)
            }
        }
    }
}
