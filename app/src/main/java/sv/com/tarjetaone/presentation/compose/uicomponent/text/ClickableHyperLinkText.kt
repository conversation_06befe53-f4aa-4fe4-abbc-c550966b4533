package sv.com.tarjetaone.presentation.compose.uicomponent.text

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.helpers.onClickableTextClick

@Composable
fun ClickableHyperLinkText(
    modifier: Modifier = Modifier,
    hyperLinkTextProps: ClickableHyperLinkTextProps,
) {
    ClickableText(
        modifier = modifier,
        text = hyperLinkTextProps.annotatedString,
        style = hyperLinkTextProps.textStyle,
        onClick = { offset ->
            hyperLinkTextProps.annotatedString.onClickableTextClick(offset) { annotationTag ->
                hyperLinkTextProps.onClick(annotationTag)
            }
        },
    )
}

@Preview
@Composable
fun ClickableHyperLinkTextPreview() {
    OneAppTheme {
        val annotatedString = buildAnnotatedString {
            append("This is a ")
            pushStringAnnotation(tag = "FIRST_CLICKABLE_TEXT", annotation = "FIRST_CLICKABLE_TEXT")
            withStyle(
                style = SpanStyle(
                    color = MaterialTheme.colorScheme.primary,
                    textDecoration = TextDecoration.Underline
                )
            ) {
                append("clickable link")
            }
            pop()
            append(" and this is a ")
            pushStringAnnotation(tag = "SECOND_CLICKABLE_TEXT", annotation = "SECOND_CLICKABLE_TEXT")
            withStyle(
                style = SpanStyle(
                    color = MaterialTheme.colorScheme.primary,
                    textDecoration = TextDecoration.Underline
                )
            ) {
                append("second clickable link")
            }
            pop()
            append(" in the text.")
        }

        val hyperLinkTextProps = ClickableHyperLinkTextProps(
            annotatedString = annotatedString,
            onClick = { },
            textStyle = MaterialTheme.typography.labelMedium.copy(
                color = MaterialTheme.colorScheme.onBackground,
            ),
        )

        ClickableHyperLinkText(
            modifier = Modifier.fillMaxWidth(),
            hyperLinkTextProps = hyperLinkTextProps
        )
    }
}

data class ClickableHyperLinkTextProps(
    val annotatedString: AnnotatedString,
    val textStyle: TextStyle,
    val onClick: (String) -> Unit,
)
