package sv.com.tarjetaone.presentation.view.common.management.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens

@Composable
fun ManagementFeedbackMessage(
    feedbackIcon: Int?,
    feedbackMessageTitle: Int?,
    feedbackMessageDescription: Int?
) {
    Row(
        modifier = Modifier.fillMaxWidth()
            .padding(horizontal = MaterialTheme.customDimens.dimen32),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(
            MaterialTheme.customDimens.dimen12
        )
    ) {
        feedbackIcon?.let {
            Image(
                imageVector = ImageVector.vectorResource(id = feedbackIcon),
                contentDescription = null
            )
        }

        Column(modifier = Modifier.weight(ONE_FLOAT_VALUE)) {
            feedbackMessageTitle?.let { title ->
                Text(
                    text = stringResource(title),
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = MaterialTheme.customColors.gray600,
                        fontWeight = FontWeight.Bold
                    )
                )
            }

            feedbackMessageDescription?.let { description ->
                Text(
                    text = stringResource(description),
                    style = MaterialTheme.typography.labelMedium.copy(
                        color = MaterialTheme.customColors.gray600
                    )
                )
            }
        }
    }
}