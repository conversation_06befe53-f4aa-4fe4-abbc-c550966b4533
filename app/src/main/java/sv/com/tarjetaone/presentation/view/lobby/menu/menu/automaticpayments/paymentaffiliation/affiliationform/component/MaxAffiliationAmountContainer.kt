package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.paymentaffiliation.affiliationform.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusManager
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.DOLLAR_STRING
import sv.com.tarjetaone.common.utils.AppConstants.ONE_HUNDRED_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.compose.uicomponent.radiobutton.GradientRadioButton
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.compose.util.input.CurrencyVisualTransformation
import sv.com.tarjetaone.presentation.helpers.dropLeadingZeros
import sv.com.tarjetaone.presentation.helpers.filterDigits

@Composable
fun MaxAffiliationAmountContainer(
    modifier: Modifier = Modifier,
    focusManager: FocusManager,
    maxAmountEnabled: Boolean?,
    maxChargeAmount: String,
    onMaxAmountChange: (String) -> Unit = { },
    onMaxAmountOptionChange: (Boolean) -> Unit = { },
) {
    Column(modifier = modifier) {
        Text(
            text = stringResource(id = R.string.activate_max_charge_amount_label),
            style = MaterialTheme.typography.bodySmall.copy(
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.customColors.textBodyLight
            ),
        )
        Spacer8()
        Row(
            modifier = Modifier.fillMaxWidth()
        ) {
            GradientRadioButton(
                text = stringResource(id = R.string.no),
                textStyle = MaterialTheme.typography.bodySmall.copy(
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.customColors.gray600
                ),
                selected = maxAmountEnabled == false,
                onClick = { onMaxAmountOptionChange(false) }
            )
            Spacer32()
            GradientRadioButton(
                text = stringResource(id = R.string.yes),
                textStyle = MaterialTheme.typography.bodySmall.copy(
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.customColors.gray600
                ),
                selected = maxAmountEnabled == true,
                onClick = { onMaxAmountOptionChange(true) }
            )
        }
        AnimatedVisibility(
            visible = maxAmountEnabled == true
        ) {
            Column {
                Spacer16()
                SimpleElevatedTextField(
                    modifier = Modifier.fillMaxWidth(),
                    value = maxChargeAmount,
                    placeholder = stringResource(id = R.string.hint_zero_dollars),
                    onValueChange = {
                        val value = it.filterDigits().dropLeadingZeros()
                        onMaxAmountChange(value)
                    },
                    label = stringResource(id = R.string.max_charge_amount_label),
                    labelStyle = MaterialTheme.typography.bodySmall.copy(
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.customColors.textBodyLight
                    ),
                    visualTransformation = CurrencyVisualTransformation(
                        displayIfEmpty = false,
                        currencySymbol = DOLLAR_STRING
                    ),
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Decimal,
                        imeAction = ImeAction.Done
                    ),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            focusManager.clearFocus()
                        }
                    ),
                    hint = stringResource(id = R.string.max_charge_amount_hint),
                    hintStyle = MaterialTheme.typography.labelMedium.copy(
                        color = MaterialTheme.customColors.gray600,
                    )
                )
            }
        }
    }
}

@Preview(
    showBackground = true,
)
@Composable
private fun MaxAffiliationAmountContainerPreview() {
    OneAppTheme {
        MaxAffiliationAmountContainer(
            maxAmountEnabled = true,
            focusManager = LocalFocusManager.current,
            maxChargeAmount = ONE_HUNDRED_VALUE.times(ONE_HUNDRED_VALUE).toString(),
            onMaxAmountChange = { },
            onMaxAmountOptionChange = { }
        )
    }
}
