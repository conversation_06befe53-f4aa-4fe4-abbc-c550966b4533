package sv.com.tarjetaone.presentation.view.lobby.menu.home.account_status

import android.net.Uri
import android.util.Base64
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import sv.com.tarjetaone.core.utils.BaseViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.analytics.dynatrace.DynatraceEvent
import sv.com.tarjetaone.analytics.dynatrace.DynatraceManager
import sv.com.tarjetaone.common.utils.AppConstants.SUCCESS_RESPONSE_CODE
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.core.utils.files.ContentType
import sv.com.tarjetaone.core.utils.files.FileUtils
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.response.CreditCardStatus
import sv.com.tarjetaone.domain.entities.response.DataAccountStateUI
import sv.com.tarjetaone.domain.usecases.card.DownloadCreditCardStatementUseCase
import sv.com.tarjetaone.domain.usecases.card.GetCreditCardStatementUseCase

@HiltViewModel
class AccountStatusViewModel @Inject constructor(
    private val sharedPref: SecureSharedPreferencesRepository,
    private val getCreditCardStatementUseCase: GetCreditCardStatementUseCase,
    private val downloadCreditCardStatementUseCase: DownloadCreditCardStatementUseCase,
    private val fileUtils: FileUtils,
    private val dynatraceManager: DynatraceManager
) : BaseViewModel() {
    private val _uiState: MutableStateFlow<AccountStatusUiState> =
        MutableStateFlow(AccountStatusUiState())
    val uiState: StateFlow<AccountStatusUiState> = _uiState.asStateFlow()

    private fun onStart() {
        dynatraceManager.sendInAppEvent(DynatraceEvent.MyAccountStatus.ViewAccountStatus)

        setAccountNumber()
        //TODO REPLACE WITH isActive from ccStatus variable on v.1.19 merge back
        if (sharedPref.mainCard?.ccStatus == CreditCardStatus.Active.name) {
            // Only request account states if the main card is active
            requestCustomerAccountStates()
        }
    }

    private fun setAccountNumber() {
        _uiState.update { it.copy(accountNumber = sharedPref.mainCard?.cardNumMasked.orEmpty()) }
    }

    private fun onUpdateCutOffDate(data: DataAccountStateUI) {
        _uiState.update { it.copy(accountStateSelected = data) }
    }

    private fun requestCustomerAccountStates() {
        sendEvent(SideEffect.Loading(true))
        viewModelScope.launch {
            getCreditCardStatementUseCase(
                customerId = sharedPref.getCustomerId().orZero(),
                cardId = sharedPref.mainCard?.creditCardId.orZero()
            ).executeUseCase {
                sendEvent(SideEffect.Loading(false))

                //TODO REFACTOR IN A LATER TICKET TO USE ENCAPSULATED RESPONSE
                val responseStatus = it.statusResponse?.responseStatus
                if (responseStatus?.code != SUCCESS_RESPONSE_CODE) {
                    showUpsErrorMessage()
                    return@executeUseCase
                }

                _uiState.update { state ->
                    state.copy(
                        accountStates = it.dataAccountState.orEmpty().take(ACCOUNT_STATUS_PERIOD),
                        accountStateSelected = it.dataAccountState?.firstOrNull()
                    )
                }
            }
        }
    }

    private fun downloadCreditCardStatement() {
        dynatraceManager.sendInAppEvent(DynatraceEvent.MyAccountStatus.Download)

        sendEvent(SideEffect.Loading(true))
        viewModelScope.launch {
            downloadCreditCardStatementUseCase(
                customerId = sharedPref.getCustomerId().orZero(),
                cardId = sharedPref.mainCard?.creditCardId.orZero(),
                cutOffDate = uiState.value.accountStateSelected?.cutOffYearMonth.orEmpty()
            ).executeUseCase {
                sendEvent(SideEffect.Loading(false))

                //TODO REFACTOR IN A LATER TICKET TO USE ENCAPSULATED RESPONSE

                val responseStatus = it.statusResponse?.responseStatus
                if (responseStatus?.code != SUCCESS_RESPONSE_CODE || it.dataAccountState.isNullOrEmpty()) {
                    showUpsErrorMessage()
                    return@executeUseCase
                }

                val document = it.dataAccountState?.firstOrNull()?.document
                val documentName = it.dataAccountState?.firstOrNull()?.documentName
                //TODO REFACTOR IN A LATER TICKET  TO USE decryptBASE64 FROM CryptoHelper
                val byteArray = Base64.decode(document, Base64.DEFAULT)

                val storedUri = fileUtils.getUriIfExists(documentName.orEmpty(), false)
                if (storedUri != null) {
                    openCreditCardStatement(storedUri)
                } else {
                    storeTempPdfFile(documentName.orEmpty(), byteArray)?.let { savedUri ->
                        openCreditCardStatement(savedUri)
                    }
                }
            }
        }
    }

    //TODO REFACTOR A LATER TICKET TO USE saveTempPdfFileAsync
    private fun storeTempPdfFile(fileName: String, fileContent: ByteArray): Uri? =
        fileUtils.storeTempPdfFile(
            fileName = fileName,
            fileContent = fileContent,
            useFileScheme = false
        )

    private fun openCreditCardStatement(pdfUri: Uri) {
        sendEvent(SideEffect.ShareContent(pdfUri, ContentType.PDF))
    }

    fun onEvent(event: AccountStatusUiEvent) {
        when (event) {
            AccountStatusUiEvent.OnStart -> onStart()
            AccountStatusUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            AccountStatusUiEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
            is AccountStatusUiEvent.OnCutOffDateChange -> onUpdateCutOffDate(event.data)
            AccountStatusUiEvent.OnDownloadClick -> downloadCreditCardStatement()
        }
    }

    companion object {
        private const val ACCOUNT_STATUS_PERIOD = 6
    }
}
