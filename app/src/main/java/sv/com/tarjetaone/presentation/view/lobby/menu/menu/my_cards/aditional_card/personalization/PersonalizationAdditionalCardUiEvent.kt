package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.personalization

import com.facephi.fphiselphidwidgetcore.WidgetSelphIDResult
import sv.com.tarjetaone.common.utils.CardColor

sealed interface PersonalizationAdditionalCardUiEvent {
    data class OnSliderProgressChange(val sliderProgress: Int) : PersonalizationAdditionalCardUiEvent
    data class OnCardLimitChange(val cardLimit: String) : PersonalizationAdditionalCardUiEvent
    data object OnSliderProgressChangeFinished : PersonalizationAdditionalCardUiEvent
    data class OnCardColorChange(val cardColor: CardColor) : PersonalizationAdditionalCardUiEvent
    data object OnStart: PersonalizationAdditionalCardUiEvent
    data object OnNavigateBack: PersonalizationAdditionalCardUiEvent
    data object OnTwilioButtonClick: PersonalizationAdditionalCardUiEvent
    data object OnSavePersonalizationCard: PersonalizationAdditionalCardUiEvent
    data class OnDocumentCapture(val result: WidgetSelphIDResult): PersonalizationAdditionalCardUiEvent
    data class OnNameSelected(val index: Int): PersonalizationAdditionalCardUiEvent
    data object OnResetState: PersonalizationAdditionalCardUiEvent
}
