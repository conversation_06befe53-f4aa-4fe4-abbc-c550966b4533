package sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.SolidLargeButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer4

@Composable
fun ScreenScaffold(
    modifier: Modifier = Modifier,
    scrollState: ScrollState = rememberScrollState(),
    backgroundColor: Color = MaterialTheme.colorScheme.surface,
    topBar: @Composable () -> Unit,
    header: (@Composable () -> Unit)? = null,
    actions: (@Composable () -> Unit)? = null,
    content: @Composable () -> Unit
) {
    Column(
        modifier = modifier.background(backgroundColor)
    ) {
        topBar()
        Column(
            modifier = Modifier
                .weight(ONE_FLOAT_VALUE)
                .verticalScroll(scrollState),
        ) {
            header?.invoke()
            content()
        }
        actions?.invoke()
    }
}

@Preview
@Composable
private fun ScreenScaffoldPreview() {
    val icons = listOf(
        R.drawable.ic_direction,
        R.drawable.ic_bio_metric,
        R.drawable.ic_savings,
        R.drawable.ic_archive,
        R.drawable.ic_aditional_card_dialog,
    )
    OneAppTheme {
        ScreenScaffold(
            modifier = Modifier
                .fillMaxSize(),
            topBar = {
                SimpleTopAppBar(
                    onLeftButtonClick = {},
                    onRightButtonClick = {}
                )
            },
            header = {
                OnboardingHeader(
                    currentStep = 1,
                    icons = icons,
                    title = "this is a title",
                )
            },
            actions = {
                SolidLargeButton(
                    buttonVariant = ButtonVariant.TERTIARY_VARIANT,
                    text = "this is a button"
                ) {}
            }
        ) {
            Column(
                modifier = Modifier
                    .padding(
                        horizontal = MaterialTheme.customDimens.dimen24
                    )
            ) {
                SimpleElevatedTextField(
                    label = stringResource(id = R.string.first_name_without),
                    placeholder = stringResource(id = R.string.name_label),
                    value = "",
                    onValueChange = {
                    }
                )
                Spacer4()
                SimpleElevatedTextField(
                    label = stringResource(id = R.string.second_name_without),
                    placeholder = stringResource(id = R.string.name_label),
                    value = "",
                    onValueChange = {
                    }
                )
                Spacer4()
                SimpleElevatedTextField(
                    label = stringResource(id = R.string.first_last_name_without),
                    placeholder = stringResource(id = R.string.last_name_label),
                    value = "",
                    onValueChange = {

                    }
                )
                Spacer4()
                SimpleElevatedTextField(
                    label = stringResource(id = R.string.second_last_name_without),
                    placeholder = stringResource(id = R.string.last_name_label),
                    value = "",
                    onValueChange = {
                    }
                )
            }
        }
    }
}
