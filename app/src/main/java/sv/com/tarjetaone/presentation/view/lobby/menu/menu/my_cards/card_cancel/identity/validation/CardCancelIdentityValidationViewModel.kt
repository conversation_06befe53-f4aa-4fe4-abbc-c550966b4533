package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_cancel.identity.validation

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.request.CardCloseRequestUI
import sv.com.tarjetaone.domain.entities.response.DataCardReplacementResponseUI
import sv.com.tarjetaone.domain.usecases.auth.FaceAuthenticationUseCase
import sv.com.tarjetaone.domain.usecases.card.CancelCardUseCase
import sv.com.tarjetaone.presentation.view.common.identity_validation.IdentityValidationBaseViewModel
import javax.inject.Inject

@HiltViewModel
class CardCancelIdentityValidationViewModel @Inject constructor(
    private val sharedPreferencesRepository: SecureSharedPreferencesRepository,
    private val cancelCardUseCase: CancelCardUseCase,
    faceAuthenticationUseCase: FaceAuthenticationUseCase,
    savedStateHandle: SavedStateHandle
) : IdentityValidationBaseViewModel(faceAuthenticationUseCase, sharedPreferencesRepository) {

    private val args =
        CardCancelIdentityValidationFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override val onBlockedAction = {
        sendEvent(UiEvent.Navigate(CardCancelIdentityValidationFragmentDirections.actionHome()))
    }

    override fun operation(biometryId: Int) {
        val cardCancelRequest = CardCloseRequestUI(
            cCardId = args.card?.creditCardId.orZero(),
            customerId = sharedPreferencesRepository.getCustomerId().orZero(),
            closingReasonId = args.reason?.id?.toIntOrNull().orZero(),
            comments = args.comments,
            biometryId = biometryId
        )
        viewModelScope.launch {
            cancelCardUseCase(cardCancelRequest).executeUseCase(
                onApiErrorAction = { _, _, _, _ ->
                    showErrorMessage()
                },
                onNetworkErrorAction = {
                    showErrorMessage()
                },
                onSuccessAction = {
                    navigateToTrackingDetailCancelCard(it)
                }
            )
        }
    }

    private fun showErrorMessage() = showUpsErrorMessage(false) { sendEvent(UiEvent.NavigateBack) }

    private fun navigateToTrackingDetailCancelCard(data: DataCardReplacementResponseUI) {
        sendEvent(
            UiEvent.Navigate(
                CardCancelIdentityValidationFragmentDirections.navigateToNavigationTrackingDetailCancelCard(
                    card = args.card,
                    data = data
                )
            )
        )
    }

    override fun onFailedIdentityValidation(resultCode: String?) = sendEvent(UiEvent.NavigateBack)
}