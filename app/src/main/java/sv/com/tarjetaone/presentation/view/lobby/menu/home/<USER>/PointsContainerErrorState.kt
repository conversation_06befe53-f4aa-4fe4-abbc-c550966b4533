package sv.com.tarjetaone.presentation.view.lobby.menu.home.skeleton

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.util.Spacer12
import sv.com.tarjetaone.presentation.compose.util.Spacer4
import sv.com.tarjetaone.presentation.compose.util.Spacer8

@Composable
fun PointsContainerErrorState(
    modifier: Modifier = Modifier,
    onRetry: () -> Unit
) {
    Column(
        modifier = modifier
            .background(
                color = MaterialTheme.customColors.gray300,
                shape = MaterialTheme.shapes.large
            ),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.customDimens.dimen24,
                    vertical = MaterialTheme.customDimens.dimen24
                ),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(
                text = stringResource(id = R.string.unable_to_load_content),
                style = MaterialTheme.typography.bodySmall.copy(
                    color = MaterialTheme.customColors.gray700,
                ),
                textAlign = TextAlign.Center
            )
            Spacer12()
            Row(
                modifier = Modifier
                    .clip(MaterialTheme.shapes.large)
                    .clickable { onRetry() },
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Spacer8()
                Text(
                    text = stringResource(R.string.load_again_button_label),
                    style = MaterialTheme.typography.labelMedium.copy(
                        lineHeight = MaterialTheme.customDimensSp.sp14,
                        color = MaterialTheme.colorScheme.primary,
                        textDecoration = TextDecoration.Underline
                    )
                )
                Spacer4()
                Icon(
                    modifier = Modifier.size(MaterialTheme.customDimens.dimen12),
                    imageVector = ImageVector.vectorResource(R.drawable.ic_retry),
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer8()
            }
        }
    }
}

@Preview
@Composable
fun PointsContainerErrorStatePreview() {
    OneAppTheme {
        PointsContainerErrorState(
            onRetry = { }
        )
    }
}
