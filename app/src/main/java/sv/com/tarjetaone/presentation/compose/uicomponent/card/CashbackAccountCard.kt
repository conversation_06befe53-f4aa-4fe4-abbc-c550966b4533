package sv.com.tarjetaone.presentation.compose.uicomponent.card

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer32

@Composable
fun CashbackAccountCard(
    accountNumber: String,
    accountHolderName: String
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.customColors.cardRed,
            contentColor = MaterialTheme.colorScheme.onPrimary
        ),
        modifier = Modifier.width(MaterialTheme.customDimens.dimen230)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.customDimens.dimen20,
                    vertical = MaterialTheme.customDimens.dimen24
                )
        ) {
            Text(
                text = stringResource(id = R.string.title_card_cashback),
                style = MaterialTheme.typography.bodyLarge.copy(fontWeight = FontWeight.Bold)
            )
            Spacer32()
            Text(
                text = stringResource(id = R.string.account_number),
                style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Bold)
            )
            Text(
                text = accountNumber,
                style = MaterialTheme.typography.displaySmall.copy(fontWeight = FontWeight.Medium)
            )
            Spacer24()
            Text(
                text = stringResource(id = R.string.owner_card_label),
                style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Bold)
            )
            Text(
                text = accountHolderName,
                style = MaterialTheme.typography.displaySmall.copy(fontWeight = FontWeight.Medium)
            )
            Spacer24()
            Image(
                painter = painterResource(id = R.drawable.ic_white_full_logo),
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.End)
                    .size(
                        width = MaterialTheme.customDimens.dimen93,
                        height = MaterialTheme.customDimens.dimen20
                    )
            )
        }
    }
}

@Preview
@Composable
private fun CashbackAccountCardPreview() {
    OneAppTheme {
        CashbackAccountCard(
            accountNumber = "*********",
            accountHolderName = "Juan Manolo Perez Cruz"
        )
    }
}
