package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.address_location

import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.common.utils.AppConstants.GENERAL_ANIMATION_DURATION
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.address_location.home_address_list.UserAddress

@Composable
fun UserPersonalDataCardItem(
    modifier: Modifier = Modifier,
    userAddressData: UserAddress,
    icon: Int,
    showDeleteOption: Boolean = false,
    onEditClick: (id: Int) -> Unit = {},
    onDeleteClick: (id: Int) -> Unit = {},
) {
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(
            defaultElevation = MaterialTheme.customDimens.defaultCardElevation
        ),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.background
        )
    ) {
        Row(
            modifier = Modifier.padding(MaterialTheme.customDimens.dimen16),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(id = icon),
                contentDescription = null
            )
            Column(modifier = Modifier.padding(start = MaterialTheme.customDimens.dimen16)) {
                var isExpanded by remember { mutableStateOf(false) }
                var showSeeMore by remember { mutableStateOf(false) }
                val maxLines = if (isExpanded) Int.MAX_VALUE else AppConstants.THREE_VALUE
                val overflowStyle = if (isExpanded) TextOverflow.Visible else TextOverflow.Ellipsis

                Text(
                    text = userAddressData.addressName.asString(),
                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.SemiBold),
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = userAddressData.fullAddress.asString(),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface,
                    maxLines = maxLines,
                    overflow = overflowStyle,
                    onTextLayout = { layoutResult ->
                        if (!showSeeMore) {
                            showSeeMore = layoutResult.hasVisualOverflow
                        }
                    },
                    modifier = Modifier.animateContentSize(
                        animationSpec = tween(
                            GENERAL_ANIMATION_DURATION
                        )
                    )
                )
                SeeMoreButton(
                    modifier = Modifier.align(Alignment.End),
                    showSeeMore = showSeeMore,
                    isExpanded = isExpanded,
                    onClick = { isExpanded = !isExpanded }
                )
                UserAddressItemActions(
                    id = userAddressData.addressId,
                    showDeleteOption = showDeleteOption,
                    onEditClick = onEditClick,
                    onDeleteClick = onDeleteClick
                )
            }
        }
    }
}

@Composable
private fun SeeMoreButton(
    modifier: Modifier = Modifier,
    showSeeMore: Boolean,
    isExpanded: Boolean,
    onClick: () -> Unit
) {
    if (showSeeMore) {
        Text(
            text = stringResource(id = if (!isExpanded) R.string.show_more else R.string.show_less),
            style = MaterialTheme.typography.labelMedium.copy(
                lineHeight = MaterialTheme.customDimensSp.sp14
            ),
            color = MaterialTheme.colorScheme.primary,
            textDecoration = TextDecoration.Underline,
            modifier = modifier
                .padding(end = MaterialTheme.customDimens.dimen8)
                .clickable { onClick() }
        )
    }
}

@Composable
private fun UserAddressItemActions(
    modifier: Modifier = Modifier,
    id: Int,
    showDeleteOption: Boolean,
    onEditClick: (id: Int) -> Unit = {},
    onDeleteClick: (id: Int) -> Unit = {},
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen16)
    ) {
        if (showDeleteOption) {
            Text(
                text = stringResource(id = R.string.delete_label),
                style = MaterialTheme.typography.labelMedium.copy(lineHeight = MaterialTheme.customDimensSp.sp14),
                color = MaterialTheme.colorScheme.primary,
                textDecoration = TextDecoration.Underline,
                modifier = Modifier.clickable { onDeleteClick(id) }
            )
        }
        Text(
            text = stringResource(id = R.string.modify),
            style = MaterialTheme.typography.labelMedium.copy(lineHeight = MaterialTheme.customDimensSp.sp14),
            color = MaterialTheme.colorScheme.primary,
            textDecoration = TextDecoration.Underline,
            modifier = Modifier.clickable { onEditClick(id) }
        )
    }
}

@Preview
@Composable
fun UserAddressItemPreview() {
    OneAppTheme {
        Surface(
            color = MaterialTheme.colorScheme.inverseSurface,
            modifier = Modifier.fillMaxWidth()
        ) {
            UserPersonalDataCardItem(
                modifier = Modifier.padding(MaterialTheme.customDimens.dimen32),
                userAddressData = UserAddress(
                    addressId = 0,
                    addressName = UiText.DynamicString("Home"),
                    fullAddress = UiText.DynamicString("Calle 1, Casa 2"),
                ),
                icon = R.drawable.ic_house_door_fill,
                showDeleteOption = true
            )
        }
    }
}

@Preview
@Composable
fun UserAddressItemWithoutDeleteOptionPreview() {
    OneAppTheme {
        Surface(
            color = MaterialTheme.colorScheme.inverseSurface,
            modifier = Modifier.fillMaxWidth()
        ) {
            UserPersonalDataCardItem(
                modifier = Modifier.padding(MaterialTheme.customDimens.dimen32),
                userAddressData = UserAddress(
                    addressId = 0,
                    addressName = UiText.DynamicString("Work"),
                    fullAddress = UiText.DynamicString("Calle la reforma, Casa 4"),
                ),
                icon = R.drawable.ic_house_door_fill,
                showDeleteOption = false
            )
        }
    }
}
