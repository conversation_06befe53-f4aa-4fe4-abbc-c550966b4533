package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.scanner

import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import javax.inject.Inject

@HiltViewModel
class CodeScannerViewModel @Inject constructor() : BaseViewModel() {
    private fun onCancel() {
        sendEvent(UiEvent.NavigateBack)
    }

    private fun onCodeScan(npeCode: String) {
        baseSharedPrefs.collectorsNpeCode = npeCode
        sendEvent(UiEvent.NavigateBack)
    }

    fun onEvent(event: CodeScannerUiEvent) {
        when (event) {
            is CodeScannerUiEvent.OnCodeScan -> onCodeScan(event.code)
            CodeScannerUiEvent.OnCancel -> onCancel()
        }
    }
}