package sv.com.tarjetaone.presentation.compose.uicomponent.card

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens

@Composable
fun SimpleInformationCard(
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.surface,
    textStyle: TextStyle = MaterialTheme.typography.displayMedium.copy(
        color = MaterialTheme.colorScheme.onPrimary
    ),
    shape: Shape = RectangleShape,
    informationText: String
) {
    Box(
        modifier = Modifier
            .padding(top = MaterialTheme.customDimens.dimen16)
            .background(
                color = color,
                shape = shape
            )
            .then(modifier)
    ) {
        Text(
            modifier = Modifier
                .align(Alignment.Center)
                .padding(
                    start = MaterialTheme.customDimens.dimen40,
                    end = MaterialTheme.customDimens.dimen40,
                    bottom = MaterialTheme.customDimens.dimen32
                ),
            text = informationText,
            style = textStyle
        )
        OneAppByBancoAtlantida(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = MaterialTheme.customDimens.dimen32)
        )
    }
}

@Preview
@Composable
fun SimpleInformationCardPreview() {
    SimpleInformationCard(
        color = MaterialTheme.customColors.gray500,
        informationText = "This is a simple information card"
    )
}
