package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_activation.identity.capture

import android.graphics.Bitmap
import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.analytics.amplitude.AmplitudeEvents
import sv.com.tarjetaone.analytics.amplitude.AmplitudeManager
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.presentation.view.common.selfiecapture.SelfieCaptureBaseViewModel
import javax.inject.Inject

@HiltViewModel
class CardActivationSelfieCaptureViewModel @Inject constructor(
    private val amplitudeManager: AmplitudeManager,
    facephiResultHandler: FacephiResultHandler,
    savedStateHandle: SavedStateHandle
) : SelfieCaptureBaseViewModel(facephiResultHandler) {
    private val args = CardActivationSelfieCaptureFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onContinueClick(requestPermission: () -> Unit) {
        amplitudeManager.track(AmplitudeEvents.ON_TAKE_PICTURE_ACTIVATION_EVENT)
        super.onContinueClick(requestPermission)
    }

    override fun onSuccessfulSelfieCapture(selfie: Bitmap) {
        sendEvent(
            UiEvent.Navigate(
                CardActivationSelfieCaptureFragmentDirections
                    .actionPrepareForPictureCardActivationToSelfieConfirmationCardActivation(args.cardId)
            )
        )
    }
}
