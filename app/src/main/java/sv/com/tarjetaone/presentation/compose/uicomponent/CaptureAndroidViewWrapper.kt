package sv.com.tarjetaone.presentation.compose.uicomponent

import android.view.View
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.viewinterop.AndroidView

@Composable
fun CaptureAndroidViewWrapper(
    modifier: Modifier = Modifier,
    view: (View?) -> Unit,
    content: @Composable () -> Unit,
) {
    AndroidView(
        modifier = modifier.wrapContentHeight(),
        factory = { ctx ->
            ComposeView(ctx).apply {
                setContent {
                    content()
                }
                view(this)
            }
        }
    )
}
