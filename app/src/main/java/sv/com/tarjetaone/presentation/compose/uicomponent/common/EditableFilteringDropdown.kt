package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusManager
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.util.FieldDecorationType

/**
 * Simple dropdown with editable text. This dropdown handles filtering of items internally
 * @param modifier Modifier for the dropdown.
 * @param items List of items to be displayed in the dropdown.
 * @param itemLabel Function to map an item to a string to be displayed as option in the dropdown or
 * as display string for the selected [value].
 * @param value Currently selected item.
 * @param onValueChange Callback for when the selected item changes. This callback will also be
 * invoked with `null` when the user types to filter the items and [value] is not already `null`
 * @param filteringCriteria Predicate to filter items based on the query.
 */
@Suppress("kotlin:S107")
@Composable
fun <T> EditableFilteringDropdown(
    modifier: Modifier = Modifier,
    label: String? = null,
    labelIcon: Int? = null,
    labelIconColor: Color = MaterialTheme.customColors.disclaimer,
    labelIconClick: () -> Unit = {},
    placeholder: String? = null,
    items: List<T>,
    itemLabel: (T) -> String,
    value: T?,
    onValueChange: (T?) -> Unit,
    filteringCriteria: (T, String) -> Boolean,
    decorationType: FieldDecorationType = FieldDecorationType.ELEVATED,
    focusManager: FocusManager = LocalFocusManager.current
) {
    var query: String by remember { mutableStateOf(EMPTY_STRING) }
    val filteredItems = remember(query) {
        items.filter { filteringCriteria(it, query) }
    }
    EditableDropdown(
        modifier = modifier,
        label = label,
        labelIcon = labelIcon,
        labelIconColor = labelIconColor,
        labelIconClick = labelIconClick,
        placeholder = placeholder,
        items = filteredItems.takeIf { it.isNotEmpty() || query.isNotBlank() } ?: items,
        itemLabel = itemLabel,
        value = value,
        onValueChange = {
            onValueChange(it)
            query = EMPTY_STRING
        },
        query = query,
        onQueryChange = {
            query = it
            value?.let { onValueChange(null) } // only reset value if it's not null
        },
        decorationType = decorationType,
        focusManager = focusManager
    )
}

@Preview
@Composable
private fun EditableDropdownPreview() {
    OneAppTheme {
        EditableFilteringDropdown(
            items = listOf(EMPTY_STRING),
            onValueChange = {},
            itemLabel = { it },
            value = EMPTY_STRING,
            filteringCriteria = { _, _ -> true },
            modifier = Modifier.fillMaxWidth()
        )
    }
}
