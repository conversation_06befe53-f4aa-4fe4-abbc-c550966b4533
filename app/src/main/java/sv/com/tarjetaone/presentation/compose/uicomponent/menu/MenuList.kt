package sv.com.tarjetaone.presentation.compose.uicomponent.menu

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme

/**
 * Reusable component for a generic menu
 * @param modifier Modifier for the menu
 * @param contentPadding Padding to wrap the menu items with. You can use it to add a padding before
 * the first item or after the last one
 * @param items List of items to be displayed in the menu.
 * @param itemIcon Function to map an item to a drawable resource to be displayed as leading icon.
 * @param itemText Function to map an item to a string to be displayed as option.
 * @param itemTextColor Function to map an item to a color to give to the [itemText]. By default
 * takes the internal text's textStyle color
 * @param displayItemArrow Function to determine if the item should display the arrow at the end.
 * @param onItemClick Callback for when an item is clicked.
 * @param leadingContent Optional content to be displayed at the beginning of the menu list.
 * @param trailingContent Optional content to be displayed at the end of the menu list.
 */
@Suppress("kotlin:S107")
@Composable
fun <T> MenuList(
    modifier: Modifier = Modifier,
    contentPadding: PaddingValues = PaddingValues(0.dp),
    items: List<T>,
    itemIcon: (T) -> Int,
    itemText: @Composable (T) -> String,
    itemTextColor: @Composable (T) -> Color = { Color.Unspecified },
    displayItemArrow: (T) -> Boolean = { true },
    onItemClick: (T) -> Unit,
    leadingContent: (LazyListScope.() -> Unit)? = null,
    trailingContent: (LazyListScope.() -> Unit)? = null
) {
    LazyColumn(
        contentPadding = contentPadding,
        modifier = modifier
    ) {
        leadingContent?.invoke(this)
        items(items = items) {
            MenuItem(
                icon = itemIcon(it),
                text = itemText(it),
                textColor = itemTextColor(it),
                onClick = { onItemClick(it) },
                displayArrow = displayItemArrow(it),
                modifier = Modifier.fillMaxWidth()
            )
        }
        trailingContent?.invoke(this)
    }
}

@Preview
@Composable
private fun MenuListPreview() {
    OneAppTheme {
        Surface {
            MenuList(
                items = listOf("Opcion 1", "Opcion 2", "Opcion 3"),
                itemIcon = { R.drawable.ic_folder_menu },
                itemText = { it },
                onItemClick = {}
            )
        }
    }
}
