package sv.com.tarjetaone.presentation.view.lobby.menu.home.inactive_product

import androidx.annotation.StringRes
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.HyperLinkTextButton
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.card.SimpleInformationCard
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer48

@Composable
fun InactiveProductScreen(
    @StringRes
    informationText: Int,
    @StringRes
    primaryButtonText: Int? = null,
    @StringRes
    secondaryButtonText: Int,
    secondaryButtonVariant: ButtonVariant = ButtonVariant.HYPERLINK_VARIANT,
    showPrimaryButton: Boolean = true,
    onPrimaryButtonAction: () -> Unit = {},
    onSecondaryButtonAction: () -> Unit = {}
) {
    InactiveProductContent(
        informationText,
        primaryButtonText,
        secondaryButtonText,
        secondaryButtonVariant,
        showPrimaryButton,
        onPrimaryButtonAction,
        onSecondaryButtonAction
    )
}

@Composable
fun InactiveProductContent(
    informationText: Int,
    primaryButtonText: Int?,
    secondaryButtonText: Int,
    secondaryButtonVariant: ButtonVariant,
    showPrimaryButton: Boolean,
    onPrimaryButtonAction: () -> Unit,
    onSecondaryButtonAction: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .safeDrawingPadding()
            .padding(horizontal = MaterialTheme.customDimens.dimen32)
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer48()
        SimpleInformationCard(
            modifier = Modifier
                .size(
                    width = MaterialTheme.customDimens.dimen330,
                    height = MaterialTheme.customDimens.dimen550
                ),
            color = MaterialTheme.customColors.gray500,
            shape = MaterialTheme.shapes.medium,
            informationText = stringResource(id = informationText)
        )
        Spacer24()
        Spacer1f()
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (showPrimaryButton) {
                OneButton(
                    modifier = Modifier.fillMaxWidth(),
                    text = primaryButtonText?.let { stringResource(id = it) } ?: EMPTY_STRING,
                    onClick = onPrimaryButtonAction,
                )
            }
            when (secondaryButtonVariant) {
                ButtonVariant.SECONDARY_VARIANT -> {
                    Spacer16()
                    OneButton(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(id = secondaryButtonText),
                        buttonVariant = ButtonVariant.SECONDARY_VARIANT,
                        onClick = onSecondaryButtonAction,
                    )
                }
                ButtonVariant.HYPERLINK_VARIANT -> {
                    HyperLinkTextButton(text = stringResource(id = secondaryButtonText)) {
                        onSecondaryButtonAction()
                    }
                }
                else -> Unit
            }
        }
        Spacer24()
    }
}

@Preview(
    showBackground = true,
    showSystemUi = true
)
@Composable
fun InactiveProductScreenPreview() {
    OneAppTheme {
        InactiveProductScreen(
            informationText = R.string.home_inactive_product_description,
            primaryButtonText = R.string.logout,
            secondaryButtonText = R.string.delete_account_definitely
        )
    }
}