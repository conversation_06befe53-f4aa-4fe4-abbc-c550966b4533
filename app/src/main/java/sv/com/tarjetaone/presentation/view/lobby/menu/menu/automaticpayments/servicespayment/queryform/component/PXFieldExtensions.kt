package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.queryform.component

import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.VisualTransformation
import sv.com.tarjetaone.common.utils.AppConstants.DOLLAR_STRING
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldValueType
import sv.com.tarjetaone.presentation.compose.util.input.CurrencyVisualTransformation

fun PXFieldValueType?.keyboardType(): KeyboardType {
    return when (this) {
        PXFieldValueType.Decimal,
        PXFieldValueType.Double,
        PXFieldValueType.Money -> KeyboardType.Decimal

        PXFieldValueType.Integer,
        PXFieldValueType.Long -> KeyboardType.Number

        PXFieldValueType.Unknown,
        PXFieldValueType.Text -> KeyboardType.Text

        else -> KeyboardType.Unspecified
    }
}

fun PXFieldValueType?.visualTransformation(): VisualTransformation {
    return when (this) {
        PXFieldValueType.Decimal,
        PXFieldValueType.Double,
        PXFieldValueType.Money -> {
            CurrencyVisualTransformation(
                displayIfEmpty = false,
                currencySymbol = if (this is PXFieldValueType.Money) DOLLAR_STRING else null
            )
        }

        else -> VisualTransformation.None
    }
}
