package sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.sent

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import javax.inject.Inject

@HiltViewModel
class ClaimSentViewModel @Inject constructor(savedStateHandle: SavedStateHandle) :
    BaseViewModel() {
    private val args = ClaimSentFragmentArgs.fromSavedStateHandle(savedStateHandle)
    private val _uiState = MutableStateFlow(ClaimSentUiState())
    val uiState: StateFlow<ClaimSentUiState> = _uiState.asStateFlow()

    private fun onStart() {
        _uiState.update { it.copy(isPriority = args.isPriority) }
    }

    private fun onClaimDetailClick() {
        sendEvent(
            UiEvent.Navigate(
                ClaimSentFragmentDirections
                    .actionClaimSentFragmentToClaimDetailFragment(claimDetail = args.claimDetail)
            )
        )
    }

    fun onEvent(event: ClaimSentUiEvent) {
        when (event) {
            ClaimSentUiEvent.OnClaimDetailClick -> onClaimDetailClick()
            ClaimSentUiEvent.OnStart -> onStart()
        }
    }
}
