package sv.com.tarjetaone.presentation.view.common.finish_personalization

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.CardColor
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.personalization.CreditCardComponent
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.compose.util.Spacer48
import sv.com.tarjetaone.presentation.view.request_card_forms.card_configuration.finish_personalization.FinishPersonalizationViewModel

@Composable
fun FinishPersonalizationScreen(
    viewModel: FinishPersonalizationViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LaunchedEffect(Unit) {
        viewModel.onFinishPersonalizationEvent(FinishPersonalizationEvent.OnStart)
    }

    FinishPersonalizationContent(
        uiState = uiState,
        onSaveCustomization = {
            viewModel.onFinishPersonalizationEvent(FinishPersonalizationEvent.OnSaveCustomization)
        },
        onCustomizeAgain = {
            viewModel.onFinishPersonalizationEvent(FinishPersonalizationEvent.OnCustomizeAgain)
        }
    )
}

@Composable
fun FinishPersonalizationContent(
    uiState: FinishPersonalizationState = FinishPersonalizationState(),
    onSaveCustomization: () -> Unit = {},
    onCustomizeAgain: () -> Unit = {}
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .safeDrawingPadding()
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.height(80.dp))
        Text(
            text = stringResource(id = R.string.final_card_personalization_header),
            color = MaterialTheme.colorScheme.primary,
            style = MaterialTheme.typography.displayMedium.copy(
                fontSize = 33.sp,
                lineHeight = 45.sp
            )
        )
        Spacer24()
        Text(
            text = stringResource(id = R.string.final_card_personalization),
            color = MaterialTheme.colorScheme.secondary,
            style = MaterialTheme.typography.bodySmall.copy(
                fontWeight = FontWeight.SemiBold
            )
        )
        Spacer32()
        uiState.cardColors?.let { cardColor ->
            CreditCardComponent(
                cardColors = cardColor,
                nameOnCard = uiState.creditCardName
            )
        }
        Spacer48()
        Text(
            text = stringResource(id = R.string.delivery_date),
            color = MaterialTheme.colorScheme.secondary,
            style = MaterialTheme.typography.bodyMedium.copy(
                fontSize = 17.sp,
                fontWeight = FontWeight.Bold
            )
        )
        Text(
            text = uiState.deliveryDate,
            color = MaterialTheme.colorScheme.secondary,
            style = MaterialTheme.typography.bodyMedium.copy(
                fontWeight = FontWeight.Normal
            )
        )
        Spacer48()
        OneButton(
            text = stringResource(id = R.string.save_and_send_my_card),
            onClick = onSaveCustomization,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.customDimens.dimen32),
        )
        Spacer16()
        OneButton(
            text = stringResource(id = R.string.finish_personalization_card_personalize_again),
            buttonVariant = ButtonVariant.SECONDARY_VARIANT,
            onClick = onCustomizeAgain,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.customDimens.dimen32)
        )
        Spacer16()
    }
}

@Preview(
    showBackground = true,
    showSystemUi = true
)
@Composable
fun FinishPersonalizationScreenPreview() {
    OneAppTheme {
        FinishPersonalizationContent(
            uiState = FinishPersonalizationState(
                creditCardName = "Juan Perez",
                cardColors = CardColor(
                    id = "1",
                    colorName ="",
                    colors = listOf("#000000"),
                    iconColor ="#FFFFFF",
                    textColor = "#FFFFFF"
                ),
                deliveryDate = "Lunes 29 de septiembre"
            )
        )
    }
}
