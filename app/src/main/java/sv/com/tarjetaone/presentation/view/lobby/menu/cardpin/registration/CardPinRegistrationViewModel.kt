package sv.com.tarjetaone.presentation.view.lobby.menu.cardpin.registration

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.R
import sv.com.tarjetaone.analytics.dynatrace.DynatraceEvent
import sv.com.tarjetaone.analytics.dynatrace.DynatraceManager
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.presentation.view.common.cardpin.CardPinBaseViewModel
import javax.inject.Inject

@HiltViewModel
class CardPinRegistrationViewModel @Inject constructor(
    private val dynatraceManager: DynatraceManager
) : CardPinBaseViewModel() {
    override fun onStart() {
        super.onStart()
        dynatraceManager.sendInAppEvent(DynatraceEvent.ChangePin.ViewChangePin)
        _uiState.update {
            it.copy(
                continueButtonLabel = R.string.continue_button_label,
                descriptionLabel = R.string.enter_your_new_pin
            )
        }
    }

    override fun onContinueClick() {
        dynatraceManager.sendInAppEvent(DynatraceEvent.ChangePin.Continue)
        sendEvent(
            UiEvent.Navigate(
                CardPinRegistrationFragmentDirections
                    .actionCardPinRegistrationFragmentToCardPinConfirmationFragment(
                        pin = _uiState.value.pin
                    )
            )
        )
    }
}
