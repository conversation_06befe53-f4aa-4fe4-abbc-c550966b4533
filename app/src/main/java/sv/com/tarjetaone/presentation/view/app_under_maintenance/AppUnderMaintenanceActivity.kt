package sv.com.tarjetaone.presentation.view.app_under_maintenance

import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import dagger.hilt.android.AndroidEntryPoint
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.BaseActivity
import sv.com.tarjetaone.core.utils.ScreenHome
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.view.MainActivity

@AndroidEntryPoint
class AppUnderMaintenanceActivity : BaseActivity() {

    private val viewModel: AppUnderMaintenanceViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            OneAppTheme {
                AppUnderMaintenanceScreen(viewModel = viewModel)
            }
        }

        viewModel.navigateTo.collectLatestWithLifecycleActivity {
            if (it != null) {
                navigateToNextScreen(it)
            }
        }
    }

    private fun navigateToNextScreen(screen: ScreenHome) {
        val intent = Intent(this, MainActivity::class.java).apply {
            putExtra(MainActivity.HOME_SCREEN_REF, screen)
        }
        startActivity(intent)
        overridePendingTransition(R.anim.slide_in_right, android.R.anim.fade_out)
        finish()
    }
}
