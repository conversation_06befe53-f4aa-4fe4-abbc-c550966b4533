package sv.com.tarjetaone.presentation.compose.theme

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat
import sv.com.tarjetaone.core.utils.getActivity

/**
 * Sets the status bar and navigation bar content appearance.
 * As the status bar color is transparent, this function helps to control the appearance of
 * the status bar icons and text to have contrast against the background.
 */
@Composable
fun SetStatusBarAppearance(lightAppearance: Boolean = true) {
    val view = LocalView.current
    if (!view.isInEditMode) {
        DisposableEffect(lightAppearance) {
            val window = view.context.getActivity().window
            WindowCompat.getInsetsController(window, view).apply {
                isAppearanceLightStatusBars = lightAppearance
                isAppearanceLightNavigationBars = lightAppearance
            }
            onDispose { }
        }
    }
}
