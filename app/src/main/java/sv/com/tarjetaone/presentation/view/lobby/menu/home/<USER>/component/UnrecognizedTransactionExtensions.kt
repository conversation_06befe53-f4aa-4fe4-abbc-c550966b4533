package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component

import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.core.utils.extensions.configCurrencyWithFractions
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.domain.entities.response.UnrecognizedTransactionUI

fun UnrecognizedTransactionUI.getDateFormatted(): String {
    return valueDate.getFormattedDateFromTo(
        AppConstants.MONTH_DAY_YEAR_WITH_SLASH,
        AppConstants.DAY_MONTH_YEAR_WITH_SPACES
    )
}

fun UnrecognizedTransactionUI.getAmountFormatted() : String =
    amount.configCurrencyWithFractions()
