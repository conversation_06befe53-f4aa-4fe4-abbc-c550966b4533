package sv.com.tarjetaone.presentation.view.common.tutorial

import android.Manifest
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.rememberPermissionState
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.core.utils.facephi.CaptureType
import sv.com.tarjetaone.core.utils.facephi.rememberFacephiWidgetLauncher
import sv.com.tarjetaone.core.utils.shouldShowRationale
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.ButtonSize
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.topbar.component.TopBarAction
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.compose.uicomponent.text.BulletText
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.view.common.tutorial.component.LinearStepProgress
import sv.com.tarjetaone.presentation.view.common.tutorial.component.TipVisualReference

@Composable
fun TutorialScreen(viewModel: TutorialBaseViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LaunchedEffect(Unit) {
        viewModel.onEvent(TutorialUiEvent.OnStart)
    }

    SideEffectHandler(events = viewModel.sideEffects) {
        TutorialScreenContent(
            uiState = uiState,
            onEvent = viewModel::onEvent
        )
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun TutorialScreenContent(
    uiState: TutorialUiState,
    onEvent: (TutorialUiEvent) -> Unit = {}
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val pagerState = rememberPagerState(
        pageCount = { uiState.items.size }
    )
    val isLastPage = pagerState.currentPage == uiState.items.lastIndex
    val tutorialScreenButtonText = if (!isLastPage) {
        stringResource(id = R.string.next_label)
    } else {
        stringResource(id = R.string.take_photo)
    }
    val cameraPermission = Manifest.permission.CAMERA
    val facephiLauncher = getFacephiLauncher(
        tutorialType = uiState.tutorialType,
        onFailure = {
            onEvent(TutorialUiEvent.OnFacephiCaptureFailed(it))
        },
        onSuccess = {
            onEvent(TutorialUiEvent.OnFacephiCaptureResult(it))
        }
    )
    val cameraPermissionState = rememberPermissionState(cameraPermission) { isGranted ->
        if (isGranted) {
            facephiLauncher.initWidget(context)
        } else {
            onEvent(
                TutorialUiEvent.OnCameraPermissionDenied(
                    showRationale = context.shouldShowRationale(cameraPermission)
                )
            )
        }
    }
    ScreenWithTopAppBar(
        contentPadding = PaddingValues(
            top = MaterialTheme.customDimens.dimen8,
            start = MaterialTheme.customDimens.dimen24,
            end = MaterialTheme.customDimens.dimen24,
        ),
        isLeftButtonVisible = false,
        isRightButtonVisible = uiState.isCloseButtonVisible,
        rightActionButton = TopBarAction.CLOSE,
        onLeftButtonClick = { /* No implementation needed */ },
        onRightButtonClick = {
            onEvent(
                TutorialUiEvent.OnCloseButtonClick(
                    requestPermission = cameraPermissionState::launchPermissionRequest
                )
            )
        },
    ) {
        Column(modifier = Modifier.fillMaxSize()) {
            LinearStepProgress(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(MaterialTheme.customDimens.dimen16),
                totalSteps = uiState.items.size,
                currentStep = pagerState.currentPage.plus(ONE_VALUE),
            )
            if (uiState.items.isNotEmpty()) {
                TutorialTipSection(
                    modifier = Modifier.weight(ONE_FLOAT_VALUE),
                    tutorialType = uiState.tutorialType,
                    pagerState = pagerState,
                    items = uiState.items
                )
            }
            OneButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        start = MaterialTheme.customDimens.dimen16,
                        end = MaterialTheme.customDimens.dimen16,
                        bottom = MaterialTheme.customDimens.dimen16,
                    ),
                size = ButtonSize.LARGE,
                trailingIcon = {
                    Icon(
                        modifier = Modifier.padding(start = MaterialTheme.customDimens.dimen12),
                        imageVector = ImageVector.vectorResource(id = R.drawable.ic_chevron_right),
                        contentDescription = null,
                    )
                },
                text = tutorialScreenButtonText,
                onClick = {
                    if (!isLastPage) {
                        onEvent(TutorialUiEvent.OnContinueClick)
                        coroutineScope.launch {
                            pagerState.animateScrollToPage(
                                page = pagerState.currentPage.inc(),
                            )
                        }
                    } else {
                        onEvent(
                            TutorialUiEvent.OnCaptureClick(
                                requestPermission = cameraPermissionState::launchPermissionRequest
                            )
                        )
                    }
                }
            )
        }
    }
}

@Composable
fun getFacephiLauncher(
    tutorialType: TutorialType,
    onFailure: (Any) -> Unit,
    onSuccess: (Any) -> Unit
) = if (tutorialType == TutorialType.Document)
    rememberFacephiWidgetLauncher(
        type = CaptureType.Document(),
        onFailure = onFailure,
        onCaptured = onSuccess
    ) else rememberFacephiWidgetLauncher(
    type = CaptureType.Selfie,
    onFailure = onFailure,
    onCaptured = onSuccess
)

@Composable
private fun TutorialTipSection(
    modifier: Modifier = Modifier,
    tutorialType: TutorialType,
    pagerState: PagerState,
    items: List<TutorialItem>,
) {
    HorizontalPager(
        modifier = modifier,
        state = pagerState,
        pageSpacing = MaterialTheme.customDimens.dimen16,
        contentPadding = PaddingValues(
            horizontal = MaterialTheme.customDimens.dimen16
        )
    ) { page ->
        TutorialTipItem(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState()),
            tutorialType = tutorialType,
            tipItem = items[page]
        )
    }
}

@Composable
private fun TutorialTipItem(
    modifier: Modifier = Modifier,
    tutorialType: TutorialType,
    tipItem: TutorialItem
) {
    Column(modifier = modifier) {
        Spacer8()
        Text(
            text = tipItem.title.asString(),
            style = MaterialTheme.typography.displayMedium.copy(
                color = MaterialTheme.customColors.gray700
            ),
        )
        Spacer24()
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen8),
        ) {
            TipVisualReference(
                modifier = Modifier.weight(ONE_FLOAT_VALUE),
                referenceImage = if (tutorialType == TutorialType.Document) {
                    R.drawable.document_tutorial_correct_photo
                } else {
                    R.drawable.selfie_tutorial_correct_photo
                },
                bottomIcon = R.drawable.ic_check_green,
            )
            TipVisualReference(
                modifier = Modifier.weight(ONE_FLOAT_VALUE),
                referenceImage = tipItem.incorrectImageTip,
                bottomIcon = R.drawable.ic_incorrect,
            )
        }
        Spacer8()
        if (tipItem.tips.size > ONE_VALUE) {
            tipItem.tips.forEach { tip ->
                BulletText(
                    modifier = Modifier.fillMaxWidth(),
                    text = tip.asString(),
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = MaterialTheme.customColors.gray700
                    )
                )
                if (tipItem.tips.last() != tip) {
                    Spacer8()
                }
            }
        } else {
            Text(
                text = tipItem.tips.first().asString(),
                style = MaterialTheme.typography.bodySmall.copy(
                    color = MaterialTheme.customColors.gray700
                )
            )
        }
        Spacer24()
    }
}

@Preview
@Composable
private fun TutorialScreenPreview() {
    OneAppTheme {
        TutorialScreenContent(
            uiState = TutorialUiState(
                tutorialType = TutorialType.Document,
                isCloseButtonVisible = false,
                items = TutorialType.Document.items
            ),
        )
    }
}