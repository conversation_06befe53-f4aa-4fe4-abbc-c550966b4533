package sv.com.tarjetaone.presentation.view.lobby.menu.cardpin.identity.validation

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.usecases.auth.FaceAuthenticationUseCase
import sv.com.tarjetaone.domain.usecases.mycards.updatepin.UpdateCardPinUseCase
import sv.com.tarjetaone.domain.usecases.requestcard.configuration.ConfigurationUseCase
import sv.com.tarjetaone.presentation.view.common.identity_validation.IdentityValidationBaseViewModel
import javax.inject.Inject

@HiltViewModel
class CardPinIdentityValidationViewModel @Inject constructor(
    private val updateCardPinUseCase: UpdateCardPinUseCase,
    private val configurationUseCase: ConfigurationUseCase,
    faceAuthenticationUseCase: FaceAuthenticationUseCase,
    sharedPrefs: SecureSharedPreferencesRepository,
    savedStateHandle: SavedStateHandle
) : IdentityValidationBaseViewModel(faceAuthenticationUseCase, sharedPrefs) {
    private val args = CardPinIdentityValidationFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override val onBlockedAction = {
        sendEvent(
            UiEvent.Navigate(
                CardPinIdentityValidationFragmentDirections.actionHome()
            )
        )
    }

    private fun getPinConfigModule() {
        viewModelScope.launch {
            configurationUseCase(PIN_MODULE, EMPTY_STRING).executeUseCase(
                onSuccessAction = {
                    updatePIN(module = it.result)
                },
                onApiErrorAction = { _, _, _, _ ->
                    showUpsErrorMessage(
                        isDismissible = false,
                        onButtonClick = ::onBackAction
                    )
                }
            )
        }
    }

    private suspend fun updatePIN(module: String) =
        updateCardPinUseCase(
            cCardId = baseSharedPrefs.mainCard?.creditCardId,
            customerId = sharedPrefs.getCustomerId(),
            unencryptedPin = args.pin,
            module = module
        ).executeUseCase(
            onApiErrorAction = { _, _, _, _ ->
                showUpsErrorMessage(
                    isDismissible = false,
                    onButtonClick = ::onBackAction
                )
            },
            onSuccessAction = {
                sendEvent(
                    UiEvent.Navigate(
                        CardPinIdentityValidationFragmentDirections
                            .actionIdentityValidationPinChangeFragmentToFollowingPinChangedFragment(
                                data = it
                            )
                    )
                )
            }
        )

    override fun operation(biometryId: Int) {
        getPinConfigModule()
    }

    override fun onFailedIdentityValidation(resultCode: String?) {
        onBackAction()
    }

    override fun onBackAction() {
        sendEvent(UiEvent.NavigateBackTo(R.id.cardPinRegistrationFragment))
    }

    companion object {
        private const val PIN_MODULE = "MODULO_RSA_PIN"
    }
}
