package sv.com.tarjetaone.presentation.compose.uicomponent.button

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.compose.util.getButtonColors

/**
 * Button to be used as primary/secondary/tertiary button.
 *
 * Consider modifying it according to the desired needs.
 * E.g. To to receive an icon.
 */
@Suppress("kotlin:S107")
@Composable
fun OneButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    buttonVariant: ButtonVariant = ButtonVariant.PRIMARY_VARIANT,
    size: ButtonSize = ButtonSize.LARGE,
    textStyle: TextStyle = size.textStyle(),
    contentPadding: PaddingValues = size.contentPadding(),
    leadingIcon: @Composable (() -> Unit)? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
) {
    val buttonContent: @Composable RowScope.() -> Unit = {
        leadingIcon?.let {
            it()
            Spacer8()
        }
        Text(
            text = text,
            modifier = Modifier.align(Alignment.CenterVertically),
            style = textStyle,
            textAlign = TextAlign.Center
        )
        trailingIcon?.invoke()
    }

    if (buttonVariant == ButtonVariant.TERTIARY_VARIANT) {
        OutlinedButton(
            modifier = modifier.sizeIn(minHeight = size.minHeight()),
            enabled = enabled,
            colors = buttonVariant.getButtonColors(),
            shape = MaterialTheme.shapes.extraLarge,
            onClick = onClick,
            content = buttonContent,
            contentPadding = contentPadding
        )
    } else {
        Button(
            modifier = modifier.sizeIn(minHeight = size.minHeight()),
            enabled = enabled,
            colors = buttonVariant.getButtonColors(),
            shape = MaterialTheme.shapes.extraLarge,
            onClick = onClick,
            content = buttonContent,
            contentPadding = contentPadding
        )
    }
}

@Preview
@Composable
private fun OneButtonLargePreview() {
    OneAppTheme {
        OneButton(
            modifier = Modifier.fillMaxWidth(),
            text = "Primary Large button",
            onClick = {}
        )
    }
}

@Preview
@Composable
private fun OneButtonMediumPreview() {
    OneAppTheme {
        OneButton(
            text = "Secondary Medium Button",
            onClick = {},
            size = ButtonSize.MEDIUM,
            buttonVariant = ButtonVariant.SECONDARY_VARIANT
        )
    }
}

@Preview
@Composable
private fun OneButtonSmallPreview() {
    OneAppTheme {
        OneButton(
            text = "Small Tertiary Button",
            onClick = {},
            size = ButtonSize.SMALL,
            buttonVariant = ButtonVariant.TERTIARY_VARIANT
        )
    }
}

/**
 * Enum class to define the size of the button.
 * Also contains helper methods to get other UI properties dependent on the size.
 */
enum class ButtonSize {
    SMALL,
    MEDIUM,
    LARGE;

    @Composable
    fun contentPadding(): PaddingValues = when (this) {
        SMALL -> PaddingValues(
            vertical = MaterialTheme.customDimens.dimen8,
            horizontal = MaterialTheme.customDimens.dimen16
        )
        MEDIUM -> PaddingValues(
            vertical = MaterialTheme.customDimens.dimen12,
            horizontal = MaterialTheme.customDimens.dimen20
        )
        LARGE -> PaddingValues(
            vertical = MaterialTheme.customDimens.dimen16,
            horizontal = MaterialTheme.customDimens.dimen24
        )
    }

    @Composable
    fun textStyle(): TextStyle = when (this) {
        SMALL -> MaterialTheme.typography.labelMedium
        MEDIUM -> MaterialTheme.typography.bodySmall
        LARGE -> MaterialTheme.typography.bodyLarge
    }.copy(fontWeight = FontWeight.SemiBold)

    @Composable
    fun minHeight(): Dp = when (this) {
        SMALL -> MaterialTheme.customDimens.dimen26
        MEDIUM -> MaterialTheme.customDimens.dimen38
        LARGE -> MaterialTheme.customDimens.dimen48
    }
}
