package sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.deactivate_program_protection.confirmation

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import javax.inject.Inject

@HiltViewModel
class DeactivateProgramProtectionConfirmViewModel @Inject constructor(
    savesStateHandle: SavedStateHandle
) : BaseViewModel() {

    private val navArgs = DeactivateProgramProtectionFragmentArgs.fromSavedStateHandle(savesStateHandle)

    private fun navigateToMenu() {
        sendEvent(
            UiEvent.NavigateBackTo(R.id.navigation_menu)
        )
    }

    private fun navigateToPrepareForPicture() {
        sendEvent(
            UiEvent.Navigate(
                DeactivateProgramProtectionFragmentDirections
                    .actionDeactivateProgramProtectionFragmentToPrepareForPictureDeactivateFPFragment(
                        fraud = navArgs.fraud
                    )
            )
        )
    }

    fun onEvent(event: DeactivateProgramProtectionConfirmationUiEvent) {
        when (event) {
            DeactivateProgramProtectionConfirmationUiEvent.OnKeepClick -> navigateToMenu()
            DeactivateProgramProtectionConfirmationUiEvent.OnDeactivateClick -> navigateToPrepareForPicture()
            DeactivateProgramProtectionConfirmationUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            DeactivateProgramProtectionConfirmationUiEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
        }
    }
}
