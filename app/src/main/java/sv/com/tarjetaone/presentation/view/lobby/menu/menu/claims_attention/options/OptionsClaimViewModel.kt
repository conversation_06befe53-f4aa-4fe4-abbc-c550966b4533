package sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.options

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI
import sv.com.tarjetaone.domain.entities.response.CatalogType
import sv.com.tarjetaone.domain.usecases.catalog.GetCatalogByParentUseCase
import javax.inject.Inject

@HiltViewModel
class OptionsClaimViewModel @Inject constructor(
    private val getCatalogByParent: GetCatalogByParentUseCase,
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {
    private val args = OptionsClaimFragmentArgs.fromSavedStateHandle(savedStateHandle)

    private val _uiState = MutableStateFlow(OptionsClaimUiState())
    val uiState = _uiState.asStateFlow()

    private fun getClaimOptions() {
        if (uiState.value.claimOptions.isNotEmpty()) return // Avoid reloading when coming back from form screen
        viewModelScope.launch {
            sendEvent(UiEvent.Loading(true))
            getCatalogByParent(CatalogType.MANAGEMENT_TYPES, args.parentId).executeUseCase {
                sendEvent(UiEvent.Loading(false))
                if (it.statusCatalog?.responseStatus?.code == AppConstants.SUCCESS_RESPONSE_CODE) {
                    _uiState.update { state ->
                        state.copy(
                            claimOptions = it.dataCatalog?.catalogItemsCollection.orEmpty(),
                            claimName = args.catalogName
                        )
                    }
                } else {
                    showUpsErrorMessage(false) { sendEvent(UiEvent.NavigateBack) }
                }
            }
        }
    }

    private fun onOptionClick(option: CatalogItemsCollectionUI) {
        sendEvent(
            UiEvent.Navigate(
                OptionsClaimFragmentDirections
                    .actionOptionsClaimFragmentToClaimDynamicFormFragment(
                        claimOptionId = option.id.toInt(),
                        claimOptionName = option.name
                    )
            )
        )
    }

    fun onEvent(event: OptionsClaimUiEvent) {
        when (event) {
            is OptionsClaimUiEvent.OnStart -> getClaimOptions()
            is OptionsClaimUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            is OptionsClaimUiEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
            is OptionsClaimUiEvent.OnOptionClick -> onOptionClick(event.option)
        }
    }
}
