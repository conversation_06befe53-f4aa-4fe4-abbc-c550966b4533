package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.installmentpayment

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.response.InstallmentTermsUI
import javax.inject.Inject

@HiltViewModel
class InstallmentPaymentViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
) : BaseViewModel() {

    private val args = InstallmentPaymentFragmentArgs.fromSavedStateHandle(savedStateHandle)

    private val _uiState = MutableStateFlow(InstallmentPaymentUiState())
    val uiState = _uiState.asStateFlow()

    private fun onStart() {
        _uiState.update { state ->
            state.copy(
                storeName = args.storeName,
                installments = args.installments.toList(),
                selectedInstallment = args.selectedInstallment,
                availableInstallmentSlots = args.selectedInstallment.installmentsAvailable.orZero(),
                totalAmount = args.totalAmount.toDouble(),
            )
        }
    }

    private fun onSelectInstallment(installment: InstallmentTermsUI) =
        _uiState.update { it.copy(selectedInstallment = installment) }

    private fun onKnowMoreClick() {
        _uiState.update {
            it.copy(
                showBottomSheet = true,
                bottomSheetType = InstallmentBottomSheetType.Info,
            )
        }
    }

    private fun onContinueClick() {
        toggleBottomSheetVisibility(
            visible = true,
            type = InstallmentBottomSheetType.Confirmation,
        )
    }

    private fun toggleBottomSheetVisibility(
        visible: Boolean,
        type: InstallmentBottomSheetType? = null,
    ) {
        _uiState.update {
            it.copy(
                showBottomSheet = visible,
                bottomSheetType = type,
            )
        }
    }

    private fun onInfoBottomSheetEvent(action: InfoBottomSheetUiEvent) {
        when (action) {
            InfoBottomSheetUiEvent.OnAgreeClick -> _uiState.update {
                it.copy(
                    showBottomSheet = false,
                    bottomSheetType = null,
                )
            }
        }
    }

    private fun onConfirmationBottomSheetEvent(action: ConfirmationBottomSheetUiEvent) {
        when (action) {
            ConfirmationBottomSheetUiEvent.OnContinueClick -> {
                toggleBottomSheetVisibility(
                    visible = false,
                    type = null,
                )
                sendEvent(
                    UiEvent.Navigate(
                        InstallmentPaymentFragmentDirections.navigateToInstallmentPaymentPrepareForPictureFragment(
                            selectedInstallment = uiState.value.selectedInstallment,
                            totalAmount = uiState.value.totalAmount.orZero().toFloat(),
                            transaction = args.transaction,
                        )
                    )
                )
            }
        }
    }

    fun onEvent(event: InstallmentPaymentUiEvent) {
        when (event) {
            is InstallmentPaymentUiEvent.OnStart -> onStart()
            is InstallmentPaymentUiEvent.OnSelectInstallment -> onSelectInstallment(event.installment)
            is InstallmentPaymentUiEvent.OnInfoBottomSheetEvent -> onInfoBottomSheetEvent(event.action)
            is InstallmentPaymentUiEvent.OnDismissBottomSheet -> toggleBottomSheetVisibility(
                visible = false,
                type = null,
            )
            is InstallmentPaymentUiEvent.OnShowBottomSheet -> toggleBottomSheetVisibility(
                visible = true,
                type = event.type,
            )
            is InstallmentPaymentUiEvent.OnConfirmationBottomSheetEvent -> onConfirmationBottomSheetEvent(event.action)
            is InstallmentPaymentUiEvent.OnKnowMoreClick -> onKnowMoreClick()
            is InstallmentPaymentUiEvent.OnContinueClick -> onContinueClick()
            is InstallmentPaymentUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            is InstallmentPaymentUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
        }
    }

    companion object {
        const val INSTALLMENT_PAYMENT_MORE_INFO = "INSTALLMENT_PAYMENT_MORE_INFO"
        const val ZERO_INTEREST_RATE_STRING = "0%"
    }
}
