package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.catalog

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.usecases.automaticpayments.servicespayment.catalogs.GetPXCategoriesUseCase
import sv.com.tarjetaone.domain.usecases.automaticpayments.servicespayment.catalogs.GetPXCatalogsUseCase
import sv.com.tarjetaone.domain.utils.onSuccess

@HiltViewModel
class ServiceCatalogViewModel @Inject constructor(
    private val getPaymentCatalogCategoriesUseCase: GetPXCategoriesUseCase,
    private val getPaymentCatalogServicesUseCase: GetPXCatalogsUseCase,
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {

    private val _uiState: MutableStateFlow<ServiceCatalogUiState> =
        MutableStateFlow(ServiceCatalogUiState())
    val uiState = _uiState.asStateFlow()

    private val args = ServiceCatalogFragmentArgs.fromSavedStateHandle(savedStateHandle)

    private fun onStart() {
        _uiState.update { it.copy(isAutomaticPayment = args.isAutomaticPayment) }

        if (uiState.value.servicesList.isNotEmpty()) return // avoid loading services again
        viewModelScope.launch {
            sendEvent(UiEvent.Loading(true))
            val categoriesDeferred = async {
                getPaymentCatalogCategoriesUseCase().onSuccess { response ->
                    _uiState.update { it.copy(categoriesList = response) }
                }
            }
            val servicesDeferred = async {
                getPaymentCatalogServicesUseCase(null).onSuccess { response ->
                    _uiState.update { state ->
                        state.copy(
                            servicesList = if (args.isAutomaticPayment) {
                                response.filter { it.automaticPayment }
                            } else { 
                                response
                            }
                        )
                    }
                }
            }

            awaitAll(categoriesDeferred, servicesDeferred).any { it.handleErrors() }
            sendEvent(UiEvent.Loading(false))

            // If the service list is not empty, should show the payment category list
            // without icon, if categoriesList is not empty icon will be set
            if (uiState.value.servicesList.isNotEmpty()) {
                setPaymentCategoryList()
            }
        }
    }

    private fun setPaymentCategoryList(query: String? = null) {
        // If query is not null, filter the servicesList based on the query by catalogName or categoriesName
        // otherwise, all services will be returned
        val filteredServicesList = uiState.value.servicesList.filter { service ->
            query?.let {
                service.catalogName.contains(it, ignoreCase = true) ||
                        service.categoriesName.contains(it, ignoreCase = true)
            } ?: true // True returned to get all services
        }

        // First, get the list of categoriesID from the servicesList to build each category
        // in order to fill info based on services endpoint response, categoriesList is used only
        // to get the icon name
        val categories = filteredServicesList.map { it.categoriesId }.distinct().sorted()
        _uiState.update {
            it.copy(
                filteredServiceCategoriesList = categories.mapIndexed { index, categoryId ->
                    ServiceCategory(
                        categoryId = categoryId,
                        categoryName = filteredServicesList.find { service ->
                            service.categoriesId == categoryId
                        }?.categoriesName.orEmpty(),
                        categoryIcon = it.categoriesList.find { category ->
                            category.categoriesId == categoryId
                        }?.iconName.orEmpty(),
                        isExpanded = !query.isNullOrEmpty() && index == ZERO_VALUE, // If the query is not empty, the first category will be expanded
                        serviceItems = filteredServicesList.filter { service ->
                            service.categoriesId == categoryId
                        }.map { service ->
                            ServiceItem(
                                catalogServiceCode = service.catalogServiceCode,
                                categoriesName = service.categoriesName,
                                serviceName = service.catalogName,
                                isManualCapture = service.manualCapture,
                                isAutomaticPayment = service.automaticPayment,
                                fieldAutomatic = service.fieldAutomatic,
                                serviceLabel = service.label
                            )
                        }
                    )
                }
            )
        }
    }

    private fun onCategoryClick(categoryId: Int, isExpanded: Boolean) {
        // To expand or collapse the category, categoryId is used to identify the category
        // and isExpanded is used to change the state of the category
        _uiState.update { state ->
            state.copy(
                filteredServiceCategoriesList = state.filteredServiceCategoriesList.map { category ->
                    if (category.categoryId == categoryId) {
                        category.copy(isExpanded = !isExpanded)
                    } else {
                        category.copy(isExpanded = false)
                    }
                }
            )
        }
    }

    private fun onServiceClick(service: ServiceItem) {
        val nextScreen = if (args.isAutomaticPayment) {
            ServiceCatalogFragmentDirections
                .actionServiceCatalogFragmentToServiceAffiliationQueryFormFragment(service)
        } else {
            ServiceCatalogFragmentDirections
                .actionServiceCatalogFragmentToServiceQueryFormFragment(service)
        }
        sendEvent(UiEvent.Navigate(nextScreen))
    }

    private fun onChangeSearchQuery(searchServiceName: String) {
        _uiState.update { it.copy(searchServiceName = searchServiceName) }
    }

    private fun onFetchServiceName(query: String) {
        setPaymentCategoryList(query)
    }

    fun onEvent(event: ServiceCatalogUiEvent) {
        when (event) {
            ServiceCatalogUiEvent.OnStart -> onStart()
            ServiceCatalogUiEvent.OnBack -> sendEvent(UiEvent.NavigateBack)
            ServiceCatalogUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            is ServiceCatalogUiEvent.OnCategoryClick -> onCategoryClick(event.categoryId, event.isExpanded)
            is ServiceCatalogUiEvent.OnServiceClick -> onServiceClick(event.service)
            is ServiceCatalogUiEvent.OnChangeSearchQuery -> onChangeSearchQuery(event.searchServiceName)
            is ServiceCatalogUiEvent.OnFetchServiceName -> onFetchServiceName(event.query)
        }
    }
}
