package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.analytics.dynatrace.DynatraceEvent
import sv.com.tarjetaone.analytics.dynatrace.DynatraceManager
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.SUCCESS_RESPONSE_CODE
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.core.utils.extensions.takeIfNotBlank
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.response.PointStatementUI
import sv.com.tarjetaone.domain.usecases.home.GetPointStatementUseCase
import sv.com.tarjetaone.domain.usecases.transactions.GetPointTransactionsUseCase
import sv.com.tarjetaone.domain.utils.onApiError
import sv.com.tarjetaone.domain.utils.onNetworkError
import sv.com.tarjetaone.domain.utils.onSuccess
import javax.inject.Inject

@HiltViewModel
class MyMegaPointsViewModel @Inject constructor(
    private val getPointStatementUseCase: GetPointStatementUseCase,
    private val getPointTransactionsUseCase: GetPointTransactionsUseCase,
    private val sharedPref: SecureSharedPreferencesRepository,
    private val dynatraceManager: DynatraceManager
) : BaseViewModel() {
    private val _uiState: MutableStateFlow<MyMegaPointsUiState> =
        MutableStateFlow(MyMegaPointsUiState())
    val uiState: StateFlow<MyMegaPointsUiState> = _uiState.asStateFlow()

    private val hasPaymentPenalty: Boolean
        get() = baseSharedPrefs.mainCard?.hasPaymentPenalty ?: false

    private fun onStart() {
        if (hasPaymentPenalty) {
            _uiState.update { it.copy(hasPaymentPenalty = hasPaymentPenalty) }
            return
        }

        dynatraceManager.sendInAppEvent(DynatraceEvent.MyMegaPoints.ViewPoints)
        getPointStatement()
    }

    private fun onPointStatementChange(statement: PointStatementUI) {
        _uiState.update { it.copy(pointStatementSelected = statement) }

        requestPointsTransactions()
    }

    private fun onSearchQueryChange(query: String) {
        _uiState.update { it.copy(searchQuery = query) }
    }

    private fun onPerformSearchQuery() = requestPointsTransactions()


    private fun onFilterStatusChange(status: MegaPointsStatus) {
        _uiState.update {
            it.copy(
                megaPointsStatusFilter = status,
                searchQuery = EMPTY_STRING
            )
        }

        requestPointsTransactions()
    }

    private fun getPointStatement() {
        sendEvent(SideEffect.Loading(true))
        viewModelScope.launch {
            getPointStatementUseCase(
                sharedPref.mainCard?.creditCardId.orZero()
            ).onSuccess { result ->
                sendEvent(SideEffect.Loading(false))
                val response = result.statusResponse?.responseStatus
                if (response?.code != SUCCESS_RESPONSE_CODE) {
                    showUpsErrorMessage {
                        sendEvent(UiEvent.NavigateBack)
                    }
                    return@onSuccess
                }

                result.data?.let { data ->
                    _uiState.update {
                        it.copy(
                            pointStatementList = data.pointStatements,
                            pointStatementSelected = data.pointStatements.firstOrNull()
                        )
                    }
                }

                requestPointsTransactions()
            }.onApiError { _, _, _, _ ->
                showUpsErrorMessage {
                    sendEvent(UiEvent.NavigateBack)
                }
            }.onNetworkError {
                showUpsErrorMessage {
                    sendEvent(UiEvent.NavigateBack)
                }
            }
        }
    }

    private fun getMegaPointsMode(uiState: MyMegaPointsUiState): MegaPointsTransactionsMode {
        with(uiState) {
            val hasCutOffDate = pointStatementSelected?.cutOffDate.isNullOrBlank().not()
            val hasSearchQuery = searchQuery.isNotBlank()
            return when {
                !hasCutOffDate && !hasSearchQuery -> MegaPointsTransactionsMode.Current
                !hasCutOffDate && hasSearchQuery -> MegaPointsTransactionsMode.ByText
                hasCutOffDate && hasSearchQuery -> MegaPointsTransactionsMode.ByText
                else -> MegaPointsTransactionsMode.ByPeriod
            }
        }
    }

    private fun requestPointsTransactions() {
        _uiState.update {
            val mode = getMegaPointsMode(it)
            it.copy(
                megaPointsSearchMode = mode,
                pointTransactionsPaging = getPointTransactionsUseCase(
                    cardId = sharedPref.mainCard?.creditCardId.orZero(),
                    mode = if (it.megaPointsStatusFilter == MegaPointsStatus.Accumulated) {
                        mode.accumulated
                    } else {
                        mode.redeemed
                    },
                    cutOffDate = it.pointStatementSelected?.cutOffDate?.takeIfNotBlank(),
                    queryText = it.searchQuery.takeIfNotBlank()
                )
            )
        }
    }

    fun onEvent(event: MyMegaPointsUiEvent) {
        when (event) {
            MyMegaPointsUiEvent.OnStart -> onStart()
            is MyMegaPointsUiEvent.OnPointStatementSelected -> onPointStatementChange(event.pointStatement)
            is MyMegaPointsUiEvent.OnSearchQueryChange -> onSearchQueryChange(event.query)
            is MyMegaPointsUiEvent.OnPerformSearchQuery -> onPerformSearchQuery()
            is MyMegaPointsUiEvent.OnFilterStatusChange -> onFilterStatusChange(event.status)
            is MyMegaPointsUiEvent.OnTransactionLoadError -> showUpsErrorMessage { event.onRetry() }
            is MyMegaPointsUiEvent.OnBackPressed -> sendEvent(UiEvent.NavigateBack)
            is MyMegaPointsUiEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
        }
    }
}
