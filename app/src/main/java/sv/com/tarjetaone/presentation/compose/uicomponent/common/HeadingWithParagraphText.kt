package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.foundation.layout.Column
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp

/**
 * composable component that groups vertically a heading text with a paragraph one.
 */
@Composable
fun HeadingWithLabelText(
    headingText: String,
    label: String,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = headingText,
            style = MaterialTheme.typography.bodyLarge.copy(
                fontWeight = FontWeight.SemiBold,
                fontSize = MaterialTheme.customDimensSp.sp17
            ),
            color = MaterialTheme.colorScheme.onSurface,
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

@Preview(showBackground = true)
@Composable
fun HeadingWithParagraphTextPreview() {
    OneAppTheme {
        HeadingWithLabelText(
            headingText = "Juan Daniel Perez Smith",
            label = "Nombre"
        )
    }
}
