package sv.com.tarjetaone.presentation.compose.uicomponent.personalization

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.common.utils.AppConstants.FIVE_VALUE_PERCENT
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.common.utils.CardColor
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.util.setBackgroundFromList
import sv.com.tarjetaone.presentation.compose.util.toColor

@Composable
fun ColorSelectorContainer(
    colors: List<CardColor> = listOf(),
    cardColor: CardColor,
    onColorSelected: (CardColor) -> Unit
) {
    Column(modifier = Modifier.height(IntrinsicSize.Min)) {
        Box(
            modifier = Modifier
                .size(
                    MaterialTheme.customDimens.dimen48,
                    MaterialTheme.customDimens.dimen370
                )
                .fillMaxSize()
        ) {
            LazyColumn(
                modifier = Modifier.align(Alignment.Center),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen16)
            ) {
                items(colors) { color ->
                    ColorSelectorComponent(
                        color = color,
                        selected = color.id.toIntOrNull() == cardColor.id.toIntOrNull(),
                        onColorSelected = onColorSelected
                    )
                }
            }
        }
    }
}

@Composable
fun ColorSelectorComponent(
    color: CardColor,
    selected: Boolean,
    onColorSelected: (CardColor) -> Unit = {}
) {
    Box(
        modifier = Modifier
            .height(MaterialTheme.customDimens.dimen48)
            .aspectRatio(ONE_FLOAT_VALUE, matchHeightConstraintsFirst = true)
            .background(
                color = MaterialTheme.colorScheme.secondaryContainer,
                shape = CircleShape
            )
            .border(
                width = if (selected) {
                    MaterialTheme.customDimens.dimen1
                } else {
                    MaterialTheme.customDimens.dimenZeroDotTwo
                },
                color = if (selected) {
                    MaterialTheme.customColors.successContainer
                } else {
                    MaterialTheme.customColors.gray500.copy(alpha = FIVE_VALUE_PERCENT)
                },
                shape = CircleShape
            )
            .clip(CircleShape)
            .clickable { onColorSelected(color) }
    ) {
        Box(
            modifier = Modifier
                .size(MaterialTheme.customDimens.dimen26)
                .then(
                    if (color.colors.contains(WHITE_COLOR_STRING)) {
                        Modifier.shadow(
                            elevation = MaterialTheme.customDimens.dimen4,
                            shape = CircleShape,
                            clip = true
                        )
                    } else {
                        Modifier
                    }
                )
                .setBackgroundFromList(
                    colors = color.colors.map { it.toColor() },
                    shape = CircleShape
                )
                .align(Alignment.Center)
        )
    }
}

@Preview(showBackground = false)
@Composable
private fun ColorSelectorComponentPreview() {
    OneAppTheme {
        ColorSelectorContainer(
            colors = CardPersonalizationDummyData.colors,
            cardColor = CardPersonalizationDummyData.colors.first()
        ) { }
    }
}

const val WHITE_COLOR_STRING = "#FFFFFF"
