package sv.com.tarjetaone.presentation.compose.uicomponent.text

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntSize
import sv.com.tarjetaone.common.utils.AppConstants.BULLET
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.compose.util.TextFromHtml

@Composable
fun BulletText(
    modifier: Modifier = Modifier,
    text: String,
    bullet: String = BULLET,
    style: TextStyle = MaterialTheme.typography.bodySmall,
) {
    val bulletMeasurer = remember { mutableStateOf(IntSize.Zero) }

    Row(modifier = modifier) {
        Text(
            text = bullet,
            style = style,
            modifier = Modifier
                .onGloballyPositioned {
                    bulletMeasurer.value = it.size
                }
        )

        TextFromHtml(
            text = text,
            style = style,
            modifier = Modifier.padding(start = with(LocalDensity.current) {
                (bulletMeasurer.value.width).toDp()
            })
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun BulletTextPreview() {
    OneAppTheme {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            BulletText(
                text = "This is a bullet point example.",
                style = MaterialTheme.typography.bodySmall.copy(
                    color = MaterialTheme.customColors.gray700
                )
            )
            Spacer8()
            BulletText(
                text = "Another bullet point. This is a longer text to demonstrate wrapping.",
                style = MaterialTheme.typography.bodySmall.copy(
                    color = MaterialTheme.customColors.gray700
                )
            )
        }
    }
}
