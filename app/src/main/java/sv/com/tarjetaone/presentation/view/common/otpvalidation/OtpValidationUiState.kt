package sv.com.tarjetaone.presentation.view.common.otpvalidation

import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.domain.entities.response.ContactType
import sv.com.tarjetaone.presentation.compose.util.InfoStatus
import sv.com.tarjetaone.presentation.helpers.UiText

data class OtpValidationUiState(
    val code: String = EMPTY_STRING,
    val sharedKey: String = EMPTY_STRING,
    val otpStatus: InfoStatus = InfoStatus.None,
    val contactType: ContactType = ContactType.Phone,
    val contact: String = EMPTY_STRING,
    val otpMessage: UiText? = null,
    val showOtpMethodDialog: Boolean = false,
    val otpLifeTime: Int = ZERO_VALUE,
    val isSnackBarVisible: Boolean = false,
    val showProgressBar: Boolean = true
)
