package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.core.utils.extensions.formatAsString
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.response.PointsTUI
import sv.com.tarjetaone.presentation.compose.theme.customColors

internal fun PointsTUI.formatPoints(): String = this.pointsAmount.orZero().formatAsString()

internal fun PointsTUI.formatDate(): String = this.valueDate?.getFormattedDateFromTo(
    AppConstants.DAY_MONTH_YEAR_WITH_SLASH, AppConstants.DAY_MONTH_YEAR_WITH_SPACES
)?.replace(AppConstants.DOT_STRING, AppConstants.EMPTY_STRING).orEmpty()

@Composable
internal fun MegaPointsStatus.getPointsColor(): Color {
    return when (this) {
        MegaPointsStatus.Accumulated -> MaterialTheme.customColors.successContainer
        MegaPointsStatus.Redeemed -> MaterialTheme.colorScheme.primary
    }
}
