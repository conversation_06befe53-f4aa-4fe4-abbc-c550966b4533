package sv.com.tarjetaone.presentation.view.common.tutorial

/**
 * Represents the set of possible UI events that can occur within a tutorial flow.
 * This sealed class helps ensure type safety and exhaustive handling of events.
 */
sealed class TutorialUiEvent {
    data object OnStart : TutorialUiEvent()
    data object OnContinueClick : TutorialUiEvent()
    data class OnCaptureClick(val requestPermission: () -> Unit) : TutorialUiEvent()
    data class OnCameraPermissionDenied(val showRationale: Boolean) : TutorialUiEvent()
    data class OnFacephiCaptureResult(val result: Any) : TutorialUiEvent()
    data class OnFacephiCaptureFailed(val result: Any) : TutorialUiEvent()
    data class OnCloseButtonClick(val requestPermission: () -> Unit) : TutorialUiEvent()
}