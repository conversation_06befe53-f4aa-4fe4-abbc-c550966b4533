package sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.dynamic_form

import sv.com.tarjetaone.domain.entities.response.CatalogsUI

sealed class ClaimDynamicFormUiEvent {
    data object OnStart : ClaimDynamicFormUiEvent()
    data object OnBackClick : ClaimDynamicFormUiEvent()
    data object OnSupportClick : ClaimDynamicFormUiEvent()
    data object OnSendClick : ClaimDynamicFormUiEvent()
    data object OnCancelClick : ClaimDynamicFormUiEvent()
    data class OnVariableFieldChange(
        val index: Int,
        val typedValue: String,
        val selectedValue: CatalogsUI?
    ) : ClaimDynamicFormUiEvent()
    data class OnDescriptionChange(val description: String) : ClaimDynamicFormUiEvent()
}
