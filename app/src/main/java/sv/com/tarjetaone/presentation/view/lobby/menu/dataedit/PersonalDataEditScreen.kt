package sv.com.tarjetaone.presentation.view.lobby.menu.dataedit

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.EIGHT_VALUE_PERCENT
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.FieldDecorationType
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer24
import sv.com.tarjetaone.presentation.compose.util.input.MaskVisualTransformation

@Composable
fun PersonalDataEditScreen(viewModel: PersonalDataEditViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    PersonalDataEditContent(
        uiState = uiState,
        onUiEvent = viewModel::onUiEvent
    )
}

@Composable
fun PersonalDataEditContent(
    uiState: PersonalDataEditUiState,
    onUiEvent: (PersonalDataEditUiEvent) -> Unit
) {
    LaunchedEffect(Unit) {
        onUiEvent(PersonalDataEditUiEvent.OnStart)
    }
    ScreenWithTopAppBar(
        title = stringResource(R.string.general_data),
        isProgressbarVisible = true,
        progress = EIGHT_VALUE_PERCENT,
        onLeftButtonClick = {
            onUiEvent(PersonalDataEditUiEvent.OnBackClick)
        },
        onRightButtonClick = {
            onUiEvent(PersonalDataEditUiEvent.OnTwilioClick)
        },
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(MaterialTheme.colorScheme.surface)
                .padding(horizontal = MaterialTheme.customDimens.dimen32)
        ) {
            Spacer24()
            SimpleElevatedTextField(
                hasError = uiState.errorList.firstOrNull()?.let { !it } ?: false,
                error = stringResource(R.string.document_name_error),
                label = stringResource(R.string.document_names_label),
                value = uiState.name,
                decorationType = FieldDecorationType.OUTLINED,
                onValueChange = {
                    onUiEvent(PersonalDataEditUiEvent.OnNameChange(it))
                }
            )
            Spacer24()
            SimpleElevatedTextField(
                hasError = uiState.errorList.getOrNull(ONE_VALUE)?.let { !it } ?: false,
                error = stringResource(R.string.document_name_error),
                label = stringResource(R.string.document_lastname_label),
                value = uiState.lastName,
                decorationType = FieldDecorationType.OUTLINED,
                onValueChange = {
                    onUiEvent(PersonalDataEditUiEvent.OnLastNameChange(it))
                }
            )
            Spacer24()
            SimpleElevatedTextField(
                visualTransformation = MaskVisualTransformation(
                    mask = MaskVisualTransformation.DUI_WITH_DASH_MASK
                ),
                hasError = uiState.errorList.lastOrNull()?.not()
                    ?: (uiState.duiError != DuiError.None),
                error = when (uiState.duiError) {
                    DuiError.InvalidCharacters -> stringResource(R.string.document_dui_error)
                    DuiError.Incomplete -> stringResource(R.string.document_dui_incomplete_error)
                    else -> stringResource(R.string.document_name_error)
                },
                label = stringResource(R.string.dui_label),
                value = uiState.dui,
                decorationType = FieldDecorationType.OUTLINED,
                onValueChange = {
                    onUiEvent(PersonalDataEditUiEvent.OnDuiChange(it))
                }
            )
            Spacer1f()
            OneButton(
                modifier = Modifier.fillMaxWidth(),
                enabled = uiState.isSaveEnabled,
                text = stringResource(R.string.document_save_data),
                onClick = {
                    onUiEvent(PersonalDataEditUiEvent.OnSaveChanges)
                }
            )
            Spacer16()
            OneButton(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(R.string.cancel),
                buttonVariant = ButtonVariant.SECONDARY_VARIANT,
                onClick = {
                    onUiEvent(PersonalDataEditUiEvent.OnCancelChanges)
                }
            )
            Spacer16()
        }
    }
}

@Composable
@Preview
fun PersonalDataEditScreenPreview() {
    OneAppTheme {
        PersonalDataEditContent(
            PersonalDataEditUiState()
        ) {}
    }
}