package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_activation.success

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.CommonSuccessScreen
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.OneBackHandler

@Composable
fun CardActivationSuccessScreen(viewModel: CardActivationSuccessViewModel) {
    OneBackHandler()
    CommonSuccessScreen(
        title = stringResource(id = R.string.general_cool),
        description = buildAnnotatedString {
            append(stringResource(id = R.string.your_card_is_active))
        },
        descriptionStyle = MaterialTheme.typography.titleMedium.copy(
            fontWeight = FontWeight.Bold
        ),
        buttonText = stringResource(id = R.string.ok_message),
        onButtonClicked = { viewModel.onEvent(CardActivationSuccessUiEvent.OnContinueClick) }
    )
}
