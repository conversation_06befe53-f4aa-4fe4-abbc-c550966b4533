package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.card_cancel.confirmation

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import javax.inject.Inject

@HiltViewModel
class CardCancelConfirmationViewModel @Inject constructor(savedStateHandle: SavedStateHandle) :
    BaseViewModel() {
    private val args = CardCancelConfirmationFragmentArgs.fromSavedStateHandle(savedStateHandle)
    private val _uiState = MutableStateFlow(CardCancelConfirmationUiState())
    val uiState = _uiState.asStateFlow()

    private fun start() {
        _uiState.update {
            it.copy(
                expirationDate = args.card?.expirationDate.orEmpty(),
                cardMask = args.card?.cardNumMasked.orEmpty(),
                isMainCard = args.card?.mainCard ?: false
            )
        }
    }

    private fun cancelCreditCard() {
        sendEvent(
            UiEvent.Navigate(
                CardCancelConfirmationFragmentDirections
                    .navigateToCardCancelPreparePictureFragment(
                        card = args.card,
                        reason = args.reason,
                        comments = args.comments
                    )
            )
        )
    }

    fun onEvent(event: CardCancelConfirmationUiEvent) {
        when (event) {
            CardCancelConfirmationUiEvent.OnStart -> start()
            CardCancelConfirmationUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            CardCancelConfirmationUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            CardCancelConfirmationUiEvent.OnNotKnowClick ->
                sendEvent(UiEvent.NavigateBackTo(R.id.navigation_menu_cancel_cards))

            CardCancelConfirmationUiEvent.OnCardCancelClick -> cancelCreditCard()
        }
    }
}