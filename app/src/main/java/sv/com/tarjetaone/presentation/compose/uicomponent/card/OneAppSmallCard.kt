package sv.com.tarjetaone.presentation.compose.uicomponent.card

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import sv.com.tarjetaone.R

/**
 * Reusable card alert component, please consider modifying it to desired needs.
 * E.g. Color, icon, text, etc.
 */
@Composable
fun OneAppSmallCardAlert(modifier: Modifier = Modifier) {
    Image(
        modifier = modifier
            .width(49.dp)
            .height(71.dp),
        painter = painterResource(id = R.drawable.ic_icon_alert),
        contentDescription = null,
    )
}

@Preview
@Composable
fun OneAppSmallCardAlertPreview() {
    OneAppSmallCardAlert()
}
