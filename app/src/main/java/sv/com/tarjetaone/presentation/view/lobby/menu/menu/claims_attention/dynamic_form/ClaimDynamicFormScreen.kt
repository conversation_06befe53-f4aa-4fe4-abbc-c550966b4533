package sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.dynamic_form

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.SideEffectHandler
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.FieldDecorationType
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.dynamic_form.component.ClaimDisclaimer
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.dynamic_form.component.ClaimVariableField
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.dynamic_form.util.VariableFieldDummyUiStates

@Composable
fun ClaimDynamicFormScreen(
    viewModel: ClaimDynamicFormViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LaunchedEffect(Unit) {
        viewModel.onEvent(ClaimDynamicFormUiEvent.OnStart)
    }

    SideEffectHandler(viewModel.sideEffects) {
        ClaimDynamicFormScreenContent(
            uiState = uiState,
            onEvent = viewModel::onEvent
        )
    }
}

@Composable
fun ClaimDynamicFormScreenContent(
    uiState: ClaimDynamicFormUiState,
    onEvent: (ClaimDynamicFormUiEvent) -> Unit = {}
) {
    ScreenWithTopAppBar(
        title = stringResource(R.string.claims_attention),
        onLeftButtonClick = { onEvent(ClaimDynamicFormUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(ClaimDynamicFormUiEvent.OnSupportClick) },
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = MaterialTheme.customDimens.dimen32)
                .imePadding()
        ) {
            LazyColumn(
                modifier = Modifier.weight(ONE_FLOAT_VALUE),
                verticalArrangement = Arrangement.spacedBy(
                    MaterialTheme.customDimens.dimen16
                )
            ) {
                item {
                    SimpleElevatedTextField(
                        value = uiState.claimOptionName,
                        onValueChange = {},
                        label = stringResource(id = R.string.selected_option),
                        decorationType = FieldDecorationType.OUTLINED,
                        readOnly = true
                    )
                }
                items(uiState.variableFields.size) {
                    ClaimVariableField(
                        fieldState = uiState.variableFields[it],
                        onTypedValueChange = { typedValue ->
                            onEvent(
                                ClaimDynamicFormUiEvent.OnVariableFieldChange(
                                    index = it,
                                    typedValue = typedValue,
                                    selectedValue = uiState.variableFields[it].selectedValue
                                )
                            )
                        },
                        onSelectedValueChange = { selectedValue ->
                            onEvent(
                                ClaimDynamicFormUiEvent.OnVariableFieldChange(
                                    index = it,
                                    typedValue = uiState.variableFields[it].typedValue,
                                    selectedValue = uiState.variableFields[it].selectedValue
                                )
                            )
                        }
                    )
                }
                item {
                    SimpleElevatedTextField(
                        value = uiState.claimDescription,
                        placeholder = stringResource(id = R.string.claim_description_hint),
                        onValueChange = {
                            onEvent(ClaimDynamicFormUiEvent.OnDescriptionChange(it))
                        },
                        label = stringResource(id = R.string.claim_description),
                        decorationType = FieldDecorationType.OUTLINED,
                        singleLine = false,
                        modifier = Modifier.height(MaterialTheme.customDimens.dimen250)
                    )
                }
                uiState.usageInfo?.let {
                    item { ClaimDisclaimer(text = it) }
                }
            }
            Spacer16()
            OneButton(
                text = stringResource(id = R.string.claim_send),
                onClick = { onEvent(ClaimDynamicFormUiEvent.OnSendClick) },
                enabled = uiState.canSend,
                modifier = Modifier.fillMaxWidth()
            )
            Spacer16()
            OneButton(
                text = stringResource(id = R.string.cancel),
                onClick = { onEvent(ClaimDynamicFormUiEvent.OnCancelClick) },
                modifier = Modifier.fillMaxWidth(),
                buttonVariant = ButtonVariant.SECONDARY_VARIANT
            )
            Spacer16()
        }
    }
}

@Preview
@Composable
private fun ClaimDynamicFormScreenPreview() {
    OneAppTheme {
        ClaimDynamicFormScreenContent(
            uiState = ClaimDynamicFormUiState(
                claimOptionName = "Gestion de cobro",
                variableFields = remember {
                    mutableStateListOf(
                        VariableFieldDummyUiStates.typedEmail,
                        VariableFieldDummyUiStates.selectableMonth
                    )
                },
                usageInfo = "Uso del reclamo"
            ),
            onEvent = {}
        )
    }
}
