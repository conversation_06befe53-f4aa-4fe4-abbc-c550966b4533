package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.report.report_purchase

import androidx.compose.runtime.Stable
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.domain.entities.response.TransactionsUI
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.DynamicDialogButtonParam

@Stable
data class ReportPurchaseUIState(
    val cardId: Int = ZERO_VALUE,
    val transaction: TransactionsUI = TransactionsUI(installmentTerms = listOf()),
    val reportDialogList: List<DynamicDialogButtonParam> = listOf(),
    val dynamicDialogVisibility: Boolean = false
)