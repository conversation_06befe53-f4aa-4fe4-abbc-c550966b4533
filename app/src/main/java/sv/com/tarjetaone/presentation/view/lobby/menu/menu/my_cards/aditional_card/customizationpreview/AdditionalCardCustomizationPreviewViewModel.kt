package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.customizationpreview

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.common.utils.AppConstants.DAY_OF_WEEK_DAY_MONTH
import sv.com.tarjetaone.common.utils.CardColor
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.presentation.view.utils.parseDateByString
import javax.inject.Inject

@HiltViewModel
class AdditionalCardCustomizationPreviewViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val sharedPrefsRepo: SecureSharedPreferencesRepository
) : BaseViewModel() {

    private val args =
        AdditionalCardCustomizationPreviewFragmentArgs.fromSavedStateHandle(savedStateHandle)

    private val _uiState = MutableStateFlow(AdditionalCardCustomizationPreviewUiState())
    val uiState = _uiState.asStateFlow()

    private fun onStart() {
        _uiState.update { state ->
            state.copy(
                cardColors = args.cardColor ?: CardColor(),
                nameOnCard = args.embossedName,
                deliveryDate = parseDateByString(
                    inputDate = sharedPrefsRepo.userAdditionalCardData?.day.orEmpty(),
                    outputFormatString = DAY_OF_WEEK_DAY_MONTH
                )
            )
        }
    }

    private fun onSendCardAndFinish() {
        sendEvent(
            UiEvent.Navigate(
                AdditionalCardCustomizationPreviewFragmentDirections
                    .actionFinishPersonalizationTcFragmentToPrepareForPicture()
            )
        )
    }

    fun onEvent(event: AdditionalCardCustomizationPreviewUiEvent) {
        when (event) {
            AdditionalCardCustomizationPreviewUiEvent.OnStart -> onStart()
            AdditionalCardCustomizationPreviewUiEvent.OnRetryCardCustomizationClick -> sendEvent(UiEvent.NavigateBack)
            AdditionalCardCustomizationPreviewUiEvent.OnFinishAndSendCardClick -> onSendCardAndFinish()
        }
    }
}
