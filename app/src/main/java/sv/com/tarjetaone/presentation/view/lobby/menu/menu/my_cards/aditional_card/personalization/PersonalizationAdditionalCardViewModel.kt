package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.personalization

import androidx.lifecycle.viewModelScope
import com.facephi.fphiselphidwidgetcore.WidgetSelphIDResult
import com.google.gson.Gson
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.analytics.amplitude.AmplitudeEventProperties.CARD_SELECTED_COLOR
import sv.com.tarjetaone.analytics.amplitude.AmplitudeEvents.ON_SELECT_COLOR_EVENT
import sv.com.tarjetaone.analytics.amplitude.AmplitudeManager
import sv.com.tarjetaone.common.utils.AppConstants.BLANK_SPACE
import sv.com.tarjetaone.common.utils.AppConstants.TWENTY_ONE_VALUE
import sv.com.tarjetaone.common.utils.CardColor
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.data.api.models.ColorCardCatalog
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.request.CardData
import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI
import sv.com.tarjetaone.domain.entities.response.CatalogType
import sv.com.tarjetaone.domain.usecases.catalog.GetCatalogUseCase
import sv.com.tarjetaone.domain.usecases.requestcard.personalizeadditionalcard.GetAdditionalCardLimitUseCase
import sv.com.tarjetaone.domain.utils.onSuccess
import sv.com.tarjetaone.presentation.view.utils.NamesCard
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.DialogAction
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.MessageParams
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.OneDialogParams
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.helpers.capitalizeAllWords
import sv.com.tarjetaone.presentation.helpers.dropLeadingZeros
import sv.com.tarjetaone.presentation.helpers.filterDigits
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.personalization.PersonalizationAdditionalCardUiEvent.OnCardColorChange
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.personalization.PersonalizationAdditionalCardUiEvent.OnCardLimitChange
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.personalization.PersonalizationAdditionalCardUiEvent.OnDocumentCapture
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.personalization.PersonalizationAdditionalCardUiEvent.OnNameSelected
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.personalization.PersonalizationAdditionalCardUiEvent.OnNavigateBack
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.personalization.PersonalizationAdditionalCardUiEvent.OnResetState
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.personalization.PersonalizationAdditionalCardUiEvent.OnSavePersonalizationCard
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.personalization.PersonalizationAdditionalCardUiEvent.OnSliderProgressChange
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.personalization.PersonalizationAdditionalCardUiEvent.OnSliderProgressChangeFinished
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.personalization.PersonalizationAdditionalCardUiEvent.OnStart
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.personalization.PersonalizationAdditionalCardUiEvent.OnTwilioButtonClick
import javax.inject.Inject

@HiltViewModel
class PersonalizationAdditionalCardViewModel @Inject constructor(
    private val getAdditionalCardLimitUseCase: GetAdditionalCardLimitUseCase,
    private val getCatalogUseCase: GetCatalogUseCase,
    private val facephiResultHandler: FacephiResultHandler,
    private val sharedPrefsRepo: SecureSharedPreferencesRepository,
    private val amplitudeManager: AmplitudeManager,
    private val gson: Gson
) : BaseViewModel() {

    private val _uiState: MutableStateFlow<PersonalizationAdditionalCardUiState> =
        MutableStateFlow(PersonalizationAdditionalCardUiState())
    val uiState: StateFlow<PersonalizationAdditionalCardUiState> = _uiState.asStateFlow()

    private fun onStart() {
        sendEvent(SideEffect.Loading(true))
        viewModelScope.launch {
            val cardColorsCatalogDeferred = async {
                getCatalogUseCase(CatalogType.GC_CARD_COLORS).onSuccess {
                    val colors = catalogToColorCard(
                        it.dataCatalog?.catalogItemsCollection.orEmpty()
                    )
                    _uiState.update { state ->
                        state.copy(
                            colors = colors,
                            selectedColor = colors.firstOrNull()
                        )
                    }
                }
            }
            val cardLimitDeferred = async {
                getAdditionalCardLimitUseCase(sharedPrefsRepo.getCustomerId().orZero())
                    .onSuccess {
                        setCardLimits(
                            minLimit = it.lowerLimit.orZero(),
                            maxLimit = it.upperLimit.orZero()
                        )
                    }
            }
            awaitAll(cardColorsCatalogDeferred, cardLimitDeferred).any {
                it.handleErrors()
            }
            fillNameListSelector()
            sendEvent(SideEffect.Loading(false))
        }
    }

    private fun resetState() {
        _uiState.update { PersonalizationAdditionalCardUiState() }
    }

    private fun setCardLimits(minLimit: Double, maxLimit: Double) {
        _uiState.update {
            it.copy(
                sliderProgress = minLimit.toInt(),
                minLimit = minLimit,
                maxLimit = maxLimit,
                cardLimit = minLimit.toInt().toString(),
                isValidAmount = validateCardLimit(
                    amount = minLimit.toInt(),
                    minLimit = minLimit,
                    maxLimit = maxLimit
                ),
                errorMessage = getErrorMessage(
                    amount = minLimit.toInt(),
                    minLimit = minLimit,
                    maxLimit = maxLimit
                )
            )
        }
    }

    private fun onSliderProgressChange(sliderProgress: Int) {
        _uiState.update {
            it.copy(
                sliderProgress = sliderProgress,
                errorMessage = getErrorMessage(sliderProgress, it.minLimit, it.maxLimit)
            )
        }
    }

    private fun onCardLimitChange(cardLimit: String) {
        val cardLimitStr = cardLimit.filterDigits().dropLeadingZeros()
        val cardLimitValue = cardLimitStr.toIntOrNull().orZero()

        _uiState.update {
            it.copy(
                sliderProgress = cardLimitValue / it.stepSize,
                cardLimit = cardLimitStr,
                isValidAmount = validateCardLimit(cardLimitValue, it.minLimit, it.maxLimit),
                errorMessage = getErrorMessage(cardLimitValue, it.minLimit, it.maxLimit)
            )
        }
    }

    private fun onSliderProgressChangeFinished() {
        _uiState.update {
            val cardLimit = it.sliderProgress.times(it.stepSize)
            it.copy(
                sliderProgress = it.sliderProgress,
                cardLimit = cardLimit.toString(),
                isValidAmount = validateCardLimit(cardLimit, it.minLimit, it.maxLimit),
                errorMessage = getErrorMessage(cardLimit, it.minLimit, it.maxLimit)
            )
        }
    }

    private fun onCardColorChange(color: CardColor) {
        _uiState.update { it.copy(selectedColor = color) }
    }

    private fun validateCardLimit(
        amount: Int,
        minLimit: Double,
        maxLimit: Double
    ): Boolean = amount <= maxLimit && amount >= minLimit

    private fun getErrorMessage(
        amount: Int,
        minLimit: Double,
        maxLimit: Double
    ): UiText? = when {
        amount > maxLimit -> UiText.StringResource(R.string.card_max_limit_error)
        amount < minLimit -> UiText.StringResource(R.string.card_min_limit_error)
        else -> null
    }

    private fun fillNameListSelector() {
        val customerData = sharedPrefsRepo.userAdditionalCardData?.customer

        customerData?.let { customer ->
            _uiState.update { state ->
                state.copy(
                    customerFirstNames = customer.name.split(BLANK_SPACE)
                        .filter { it.isNotBlank() },
                    customerLastNames = listOf(
                        customer.lastName,
                        customer.marriedName.orEmpty()
                    ).flatMap { it.split(BLANK_SPACE) }.filter { it.isNotBlank() }
                )
            }
            val customerNames = _uiState.value.customerFirstNames
            val customerLastNames = _uiState.value.customerLastNames
            val fullName = (customerNames + customerLastNames).joinToString(BLANK_SPACE)

            if (fullName.isNotBlank()) {
                _uiState.update { state ->
                    state.copy(
                        customerNames = (customerNames + customerLastNames)
                            .map { NamesCard(it.capitalizeAllWords(), false) }
                    )
                }
            }
        }
    }

    private fun catalogToColorCard(catalog: List<CatalogItemsCollectionUI>): List<CardColor> {
        return catalog.mapNotNull { item ->
            gson.fromJson(item.behaviourInfo, ColorCardCatalog::class.java)?.let {
                CardColor(
                    id = item.id,
                    textColor = it.textColor,
                    iconColor = it.visaColor,
                    colors = it.colors.map { color -> color.color }
                )
            }
        }
    }

    private fun savePersonalizationCardData() {
        sharedPrefsRepo.userAdditionalCardData?.let {
            sharedPrefsRepo.userAdditionalCardData = it.copy(
                customer = it.customer.copy(
                    card = CardData(
                        cardColorId = uiState.value.selectedColor?.id?.toIntOrNull().orZero(),
                        cardLimit = uiState.value.cardLimit.toDoubleOrNull().orZero(),
                        embossedName = uiState.value.nameOnCard
                    )
                )
            )
        }

        with(uiState.value) {
            amplitudeManager.track(
                type = ON_SELECT_COLOR_EVENT,
                properties = mapOf(CARD_SELECTED_COLOR to selectedColor?.textColor)
            )
            sendEvent(
                UiEvent.Navigate(
                    PersonalizationAdditionalCardFragmentDirections
                        .actionReadabilityAditionalPersonalizationToNavigationMenuPersonalizacionTcFinish(
                            cardColor = selectedColor,
                            embossedName = nameOnCard,
                        )
                )
            )
        }
    }

    private fun onNameSelected(index: Int) {
        setNameSelectedAt(index)

        val selectedNames = _uiState.value.customerNames
            .filter { it.selected }
            .map { it.name }

        val fullName = selectedNames.joinToString(BLANK_SPACE).trim()
        val validName = listOf(
            _uiState.value.customerFirstNames,
            _uiState.value.customerLastNames
        ).all { names ->
            names.any { fullName.contains(it, true) }
        }

        _uiState.update {
            it.copy(
                nameOnCard = fullName,
                animateToBack = fullName.isNotBlank(),
                isValidNameSelection = validName
            )
        }
    }

    private fun setNameSelectedAt(index: Int) {
        if (!validateLengthName(index)) return

        _uiState.update {
            it.copy(
                customerNames = _uiState.value.customerNames.mapIndexed { position, nameCard ->
                    if (position == index)
                        nameCard.copy(selected = nameCard.selected.not())
                    else nameCard
                }
            )
        }
    }

    private fun validateLengthName(index: Int): Boolean {
        val updatedNameCard = with(_uiState.value) {
            val selectedName = customerNames[index].name

            if (customerNames[index].selected) {
                nameOnCard
            } else {
                nameOnCard.takeIf { it.isNotBlank() }
                    ?.let { it + BLANK_SPACE + selectedName }
                    ?: selectedName
            }
        }

        return if (updatedNameCard.length > TWENTY_ONE_VALUE) {
            notifyInvalidNameSelection()
            false
        } else {
            true
        }
    }

    private fun notifyInvalidNameSelection() {
        _uiState.update { it.copy(isValidNameSelection = false) }
        sendEvent(
            SideEffect.ShowToast(
                UiText.StringResource(R.string.personalization_additional_card_invalid_length_name_error)
            )
        )
    }

    private fun onDocumentCaptured(result: WidgetSelphIDResult) {
        val event = facephiResultHandler.onDocumentCaptured(result)?.let {
            UiEvent.Navigate(
                PersonalizationAdditionalCardFragmentDirections
                    .actionReadabilityAditionalPersonalizationToAdditionalCardDocumentPreviewFragment(
                        isAdditionalCard = true,
                        shouldSkipAdditionalInformation = true
                    )
            )
        } ?: SideEffect.ShowOneDialog(
            params = OneDialogParams(
                icon = R.drawable.ic_error_yellow,
                title = UiText.StringResource(R.string.error_doc_picture_title),
                message = MessageParams(
                    text = UiText.StringResource(R.string.error_doc_picture_desc),
                ),
                primaryAction = DialogAction(
                    text = UiText.StringResource(R.string.take_picture),
                    actionType = DialogAction.ActionType.WARNING
                ),
                isDismissible = false
            )
        )

        sendEvent(event)
    }

    fun onEvent(event: PersonalizationAdditionalCardUiEvent) {
        when (event) {
            OnStart -> onStart()
            OnResetState -> resetState()
            OnNavigateBack -> sendEvent(UiEvent.NavigateBack)
            OnTwilioButtonClick -> sendEvent(UiEvent.TwilioClick)
            OnSliderProgressChangeFinished -> onSliderProgressChangeFinished()
            OnSavePersonalizationCard -> savePersonalizationCardData()
            is OnCardColorChange -> onCardColorChange(event.cardColor)
            is OnCardLimitChange -> onCardLimitChange(event.cardLimit)
            is OnSliderProgressChange -> onSliderProgressChange(event.sliderProgress)
            is OnDocumentCapture -> onDocumentCaptured(event.result)
            is OnNameSelected -> onNameSelected(event.index)
        }
    }
}
