package sv.com.tarjetaone.presentation.view.lobby.menu.menu.claims_attention.options

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer16

@Composable
fun OptionsClaimScreen(viewModel: OptionsClaimViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    OptionsClaimContent(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@Composable
fun OptionsClaimContent(
    uiState: OptionsClaimUiState,
    onEvent: (OptionsClaimUiEvent) -> Unit
) {
    LaunchedEffect(Unit) {
        onEvent(OptionsClaimUiEvent.OnStart)
    }
    ScreenWithTopAppBar(
        contentPadding = PaddingValues(
            start = MaterialTheme.customDimens.dimen32,
            end = MaterialTheme.customDimens.dimen32,
            top = MaterialTheme.customDimens.dimen32,
            bottom = MaterialTheme.customDimens.dimen8
        ),
        title = uiState.claimName,
        onLeftButtonClick = { onEvent(OptionsClaimUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(OptionsClaimUiEvent.OnSupportClick) },
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = MaterialTheme.customDimens.dimen32)
        ) {
            Text(
                text = stringResource(id = R.string.select_claim_option),
                style = MaterialTheme.typography.bodySmall
            )
            Spacer16()
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen16),
                contentPadding = PaddingValues(vertical = MaterialTheme.customDimens.dimen16),
                modifier = Modifier.fillMaxSize()
            ) {
                items(items = uiState.claimOptions) {
                    OneButton(
                        text = it.name.orEmpty(),
                        buttonVariant = ButtonVariant.TERTIARY_VARIANT,
                        onClick = { onEvent(OptionsClaimUiEvent.OnOptionClick(it)) },
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun OptionsClaimScreenPreview() {
    OneAppTheme {
        OptionsClaimContent(
            uiState = OptionsClaimUiState(
                claimName = "Funcionamiento de mi tarjeta",
                claimOptions = listOf(
                    CatalogItemsCollectionUI(
                        id = "1",
                        name = "Transaccion flotante",
                        isActive = true,
                        parentId = null,
                        behaviourInfo = null
                    ),
                    CatalogItemsCollectionUI(
                        id = "1",
                        name = "Gestion de cobro",
                        isActive = true,
                        parentId = null,
                        behaviourInfo = null
                    )
                )
            ),
            onEvent = { }
        )
    }
}
