package sv.com.tarjetaone.presentation.view.lobby.menu.dataedit

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.AppConstants.NINE_VALUE
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.entities.request.StringsCoincidenceListRequest
import sv.com.tarjetaone.domain.entities.request.StringsCoincidenceRequest
import sv.com.tarjetaone.domain.usecases.validatacoincidence.ValidateStringsCoincidenceUseCase
import sv.com.tarjetaone.presentation.helpers.removeDash
import javax.inject.Inject

@HiltViewModel
class PersonalDataEditViewModel @Inject constructor(
    private val validateStringsCoincidenceUseCase: ValidateStringsCoincidenceUseCase
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(PersonalDataEditUiState())
    val uiState = _uiState.asStateFlow()

    private fun onStart() {
        _uiState.update {
            it.copy(
                name = baseSharedPrefs.userAdditionalCardData?.customer?.name.orEmpty(),
                lastName = baseSharedPrefs.userAdditionalCardData?.customer?.lastName.orEmpty(),
                dui = baseSharedPrefs.userAdditionalCardData?.customer?.documents?.document.orEmpty()
            )
        }
    }

    private fun onNameChange(name: String) {
        _uiState.update {
            it.copy(
                name = name.uppercase()
            )
        }
    }

    private fun onLastNameChange(lastName: String) {
        _uiState.update {
            it.copy(
                lastName = lastName.uppercase()
            )
        }
    }

    private fun onDuiChange(dui: String) {
        val error = validateDui(dui = dui)
        _uiState.update {
            it.copy(
                dui = dui,
                duiError = error,
                isSaveEnabled = error == DuiError.None
            )
        }
    }

    private fun validateDui(dui: String): DuiError {
        return when {
            dui.isBlank() || dui.length < NINE_VALUE -> DuiError.Incomplete
            dui.any { !it.isDigit() } -> DuiError.InvalidCharacters
            else -> DuiError.None
        }
    }

    private fun onSaveChanges() {
        onValidateStringsCoincidence()
    }

    private fun onValidateStringsCoincidence() {
        sendEvent(
            UiEvent.Loading(
                isLoading = true
            )
        )
        viewModelScope.launch {
            validateStringsCoincidenceUseCase(
                request = StringsCoincidenceListRequest(
                    stringCoincidenceList = listOf(
                        StringsCoincidenceRequest(
                            baseString = baseSharedPrefs.getOcr()?.firstName.orEmpty(),
                            editedString = uiState.value.name
                        ), StringsCoincidenceRequest(
                            baseString = baseSharedPrefs.getOcr()?.lastName.orEmpty(),
                            editedString = uiState.value.lastName
                        ), StringsCoincidenceRequest(
                            baseString = baseSharedPrefs.getOcr()?.frontMLDocumentNumber?.removeDash()
                                .orEmpty(),
                            editedString = uiState.value.dui.removeDash()
                        )
                    )
                )
            ).executeUseCase { response ->
                sendEvent(UiEvent.Loading(false))
                if (!response.stringsCoincidenceResponses.contains(
                        false
                    )
                ) {
                    baseSharedPrefs.userAdditionalCardData?.let { userAdditionalCard ->
                        baseSharedPrefs.userAdditionalCardData = userAdditionalCard.copy(
                            customer = userAdditionalCard.customer.copy(
                                name = uiState.value.name,
                                lastName = uiState.value.lastName,
                                documents = userAdditionalCard.customer.documents.copy(
                                    document = uiState.value.dui.removeDash()
                                )
                            )
                        )
                    }
                    sendEvent(UiEvent.NavigateBack)
                } else {
                    _uiState.update {
                        it.copy(
                            errorList = response.stringsCoincidenceResponses
                        )
                    }
                }
            }
        }
    }

    private fun onCancelChanges() {
        sendEvent(UiEvent.NavigateBack)
    }

    private fun onBackCLick() {
        sendEvent(UiEvent.NavigateBack)
    }

    private fun onTwilioCLick() {
        sendEvent(UiEvent.TwilioClick)
    }

    fun onUiEvent(event: PersonalDataEditUiEvent) {
        when (event) {
            is PersonalDataEditUiEvent.OnStart -> onStart()
            is PersonalDataEditUiEvent.OnNameChange -> onNameChange(event.value)
            is PersonalDataEditUiEvent.OnLastNameChange -> onLastNameChange(event.value)
            is PersonalDataEditUiEvent.OnDuiChange -> onDuiChange(event.value)
            PersonalDataEditUiEvent.OnSaveChanges -> onSaveChanges()
            PersonalDataEditUiEvent.OnCancelChanges -> onCancelChanges()
            PersonalDataEditUiEvent.OnBackClick -> onBackCLick()
            PersonalDataEditUiEvent.OnTwilioClick -> onTwilioCLick()
        }
    }
}