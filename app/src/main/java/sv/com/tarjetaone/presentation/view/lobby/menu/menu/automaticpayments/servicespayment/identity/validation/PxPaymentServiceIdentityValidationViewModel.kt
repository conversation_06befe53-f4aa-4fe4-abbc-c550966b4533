package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.identity.validation

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.CommonDialogWithIconParams
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.ErrorResponse
import sv.com.tarjetaone.domain.entities.request.automaticspayment.servicespayment.paymentdetail.PxPaymentServiceRequestUI
import sv.com.tarjetaone.domain.usecases.auth.FaceAuthenticationUseCase
import sv.com.tarjetaone.domain.usecases.automaticpayments.servicespayment.paymentdetail.PxPaymentServiceUseCase
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.common.identity_validation.IdentityValidationBaseViewModel

@HiltViewModel
class PxPaymentServiceIdentityValidationViewModel @Inject constructor(
    faceAuthenticationUseCase: FaceAuthenticationUseCase,
    sharedPrefs: SecureSharedPreferencesRepository,
    private val pxPaymentServiceUseCase: PxPaymentServiceUseCase,
    savedStateHandle: SavedStateHandle,
) : IdentityValidationBaseViewModel(faceAuthenticationUseCase, sharedPrefs) {

    private val args = PxPaymentServiceIdentityValidationFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun operation(biometryId: Int) {
        sendPxPaymentServiceRequest(biometryId)
    }

    private fun sendPxPaymentServiceRequest(biometryId: Int) {
        sendEvent(UiEvent.Loading(true))
        viewModelScope.launch {
            pxPaymentServiceUseCase(setupPxRequest(biometryId))
                .executeUseCase(
                    onApiErrorAction = { _, error, _, _ ->
                        showPxPaymentErrorMessage(error)
                    },
                    onSuccessAction = {
                        sendEvent(UiEvent.Loading(false))
                        sendEvent(
                            UiEvent.Navigate(
                                PxPaymentServiceIdentityValidationFragmentDirections
                                    .navigateToPxPaymentServiceDetailFragment(
                                        pxPaymentServiceDetail = it,
                                        paymentMethodType = args.pxPaymentInformation.paymentMethodType,
                                        selectedPaymentMethod = args.pxPaymentInformation.selectedPaymentMethod,
                                        isFromScanning = args.pxPaymentInformation.isFromScanning
                                    )
                            )
                        )
                    }
                )
        }
    }

    private fun setupPxRequest(biometryId: Int): PxPaymentServiceRequestUI {
        return PxPaymentServiceRequestUI(
            requestData = args.pxPaymentInformation.requestData,
            transactionNumber = args.pxPaymentInformation.transactionNumber,
            paymentMethodType = args.pxPaymentInformation.paymentMethodType,
            paymentNumber = args.pxPaymentInformation.paymentNumber,
            biometryId = biometryId
        )
    }

    private fun showPxPaymentErrorMessage(errorResponse: ErrorResponse?) {
        sendEvent(UiEvent.Loading(false))
        when (errorResponse?.code?.toInt()) {
            ERROR_NO_FUNDS_AVAILABLE,
            ERROR_INSUFFICIENT_BALANCE,
            ERROR_INVALID_AMOUNT,
            ERROR_PAYMENT_NOT_ALLOWED -> {
                sendEvent(
                    UiEvent.ShowCommonDialog(
                        CommonDialogWithIconParams(
                            isDismissible = false,
                            icon = R.drawable.ic_error_red_light,
                            title = UiText.StringResource(R.string.payment_method_error_title),
                            message = UiText.DynamicString(errorResponse.result.orEmpty()),
                            buttonText = UiText.StringResource(R.string.payment_method_change_payment_method),
                            secondaryActionVisible = true,
                            secondaryActionText = UiText.StringResource(R.string.back_to_my_account_hyperlink),
                            buttonColor = R.color.error_red,
                            primaryActionTextSize = 13f,
                            onButtonClick = {
                                sendEvent(
                                    UiEvent.NavigateBackTo(
                                        R.id.invoiceSelectionFragment,
                                        inclusive = false
                                    )
                                )
                            },
                            onSecondaryActionClick = {
                                sendEvent(
                                    UiEvent.NavigateBackTo(
                                        R.id.servicePaymentHomeFragment,
                                        inclusive = false
                                    )
                                )
                            }
                        )
                    )
                )
            }

            else -> {
                showUpsErrorMessage(isDismissible = false) {
                    sendEvent(
                        UiEvent.NavigateBackTo(
                            R.id.invoiceSelectionFragment,
                            inclusive = false
                        )
                    )
                }
            }
        }
    }

    override fun onFailedIdentityValidation(resultCode: String?) {
        sendEvent(UiEvent.NavigateBack)
    }

    companion object {
        // PxPayment error codes
        const val ERROR_NO_FUNDS_AVAILABLE = 101
        const val ERROR_INSUFFICIENT_BALANCE = 102
        const val ERROR_INVALID_AMOUNT = 103
        const val ERROR_PAYMENT_NOT_ALLOWED = 104
    }
}
