package sv.com.tarjetaone.presentation.view.common.contracts

import android.net.Uri
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavDirections
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.helpers.CryptoHelper
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.files.FileUtils
import sv.com.tarjetaone.domain.entities.response.ContractsMode
import sv.com.tarjetaone.domain.entities.response.DataContractUI
import sv.com.tarjetaone.domain.entities.response.DataDocumentContractUI
import sv.com.tarjetaone.domain.usecases.contracts.GetContractsUseCase

abstract class BaseContractsViewModel(
    private val getContractsUseCase: GetContractsUseCase,
    private val fileUtils: FileUtils,
    private val cryptoHelper: CryptoHelper
) : BaseViewModel() {

    protected val _uiState = MutableStateFlow(ContractListUiState())
    val uiState: StateFlow<ContractListUiState> = _uiState.asStateFlow()

    protected abstract fun getSeeContractDestination(): NavDirections
    protected abstract fun onStart()
    protected abstract fun onPrimaryClick()
    protected abstract fun onAllContractsChecked()

    protected fun getContracts(mode: ContractsMode, queryId: Int) {
        sendEvent(SideEffect.Loading(true))
        viewModelScope.launch {
            getContractsUseCase(
                mode = mode,
                queryId = queryId
            ).executeUseCase {
                sendEvent(SideEffect.Loading(false))
                onSuccessGetContracts(mode, it)
            }
        }
    }

    private fun getContractToRead(documentId: Int) {
        _uiState.update { it.copy(contractIdSelected = documentId) }
        getContracts(
            mode = ContractsMode.DOCUMENT,
            queryId = documentId
        )
    }

    private suspend fun getFileUri(file: DataContractUI): Uri? {
        val fileName = "${file.documentID}_${file.documentTypeNameApp}"
        fileUtils.getUriIfExists(fileName, true)?.let { return it }

        val fileContent = cryptoHelper.decryptBASE64Async(file.document.orEmpty())

        return fileUtils.saveTempPdfFileAsync(
            fileName = fileName,
            fileContent = fileContent,
            useFileScheme = true
        )
    }

    /**
     * Action to perform when the contracts are successfully retrieved
     */
    private suspend fun onSuccessGetContracts(
        mode: ContractsMode,
        response: DataDocumentContractUI
    ) {
        when (mode) {
            ContractsMode.ONBOARDING -> {
                _uiState.update {
                    it.copy(
                        contracts = response.documents.orEmpty(),
                        isContractsDataLoaded = true
                    )
                }
            }

            ContractsMode.ENVELOP -> {
                _uiState.update {
                    it.copy(
                        contracts = response.documents.orEmpty() + it.contracts,
                        isContractsDataLoaded = true
                    )
                }
            }

            ContractsMode.DOCUMENT -> {
                response.documents?.find {
                    it.documentID == uiState.value.contractIdSelected
                }?.let {
                    _uiState.update { state ->
                        state.copy(
                            documentUri = getFileUri(it),
                            documentName = it.documentTypeNameApp.orEmpty()
                        )
                    }
                    sendEvent(
                        UiEvent.Navigate(getSeeContractDestination())
                    )
                }
            }

            else -> {
                onDefaultApiError()
            }
        }
    }

    private fun onCheckContract(documentId: Int) {
        val newContracts = _uiState.value.contracts.map {
            if (it.documentID == documentId)
                it.copy(checked = it.checked.not())
            else
                it
        }

        _uiState.update { state ->
            state.copy(
                contracts = newContracts,
                areDocumentsRead = newContracts.all { it.checked }.also {
                    if (it) onAllContractsChecked()
                }
            )
        }
    }

    private fun onCheckAllContracts() {
        onAllContractsChecked()

        val newContracts = _uiState.value.contracts.map {
            it.copy(checked = true)
        }
        _uiState.update { state ->
            state.copy(
                contracts = newContracts,
                areDocumentsRead = true
            )
        }
    }

    protected open fun onBackAction() = Unit

    fun onEvent(event: ContractListUiEvent) {
        when (event) {
            ContractListUiEvent.OnStart -> onStart()
            ContractListUiEvent.OnBackClick -> onBackAction()
            ContractListUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            ContractListUiEvent.OnPrimaryButtonClick -> onPrimaryClick()
            ContractListUiEvent.OnCheckAllDocuments -> onCheckAllContracts()
            is ContractListUiEvent.OnClickReadContract -> getContractToRead(event.documentId)
            is ContractListUiEvent.OnContractChecked -> onCheckContract(event.documentId)
        }
    }
}