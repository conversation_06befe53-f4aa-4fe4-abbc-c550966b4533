package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.installmentpayment.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.SheetState
import androidx.compose.material3.Text
import androidx.compose.material3.rememberStandardBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.datasource.LoremIpsum
import sv.com.tarjetaone.common.utils.AppConstants.TWENTY_ONE_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component.CommonInstallmentBottomSheet

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun InstallmentPaymentBottomSheet(
    bottomSheetState: SheetState,
    onDismiss: () -> Unit,
    content: @Composable () -> Unit,
) {
    CommonInstallmentBottomSheet(
        bottomSheetState = bottomSheetState,
        onDismissRequest = onDismiss,
    ) {
        content()
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Preview
@Composable
private fun InstallmentPaymentBottomSheetPreview() {
    OneAppTheme {
        InstallmentPaymentBottomSheet(
            bottomSheetState = rememberStandardBottomSheetState(),
            onDismiss = {},
        ) {
            Column(
                modifier = Modifier.padding(MaterialTheme.customDimens.dimen16)
            ) {
                Text(
                    text = LoremIpsum(TWENTY_ONE_VALUE).values.first(),
                    color = MaterialTheme.colorScheme.secondary
                )
            }
        }
    }
}
