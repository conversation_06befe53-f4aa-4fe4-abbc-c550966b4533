package sv.com.tarjetaone.presentation.view.lobby.menu.home.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.response.MainCardUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens

@Composable
fun CardDetailsAction(
    modifier: Modifier = Modifier,
    card: MainCardUI,
    onClick: () -> Unit = {}
) {
    var showDetails by remember { mutableStateOf(false) }
    Column(
        modifier = modifier
    ) {
        HomeCardAction(
            label = stringResource(R.string.card_details),
            onClick = {
                onClick()
                showDetails = !showDetails
            },
            isExpanded = showDetails
        ) {
            Column(
                modifier = Modifier.padding(horizontal = MaterialTheme.customDimens.dimen16)
            ) {
                CardDetailItem(
                    label = stringResource(R.string.last_amount),
                    value = card.getFormattedLastStatementBalance()
                )
                CardDetailItem(
                    label = stringResource(R.string.current_amount),
                    value = card.getFormattedCurrentBalance()
                )
                CardDetailItem(
                    label = stringResource(R.string.available_for_withdraws_and_shopping),
                    value = card.getFormattedBalance()
                )
                CardDetailItem(
                    label = stringResource(R.string.reserved_funds),
                    value = card.getFormattedBlockedFunds()
                )
                CardDetailItem(
                    label = stringResource(R.string.monthly_charge),
                    value = card.getFormattedDebitAmount()
                )
                CardDetailItem(
                    label = stringResource(R.string.transit_charge),
                    value = card.getFormattedMemoDebitAmount()
                )
                CardDetailItem(
                    label = stringResource(R.string.monthly_payment),
                    value = card.getFormattedCreditAmount()
                )
                CardDetailItem(
                    label = stringResource(R.string.transit_payment),
                    value = card.getFormattedMemoCreditAmount()
                )
                CardDetailItem(
                    label = stringResource(R.string.minimum_payment),
                    value = card.getFormattedTotalPaymentAmount()
                )
                CardDetailItem(
                    label = stringResource(R.string.cash_payment_label),
                    value = card.getFormattedPaymentBalance()
                )
            }
        }
        AnimatedVisibility(showDetails) {
            ElevatedCard(
                modifier = Modifier.padding(top = MaterialTheme.customDimens.dimen16),
                colors = CardDefaults.elevatedCardColors(
                    containerColor = MaterialTheme.customColors.defaultSurface,
                    contentColor = MaterialTheme.colorScheme.secondary
                )
            ) {
                Column(
                    modifier = Modifier.padding(horizontal = MaterialTheme.customDimens.dimen16)
                ) {
                    CardDetailItem(
                        label = stringResource(R.string.cut_date),
                        value = card.getCutFormattedDate(),
                        valueColor = MaterialTheme.customColors.gray600,
                        showDivider = false
                    )
                    CardDetailItem(
                        label = stringResource(R.string.ex_date),
                        value = card.getDueFormattedDate(),
                        valueColor = MaterialTheme.customColors.gray600
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun CardDetailsActionPreview() {
    OneAppTheme {
        CardDetailsAction(
            card = MainCardUI(
                creditAmount = 2000.0,
                currentBalance = 500.0,
                fullName = "John Doe",
                mainCard = true,
                lastStatementDate = "2021-01-01",
                paymentDueDate = "2021-01-31",
            )
        )
    }
}
