package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.applicantinfo

import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI
import sv.com.tarjetaone.presentation.helpers.UiText

data class AdditionalCardApplicantInfoUiState(
    val phone: String = EMPTY_STRING,
    val isPhoneValid: Boolean = false,
    val selectedItem: CatalogItemsCollectionUI? = null,
    val phoneError: UiText? = null,
    val relationships: List<CatalogItemsCollectionUI> = emptyList()
) {
    val isFormValid = !selectedItem?.id.isNullOrEmpty() && isPhoneValid
    val hasLoadedData = relationships.isNotEmpty()
}