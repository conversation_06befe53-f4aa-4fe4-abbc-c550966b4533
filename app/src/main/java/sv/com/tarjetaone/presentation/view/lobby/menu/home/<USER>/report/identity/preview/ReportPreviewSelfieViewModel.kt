package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.report.identity.preview

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.facephi.FacephiResultHandler
import sv.com.tarjetaone.presentation.view.common.selfiepreview.SelfiePreviewViewModel
import javax.inject.Inject

@HiltViewModel
class ReportPreviewSelfieViewModel @Inject constructor(
    imageUtils: ImageUtils,
    facephiResultHandler: <PERSON>phiResultHandler,
    savedStateHandle: SavedStateHandle
) : SelfiePreviewViewModel(imageUtils, facephiResultHandler) {
    private val args = ReportPreviewSelfieFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onContinueClick() {
        sendEvent(
            UiEvent.Navigate(
                ReportPreviewSelfieFragmentDirections
                    .actionReportPreviewSelfieFragmentToReportValidationFragment(
                        cardStatusId = args.cardStatusId,
                        cardId = args.cardId,
                        reportableTransactions = args.reportableTransactions
                    )
            )
        )
    }
}