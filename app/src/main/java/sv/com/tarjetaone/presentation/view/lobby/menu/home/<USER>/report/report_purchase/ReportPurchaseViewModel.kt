package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.report.report_purchase

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.SideEffect
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI
import sv.com.tarjetaone.domain.entities.response.CatalogType
import sv.com.tarjetaone.domain.usecases.catalog.GetCatalogUseCase
import sv.com.tarjetaone.presentation.compose.uicomponent.dialog.DynamicDialogButtonParam
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import javax.inject.Inject

@HiltViewModel
class ReportPurchaseViewModel @Inject constructor(
    private val getCatalogUseCase: GetCatalogUseCase,
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {
    private val args = ReportPurchaseFragmentArgs.fromSavedStateHandle(savedStateHandle)
    private val _uiState = MutableStateFlow(ReportPurchaseUIState())
    val uiState = _uiState.asStateFlow()

    private fun onStart() {
        args.transaction?.let { transaction ->
            _uiState.update {
                it.copy(
                    cardId = args.cardId,
                    transaction = transaction
                )
            }
        }
    }

    private fun onReportClick(cardStatusId: String) {
        _uiState.update { state -> state.copy(dynamicDialogVisibility = false) }
        sendEvent(
            UiEvent.Navigate(
                ReportPurchaseFragmentDirections.actionReportPurchaseFragmentToReportAndBlockFragment(
                    cardStatusId,
                    uiState.value.transaction,
                    _uiState.value.cardId
                )
            )
        )
    }

    private fun requestGetCatalogUseCase() {
        viewModelScope.launch {
            sendEvent(SideEffect.Loading(true))
            getCatalogUseCase.invoke(CatalogType.GC_CARD_STATUS).executeUseCase {
                sendEvent(SideEffect.Loading(false))
                it.dataCatalog?.catalogItemsCollection?.let { catalog ->
                    setUpDialogButtons(catalog)
                }
            }
        }
    }

    //TODO REPLACE ARRAYLIST IMPL WITH LIST OR MAYBE buildList
    private fun setUpDialogButtons(catalog: List<CatalogItemsCollectionUI>) {
        val reportDialogList = arrayListOf<DynamicDialogButtonParam>()
        catalog.forEach {
            reportDialogList.add(
                DynamicDialogButtonParam(
                    it.id,
                    it.name.orEmpty(),
                    if (it == catalog.first()) {
                        ButtonVariant.PRIMARY_VARIANT
                    } else {
                        ButtonVariant.SECONDARY_VARIANT
                    }
                )
            )
        }
        _uiState.update {
            it.copy(reportDialogList = reportDialogList, dynamicDialogVisibility = true)
        }
    }

    fun onEvent(event: ReportPurchaseUiEvent) {
        when (event) {
            is ReportPurchaseUiEvent.OnContinueClick -> requestGetCatalogUseCase()
            ReportPurchaseUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            ReportPurchaseUiEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
            ReportPurchaseUiEvent.OnNotReportClick -> sendEvent(UiEvent.NavigateBack)
            ReportPurchaseUiEvent.OnStart -> onStart()
            is ReportPurchaseUiEvent.OnReportUnrecognizedPurchaseClick -> onReportClick(
                event.id
            )
        }
    }
}
