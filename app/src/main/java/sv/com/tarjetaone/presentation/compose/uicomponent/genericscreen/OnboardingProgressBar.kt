package sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_FLOAT_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens

/**
 * @param currentStep Step where the user is at on the progress
 * @param icons List of icons use to determine
 * @param circleSize Step circle size
 * @param iconSize Step icon size
 * @param lineHeight Line height for the progress lines that separates steps
 * @param lineColor Line color for the progress lines that separates steps
 * @param completedStepColor Background color on completed steps
 * @param selectedStepColor Background color on selected step
 * */
@Suppress("kotlin:S107")
@Composable
fun OnboardingProgressBar(
    currentStep: Int,
    icons: List<Int>,
    modifier: Modifier = Modifier,
    circleSize: Dp = MaterialTheme.customDimens.dimen32,
    iconSize: Dp = MaterialTheme.customDimens.dimen16,
    lineHeight: Dp = MaterialTheme.customDimens.dimen2,
    lineColor: Color = MaterialTheme.customColors.gray400,
    completedStepColor: Color = MaterialTheme.customColors.lightProgress,
    selectedStepColor: Color = MaterialTheme.customColors.lightBackground
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
    ) {
        icons.forEachIndexed { index, icon ->
            CircleBox(
                index = index,
                currentStep = currentStep,
                iconSize = iconSize,
                circleSize = circleSize,
                completedStepColor = completedStepColor,
                selectedStepColor = selectedStepColor,
                lineColor = lineColor,
                iconResource = if (index < currentStep) R.drawable.ic_white_check else icon
            )

            if (index < (icons.size - ONE_VALUE)) {
                StepLine(
                    modifier = Modifier.weight(ONE_FLOAT_VALUE),
                    index = index,
                    currentStep = currentStep,
                    lineHeight = lineHeight,
                    completedStepColor = completedStepColor,
                    lineColor = lineColor
                )
            }
        }
    }
}

@Composable
private fun CircleBox(
    index: Int,
    currentStep: Int,
    iconSize: Dp,
    circleSize: Dp,
    completedStepColor: Color,
    selectedStepColor: Color,
    lineColor: Color,
    iconResource: Int
) {
    val circleColor = when {
        index < currentStep -> completedStepColor
        index == currentStep -> selectedStepColor
        else -> lineColor
    }

    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier
            .size(circleSize)
            .background(color = circleColor, shape = CircleShape)
    ) {
        Icon(
            imageVector = ImageVector.vectorResource(iconResource),
            contentDescription = null,
            tint = MaterialTheme.customColors.defaultSurface,
            modifier = Modifier.size(iconSize)
        )
    }
}

@Composable
private fun StepLine(
    modifier: Modifier = Modifier,
    index: Int,
    currentStep: Int,
    lineHeight: Dp,
    completedStepColor: Color,
    lineColor: Color
) {

    val completedStepColor = if (index < currentStep) {
        completedStepColor
    } else {
        lineColor
    }

    Row(modifier = modifier) {
        Canvas(
            modifier = Modifier
                .weight(ONE_FLOAT_VALUE)
                .height(lineHeight)
        ) {
            drawRoundRect(
                color = completedStepColor,
                cornerRadius = CornerRadius(
                    x = lineHeight.toPx(),
                    y = lineHeight.toPx()
                )
            )
        }
    }
}

@Composable
@Preview
fun StepProgressBarPreview() {
    val icons = listOf(
        R.drawable.ic_direction,
        R.drawable.ic_bio_metric,
        R.drawable.ic_savings,
        R.drawable.ic_archive,
        R.drawable.ic_aditional_card_dialog,
    )
    OneAppTheme {
        OnboardingProgressBar(
            currentStep = 2,
            icons = icons,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

