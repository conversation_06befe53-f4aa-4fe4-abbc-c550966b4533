package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.generaldata.validatedataoverview

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.common.utils.AppConstants
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.UserUtils
import sv.com.tarjetaone.core.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.core.utils.extensions.setGender
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.request.IdentityDocumentsUI
import sv.com.tarjetaone.domain.entities.request.UserLocationUI
import sv.com.tarjetaone.domain.entities.request.ValidateOCRRequestUI
import sv.com.tarjetaone.domain.entities.response.CustomerDataUI
import sv.com.tarjetaone.domain.entities.response.ValidateOCRContentUI
import sv.com.tarjetaone.domain.usecases.requestcard.validateocr.ValidateOcrUseCase
import sv.com.tarjetaone.domain.utils.onApiError
import sv.com.tarjetaone.domain.utils.onNetworkError
import sv.com.tarjetaone.domain.utils.onSuccess
import sv.com.tarjetaone.presentation.helpers.addDashDui
import sv.com.tarjetaone.presentation.helpers.capitalizeAllWords
import sv.com.tarjetaone.presentation.helpers.removeDash
import sv.com.tarjetaone.presentation.view.utils.DAY_MONTH_LONG_YEAR_SLASH_FORMAT
import sv.com.tarjetaone.presentation.view.utils.DAY_MONTH_YEAR_FORMAT
import sv.com.tarjetaone.presentation.view.utils.YEAR_MONTH_DAY_JOINED_FORMAT

@HiltViewModel
class ValidatePersonalDataOverviewViewModel @Inject constructor(
    private val userUtils: UserUtils,
    private val validateOcrUseCase: ValidateOcrUseCase,
    private val sharedPrefsRepo: SecureSharedPreferencesRepository,
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {

    private val args = ValidatePersonalDataOverviewFragmentArgs.fromSavedStateHandle(savedStateHandle)

    private val _uiState = MutableStateFlow(ValidatePersonalDataOverviewUiState())
    val uiState = _uiState.asStateFlow()

    private fun validateOcr() {
        sendEvent(UiEvent.Loading(true))
        viewModelScope.launch {
            val request = createValidateOcrRequest() ?: run {
                showUpsErrorMessage()
                return@launch
            }
            val result = validateOcrUseCase.invoke(request)
            result.onSuccess { response ->
                val responseCode = response.statusResponse?.responseStatus?.code
                if (responseCode != AppConstants.SUCCESS_RESPONSE_CODE) {
                    showUpsErrorMessage()
                } else {
                    response.data?.let {
                        sharedPrefsRepo.putValidatedOcr(it)
                        updateUserData(it)
                        setUserAddressFromDui(it)
                    }
                    sendEvent(UiEvent.Loading(false))
                }
            }.onApiError { _, _, _, _ ->
                showUpsErrorMessage()
            }.onNetworkError {
                showUpsErrorMessage()
            }
        }
    }

    private fun createValidateOcrRequest(): ValidateOCRRequestUI? {
        return sharedPrefsRepo.getOcr()?.let { ocrData ->
            ValidateOCRRequestUI(
                name = ocrData.frontMLFirstName,
                birthDate = ocrData.frontMLDateOfBirth?.getFormattedDateFromTo(
                    DAY_MONTH_LONG_YEAR_SLASH_FORMAT,
                    YEAR_MONTH_DAY_JOINED_FORMAT
                ),
                knownBy = ocrData.frontMLFirstName,
                lastName = ocrData.frontMLLastName,
                genderCode = ocrData.gender,
                marriedName = ocrData.marriedName,
                matchingSidesScore = ocrData.matchingSidesScore?.toDouble(),
                identityDocuments = IdentityDocumentsUI(
                    document = ocrData.frontMLDocumentNumber?.removeDash(),
                    issuedDate = ocrData.frontMLDateOfIssue
                        ?.getFormattedDateFromTo(
                            DAY_MONTH_LONG_YEAR_SLASH_FORMAT,
                            YEAR_MONTH_DAY_JOINED_FORMAT
                        ),
                    expirationDate = ocrData.frontMLDateOfExpiry
                        ?.getFormattedDateFromTo(
                            DAY_MONTH_LONG_YEAR_SLASH_FORMAT,
                            YEAR_MONTH_DAY_JOINED_FORMAT
                        ),
                    idTypeCode = AppConstants.EMPTY_STRING
                )
            )
        }
    }

    private fun updateUserData(data: ValidateOCRContentUI) {
        val names = data.name?.capitalizeAllWords()
        val lastnames = data.lastName?.capitalizeAllWords()
        val marriedName = data.marriedName?.capitalizeAllWords()

        _uiState.update {
            it.copy(
                customerData = CustomerDataUI(
                    fullName = "$names $lastnames $marriedName",
                    genderCode = data.genderCode?.setGender().orEmpty(),
                    dui = data.identityDocuments?.document?.addDashDui().orEmpty(),
                    duiExpirationDate = data.identityDocuments?.expirationDate
                        ?.getFormattedDateFromTo(
                            YEAR_MONTH_DAY_JOINED_FORMAT,
                            DAY_MONTH_YEAR_FORMAT
                        ).orEmpty(),
                    birthDate = data.birthDate
                        ?.getFormattedDateFromTo(
                            YEAR_MONTH_DAY_JOINED_FORMAT,
                            DAY_MONTH_YEAR_FORMAT
                        ).orEmpty(),
                    address = data.address,
                    department = data.department.orEmpty(),
                    municipality = data.municipality.orEmpty(),
                )
            )
        }
    }

    private fun setUserAddressFromDui(data: ValidateOCRContentUI) {
        userUtils.clearUserLocation()
        userUtils.addUserLocation(
            UserLocationUI(
                address = data.address,
                addressType = AppConstants.IMAGE_CODE_DUI,
                department = data.department?.capitalizeAllWords(),
                municipio = data.municipality?.capitalizeAllWords(),
                idMunicipio = data.municipalityId?.toString(),
                residential = data.address
            )
        )
    }

    fun onEvent(event: ValidatePersonalDataOverviewUiEvent) {
        when (event) {
            ValidatePersonalDataOverviewUiEvent.OnGetCustomerOcrData -> validateOcr()
            ValidatePersonalDataOverviewUiEvent.OnConfirmPersonalDataClick -> {
                sendEvent(
                    UiEvent.Navigate(
                        ValidatePersonalDataOverviewFragmentDirections
                            .actionValidatePersonalDataOverviewFragmentToSavingPersonalDataFragment(
                                biometryProcessId = args.biometryProcessId.orZero()
                            )
                    )
                )
            }

            ValidatePersonalDataOverviewUiEvent.OnCapturePersonalDataAgainClick -> {
                sendEvent(UiEvent.NavigateBack)
            }

            ValidatePersonalDataOverviewUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            ValidatePersonalDataOverviewUiEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
        }
    }
}
