package sv.com.tarjetaone.presentation.view.common.management

import android.graphics.Bitmap
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.CommonDialogWithIconParams
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.biometry.ImageUtils
import sv.com.tarjetaone.core.utils.files.ContentType
import sv.com.tarjetaone.core.utils.openAppSettings
import sv.com.tarjetaone.domain.entities.response.ManAttributesUI
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.helpers.fileCapturedName

abstract class ManagementDetailBaseViewModel(
    private val imageUtils: ImageUtils
) : BaseViewModel() {
    protected val _uiState = MutableStateFlow(DetailManagementUiState())
    val uiState: StateFlow<DetailManagementUiState> = _uiState.asStateFlow()

    private fun onShareVoucher(bitmap: Bitmap?) {
        bitmap?.let { bitmapImage ->
            imageUtils.saveImage(fileCapturedName(), bitmapImage) { uri ->
                sendEvent(UiEvent.ShareContent(uri, ContentType.ANY_IMAGE))
                _uiState.update { it.copy(isCapturingVoucherView = false) }
            }
        }
    }

    private fun onHideComponentBeforeCapture() {
        _uiState.update { it.copy(isCapturingVoucherView = true) }
    }

    private fun onShareVoucherButtonClick(requestPermissionCallback: () -> Unit) {
        requestPermissionCallback()
    }

    private fun onStoragePermissionGranted(bitmap: Bitmap?) {
        onShareVoucher(bitmap)
    }

    private fun onTwilioClick() {
        sendEvent(UiEvent.TwilioClick)
    }

    private fun onStoragePermissionDenied(showRationale: Boolean) {
        sendEvent(
            UiEvent.ShowCommonDialog(
                CommonDialogWithIconParams(
                    title = UiText.StringResource(R.string.multimedia_access),
                    message = UiText.StringResource(R.string.permission_external_storage),
                    buttonText = UiText.StringResource(if (showRationale) R.string.accept_label else R.string.settings),
                    onButtonClick = {
                        if (!showRationale) sendEvent(UiEvent.StartIntent(openAppSettings()))
                    }
                )
            )
        )
    }

    protected abstract fun onStart()

    protected abstract fun onPrimaryButtonClick()

    protected open fun onSecondaryButtonClick() = Unit

    protected open fun onExitButtonClick() = Unit

    protected open fun onBackButtonClick() {
        sendEvent(UiEvent.NavigateBack)
    }

    protected fun transformAttributesToUiTextList(
        list: List<ManAttributesUI>
    ): List<ManagementAttributesUiModel> = list.map { attribute ->
        attribute.toAttributeUiModel()
    }

    fun onEvent(event: DetailManagementUiEvent) {
        when (event) {
            is DetailManagementUiEvent.OnStart -> onStart()
            is DetailManagementUiEvent.OnPrimaryButtonClick -> onPrimaryButtonClick()
            is DetailManagementUiEvent.OnSecondaryButtonClick -> onSecondaryButtonClick()
            is DetailManagementUiEvent.OnStoragePermissionGranted -> onStoragePermissionGranted(
                event.bitmap
            )

            is DetailManagementUiEvent.OnStoragePermissionDenied -> onStoragePermissionDenied(event.showRationale)
            is DetailManagementUiEvent.OnHideComponentBeforeCapture -> onHideComponentBeforeCapture()
            is DetailManagementUiEvent.OnShareVoucherClick -> onShareVoucherButtonClick(event.requestPermissionCallback)
            DetailManagementUiEvent.OnBackButtonCLick -> onBackButtonClick()
            DetailManagementUiEvent.OnTwilioClick -> onTwilioClick()
            DetailManagementUiEvent.OnExitButtonClick -> onExitButtonClick()
        }
    }
}