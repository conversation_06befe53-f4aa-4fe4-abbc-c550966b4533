package sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.deactivate_program_protection.actived_program_protection

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE_DOUBLE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.compose.util.Spacer24

@Composable
fun DeactivateProgramProtectionInfoContent(
    modifier: Modifier = Modifier,
    amount: Double = ZERO_VALUE_DOUBLE,
    canDeactivate: Boolean = false,
    onKeepFraudProtectionButtonClick: () -> Unit = {},
    onDeactivateFraudProtectionButtonClick: () -> Unit = {}
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.Center,
        ) {
            DeactivateProgramProtectionCard(
                iconId = R.drawable.ic_shield_check_protection,
                titleCard = stringResource(id = R.string.protection_program),
            ) {
                Text(
                    text = stringResource(
                        id = R.string.monthly_charge_label_with_placeholder,
                        amount
                    ),
                    modifier = Modifier.padding(bottom = MaterialTheme.customDimens.dimen16),
                )
                Text(
                    text = stringResource(id = R.string.with_this_protection_program_you_have_disclaimer), //TODO: Consider receive this value from the backend
                    fontSize = MaterialTheme.customDimensSp.sp13,
                )
                Text(
                    text = stringResource(id = R.string.with_this_protection_program_you_have_options), //TODO: Consider receive this value from the backend
                    fontSize = MaterialTheme.customDimensSp.sp13,
                    overflow = TextOverflow.Visible,
                    modifier = Modifier.padding(
                        start = MaterialTheme.customDimens.dimen4,
                        top = MaterialTheme.customDimens.dimen8
                    )
                )
            }
        }
        Spacer24()
        Column {
            OneButton(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(id = R.string.keep_active_program_protection_text_button),
                onClick = onKeepFraudProtectionButtonClick
            )
            if (canDeactivate) {
                OneButton(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            top = MaterialTheme.customDimens.dimen16,
                            bottom = MaterialTheme.customDimens.dimen32,
                        ),
                    text = stringResource(id = R.string.deactivate_program_protection),
                    buttonVariant = ButtonVariant.SECONDARY_VARIANT,
                    onClick = onDeactivateFraudProtectionButtonClick
                )
            } else {
                DeactivateProgramProtectionDisclaimer(
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                )
            }
        }
    }
}

@Composable
fun DeactivateProgramProtectionDisclaimer(
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.padding(vertical = MaterialTheme.customDimens.dimen16),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_info_2),
            contentDescription = null,
            modifier = Modifier.padding(
                start = MaterialTheme.customDimens.dimen16,
            )
        )
        Text(
            modifier = Modifier.padding(
                end = MaterialTheme.customDimens.dimen24,
            ),
            text = stringResource(R.string.fp_disable_not_allow),
            style = MaterialTheme.typography.labelMedium.copy(
                color = MaterialTheme.customColors.disclaimer,
                textAlign = TextAlign.Center,
            ),
        )
    }
}

@Preview(
    showBackground = true,
    name = "Deactivate program protection without option to deactivate"
)
@Composable
fun DeactivateProgramProtectionInfoScreenWithDisclaimerPreview() {
    OneAppTheme {
        DeactivateProgramProtectionInfoContent(
            modifier = Modifier
                .fillMaxSize()
                .padding(
                    horizontal = MaterialTheme.customDimens.dimen24
                ),
            canDeactivate = false,
            amount = 0.99
        )
    }
}

@Preview(
    showBackground = true,
    name = "Deactivate program protection with option to deactivate"
)
@Composable
fun DeactivateProgramProtectionInfoScreenWithDeactivateButtonPreview() {
    OneAppTheme {
        DeactivateProgramProtectionInfoContent(
            modifier = Modifier
                .fillMaxSize()
                .padding(
                    horizontal = MaterialTheme.customDimens.dimen24
                ),
            canDeactivate = true,
            amount = 1.99
        )
    }
}
