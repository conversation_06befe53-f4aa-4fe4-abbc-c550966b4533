package sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.invoiceselection

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.domain.entities.response.automaticpayments.servicespayment.fields.PXFieldType
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.ButtonSize
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.genericscreen.ScreenWithTopAppBar
import sv.com.tarjetaone.presentation.compose.uicomponent.radiobutton.GradientRadioButton
import sv.com.tarjetaone.presentation.compose.util.Spacer16
import sv.com.tarjetaone.presentation.compose.util.Spacer1f
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.helpers.capitalize
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.component.PXFormField
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.component.PXFormFieldDisposition
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.component.PaymentProductsContainer
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.invoiceselection.util.DummyPxFormResponse
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.invoiceselection.util.InvoicePXFieldUiState
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.automaticpayments.servicespayment.invoiceselection.util.toInvoiceList

@Composable
fun InvoiceSelectionScreen(
    viewModel: InvoiceSelectionViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    LaunchedEffect(Unit) {
        viewModel.onEvent(InvoiceSelectionUiEvent.OnStart)
    }
    InvoiceSelectionScreen(
        uiState = uiState,
        onEvent = viewModel::onEvent
    )
}

@Composable
fun InvoiceSelectionScreen(
    uiState: InvoiceSelectionUiState,
    onEvent: (InvoiceSelectionUiEvent) -> Unit
) {
    ScreenWithTopAppBar(
        title = stringResource(id = R.string.service_payment_title),
        onLeftButtonClick = { onEvent(InvoiceSelectionUiEvent.OnBackClick) },
        onRightButtonClick = { onEvent(InvoiceSelectionUiEvent.OnTwilioClick) },
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(
                    horizontal = MaterialTheme.customDimens.dimen24,
                )
        ) {
            Spacer16()
            Box(modifier = Modifier.weight(1f)) {
                Column(modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                ) {
                    Spacer1f()
                    InvoiceSelectionFormContainer(
                        uiState = uiState,
                        onEvent = onEvent,
                    )
                    Spacer1f()
                }
            }
            Spacer16()
            OneButton(
                modifier = Modifier.fillMaxWidth(),
                size = ButtonSize.LARGE,
                text = stringResource(id = R.string.continue_button_label),
                enabled = uiState.canSendInvoice(),
                onClick = { onEvent(InvoiceSelectionUiEvent.OnContinueClick) }
            )
        }
    }
}

@Composable
private fun InvoiceSelectionFormContainer(
    modifier: Modifier = Modifier,
    uiState: InvoiceSelectionUiState,
    onEvent: (InvoiceSelectionUiEvent) -> Unit,
) {
    Column(
        modifier = modifier
            .background(
                color = MaterialTheme.colorScheme.secondaryContainer,
                shape = MaterialTheme.shapes.extraLarge
            )
            .padding(
                vertical = MaterialTheme.customDimens.dimen16
            )
    ) {
        InvoiceSelectionFormHeader(
            modifier = Modifier.padding(
                bottom = MaterialTheme.customDimens.dimen8,
                start = MaterialTheme.customDimens.dimen16,
                end = MaterialTheme.customDimens.dimen16
            ),
            serviceName = uiState.serviceName,
            serviceCategoryName = uiState.serviceCategoryName,
        )
        uiState.invoices.forEachIndexed { invoiceIndex, invoice ->
            InvoiceSelectionForm(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        horizontal = MaterialTheme.customDimens.dimen16,
                    ),
                inputs = invoice.invoiceFields,
                showRadioButton = uiState.invoices.size > ONE_VALUE,
                isInvoiceSelected = invoiceIndex == uiState.selectedInvoice,
                formFieldStyle = if (uiState.invoices.size > ONE_VALUE) PXFormFieldDisposition.SINGLE_LINE
                else PXFormFieldDisposition.DOUBLE_LINE,
                onValueChanged = { fieldIndex, value ->
                    onEvent(
                        InvoiceSelectionUiEvent.OnFieldValueChanged(
                            invoice.formKey,
                            fieldIndex,
                            value
                        )
                    )
                },
                onSelectInvoice = {
                    onEvent(InvoiceSelectionUiEvent.OnSelectInvoice(invoiceIndex))
                }
            )
            if (invoiceIndex < uiState.invoices.lastIndex) {
                HorizontalDivider(
                    thickness = MaterialTheme.customDimens.dimen3,
                    color = MaterialTheme.customColors.gray200
                )
            }
        }
        PaymentProductsContainer(
            modifier = Modifier
                .padding(
                    start = MaterialTheme.customDimens.dimen16,
                    end = MaterialTheme.customDimens.dimen16,
                    bottom = MaterialTheme.customDimens.dimen16,
                ),
            userProductsTypes = uiState.userProductsTypes,
            productsByTypeFiltered = uiState.userProductsFiltered,
            selectedProduct = uiState.selectedProduct,
            selectedProductType = uiState.selectedProductType,
            onSelectProductType = {
                onEvent(InvoiceSelectionUiEvent.OnSelectProductType(it))
            },
            onSelectProduct = {
                onEvent(InvoiceSelectionUiEvent.OnSelectProduct(it))
            }
        )
    }
}

@Composable
private fun InvoiceSelectionForm(
    modifier: Modifier = Modifier,
    inputs: List<InvoicePXFieldUiState>,
    formFieldStyle: PXFormFieldDisposition = PXFormFieldDisposition.DOUBLE_LINE,
    showRadioButton: Boolean,
    isInvoiceSelected: Boolean,
    onSelectInvoice: () -> Unit,
    onValueChanged: (Int, String) -> Unit,
) {
    Column(
        modifier = modifier
    ) {
        inputs.forEachIndexed { index, input ->
            if (input.pxField.type != PXFieldType.Hidden) {
                PXFormField(
                    modifier = Modifier.fillMaxWidth(),
                    labelValue = input.pxField.label?.lowercase()?.capitalize().orEmpty(),
                    fieldState = input,
                    formFieldStyle = formFieldStyle,
                    onValueChanged = { value -> onValueChanged(index, value) }
                )
                Spacer16()
            }
        }
        if (showRadioButton) {
            Spacer8()
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = stringResource(id = R.string.invoice_selection_selection_label),
                    style = MaterialTheme.typography.bodySmall.copy(
                        fontWeight = FontWeight.SemiBold,
                        color = MaterialTheme.customColors.gray5,
                    )
                )
                GradientRadioButton(
                    size = MaterialTheme.customDimens.dimen20,
                    selected = isInvoiceSelected,
                    onClick = { onSelectInvoice() }
                )
            }
            Spacer32()
        }
    }
}

@Composable
private fun InvoiceSelectionFormHeader(
    modifier: Modifier = Modifier,
    serviceName: String,
    serviceCategoryName: String?,
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Text(
            text = serviceName,
            style = MaterialTheme.typography.bodyLarge.copy(
                fontWeight = FontWeight.Bold,
            ),
            maxLines = TWO_VALUE,
            overflow = TextOverflow.Ellipsis,
            textAlign = TextAlign.Center
        )
        serviceCategoryName?.let {
            Text(
                text = it,
                style = MaterialTheme.typography.labelMedium.copy(
                    color = MaterialTheme.customColors.gray600
                ),
                maxLines = TWO_VALUE,
                overflow = TextOverflow.Ellipsis,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Preview
@Composable
private fun InvoiceSelectionScreenMultiplePreview() {
    OneAppTheme {
        InvoiceSelectionScreen(
            uiState = InvoiceSelectionUiState(
                serviceName = "ANDA",
                serviceCategoryName = "Agua potable",
                invoices = DummyPxFormResponse
                    .dummyMultipleFormResponse
                    .toInvoiceList(),
            ),
            onEvent = { }
        )
    }
}

@Preview
@Composable
private fun InvoiceSelectionScreenPreview() {
    OneAppTheme {
        InvoiceSelectionScreen(
            uiState = InvoiceSelectionUiState(
                serviceName = "This an extremely large service name that should be truncated if the text exceeds the maximum number of lines",
                serviceCategoryName = null,
                invoices = DummyPxFormResponse
                    .dummySimpleFormResponse
                    .toInvoiceList(),
            ),
            onEvent = { }
        )
    }
}
