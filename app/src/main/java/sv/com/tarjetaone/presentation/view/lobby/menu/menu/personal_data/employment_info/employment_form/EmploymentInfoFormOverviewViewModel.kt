package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.employment_info.employment_form

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.SUCCESS_RESPONSE_CODE
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.data.repository.SecureSharedPreferencesRepository
import sv.com.tarjetaone.domain.entities.request.CustomerContactJobsUI
import sv.com.tarjetaone.domain.entities.request.CustomerContactValidationRequestUI
import sv.com.tarjetaone.domain.entities.request.CustomerContactValidationType
import sv.com.tarjetaone.domain.entities.request.CustomerJobContactRequestUI
import sv.com.tarjetaone.domain.usecases.personalData.employment.UpdateCustomerCompanyContactUseCase
import sv.com.tarjetaone.domain.usecases.requestcard.validatecustomercontact.ValidateCustomerContactUseCase
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.helpers.hasInvalidPhoneNumber
import sv.com.tarjetaone.presentation.helpers.removeDash
import javax.inject.Inject

@HiltViewModel
class EmploymentInfoFormOverviewViewModel @Inject constructor(
    private val updateCustomerContactUseCase: UpdateCustomerCompanyContactUseCase,
    private val validateCustomerContactUseCase: ValidateCustomerContactUseCase,
    private val sharedPref: SecureSharedPreferencesRepository,
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {

    private val _uiState: MutableStateFlow<EmploymentContactPhoneUiState> =
        MutableStateFlow(EmploymentContactPhoneUiState())
    val uiState: StateFlow<EmploymentContactPhoneUiState> = _uiState.asStateFlow()

    private val navArgs =
        EmploymentInfoFormOverviewFragmentArgs.fromSavedStateHandle(savedStateHandle)
    private var phoneValidationJob: Job? = null

    fun onScreenUiEvent(screenUiEvent: EmploymentContactPhoneUiEvent) {
        when (screenUiEvent) {
            is EmploymentContactPhoneUiEvent.OnContactPhoneChange -> onTypePhoneNumber(screenUiEvent.phoneNumber)
            is EmploymentContactPhoneUiEvent.OnContinueButtonClick -> updateContact()
            EmploymentContactPhoneUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
        }
    }

    private fun onTypePhoneNumber(phoneNumber: String) {
        val phoneIsValid = phoneNumber.hasInvalidPhoneNumber().not()
        _uiState.update {
            it.copy(
                contactPhone = phoneNumber,
                phoneIsValid = phoneIsValid,
                phoneError = if (phoneIsValid) null else UiText.StringResource(R.string.invalid_phone_number),
                isValidatingPhone = phoneIsValid
            )
        }
        if (phoneIsValid) validateCustomerContact(phoneNumber) else phoneValidationJob?.cancel()
    }

    private fun validateCustomerContact(phoneNumber: String) {
        phoneValidationJob?.cancel()
        _uiState.update {
            it.copy(
                isValidatingPhone = true,
                phoneError = null
            )
        }
        val request = CustomerContactValidationRequestUI(
            customerId = sharedPref.getCustomerId().orZero(),
            contact = phoneNumber.removeDash(),
            type = CustomerContactValidationType.PHONE
        )
        phoneValidationJob = viewModelScope.launch {
            validateCustomerContactUseCase(request).executeUseCase(
                onSuccessAction = {
                    val isValidPhone = it.data
                    _uiState.update { state ->
                        state.copy(
                            phoneIsValid = isValidPhone,
                            phoneError = if (isValidPhone) {
                                null
                            } else {
                                UiText.DynamicString(it.statusResponse?.responseStatus?.message.orEmpty())
                            },
                            isValidatingPhone = false
                        )
                    }
                },
                onApiErrorAction = { _, _, _, _ ->
                    _uiState.update {
                        it.copy(
                            phoneIsValid = false,
                            isValidatingPhone = false
                        )
                    }
                    onDefaultApiError()
                }
            )
        }
    }

    private fun updateContact() {
        sendEvent(UiEvent.Loading(true))
        viewModelScope.launch {
            val request = CustomerJobContactRequestUI(
                customerId = sharedPref.getCustomerId().orZero(),
                customerContact = CustomerContactJobsUI(
                    contactId = navArgs?.contactId.orZero(),
                    contactTypeCode = navArgs?.contactTypeCode.orEmpty(),
                    contactValue = _uiState.value.contactPhone,
                    jobId = navArgs?.jobId.orZero()
                ),
                biometryId = navArgs?.biometryId
            )
            updateCustomerContactUseCase(request).executeUseCase {
                sendEvent(UiEvent.Loading(false))

                val response = it.statusResponse?.responseStatus
                if (response?.code != SUCCESS_RESPONSE_CODE) {
                    showSimpleError(response?.message.orEmpty())
                    return@executeUseCase
                }

                sendEvent(
                    UiEvent.Navigate(
                        EmploymentInfoFormOverviewFragmentDirections
                            .actionEmploymentInfoFormOverviewFragmentToSuccessEmploymentFragment()
                    )
                )
            }
        }
    }
}
