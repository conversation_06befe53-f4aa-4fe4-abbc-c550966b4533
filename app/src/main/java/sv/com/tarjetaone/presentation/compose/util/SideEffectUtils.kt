package sv.com.tarjetaone.presentation.compose.util

import android.content.Context
import android.content.Intent
import android.net.Uri
import sv.com.tarjetaone.core.utils.files.ContentType

/**
 * Creates a chooser intent to share the content of the given Uri.
 */
fun shareContent(
    context: Context,
    contentUri: Uri,
    contentType: ContentType,
    shareTitle: String? = null
) {
    val shareIntent = Intent(Intent.ACTION_SEND).apply {
        putExtra(Intent.EXTRA_STREAM, contentUri)
        flags = Intent.FLAG_GRANT_READ_URI_PERMISSION
        type = contentType.mimeType
    }

    // grant temporary permission to the content URI
    val resolvedIntentActivities = context.packageManager.queryIntentActivities(shareIntent, 0)
    for (resolvedIntentInfo in resolvedIntentActivities) {
        val packageName = resolvedIntentInfo.activityInfo.packageName
        context.grantUriPermission(
            packageName,
            contentUri,
            Intent.FLAG_GRANT_READ_URI_PERMISSION
        )
    }
    context.startActivity(Intent.createChooser(shareIntent, shareTitle))
}
