package sv.com.tarjetaone.presentation.view.app_under_maintenance

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.SetStatusBarAppearance
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleLoadingIndicator
import sv.com.tarjetaone.presentation.compose.util.Spacer8

@Composable
fun AppUnderMaintenanceScreen(
    viewModel: AppUnderMaintenanceViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    AppUnderMaintenanceContent(
        uiState = uiState,
        onRetryClick = {
            viewModel.onScreenUiEvent(AppUnderMaintenanceUiEvent.OnRetryClick)
        }
    )
}

@Composable
private fun AppUnderMaintenanceContent(
    uiState: AppUnderMaintenanceUiState = AppUnderMaintenanceUiState(),
    onRetryClick: () -> Unit = {}
) {
    SetStatusBarAppearance()
    Column(
        modifier = Modifier
            .background(MaterialTheme.colorScheme.surface)
            .fillMaxSize()
            .safeDrawingPadding()
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(MaterialTheme.customDimens.dimen32),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            if (uiState.showLoading) {
                SimpleLoadingIndicator()
            } else {
                Image(
                    painter = painterResource(id = R.drawable.app_under_maintenance),
                    contentDescription = null
                )
                Text(
                    text = stringResource(id = R.string.app_under_maintenance_message),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer8()
                Text(
                    text = stringResource(id = R.string.app_under_maintenance_support_message),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        OneButton(
            text = stringResource(id = R.string.retry_label),
            enabled = !uiState.showLoading,
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.customDimens.dimen32),
            onClick = onRetryClick
        )
    }
}

@Preview
@Composable
private fun AppUnderMaintenanceScreenPreview() {
    OneAppTheme {
        AppUnderMaintenanceContent()
    }
}

@Preview
@Composable
private fun AppUnderMaintenanceScreenWithLoadingPreview() {
    OneAppTheme {
        AppUnderMaintenanceContent(
            uiState = AppUnderMaintenanceUiState(
                showLoading = true
            )
        )
    }
}