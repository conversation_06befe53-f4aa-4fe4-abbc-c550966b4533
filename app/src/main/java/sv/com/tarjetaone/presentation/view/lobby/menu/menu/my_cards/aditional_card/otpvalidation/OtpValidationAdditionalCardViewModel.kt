package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.otpvalidation

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.CommonDialogWithIconParams
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.entities.request.ValidateOTPRequestUI
import sv.com.tarjetaone.domain.entities.response.ContactType
import sv.com.tarjetaone.domain.entities.response.OtpMethod
import sv.com.tarjetaone.domain.entities.response.OtpType
import sv.com.tarjetaone.domain.usecases.requestcard.otpvalidation.SendOtpCodeUseCase
import sv.com.tarjetaone.domain.usecases.requestcard.otpvalidation.ValidateOtpCodeUseCase
import sv.com.tarjetaone.presentation.compose.util.InfoStatus
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.helpers.removeDash
import sv.com.tarjetaone.presentation.view.common.otpvalidation.OtpValidationBaseViewModel
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.locationreceive.LocationReceiveCardAction
import javax.inject.Inject

@HiltViewModel
class OtpValidationAdditionalCardViewModel @Inject constructor(
    private val sendOtpCodeUseCase: SendOtpCodeUseCase,
    private val validateOtpCodeUseCase: ValidateOtpCodeUseCase,
    savedStateHandle: SavedStateHandle
) : OtpValidationBaseViewModel() {
    private val args = OtpValidationAdditionalCardFragmentArgs.fromSavedStateHandle(savedStateHandle)

    override fun onStart() {
        super.onStart()
        _uiState.update {
            it.copy(
                contactType = ContactType.Phone,
                contact = args.phoneNumber,
                sharedKey = args.sharedKey,
                showProgressBar = true
            )
        }
    }

    override fun resendOtp(type: OtpType, method: OtpMethod) {
        sendEvent(UiEvent.Loading(true))
        viewModelScope.launch {
            sendOtpCodeUseCase(
                otpType = type,
                value = uiState.value.contact.removeDash(),
                otpChannel = method
            ).executeUseCase(
                onApiErrorAction = { _, error, _, _ ->
                    sendEvent(UiEvent.Loading(false))
                    val errorCode = error?.code?.toIntOrNull()
                    val otpStatus = when (errorCode) {
                        MAX_REQUEST_OTP,
                        MAX_REQUEST_OTP_BAES -> InfoStatus.Error

                        else -> null
                    }
                    _uiState.update { state ->
                        state.copy(
                            otpMessage = error?.result?.let { UiText.DynamicString(it) },
                            otpStatus = otpStatus ?: state.otpStatus
                        )
                    }
                },
                onNetworkErrorAction = {
                    sendEvent(UiEvent.Loading(false))
                    showUpsErrorMessage()
                },
                onSuccessAction = {
                    sendEvent(UiEvent.Loading(false))
                    _uiState.update { state ->
                        state.copy(sharedKey = it.sharedKey.orEmpty())
                    }
                    startOtpCountdown()
                    showSnackBar()
                }
            )
        }
    }

    override fun onValidateOtp(code: String) {
        sendEvent(UiEvent.Loading(true))
        viewModelScope.launch {
            validateOtpCodeUseCase(
                ValidateOTPRequestUI(
                    otpCode = uiState.value.code,
                    sharedKey = uiState.value.sharedKey
                )
            ).executeUseCase(
                onApiErrorAction = { _, error, _, _ ->
                    sendEvent(UiEvent.Loading(false))
                    val errorCode = error?.code?.toIntOrNull()
                    val otpStatus = when (errorCode) {
                        MAX_VALIDATION_ATTEMPTS_CODE -> InfoStatus.Error
                        INVALID_OTP_CODE -> InfoStatus.Error
                        EXPIRED_OTP_CODE -> InfoStatus.Warning
                        else -> InfoStatus.Error
                    }
                    _uiState.update { state ->
                        state.copy(
                            otpStatus = otpStatus,
                            otpMessage = if (errorCode == MAX_VALIDATION_ATTEMPTS_CODE) {
                                error.result?.let { UiText.DynamicString(it) }
                            } else null
                        )
                    }
                },
                onSuccessAction = {
                    sendEvent(UiEvent.Loading(false))
                    _uiState.update { it.copy(otpStatus = InfoStatus.Success) }
                    sendEvent(
                        UiEvent.ShowCommonDialog(
                            CommonDialogWithIconParams(
                                icon = R.drawable.ic_check_yellow,
                                title = UiText.StringResource(R.string.additional_card_activation_title),
                                message = UiText.StringResource(R.string.additional_card_activation_description),
                                buttonColor = R.color.yellow_alert_button,
                                buttonText = UiText.StringResource(R.string.continue_button_label),
                                onButtonClick = {
                                    sendEvent(
                                        UiEvent.Navigate(
                                            OtpValidationAdditionalCardFragmentDirections.navigateToLocationToReceiveRequestCardFragment(
                                                cardAction = LocationReceiveCardAction.AdditionalCard
                                            )
                                        )
                                    )
                                }
                            )
                        )
                    )
                }
            )
        }
    }

    companion object {
        private const val MAX_VALIDATION_ATTEMPTS_CODE = 6
    }
}
