package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_FLOAT_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.common.topbar.component.BackActionButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.topbar.component.CancelActionButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.topbar.component.CloseActionButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.topbar.component.SupportActionButton
import sv.com.tarjetaone.presentation.compose.uicomponent.common.topbar.component.TopBarAction
import sv.com.tarjetaone.presentation.compose.util.Spacer16

/**
 * A reusable TopAppBar component for displaying title, progress information, and common actions
 * on every screen.
 *
 * @param leftActionButton Callback that is needed to either handle custom directions or execute
 * onBackPressed in the back arrow
 * @param rightActionButton Callback used to handled twilio actions on the toolbar
 */
@Suppress("kotlin:S107")
@Composable
fun TopAppBar(
    backgroundColor: Color = MaterialTheme.colorScheme.surface,
    textColor: Color = MaterialTheme.colorScheme.onSurface,
    @DrawableRes headerIcon: Int = R.drawable.one_header_icon,
    contentPadding: PaddingValues = PaddingValues(MaterialTheme.customDimens.dimen32),
    isLeftButtonVisible: Boolean = true,
    isRightButtonVisible: Boolean = true,
    leftActionButton: TopBarAction = TopBarAction.BACK,
    rightActionButton: TopBarAction = TopBarAction.SUPPORT,
    isProgressbarVisible: Boolean = false,
    progress: Float = ZERO_FLOAT_VALUE,
    title: String? = null,
    onLeftButtonClick: () -> Unit,
    onRightButtonClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(backgroundColor)
            .statusBarsPadding()
            .padding(contentPadding)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
        ) {
            BackActionSection(
                modifier = Modifier.align(Alignment.CenterStart),
                isLeftButtonVisible = isLeftButtonVisible,
                leftActionButton = leftActionButton,
                onLeftButtonClick = onLeftButtonClick
            )
            IconSection(modifier = Modifier.align(Alignment.Center), headerIcon = headerIcon)
            RightActionSection(
                modifier = Modifier.align(Alignment.CenterEnd),
                isRightButtonVisible = isRightButtonVisible,
                rightActionButton = rightActionButton,
                onRightButtonClick = onRightButtonClick
            )
        }
        ProgressBarSection(isProgressbarVisible = isProgressbarVisible, progress = progress)
        TitleSection(title = title, textColor = textColor)
    }
}

@Composable
private fun IconSection(modifier: Modifier = Modifier, @DrawableRes headerIcon: Int) {
    Icon(
        modifier = modifier,
        imageVector = ImageVector.vectorResource(headerIcon),
        contentDescription = null,
        tint = Color.Unspecified
    )
}

@Composable
private fun TitleSection(title: String?, textColor: Color) {
    Column {
        title?.let {
            Spacer16()
            Text(
                textAlign = TextAlign.Center,
                text = it,
                style = MaterialTheme.typography.titleMedium,
                color = textColor,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Composable
private fun ProgressBarSection(isProgressbarVisible: Boolean, progress: Float) {
    if (isProgressbarVisible) {
        Column {
            Spacer16()
            LinearProgressIndicator(
                progress = { progress },
                color = MaterialTheme.customColors.progressColor,
                modifier = Modifier
                    .height(MaterialTheme.customDimens.dimen8)
                    .padding(horizontal = MaterialTheme.customDimens.dimen48)
                    .clip(RoundedCornerShape(MaterialTheme.customDimens.dimen16))
                    .background(MaterialTheme.customColors.progressBackgroundColor)
                    .fillMaxWidth(),
                trackColor = MaterialTheme.customColors.progressBackgroundColor,
                drawStopIndicator = {}
            )
        }
    }

}

@Composable
private fun RightActionSection(
    modifier: Modifier = Modifier,
    isRightButtonVisible: Boolean,
    rightActionButton: TopBarAction,
    onRightButtonClick: () -> Unit
) {
    if (isRightButtonVisible) {
        when (rightActionButton) {
            TopBarAction.SUPPORT -> {
                SupportActionButton(
                    modifier = modifier,
                    onClick = onRightButtonClick
                )
            }

            TopBarAction.CANCEL -> {
                CancelActionButton(
                    modifier = modifier,
                    onClick = onRightButtonClick
                )
            }

            TopBarAction.CLOSE -> {
                CloseActionButton(
                    modifier = modifier,
                    onClick = onRightButtonClick
                )
            }

            else -> Unit
        }
    }
}

@Composable
private fun BackActionSection(
    modifier: Modifier = Modifier,
    isLeftButtonVisible: Boolean,
    leftActionButton: TopBarAction,
    onLeftButtonClick: () -> Unit
) {
    if (isLeftButtonVisible) {
        when (leftActionButton) {
            TopBarAction.BACK -> {
                BackActionButton(
                    modifier = modifier,
                    onClick = onLeftButtonClick
                )
            }

            else -> Unit
        }
    }
}

@Preview
@Composable
private fun TopAppBarWithTwilioButtonPreview() {
    OneAppTheme {
        TopAppBar(
            isProgressbarVisible = true,
            progress = 0.3f,
            title = "Screen title",
            onLeftButtonClick = {},
            onRightButtonClick = {},
        )
    }
}

@Preview
@Composable
private fun TopAppBarWithCancelButtonPreview() {
    OneAppTheme {
        TopAppBar(
            headerIcon = R.drawable.one_header_icon_white,
            backgroundColor = MaterialTheme.colorScheme.secondary,
            textColor = Color.White,
            isRightButtonVisible = false,
            isProgressbarVisible = true,
            rightActionButton = TopBarAction.CANCEL,
            progress = 0.3f,
            title = "Screen title",
            onLeftButtonClick = {},
            onRightButtonClick = {}
        )
    }
}
