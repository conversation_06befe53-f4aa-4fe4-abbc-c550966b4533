package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.employment_info.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.card.HorizontalIconWithText
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.employment_info.EmploymentInfo
import sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.employment_info.IncomeType

@Composable
fun EmploymentInfoItem(
    modifier: Modifier = Modifier,
    employmentInfo: EmploymentInfo,
    onModifyPhoneNumberClick:(jobId: Int, contactId: Int, contactTypeCode: String) -> Unit = { _, _, _ -> }
) {
    Card(
        colors = CardDefaults.outlinedCardColors(
            containerColor = MaterialTheme.colorScheme.background
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = MaterialTheme.customDimens.defaultCardElevation
        ),
        modifier = modifier,
    ) {
        Column(
            modifier = Modifier.padding(
                horizontal = MaterialTheme.customDimens.dimen24,
                vertical = MaterialTheme.customDimens.dimen16
            ),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.customDimens.dimen8)
        ) {
            EmploymentInfoItemData(employmentInfo = employmentInfo)
            if (employmentInfo.incomeType == IncomeType.SALARIED.type) {
                EmploymentInfoItemAddress(address = employmentInfo.address)
                EmploymentInfoItemPhone(
                    employmentInfo = employmentInfo,
                    onModifyPhoneNumberClick = onModifyPhoneNumberClick
                )
            }
        }
    }
}

@Composable
private fun EmploymentInfoItemData(
    modifier: Modifier = Modifier,
    employmentInfo: EmploymentInfo
) {
    Column(modifier = modifier) {
        Text(
            text = employmentInfo.incomeType,
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer8()
        EmploymentInfoItemDataRow(
            title = stringResource(id = R.string.fixed_income_monthly),
            value = employmentInfo.fixedIncome
        )
        if (employmentInfo.incomeType == IncomeType.SALARIED.type) {
            EmploymentInfoItemDataRow(
                title = stringResource(id = R.string.variable_income_monthly),
                value = employmentInfo.variableIncome,
                modifier = Modifier.padding(top = MaterialTheme.customDimens.dimen8)
            )
            EmploymentInfoItemDataRow(
                title = stringResource(id = R.string.company),
                value = employmentInfo.companyName,
                modifier = Modifier.padding(top = MaterialTheme.customDimens.dimen8)
            )
            EmploymentInfoItemDataRow(
                title = stringResource(id = R.string.from),
                value = employmentInfo.sinceDate,
                modifier = Modifier.padding(top = MaterialTheme.customDimens.dimen8)
            )
            EmploymentInfoItemDataRow(
                title = stringResource(id = R.string.contract_type),
                value = employmentInfo.hiringType,
                modifier = Modifier.padding(top = MaterialTheme.customDimens.dimen8)
            )
            EmploymentInfoItemDataRow(
                title = stringResource(id = R.string.occupancy),
                value = employmentInfo.occupation,
                modifier = Modifier.padding(top = MaterialTheme.customDimens.dimen8)
            )
        }
    }
}

@Composable
private fun EmploymentInfoItemDataRow(
    modifier: Modifier = Modifier,
    title: String,
    value: String
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.customColors.disabledPlaceholder
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.SemiBold),
            color = MaterialTheme.customColors.steelGray,
            textAlign = TextAlign.End
        )
    }
}

@Composable
private fun EmploymentInfoItemAddress(
    address: String,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = stringResource(id = R.string.address_label),
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer8()
        HorizontalIconWithText(
            leadingIcon = R.drawable.ic_building,
            iconTextSpacing = MaterialTheme.customDimens.dimen8,
            text = UiText.DynamicString(address),
            textColor = MaterialTheme.colorScheme.onSurface
        )
    }
}

@Composable
private fun EmploymentInfoItemPhone(
    modifier: Modifier = Modifier,
    employmentInfo: EmploymentInfo,
    onModifyPhoneNumberClick: (jobId: Int, contactId: Int, contactTypeCode: String) -> Unit = { _, _, _ -> }
) {
    Column(modifier = modifier) {
        Text(
            text = stringResource(id = R.string.contact_office),
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer8()
        Row {
            Image(
                painter = painterResource(id = R.drawable.ic_p_phone),
                contentDescription = null,
                modifier = Modifier.size(MaterialTheme.customDimens.dimen38)
            )
            Column(modifier = Modifier.padding(start = MaterialTheme.customDimens.dimen8)) {
                Text(
                    text = employmentInfo.phoneNumber,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = stringResource(id = R.string.modify),
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.colorScheme.primary,
                    textDecoration = TextDecoration.Underline,
                    modifier = Modifier.clickable {
                        onModifyPhoneNumberClick(
                            employmentInfo.id,
                            employmentInfo.contactId,
                            employmentInfo.contactTypeCode
                        )
                    }
                )
            }
        }
    }
}

@Preview
@Composable
private fun EmploymentInfoItemPreview() {
    OneAppTheme {
        EmploymentInfoItem(
            employmentInfo = EmploymentInfo(
                incomeType = IncomeType.SALARIED.type,
                fixedIncome = "$800",
                variableIncome = "$800",
                companyName = "TarjetaOne",
                sinceDate = "Junio 2023",
                hiringType = "Indefinido",
                occupation = "Developer",
                address = "Col. Escalón, San Salvador, San Salvador, Calle 4, Casa #4",
                phoneNumber = "2245 4526"
            )
        )
    }
}
