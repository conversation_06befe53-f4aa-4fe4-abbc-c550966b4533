package sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.contract.contractviewer

import android.os.Bundle
import android.view.View
import androidx.fragment.app.viewModels
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.base.BaseFragment
import sv.com.tarjetaone.databinding.FragmentComposeBinding
import sv.com.tarjetaone.presentation.view.common.contracts.viewer.ContractViewerScreen

class ProgramProtectionContractsViewerFragment : BaseFragment<FragmentComposeBinding>(
    R.layout.fragment_compose
) {
    override val viewModel: ProgramProtectionContractViewerViewModel by viewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.composeView.setContentScreen {
            ContractViewerScreen(viewModel)
        }
    }
}
