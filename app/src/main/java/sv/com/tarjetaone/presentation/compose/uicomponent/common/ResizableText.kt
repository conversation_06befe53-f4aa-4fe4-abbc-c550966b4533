package sv.com.tarjetaone.presentation.compose.uicomponent.common

import androidx.compose.foundation.layout.Box
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.layout
import androidx.compose.ui.text.ExperimentalTextApi
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntSize
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_VALUE
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme

/**
 * Composable that resizes the given text within the bounds of the Box content.
 * The text will be displayed in the given lines avoiding overflow.
 */
@OptIn(ExperimentalTextApi::class)
@Composable
fun ResizableText(
    modifier: Modifier = Modifier,
    text: String,
    color: Color,
    style: TextStyle = MaterialTheme.typography.bodyLarge.copy(
        fontWeight = FontWeight.Bold,
    ),
    maxLines: Int = ONE_VALUE,
) {
    val textMeasurer = rememberTextMeasurer()
    var textLayoutResult: TextLayoutResult? = remember { null }

    Box(
        modifier = modifier
            .layout { measurable, constraints ->
                val placeable = measurable.measure(constraints)
                var fontSize = style.fontSize

                // Adjust font size until the text fits within the constraints.
                do {
                    // Measure the text layout with the given style and constraints.
                    textLayoutResult = textMeasurer.measure(
                        text = text,
                        style = style.copy(fontSize = fontSize, color = color),
                        constraints = constraints,
                        maxLines = maxLines
                    )
                    // Reduce the font size by 5% in each loop iteration until it fits the box bounds,
                    // only while the given condition is met: (didOverflowHeight == true).
                    fontSize *= RESIZABLE_FONT_SIZE
                } while (textLayoutResult?.didOverflowHeight == true)

                val box = textLayoutResult?.size ?: IntSize.Zero
                layout(box.width, box.height) {
                    placeable.place(ZERO_VALUE, ZERO_VALUE)
                }
            }
            // Draw the text with the adjusted font size.
            .drawWithContent {
                textLayoutResult?.let { drawText(it) }
            }
    )
}

@Preview(showSystemUi = true)
@Composable
fun ResizableTextPreview() {
    OneAppTheme {
        ResizableText(
            text = "William Enrique",
            color = Color.Black
        )
    }
}

const val RESIZABLE_FONT_SIZE = 0.95f
