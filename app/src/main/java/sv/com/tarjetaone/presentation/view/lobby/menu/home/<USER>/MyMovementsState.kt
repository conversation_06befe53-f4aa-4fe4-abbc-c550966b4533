package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements

import androidx.compose.runtime.Stable
import androidx.paging.PagingData
import kotlinx.coroutines.flow.Flow
import sv.com.tarjetaone.common.utils.AppConstants.EMPTY_STRING
import sv.com.tarjetaone.domain.entities.response.CustomerCCardUI
import sv.com.tarjetaone.domain.entities.response.InstallmentTermsUI
import sv.com.tarjetaone.domain.entities.response.PointsCTUI
import sv.com.tarjetaone.domain.entities.response.TransactionsSearchMode
import sv.com.tarjetaone.domain.entities.response.TransactionsUI
import sv.com.tarjetaone.presentation.helpers.UiText

@Stable
data class MyMovementsState(
    val selectedCard: CustomerCCardUI? = null,
    val userCards: List<CustomerCCardUI> = emptyList(),
    val isSearchQueryVisible: Boolean = false,
    val isCalendarQueryVisible: Boolean = false,
    val searchQuery: String = EMPTY_STRING,
    val startDate: String = EMPTY_STRING,
    val endDate: String = EMPTY_STRING,
    val dateRangeDisplay: UiText = UiText.DynamicString(EMPTY_STRING),
    val statusFilter: MovementStatus = MovementStatus.Confirmed,
    val searchMode: TransactionsSearchMode = TransactionsSearchMode.ByCurrentPeriod,
    val selectedTransaction: TransactionsUI? = null,
    val transactionsPaging: Flow<PagingData<TransactionsUI>>? = null,
    val userPoints: PointsCTUI = PointsCTUI(),
    val showInstallmentsBottomSheet: Boolean = false,
    val selectedInstallment: InstallmentTermsUI? = null,
    val isInstallmentsTutorialShown: Boolean = false
)

sealed class MovementStatus {
    data object Pending : MovementStatus()
    data object Confirmed : MovementStatus()
}
