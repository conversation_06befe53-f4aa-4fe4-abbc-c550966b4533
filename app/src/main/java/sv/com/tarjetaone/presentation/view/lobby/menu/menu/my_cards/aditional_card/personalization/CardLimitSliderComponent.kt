package sv.com.tarjetaone.presentation.view.lobby.menu.menu.my_cards.aditional_card.personalization

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.ONE_VALUE
import sv.com.tarjetaone.core.utils.extensions.formattedAmount
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SliderComponent
import sv.com.tarjetaone.presentation.compose.util.FieldDecorationType
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.compose.util.input.CurrencyLimitVisualTransformation
import sv.com.tarjetaone.presentation.helpers.filterDigits

@Composable
fun CardLimitSliderComponent(
    uiState: PersonalizationAdditionalCardUiState,
    onUiEvent: (PersonalizationAdditionalCardUiEvent) -> Unit = {},
) {
    val textColor = if (uiState.isValidAmount) {
        MaterialTheme.customColors.gray700
    } else {
        MaterialTheme.colorScheme.onErrorContainer
    }

    val focusManager = remember { FocusRequester() }

    Column {
        SimpleElevatedTextField(
            label = stringResource(id = R.string.card_limit),
            labelAlignment = Alignment.CenterHorizontally,
            textFieldAlignment = Alignment.Center,
            value = uiState.cardLimit,
            onValueChange = {
                onUiEvent(
                    PersonalizationAdditionalCardUiEvent.OnCardLimitChange(
                        it.filterDigits()
                    )
                )
            },
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Number,
                imeAction = ImeAction.Done
            ),
            decorationType = FieldDecorationType.OUTLINED,
            visualTransformation = CurrencyLimitVisualTransformation(),
            placeholder = stringResource(id = R.string.hint_zero_dollars_without_decimal),
            hasError = !uiState.isValidAmount,
            error = uiState.errorMessage?.asString(),
            errorStyle =  MaterialTheme.typography.labelMedium.copy(
                color = MaterialTheme.colorScheme.onErrorContainer
            ),
            textStyle = MaterialTheme.typography.bodyLarge.copy(
                color = textColor,
                textAlign = TextAlign.Center
            ),
            modifier = Modifier
                .fillMaxWidth()
                .focusRequester(focusManager)
        )
        Spacer8()
        SliderWithCardLimits(
            uiState = uiState,
            onValueChange = {
                onUiEvent(
                    PersonalizationAdditionalCardUiEvent.OnSliderProgressChange(it.toInt())
                )
            },
            onValueChangeFinished = {
                onUiEvent(
                    PersonalizationAdditionalCardUiEvent.OnSliderProgressChangeFinished
                )
            }
        )
    }
}

@Composable
fun SliderWithCardLimits(
    uiState: PersonalizationAdditionalCardUiState,
    onValueChange: (Float) -> Unit = {},
    onValueChangeFinished: () -> Unit = {},
) {
    Column {
        SliderComponent(
            modifier = Modifier.padding(horizontal = MaterialTheme.customDimens.dimen12),
            sliderPosition = uiState.sliderProgress.toFloat(),
            minLimit = uiState.minLimit.toFloat(),
            maxLimit = uiState.maxLimit.toFloat(),
            stepSize = uiState.stepSize,
            isActive = uiState.isValidAmount,
            onValueChange = onValueChange,
            onValueChangeFinished = onValueChangeFinished
        )
        Row {
            Text(
                text = uiState.minLimit.toInt().formattedAmount(ONE_VALUE),
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.customColors.onDefaultSurface
            )
            Spacer(modifier = Modifier.weight(1f))
            Text(
                text = uiState.maxLimit.toInt().formattedAmount(ONE_VALUE),
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.customColors.onDefaultSurface
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun CardLimitSliderComponentPreview() {
    OneAppTheme {
        CardLimitSliderComponent(
            uiState = PersonalizationAdditionalCardUiState(
                sliderProgress = 100,
                minLimit = 100f.toDouble(),
                maxLimit = 1000f.toDouble()
            )
        )
    }
}