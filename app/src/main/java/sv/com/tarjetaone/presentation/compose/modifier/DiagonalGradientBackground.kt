package sv.com.tarjetaone.presentation.compose.modifier

import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import sv.com.tarjetaone.common.utils.AppConstants.VALUE_90_F
import sv.com.tarjetaone.common.utils.AppConstants.VALUE_180_F
import sv.com.tarjetaone.common.utils.AppConstants.VALUE_270_F
import sv.com.tarjetaone.common.utils.AppConstants.ZERO_FLOAT_VALUE
import sv.com.tarjetaone.presentation.compose.util.Corner
import kotlin.math.PI
import kotlin.math.asin
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.hypot
import kotlin.math.sin

/**
 * Draws a gradient that starts from a corner given by [startingPoint] and ends in the opposite corner
 *
 * Solution taken from [this StackOverflow answer](https://stackoverflow.com/a/68018215/6801193)
 */
fun Modifier.diagonalGradientBackground(
    colors: List<Color>,
    startingPoint: Corner = Corner.TOP_LEFT
) = drawBehind {
    val offset = calculateOffsetForSize(size, startingPoint) ?: return@drawBehind

    drawRect(
        brush = Brush.linearGradient(
            colors = colors,
            // negative here so that 0 degrees is left -> right
            // and 90 degrees is top -> bottom
            start = Offset(center.x - offset.x, center.y - offset.y),
            end = Offset(center.x + offset.x, center.y + offset.y)
        ),
        size = size
    )
}

private fun calculateOffsetForSize(size: Size, startingPoint: Corner): Offset? {
    /*
    Have to compute length of gradient vector so that it lies within
    the visible rectangle.
    --------------------------------------------
    | length of gradient ^  /                  |
    |             --->  /  /                   |
    |                  /  / <- rotation angle  |
    |                 /  o --------------------|  y
    |                /  /                      |
    |               /  /                       |
    |              v  /                        |
    --------------------------------------------
                         x

               diagonal angle = atan2(y, x)
             (it's hard to draw the diagonal)

    Simply rotating the diagonal around the centre of the rectanglewill lead to points outside the
    rectangle area. Further, just truncating the coordinate to be at the nearest edge of the
    rectangle to the rotated point will distort the angle.
    Let α be the desired gradient angle (in radians) and γ be the angle of the diagonal of the
    rectangle.
    The correct for the length of the gradient is given by:
    x/|cos(α)|  if -γ <= α <= γ,   or   π - γ <= α <= π + γ
    y/|sin(α)|  if  γ <= α <= π - γ, or π + γ <= α <= 2π - γ
    where γ ∈ (0, π/2) is the angle that the diagonal makes with the base of the rectangle.
    */

    val (x, y) = size
    val gamma = atan2(y, x)

    if (gamma == 0f || gamma == (PI / 2).toFloat()) return null

    val angleForSize = Math.toDegrees(asin(y / hypot(x, y)).toDouble()).toFloat()
    val degrees = angleForSize + startingPoint.degreeOffset()
    val degreesNormalised = (degrees % DEGREES_360).let { if (it < 0) it + DEGREES_360 else it }

    val alpha = (degreesNormalised * PI / DEGREES_180).toFloat()

    val gradientLength = when (alpha) {
        // ray from centre cuts the right edge of the rectangle
        in 0f..gamma, in (2 * PI - gamma)..2 * PI -> { x / cos(alpha) }
        // ray from centre cuts the top edge of the rectangle
        in gamma..(PI - gamma).toFloat() -> { y / sin(alpha) }
        // ray from centre cuts the left edge of the rectangle
        in (PI - gamma)..(PI + gamma) -> { x / -cos(alpha) }
        // ray from centre cuts the bottom edge of the rectangle
        in (PI + gamma)..(2 * PI - gamma) -> { y / -sin(alpha) }
        // default case (which shouldn't really happen)
        else -> hypot(x, y)
    }

    val centerOffsetX = cos(alpha) * gradientLength / 2
    val centerOffsetY = sin(alpha) * gradientLength / 2

    return Offset(centerOffsetX, centerOffsetY)
}

private const val DEGREES_360 = 360
private const val DEGREES_180 = 180

private fun Corner.degreeOffset(): Float = when (this) {
    Corner.TOP_LEFT -> ZERO_FLOAT_VALUE
    Corner.TOP_RIGHT -> VALUE_90_F
    Corner.BOTTOM_RIGHT -> VALUE_180_F
    Corner.BOTTOM_LEFT -> VALUE_270_F
}
