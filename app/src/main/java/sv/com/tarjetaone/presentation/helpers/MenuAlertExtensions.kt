package sv.com.tarjetaone.presentation.helpers

import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import sv.com.tarjetaone.R
import sv.com.tarjetaone.core.utils.MenuAlertType

@ColorRes
fun getAlertColorButton(code: String?): Int {
    return when(code) {
        MenuAlertType.PROTECTION.type -> R.color.color_accent
        MenuAlertType.ACTIVATION.type -> R.color.color_primary
        else -> R.color.error_red
    }
}

@StringRes
fun getAlertButtonText(code: String?): Int {
    return when(code) {
        MenuAlertType.PROTECTION.type -> R.string.activate
        MenuAlertType.ACTIVATION.type -> R.string.continue_button_label
        MenuAlertType.THIRD_DELIVERY_ATTEMPT.type -> R.string.third_attempt_alert_button
        MenuAlertType.THIRD_FAILED_DELIVERY_ATTEMPT.type -> R.string.third_attempt_alert_button
        else -> R.string.continue_button_label
    }
}

@DrawableRes
fun getMenuAlertIcon(icon: String): Int {
    return when (icon) {
        MenuAlertIcons.VISA_CARD -> R.drawable.ic_icon_alert
        MenuAlertIcons.EXCLAMATION_TRIANGLE -> R.drawable.rounded_warning
        MenuAlertIcons.SHIELD_FRAME -> R.drawable.rounded_shield_lock
        else -> R.drawable.rounded_warning
    }
}

object MenuAlertIcons {
    const val SHIELD_FRAME = "shield-frame"
    const val EXCLAMATION_TRIANGLE = "exclamation-triangle"
    const val VISA_CARD = "tarjeta-visa"
}