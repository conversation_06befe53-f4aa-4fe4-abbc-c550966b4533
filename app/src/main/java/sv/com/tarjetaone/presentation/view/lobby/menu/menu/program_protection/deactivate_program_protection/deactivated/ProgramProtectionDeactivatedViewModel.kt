package sv.com.tarjetaone.presentation.view.lobby.menu.menu.program_protection.deactivate_program_protection.deactivated

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.common.utils.extensions.getFormattedDateFromTo
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.core.utils.extensions.configCurrencyWithFractions
import sv.com.tarjetaone.presentation.view.utils.DAY_OF_MONTH_FORMAT
import sv.com.tarjetaone.presentation.view.utils.YEAR_MONTH_DAY_TIME_FORMAT
import java.util.Locale
import javax.inject.Inject

@HiltViewModel
class ProgramProtectionDeactivatedViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {
    private val args =
        ProgramProtectionDeactivatedFragmentArgs.fromSavedStateHandle(savedStateHandle)

    private val _uiState = MutableStateFlow(ProgramProtectionDeactivatedUiState())
    val uiState: StateFlow<ProgramProtectionDeactivatedUiState> = _uiState.asStateFlow()

    private fun onStart() {
        val (deleteFraud, fraud) = args.deleteFraud to args.fraud
        _uiState.update {
            it.copy(
                deactivationDate = deleteFraud?.applicationDate?.getFormattedDateFromTo(
                    YEAR_MONTH_DAY_TIME_FORMAT,
                    DAY_OF_MONTH_FORMAT,
                    Locale.US
                ).orEmpty(),
                chargeAmount = fraud?.fProtectionTypes?.firstOrNull()?.amount?.configCurrencyWithFractions()
                    .toString()
            )
        }
    }

    private fun onContinueClick() {
        sendEvent(
            UiEvent.Navigate(
                ProgramProtectionDeactivatedFragmentDirections
                    .actionDeactivatedProgramProtectionFragmentToDetailProgramProtectionFragment(
                        fraudResponse = args.deleteFraud
                    )
            )
        )
    }

    fun onEvent(event: ProgramProtectionDeactivatedUiEvent) {
        when (event) {
            is ProgramProtectionDeactivatedUiEvent.OnStart -> onStart()
            is ProgramProtectionDeactivatedUiEvent.OnContinueClick -> onContinueClick()
            ProgramProtectionDeactivatedUiEvent.OnSupportClick -> sendEvent(UiEvent.TwilioClick)
        }
    }
}
