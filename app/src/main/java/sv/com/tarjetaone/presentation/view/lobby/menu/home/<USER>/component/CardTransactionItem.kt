package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.LAYOUT_WEIGHT_1
import sv.com.tarjetaone.common.utils.AppConstants.ROTATION_90
import sv.com.tarjetaone.common.utils.AppConstants.ROTATION_ZERO
import sv.com.tarjetaone.common.utils.AppConstants.THREE_VALUE
import sv.com.tarjetaone.common.utils.AppConstants.TWO_VALUE
import sv.com.tarjetaone.core.utils.extensions.formatAsString
import sv.com.tarjetaone.core.utils.extensions.orZero
import sv.com.tarjetaone.domain.entities.response.PointsCTUI
import sv.com.tarjetaone.domain.entities.response.TransactionsUI
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.button.ButtonSize
import sv.com.tarjetaone.presentation.compose.uicomponent.button.HyperLinkTextButton
import sv.com.tarjetaone.presentation.compose.uicomponent.button.OneButton
import sv.com.tarjetaone.presentation.compose.util.ButtonVariant
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.TransactionItemAction

@Composable
fun CardTransactionItem(
    modifier: Modifier = Modifier,
    userPoints: PointsCTUI,
    transaction: TransactionsUI,
    onTransactionAction: (TransactionItemAction, TransactionsUI) -> Unit,
    isExpanded: Boolean
) {
    Card(
        shape = MaterialTheme.shapes.medium,
        modifier = modifier.shadow(
            MaterialTheme.customDimens.dimen2,
            MaterialTheme.shapes.medium
        ),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.background),
        onClick = {
            if (transaction.isPosted == true) onTransactionAction(
                TransactionItemAction.OnToggleDetails,
                transaction
            )
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.customDimens.dimen16)
        ) {
            TransactionInfoSection(transaction, isExpanded)
            AnimatedVisibility(visible = isExpanded) {
                TransactionDetailsSection(
                    userPoints = userPoints,
                    transaction = transaction,
                    onTransactionAction = onTransactionAction
                )
            }
        }
    }
}

@Composable
private fun TransactionInfoSection(
    transaction: TransactionsUI,
    isExpanded: Boolean
) {
    Column(modifier = Modifier.fillMaxWidth()) {
        TransactionHeader(
            transaction = transaction,
            isExpanded = isExpanded
        )
        if (transaction.managementText.isNullOrBlank().not()) {
            Spacer(modifier = Modifier.height(MaterialTheme.customDimens.dimen8))
            ManagementText(transaction.managementText)
        }
    }
}

@Composable
private fun TransactionHeader(
    transaction: TransactionsUI,
    isExpanded: Boolean
) {
    val descTextStyle = MaterialTheme.typography.bodySmall.copy(
        color = MaterialTheme.colorScheme.secondary,
        fontWeight = FontWeight.SemiBold,
        fontSize = MaterialTheme.customDimensSp.sp16
    )
    val detailStyle = MaterialTheme.typography.labelSmall.copy(
        color = MaterialTheme.customColors.secondaryLight,
        fontWeight = FontWeight.Normal,
        fontSize = MaterialTheme.customDimensSp.sp13
    )

    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        TransactionDescription(
            modifier = Modifier.weight(LAYOUT_WEIGHT_1),
            transaction,
            isExpanded,
            descTextStyle,
            detailStyle
        )
        Spacer(modifier = Modifier.width(MaterialTheme.customDimens.dimen4))
        TransactionAmount(
            transaction,
            descTextStyle
        )

        if (transaction.isPosted == true) {
            ExpandIcon(isExpanded = isExpanded)
        }
    }
}

@Composable
private fun TransactionDescription(
    modifier: Modifier,
    transaction: TransactionsUI,
    isExpanded: Boolean,
    descTextStyle: TextStyle,
    detailStyle: TextStyle
) {
    Column(
        verticalArrangement = Arrangement.Center,
        modifier = modifier
    ) {
        Text(
            text = transaction.description.orEmpty(),
            maxLines = if (isExpanded) THREE_VALUE else TWO_VALUE,
            overflow = TextOverflow.Ellipsis,
            style = descTextStyle
        )
        if (transaction.isCreditType().not()) {
            Text(
                text = transaction.getLastCardDigits(),
                style = detailStyle
            )
        }
    }
}

@Composable
private fun TransactionAmount(
    transaction: TransactionsUI,
    descTextStyle: TextStyle
) {
    Column(horizontalAlignment = Alignment.End) {
        Text(
            text = formatTransactionAmountText(transaction),
            style = descTextStyle.copy(
                color = when {
                    transaction.isPosted == false -> Color.Black
                    transaction.isDebitType() -> MaterialTheme.colorScheme.primary
                    else -> MaterialTheme.customColors.successContainer
                }
            )
        )
        Text(
            text = transaction.getDateFormatted(),
            style = descTextStyle
        )
    }
}

@Composable
private fun ManagementText(managementText: String?) {
    Row(
        horizontalArrangement = Arrangement.End,
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(
            text = managementText.orEmpty(),
            style = MaterialTheme.typography.labelSmall.copy(
                color = MaterialTheme.customColors.alertDark,
                fontWeight = FontWeight.Normal,
                fontSize = MaterialTheme.customDimensSp.sp10
            ),
            modifier = Modifier
                .border(
                    width = MaterialTheme.customDimens.dimen1,
                    color = MaterialTheme.customColors.alertDark,
                    shape = MaterialTheme.shapes.extraSmall
                )
                .padding(
                    horizontal = MaterialTheme.customDimens.dimen13,
                    vertical = MaterialTheme.customDimens.dimen4
                )
        )
    }
}

@Composable
private fun ExpandIcon(isExpanded: Boolean) {
    val rotation by animateFloatAsState(
        targetValue = if (isExpanded) ROTATION_90 else ROTATION_ZERO,
        label = "icon_rotation"
    )
    Spacer(modifier = Modifier.width(MaterialTheme.customDimens.dimen10))
    Icon(
        painter = painterResource(id = R.drawable.ic_arrow_right_filled),
        contentDescription = null,
        tint = MaterialTheme.colorScheme.onSurface,
        modifier = Modifier
            .padding(end = MaterialTheme.customDimens.dimen4)
            .rotate(rotation)
    )
}

@Composable
private fun TransactionDetailsSection(
    userPoints: PointsCTUI,
    transaction: TransactionsUI,
    onTransactionAction: (TransactionItemAction, TransactionsUI) -> Unit,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.fillMaxWidth()
    ) {
        Spacer(modifier = Modifier.height(MaterialTheme.customDimens.dimen16))
        Text(
            text = stringResource(
                id = R.string.authorization_number,
                transaction.authorizationCode.orEmpty()
            ),
            style = MaterialTheme.typography.headlineSmall.copy(
                color = MaterialTheme.colorScheme.secondary,
                fontWeight = FontWeight.Bold,
                fontSize = MaterialTheme.customDimensSp.sp14
            ),
        )
        if (transaction.displayPointsNeeded(userPoints)) {
            Spacer(modifier = Modifier.height(MaterialTheme.customDimens.dimen8))
            Text(
                text = stringResource(
                    id = R.string.necessary_points_to_pay,
                    transaction.pointsNeeded?.formatAsString() ?: 0
                ),
                style = MaterialTheme.typography.labelSmall.copy(
                    color = MaterialTheme.colorScheme.primary,
                    fontWeight = FontWeight.Normal,
                    fontSize = MaterialTheme.customDimensSp.sp13
                ),
                textAlign = TextAlign.Center
            )
        }
        if (transaction.notRecognizedAllowed == true) {
            Spacer(modifier = Modifier.height(MaterialTheme.customDimens.dimen4))
            HyperLinkTextButton(
                text = stringResource(id = R.string.i_did_not_make_this_purchase),
                textStyle = MaterialTheme.typography.labelSmall.copy(
                    fontSize = MaterialTheme.customDimensSp.sp13,
                    lineHeight = MaterialTheme.customDimensSp.sp14,
                    textDecoration = TextDecoration.Underline
                ),
                onClick = {
                    onTransactionAction(
                        TransactionItemAction.OnUnrecognizedPurchaseClick,
                        transaction
                    )
                }
            )
        }
        if (transaction.installmentsAllowed == true) {
            Spacer(modifier = Modifier.height(MaterialTheme.customDimens.dimen8))
            OneButton(
                text = stringResource(id = R.string.pay_in_instalments),
                textStyle = MaterialTheme.typography.labelSmall.copy(
                    fontWeight = FontWeight.SemiBold,
                    fontSize = MaterialTheme.customDimensSp.sp16
                ),
                size = ButtonSize.MEDIUM,
                onClick = {
                    onTransactionAction(TransactionItemAction.OnPayInInstallmentsClick, transaction)
                }
            )
        }
        if (transaction.pointsPaymentAllowed == true) {
            Spacer(modifier = Modifier.height(MaterialTheme.customDimens.dimen8))
            OneButton(
                text = stringResource(id = R.string.pay_with_points),
                textStyle = MaterialTheme.typography.labelSmall.copy(
                    fontWeight = FontWeight.SemiBold,
                    fontSize = MaterialTheme.customDimensSp.sp16
                ),
                buttonVariant = ButtonVariant.SECONDARY_VARIANT,
                size = ButtonSize.MEDIUM,
                onClick = {
                    onTransactionAction(TransactionItemAction.OnPayWithPointsClick, transaction)
                }
            )
        }
        if (transaction.displayNotEnoughPoints(userPoints)) {
            Spacer(modifier = Modifier.height(MaterialTheme.customDimens.dimen8))
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_info_outline),
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.error
                )
                Spacer(modifier = Modifier.width(MaterialTheme.customDimens.dimen10))
                Text(
                    text = stringResource(
                        id = R.string.not_enough_points_to_pay,
                        userPoints.amount.orZero().formatAsString()
                    ),
                    style = MaterialTheme.typography.labelSmall.copy(
                        color = MaterialTheme.colorScheme.error,
                        fontWeight = FontWeight.Normal,
                        fontSize = MaterialTheme.customDimensSp.sp13
                    )
                )
            }
        }
    }
}

@Composable
private fun formatTransactionAmountText(transaction: TransactionsUI): String {
    return if (transaction.isPosted == true) {
        stringResource(
            id = if (transaction.isDebitType()) {
                R.string.negative_transaction
            } else {
                R.string.positive_transaction
            },
            transaction.getAmountFormatted()
        )
    } else {
        transaction.getAmountFormatted()
    }
}

@Preview
@Composable
private fun CollapsedTransactionItemPreview() {
    OneAppTheme {
        CardTransactionItem(
            userPoints = PointsCTUI(),
            transaction = itemAllActionsAllowed,
            onTransactionAction = { _, _ -> },
            isExpanded = false,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

@Preview
@Composable
private fun ExpandedTransactionItemPreview() {
    OneAppTheme {
        CardTransactionItem(
            userPoints = PointsCTUI(amount = 100000),
            transaction = itemAllActionsAllowed,
            onTransactionAction = { _, _ -> },
            isExpanded = true,
            modifier = Modifier.fillMaxWidth()
        )
    }
}
