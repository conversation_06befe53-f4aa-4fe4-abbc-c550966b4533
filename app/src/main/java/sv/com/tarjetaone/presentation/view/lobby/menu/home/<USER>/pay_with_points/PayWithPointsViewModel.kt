package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_movements.pay_with_points

import androidx.lifecycle.SavedStateHandle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import sv.com.tarjetaone.core.utils.BaseViewModel
import sv.com.tarjetaone.core.utils.UiEvent
import sv.com.tarjetaone.domain.entities.request.PointsPaymentRequestUI
import javax.inject.Inject

@HiltViewModel
class PayWithPointsViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle
) : BaseViewModel() {
    private val args = PayWithPointsFragmentArgs.fromSavedStateHandle(savedStateHandle)
    private val _uiState = MutableStateFlow(PayWithPointsUiState())
    val uiState = _uiState.asStateFlow()

    private fun onStart() {
        _uiState.update {
            it.copy(
                transaction = args.transaction,
                availablePoints = args.availablePoints,
                cardId = args.cardId
            )
        }
    }

    private fun onContinueClick() {
        val request = with(uiState.value) {
            PointsPaymentRequestUI(
                cCardId = cardId,
                authorizationCode = transaction.authorizationCode,
                valueDate = transaction.valueDate,
                description = transaction.description,
                amount = transaction.amount
            )
        }
        sendEvent(
            UiEvent.Navigate(
                PayWithPointsFragmentDirections
                    .actionPayWithPointsFragmentToPayWithPointsPrepareForPictureFragment(request)
            )
        )
    }

    fun onEvent(event: PayWithPointsUiEvent) {
        when (event) {
            PayWithPointsUiEvent.OnBackClick -> sendEvent(UiEvent.NavigateBack)
            PayWithPointsUiEvent.OnTwilioClick -> sendEvent(UiEvent.TwilioClick)
            PayWithPointsUiEvent.OnContinueClick -> onContinueClick()
            PayWithPointsUiEvent.OnDoNotPayClick -> sendEvent(UiEvent.NavigateBack)
            is PayWithPointsUiEvent.OnStart -> onStart()
        }
    }
}
