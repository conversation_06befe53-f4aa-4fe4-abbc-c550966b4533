package sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.component

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.SoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import sv.com.tarjetaone.R
import sv.com.tarjetaone.presentation.compose.uicomponent.common.SimpleElevatedTextField
import sv.com.tarjetaone.presentation.compose.uicomponent.common.TransactionsStateFilter
import sv.com.tarjetaone.presentation.compose.util.Spacer8
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.MegaPointsStatus
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.MyMegaPointsUiEvent
import sv.com.tarjetaone.presentation.view.lobby.menu.home.my_megapoints.MyMegaPointsUiState

@Composable
fun MyMegaPointsFilters(
    modifier: Modifier = Modifier,
    uiState: MyMegaPointsUiState = MyMegaPointsUiState(),
    keyboardController: SoftwareKeyboardController?,
    onEvent: (event: MyMegaPointsUiEvent) -> Unit = {}
) {
    Column(modifier = modifier) {
        TransactionsStateFilter(
            status = uiState.megaPointsStatusFilter,
            firstStatus = MegaPointsStatus.Accumulated,
            firstStatusName = R.string.megaPoints_accumulated_filter,
            secondStatus = MegaPointsStatus.Redeemed,
            secondStatusName = R.string.megaPoints_redeemed_filter,
            onStatusChange = { onEvent(MyMegaPointsUiEvent.OnFilterStatusChange(it)) }
        )
        Spacer8()
        if (uiState.megaPointsStatusFilter == MegaPointsStatus.Accumulated) {
            SimpleElevatedTextField(
                value = uiState.searchQuery,
                onValueChange = { onEvent(MyMegaPointsUiEvent.OnSearchQueryChange(it)) },
                trailingIcon = {
                    Icon(
                        painter = painterResource(R.drawable.ic_search_white),
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.clickable {
                            onEvent(MyMegaPointsUiEvent.OnPerformSearchQuery)
                            keyboardController?.hide()
                        }
                    )
                },
                placeholder = stringResource(id = R.string.buscar),
                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Search),
                keyboardActions = KeyboardActions(
                    onSearch = {
                        onEvent(MyMegaPointsUiEvent.OnPerformSearchQuery)
                        keyboardController?.hide()
                    }
                )
            )
        }
    }
}
