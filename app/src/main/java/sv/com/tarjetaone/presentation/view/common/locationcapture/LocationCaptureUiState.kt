package sv.com.tarjetaone.presentation.view.common.locationcapture

import android.location.Address
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.runtime.Stable
import androidx.compose.ui.unit.Dp
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.google.maps.android.compose.DefaultMapContentPadding
import sv.com.tarjetaone.R
import sv.com.tarjetaone.common.utils.AppConstants.COMMA_WITH_SPACE
import sv.com.tarjetaone.common.utils.AppConstants.LAYOUT_WEIGHT_1
import sv.com.tarjetaone.common.utils.AppConstants.SV_DEFAULT_LOCATION_LAT
import sv.com.tarjetaone.common.utils.AppConstants.SV_DEFAULT_LOCATION_LNG
import sv.com.tarjetaone.common.utils.AppConstants.THREE_VALUE_PERCENT
import sv.com.tarjetaone.domain.entities.request.UserLocationUI
import sv.com.tarjetaone.domain.entities.response.CatalogItemsCollectionUI
import sv.com.tarjetaone.domain.entities.response.SuggestionsItemUi
import sv.com.tarjetaone.presentation.helpers.UiText
import sv.com.tarjetaone.presentation.view.common.locationcapture.helper.AddressAliasSuggestion

@Stable
data class LocationCaptureUiState(
    val userAddress: UserLocationUI = UserLocationUI(),
    val mapAddress: Address? = null,
    val location: LatLng = LatLng(SV_DEFAULT_LOCATION_LAT, SV_DEFAULT_LOCATION_LNG),
    val displayAddress: String = "", // Display address in bottom sheet
    val isAddingAddressInfo: Boolean = false,
    val validLocation: Boolean = false,
    val showExcludedZones: Boolean = false,
    val searchQuery: String = "",
    val searchSuggestions: List<SuggestionsItemUi> = emptyList(),
    val shouldShowForm: Boolean = false,
    val excludedZones: List<List<LatLng>> = emptyList(),
    val svZone: List<LatLng>? = null,
    val showSearchSnackBar: Boolean = true,
    val showSnackBarError: Boolean = false,

    // Address Info
    val addressAlias: String? = null,
    val addressAliasSuggestions: List<AddressAliasSuggestion> = listOf(
        AddressAliasSuggestion.Home,
        AddressAliasSuggestion.Work,
        AddressAliasSuggestion.Other
    ),
    val addressAliasSelected: AddressAliasSuggestion? = AddressAliasSuggestion.Home,
    val states: List<CatalogItemsCollectionUI> = emptyList(),
    val municipalities: List<CatalogItemsCollectionUI> = emptyList(),
    val selectedState: CatalogItemsCollectionUI? = null,
    val selectedMunicipality: CatalogItemsCollectionUI? = null,
    val neighborhood: String = "",
    val street: String = "",
    val homeNumber: String = "",
    val additionalInfo: String = "",
    val validAlias: Boolean = false,
    val validAliasLength: Boolean = false,
    val validNeighborhood: Boolean = false,
    val validStreet: Boolean = false,
    val validHomeNumber: Boolean = false,
    val validAdditionalInfo: Boolean = false,
) {
    val validAddressForm =
        (addressAliasSelected != AddressAliasSuggestion.Other || (validAlias && validAliasLength))
                && validNeighborhood && validStreet && validHomeNumber
                && (validAdditionalInfo || additionalInfo.isEmpty())
                && selectedState != null && selectedMunicipality != null

    fun getInvalidAliasMessage(): UiText? {
        return if (!validAlias && addressAlias?.isNotEmpty() == true) {
            UiText.StringResource(R.string.invalid_characters)
        } else if (!validAliasLength) {
            UiText.StringResource(R.string.invalid_alias_length)
        } else {
            null
        }
    }

    /**
     * Build display address with the info provided by the user in the form
     */
    fun buildFullAddress(): String = buildString {
        append(neighborhood).append(COMMA_WITH_SPACE)
        append(street).append(COMMA_WITH_SPACE)
        append(homeNumber)
    }

    fun retrieveAddressAlias(): String =
        addressAlias.takeIf { addressAliasSelected == AddressAliasSuggestion.Other }
            ?: addressAliasSelected?.value.orEmpty()

    /**
     * These functions are moved here to avoid cognitive complexity in the composable
     */

    /**
     * filter excluded zones to only those that are visible in the current map bounds
     */
    fun filterVisibleExcludedZones(mapBounds: LatLngBounds?): List<List<LatLng>> {
        return excludedZones.filter { zone ->
            zone.any { latLng -> mapBounds?.contains(latLng) == true }
        }
    }

    fun getGoogleMapMaxHeightWeight(): Float = if (isAddingAddressInfo) {
        THREE_VALUE_PERCENT
    } else {
        LAYOUT_WEIGHT_1
    }

    fun getMapContentPadding(sheetHeight: Dp) = if (isAddingAddressInfo) {
        DefaultMapContentPadding
    } else {
        PaddingValues(bottom = sheetHeight)
    }
}
