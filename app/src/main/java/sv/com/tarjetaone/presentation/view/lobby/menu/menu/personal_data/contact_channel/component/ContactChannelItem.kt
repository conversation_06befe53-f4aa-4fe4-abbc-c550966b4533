package sv.com.tarjetaone.presentation.view.lobby.menu.menu.personal_data.contact_channel.component

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import sv.com.tarjetaone.R
import sv.com.tarjetaone.domain.entities.request.ContactNotificationType
import sv.com.tarjetaone.domain.entities.request.CustomerContactsUI
import sv.com.tarjetaone.domain.entities.response.ContactType
import sv.com.tarjetaone.presentation.compose.theme.OneAppTheme
import sv.com.tarjetaone.presentation.compose.theme.customColors
import sv.com.tarjetaone.presentation.compose.theme.customDimens
import sv.com.tarjetaone.presentation.compose.theme.customDimensSp
import sv.com.tarjetaone.presentation.compose.uicomponent.common.OneSwitch
import sv.com.tarjetaone.presentation.compose.util.Spacer32
import sv.com.tarjetaone.presentation.compose.util.Spacer4
import sv.com.tarjetaone.presentation.compose.util.Spacer8

@Composable
fun ContactChannelItem(
    modifier: Modifier = Modifier,
    contactItem: ContactItemUiState,
    onModifyClick: (ContactItemUiState) -> Unit,
    onToggleNotification: (Boolean, ContactItemUiState, ContactNotificationType) -> Unit,
) {
    ElevatedCard(
        colors = CardDefaults.elevatedCardColors(
            containerColor = MaterialTheme.customColors.defaultSurface
        ),
        modifier = modifier
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    vertical = MaterialTheme.customDimens.dimen16,
                    horizontal = MaterialTheme.customDimens.dimen24
                )
        ) {
            Text(
                text = if (contactItem.contactType == ContactType.Email) {
                    stringResource(id = R.string.email_address)
                } else {
                    stringResource(id = R.string.phone_label)
                },
                style = MaterialTheme.typography.bodyLarge.copy(fontWeight = FontWeight.SemiBold),
                color = MaterialTheme.customColors.textBodyLight
            )
            Text(
                text = contactItem.contact.contactValue.orEmpty(),
                style = MaterialTheme.typography.bodyLarge.copy(
                    fontWeight = FontWeight.SemiBold,
                    fontSize = MaterialTheme.customDimensSp.sp15
                ),
                color = MaterialTheme.customColors.onDefaultSurface
            )
            Spacer8()
            Text(
                text = stringResource(id = R.string.modify),
                style = MaterialTheme.typography.labelMedium,
                color = MaterialTheme.colorScheme.primary,
                textDecoration = TextDecoration.Underline,
                modifier = Modifier.clickable { onModifyClick(contactItem) }
            )
            Spacer32()
            Text(
                text = stringResource(id = R.string.you_will_receive_notifications_from),
                style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.SemiBold),
                color = MaterialTheme.customColors.textBodyLight
            )
            Spacer4()
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = stringResource(id = R.string.shopping_withdraws),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.customColors.headingDark
                )
                OneSwitch(
                    checked = contactItem.contact.notificationTran ?: false,
                    onCheckedChange = {
                        onToggleNotification(it, contactItem, ContactNotificationType.Transactions)
                    }
                )
            }
            Spacer4()
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = stringResource(id = R.string.promotions),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.customColors.headingDark
                )
                OneSwitch(
                    checked = contactItem.contact.notificationAdvertising ?: false,
                    onCheckedChange = {
                        onToggleNotification(it, contactItem, ContactNotificationType.Advertising)
                    }
                )
            }
        }
    }
}

@Preview
@Composable
private fun ContactChannelItemPreview() {
    OneAppTheme {
        ContactChannelItem(
            contactItem = ContactItemUiState(
                contactType = ContactType.Email,
                contact = CustomerContactsUI(
                    contactValue = "<EMAIL>"
                )
            ),
            onModifyClick = {},
            onToggleNotification = { _, _, _ -> }
        )
    }
}
