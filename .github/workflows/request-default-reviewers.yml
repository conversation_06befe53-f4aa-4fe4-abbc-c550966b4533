
name: Request default reviewers

on:
  pull_request:
    types: [opened, reopened, ready_for_review]
    # Removed 'synchronize' to prevent execution on each push

permissions:
  pull-requests: write

jobs:
  request-reviewers:
    runs-on: ubuntu-latest
    steps:
      - name: Request default reviewers
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const pr = context.payload.pull_request;
            if (!pr) {
              console.log('No pull request found in context.');
              return;
            }

            const { owner, repo } = context.repo;
            const pull_number = pr.number;
            const author = pr.user.login;

            // List of individual GitHub usernames to request as reviewers
            const allReviewers = [
              'gmuller-applaudo',
              'olizama-applaudo',
              'rrubio-applaudo',
              'rescalon-applaudo',
              'ktapia-applaudo',
              'sramosc-baes',
              'dmassana-baes'
            ];

            // Filter out the PR author from the reviewers list
            const reviewers = allReviewers.filter(reviewer => reviewer !== author);

            if (reviewers.length === 0) {
              console.log(`No reviewers to request after filtering out author: ${author}`);
              return;
            }

            try {
              await github.rest.pulls.requestReviewers({
                owner,
                repo,
                pull_number,
                reviewers
              });
              console.log(`Requested reviewers for PR #${pull_number}: ${reviewers.join(', ')} (filtered out author: ${author})`);
            } catch (error) {
              console.log('Failed to request reviewers:', error.message || error);
            }
