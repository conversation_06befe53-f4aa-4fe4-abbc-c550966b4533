name: Populate PR body with commits

on:
  pull_request:
    types: [opened, reopened]
  workflow_dispatch:

permissions:
  contents: write
  pull-requests: write

jobs:
  populate-body:
    runs-on: ubuntu-latest
    steps:
      - name: Populate PR body with commit list
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const pr = context.payload.pull_request;
            
            if (!pr) {
              console.log('No pull request in context. This workflow should be triggered by a pull request event.');
              return;
            }

            const { owner, repo } = context.repo;
            const pull_number = pr.number;

            const commitsResp = await github.rest.pulls.listCommits({
              owner,
              repo,
              pull_number,
            });

            const commits = commitsResp.data || [];
            if (commits.length === 0) {
              console.log('No commits found for PR #' + pull_number);
              return;
            }

            const commitLines = commits.map(c => {
              const sha = c.sha.substring(0, 7);
              const messageLine = c.commit && c.commit.message ? c.commit.message.split('\n')[0] : '';
              return `- Commit ${sha}: ${messageLine}`;
            }).join('\n');

            const markerStart = '## What is new (required)';
            const markerEnd = '## Related issue / Jira';
            let body = pr.body || '';

            if (body.includes(markerStart)) {
              const before = body.split(markerStart)[0];
              const afterParts = body.split(markerStart)[1].split(markerEnd);
              const after = afterParts.length > 1 ? markerEnd + afterParts.slice(1).join(markerEnd) : '';
              const newSection = `${markerStart}\n${commitLines}\n\n`;
              body = before + newSection + after;
            } else {
              const newSection = `${markerStart}\n${commitLines}\n\n`;
              body = newSection + body;
            }

            if (body !== pr.body) {
              await github.rest.pulls.update({
                owner,
                repo,
                pull_number,
                body,
              });
              console.log(`Updated PR #${pull_number} body with ${commits.length} commits.`);
            } else {
              console.log('PR body already up to date.');
            }
